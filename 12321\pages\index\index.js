const app = getApp()
const util = require('../../utils/util.js')

// 使用安全的错误处理方式
const originalConsoleError = console.error;
console.error = function() {
  // 过滤掉wxfile相关的错误
  const args = Array.from(arguments);
  const errorString = args.join(' ');
  if (errorString.indexOf('wxfile://') !== -1 || 
      errorString.indexOf('no such file or directory') !== -1) {
    // 忽略文件系统错误
    return;
  }
  return originalConsoleError.apply(console, arguments);
};

// 添加检测蓝牙是否打开的函数
function checkBluetoothIsOn() {
  return new Promise((resolve) => {
    if (wx.getSystemInfoSync().bluetoothEnabled === false) {
      resolve(false);
      return;
    }
    
    // 通过API检测蓝牙状态
    wx.getBluetoothAdapterState({
      success: (res) => {
        resolve(res.available);
      },
      fail: () => {
        resolve(false);
      }
    });
  });
}

// 蓝牙状态常量
const BLE_STATE = {
  INIT: 0,      // 未初始化
  INITING: 1,   // 正在初始化
  READY: 2,     // 已初始化，可用
  SCANNING: 3,  // 正在扫描
  CONNECTING: 4 // 正在连接
};

// 蓝牙操作状态锁
let BLE_OPERATION_LOCK = {
  initializing: false,    // 是否正在初始化中
  initStartTime: 0,       // 初始化开始时间
  initTimeoutId: null,    // 初始化超时定时器ID
  scanning: false,        // 是否正在扫描中
  scanStartTime: 0,       // 扫描开始时间
  scanTimeoutId: null,    // 扫描超时定时器ID
  lastOperation: '',      // 最后一次操作
  operationCount: 0       // 操作计数
};

// 设备品牌适配
const DEVICE_PROFILES = {
  XIAOMI: {
    searchTimeout: 15000,
    useEmptyServices: true,
    searchInterval: 300
  },
  HUAWEI: {
    searchTimeout: 10000,
    useEmptyServices: true,
    searchInterval: 0,
    powerLevel: 'high',
    initDelay: 800,
    retryTimes: 2,
    retryDelay: 1500,
    initLockTimeout: 5000, // 初始化锁定超时时间，5秒
    android12: {
      // 针对安卓12的特殊处理
      searchTimeout: 8000,
      initDelay: 1200,
      retryTimes: 3,
      retryDelay: 2000,
      scanningLimit: 4000, // 限制单次扫描时间
      initLockTimeout: 8000 // 安卓12增加锁定超时
    }
  },
  OPPO: {
    searchTimeout: 12000,
    useEmptyServices: true,
    searchInterval: 0,
    powerLevel: 'high'
  },
  VIVO: {
    searchTimeout: 15000,
    useEmptyServices: true,
    searchInterval: 0
  },
  HONOR: {
    // 荣耀设备单独处理，与华为类似但有差异
    searchTimeout: 12000,
    useEmptyServices: true,
    searchInterval: 0,
    powerLevel: 'high',
    initDelay: 1000,
    initLockTimeout: 5000 // 初始化锁定超时时间，5秒
  },
  DEFAULT: {
    searchTimeout: 20000,
    useEmptyServices: false,
    searchInterval: 300,
    initLockTimeout: 3000 // 默认初始化锁定超时
  }
};

// 已知的智能锁设备前缀列表
const SMART_LOCK_PREFIXES = ['D30', 'Lock', 'Smart', 'BLE', 'JY', 'TY', 'JYLOCK', 'CASTLE'];

// 常见的干扰设备，不显示这些设备
const COMMON_INTERFERENCE_DEVICES = ['AirPods', 'Mi Band', 'HUAWEI', 'HONOR', 'Buds', 'Watch'];

// 获取设备配置
function getDeviceProfile(brand, system) {
  if (!brand) return DEVICE_PROFILES.DEFAULT;
  
  const upperBrand = brand.toUpperCase();
  let profile = null;
  
  if (upperBrand.includes('XIAOMI') || upperBrand.includes('REDMI')) {
    profile = Object.assign({}, DEVICE_PROFILES.XIAOMI);
  } else if (upperBrand.includes('HUAWEI')) {
    profile = Object.assign({}, DEVICE_PROFILES.HUAWEI);
    // 针对华为安卓12/13的特别优化
    if (system && (system.includes('Android 12') || system.includes('Android 13'))) {
      profile = Object.assign(profile, DEVICE_PROFILES.HUAWEI.android12);
    }
  } else if (upperBrand.includes('OPPO')) {
    profile = Object.assign({}, DEVICE_PROFILES.OPPO);
  } else if (upperBrand.includes('VIVO')) {
    profile = Object.assign({}, DEVICE_PROFILES.VIVO);
  } else if (upperBrand.includes('HONOR')) {
    profile = Object.assign({}, DEVICE_PROFILES.HONOR);
  } else {
    profile = Object.assign({}, DEVICE_PROFILES.DEFAULT);
  }
  
  return profile;
}

function inArray(arr, key, val) {
  for (let i = 0; i < arr.length; i++) {
    if (arr[i][key] === val) {
      return i;
    }
  }
  return -1;
}

// ArrayBuffer转16进度字符串示例
function ab2hex(buffer) {
  var hexArr = Array.prototype.map.call(
    new Uint8Array(buffer),
    function (bit) {
      return ('00' + bit.toString(16)).slice(-2)
    }
  )
  return hexArr.join('');
}

// 检查系统平台
function getSystemInfo() {
  try {
    const res = wx.getSystemInfoSync();
    return {
      platform: res.platform, // ios, android, windows, mac
      system: res.system,     // iOS 10.0.1, Android 10, etc.
      brand: res.brand,       // iPhone, HUAWEI, etc.
      model: res.model,        // iPhone X, HUAWEI P40, etc.
      SDKVersion: res.SDKVersion // 基础库版本
    };
  } catch (e) {
    console.error('获取系统信息失败:', e);
    return { platform: 'unknown', brand: 'unknown', system: 'unknown' };
  }
}

// 添加设备到列表并检查是否为智能锁设备
function addDeviceToList(devicesList, device) {
  // 确保设备有名称
  if (!device.name && !device.localName) {
    return devicesList;
  }

  // 检查设备是否已在列表中
  const idx = inArray(devicesList, 'deviceId', device.deviceId);
  const deviceName = device.name || device.localName || 'Unknown Device';
  
  // 检查是否为智能锁设备
  let isLockDevice = false;
  for (const prefix of SMART_LOCK_PREFIXES) {
    if (deviceName.toUpperCase().includes(prefix.toUpperCase())) {
      isLockDevice = true;
      break;
    }
  }

  // 检查是否为干扰设备
  let isInterferenceDevice = false;
  for (const prefix of COMMON_INTERFERENCE_DEVICES) {
    if (deviceName.toUpperCase().includes(prefix.toUpperCase())) {
      isInterferenceDevice = true;
      break;
    }
  }

  // 如果是干扰设备且不是智能锁，不添加到列表
  if (isInterferenceDevice && !isLockDevice) {
    return devicesList;
  }

  // 检查设备连接状态
  const isConnecting = app.globalData.ble_device && 
                      app.globalData.ble_device.deviceId === device.deviceId && 
                      app.globalData.bleState === BLE_STATE.CONNECTING;
                      
  const isConnected = app.globalData.ble_device && 
                     app.globalData.ble_device.deviceId === device.deviceId && 
                     app.globalData.connected;

  // 如果设备已在列表中，更新RSSI和连接状态
  if (idx !== -1) {
    devicesList[idx] = {
      ...devicesList[idx],
      RSSI: device.RSSI,
      isLock: isLockDevice,
      connecting: isConnecting || devicesList[idx].connecting,
      connected: isConnected || devicesList[idx].connected
    };
    return devicesList;
  }

  // 添加新设备到列表
  devicesList.push({
    name: deviceName,
    deviceId: device.deviceId,
    RSSI: device.RSSI,
    advertisData: device.advertisData,
    advertisServiceUUIDs: device.advertisServiceUUIDs,
    localName: device.localName,
    isLock: isLockDevice,
    connecting: isConnecting,
    connected: isConnected
  });

  return devicesList;
}

Page({
  data: {
    devices: [],
    connected: false,
    chs: [], 
    misScanding: false,
    scandbutName:"刷新设备列表",
    systemInfo: null,
    bluetoothInitialized: false,
    isInitializing: false, // 标记蓝牙是否正在初始化中
    errorCount: 0, // 记录错误次数，避免无限重试
    lastErrorTime: 0, // 记录上次错误时间，避免频繁提示
    networkType: 'unknown', // 网络类型
    showDiagnosticInfo: false, // 是否显示诊断信息
    bleState: BLE_STATE.INIT, // 蓝牙状态
    initAttempts: 0, // 初始化尝试次数
    lastInitTime: 0, // 上次初始化时间
    deviceProfile: null, // 设备配置文件
    scanningAttempts: 0, // 扫描尝试次数
    operationLocked: false, // 是否有蓝牙操作正在进行中
    hasNonSmartLockDevices: false // 是否有非智能锁设备
  },
  
  onLoad: function() {
    // 获取系统信息
    const systemInfo = getSystemInfo();
    const deviceProfile = getDeviceProfile(systemInfo.brand, systemInfo.system);
    
    this.setData({ 
      systemInfo,
      deviceProfile
    });
    
    // 获取网络状态
    this.getNetworkType();
    
    // 监听网络状态变化
    wx.onNetworkStatusChange((res) => {
      this.setData({ networkType: res.networkType });
    });
    
    // 设置全局未捕获异常处理
    wx.onError((res) => {
      // 忽略文件系统错误
      if (res.message.indexOf('wxfile://') !== -1 ||
          res.message.indexOf('no such file or directory') !== -1) {
        return;
      }
      
      // 记录错误
      const now = Date.now();
      if (now - this.data.lastErrorTime > 30000) { // 30秒内不重复提示
        this.setData({ 
          errorCount: this.data.errorCount + 1,
          lastErrorTime: now
        });
        
        // 错误次数过多时重置蓝牙
        if (this.data.errorCount > 3) {
          this.refreshBluetooth();
          this.setData({ errorCount: 0 });
        }
      }
    });
    
    // 监听蓝牙状态变化
    this._setupBluetoothListener();
    
    // 页面加载时自动开始搜索
    setTimeout(() => {
      this.initBluetoothAdapter();
    }, 500);
  },
  
  // 设置蓝牙监听器
  _setupBluetoothListener: function() {
    // 防止重复监听
    try {
      wx.offBluetoothAdapterStateChange();
    } catch (e) {}
    
    // 监听寻找到新设备的事件
    wx.onBluetoothDeviceFound((res) => {
      res.devices.forEach(device => {
        if (!device.name && !device.localName) {
          return;
        }
        
        // 添加设备到列表
        const updatedDevices = addDeviceToList([...this.data.devices], device);
        
        // 更新设备列表
        this.setData({
          devices: updatedDevices.sort((a, b) => b.RSSI - a.RSSI), // 按信号强度排序
          hasNonSmartLockDevices: updatedDevices.some(d => !d.isLock)
        });
        
        // 检查是否找到智能锁设备
        const deviceName = device.name || device.localName || 'Unknown Device';
        let isLockDevice = false;
        
        for (const prefix of SMART_LOCK_PREFIXES) {
          if (deviceName.toUpperCase().includes(prefix.toUpperCase())) {
            isLockDevice = true;
            break;
          }
        }
        
        // 如果找到智能锁设备，提示用户可以连接
        if (isLockDevice) {
          // 避免重复提示
          const now = Date.now();
          if (!this._lastLockDeviceFoundTime || (now - this._lastLockDeviceFoundTime > 5000)) {
            this._lastLockDeviceFoundTime = now;
            
            // 轻微振动提示发现设备
            wx.vibrateShort({ type: 'light' });
            
            // 显示找到设备的提示
            wx.showToast({
              title: `发现智能锁: ${deviceName}`,
              icon: 'none',
              duration: 2000
            });
          }
        }
      });
    });
    
    // 监听蓝牙适配器状态变化
    wx.onBluetoothAdapterStateChange((res) => {
      if (res.available) {
        this.setData({
          bluetoothInitialized: true,
          bleState: this._discoveryStarted ? BLE_STATE.SCANNING : BLE_STATE.READY
        });
      } else {
        this.setData({
          bluetoothInitialized: false,
          bleState: BLE_STATE.INIT
        });
        
        // 蓝牙关闭时，清空设备列表
        if (this.data.devices.length > 0) {
          this.setData({ devices: [] });
        }
      }
    });
  },
  
  // 处理发现的设备
  _handleDeviceFound: function(res) {
    if (!res || !res.devices) return;
    
    res.devices.forEach(device => {
      // 过滤掉没有任何名称标识的设备，但接受所有有名称的设备
      if (!device.name && !device.localName) {
        return;
      }
      
      // 增加设备类型识别
      const deviceName = device.name || device.localName || '';
      const isSmartLock = this.isSmartLockDevice(device);
      const isInterference = this.isInterferenceDevice(device);
      
      // 为设备添加额外信息
      device.isSmartLock = isSmartLock;
      device.isInterference = isInterference;
      
      // 优先显示智能锁设备，可以选择性隐藏干扰设备
      // 如果设备是干扰设备且不是智能锁，可以选择不显示它
      // if (isInterference && !isSmartLock) {
      //   return;
      // }
      
      // 按照信号强度过滤，太弱的信号可能导致连接问题
      if (device.RSSI < -90 && !isSmartLock) {
        return;
      }
      
      const foundDevices = this.data.devices;
      const idx = inArray(foundDevices, 'deviceId', device.deviceId);
      const data = {};
      
      if (idx === -1) {
        data[`devices[${foundDevices.length}]`] = device;
      } else {
        data[`devices[${idx}]`] = device;
      }
      
      this.setData(data);
    });
    
    // 根据信号强度对设备列表进行排序
    this._sortDevicesBySignalStrength();
  },
  
  // 根据信号强度对设备列表进行排序
  _sortDevicesBySignalStrength: function() {
    // 智能锁设备排前面，然后按照信号强度排序
    const devices = this.data.devices.slice();
    
    if (devices.length > 1) {
      devices.sort((a, b) => {
        // 智能锁设备排在前面
        if (a.isSmartLock && !b.isSmartLock) {
          return -1;
        }
        if (!a.isSmartLock && b.isSmartLock) {
          return 1;
        }
        
        // 然后按照信号强度排序（RSSI值越大，信号越强）
        return b.RSSI - a.RSSI;
      });
      
      this.setData({ devices });
    }
    
    // 检查是否存在非智能锁设备
    let hasNonSmartLock = false;
    for (let i = 0; i < devices.length; i++) {
      if (!devices[i].isSmartLock) {
        hasNonSmartLock = true;
        break;
      }
    }
    
    this.setData({ hasNonSmartLockDevices: hasNonSmartLock });
  },
  
  // 获取网络状态
  getNetworkType() {
    wx.getNetworkType({
      success: (res) => {
        this.setData({ networkType: res.networkType });
      }
    });
  },
  
  onShow: function() {
    this._pageShowing = true;
    
    // 页面显示时，如果没有正在搜索且没有设备，则自动搜索
    if (!this._discoveryStarted && this.data.devices.length === 0 && this.data.bluetoothInitialized) {
      setTimeout(() => {
        this.startBluetoothDevicesDiscovery();
      }, 500);
    } else if (!this.data.bluetoothInitialized && !this.data.isInitializing) {
      // 如果蓝牙未初始化且不在初始化过程中，则重新初始化
      setTimeout(() => {
        this.initBluetoothAdapter();
      }, 500);
    }
  },
  
  onHide: function() {
    this._pageShowing = false;
    
    if (this._discoveryStarted) {
      this.stopBluetoothDevicesDiscovery();
    }
  },
  
  // 初始化蓝牙适配器 - 直接从这里开始，不再调用openBluetoothAdapter
  initBluetoothAdapter(force = false) {
    // 状态锁检查 - 防止重复初始化
    if (BLE_OPERATION_LOCK.initializing && !force) {
      console.log('蓝牙正在初始化中，请勿重复操作');
      return;
    }
    
    // 如果正在初始化且不是强制重新初始化，则不重复操作
    if (this.data.isInitializing && !force) {
      return;
    }
    
    // 获取设备配置和全局状态
    const app = getApp();
    const systemInfo = this.data.systemInfo;
    const profile = this.data.deviceProfile || DEVICE_PROFILES.DEFAULT;
    const isHuawei = app.globalData.isHuawei;
    const hasBluetoothPermission = app.globalData.hasBluetoothPermission;
    const preInitBluetooth = app.globalData.preInitBluetooth;
    
    // 激活状态锁 - 记录初始化状态
    BLE_OPERATION_LOCK.initializing = true;
    BLE_OPERATION_LOCK.initStartTime = Date.now();
    BLE_OPERATION_LOCK.lastOperation = 'initBluetoothAdapter';
    BLE_OPERATION_LOCK.operationCount++;
    
    // 设置状态锁自动释放的超时保护
    if (BLE_OPERATION_LOCK.initTimeoutId) {
      clearTimeout(BLE_OPERATION_LOCK.initTimeoutId);
    }
    
    // 获取当前设备对应的超时时间
    const lockTimeout = isHuawei && systemInfo.system.includes('Android 12') 
      ? profile.android12?.initLockTimeout || 8000 
      : profile.initLockTimeout || 3000;
    
    BLE_OPERATION_LOCK.initTimeoutId = setTimeout(() => {
      // 超时自动释放锁，以防止死锁
      if (BLE_OPERATION_LOCK.initializing) {
        console.log('初始化操作超时，自动释放锁');
        BLE_OPERATION_LOCK.initializing = false;
        this.setData({ isInitializing: false, operationLocked: false });
      }
    }, lockTimeout);
    
    // 记录初始化时间和状态
    this.setData({ 
      isInitializing: true,
      bleState: BLE_STATE.INITING,
      lastInitTime: Date.now(),
      errorCount: 0, // 重置错误计数
      scanningAttempts: 0, // 重置扫描尝试次数
      operationLocked: true // 设置操作锁定状态
    });
    
    this.misScanding = false;
    
    // 如果是华为设备且没有蓝牙权限，先申请权限
    if (isHuawei && !hasBluetoothPermission) {
      wx.authorize({
        scope: 'scope.bluetooth',
        success: () => {
          app.globalData.hasBluetoothPermission = true;
          // 成功获取权限后继续初始化
          this._doBleInit(force, profile, systemInfo);
        },
        fail: (err) => {
          // 处理权限拒绝情况
          app.globalData.hasBluetoothPermission = false;
          
          // 如果错误码为 -1，可能是通用错误
          if (err.errCode === -1) {
            wx.showModal({
              title: '蓝牙权限请求失败',
              content: '系统返回错误码 -1。请确保在"设置"中已开启蓝牙和位置权限，并允许小程序使用这些权限。',
              showCancel: false,
              success: () => {
                // 无论如何继续尝试初始化
                this._doBleInit(force, profile, systemInfo);
              }
            });
          } else {
            // 无论成功失败，继续初始化流程
            this._doBleInit(force, profile, systemInfo);
          }
        }
      });
      return;
    }
    
    // 正常设备或已获得权限的华为设备
    this._doBleInit(force, profile, systemInfo);
  },
  
  // 实际执行蓝牙初始化逻辑
  _doBleInit(force, profile, systemInfo) {
    try {
      // 先关闭蓝牙适配器，确保重新初始化
      wx.closeBluetoothAdapter({
        complete: () => {
          // 华为等特殊设备可能需要更长的延迟
          const initDelay = profile.initDelay || 300;
          
          // 延迟一下再打开蓝牙适配器
          setTimeout(() => {
            try {
              wx.openBluetoothAdapter({
                success: (res) => {
                  // 设置初始化成功状态
                  this.setData({ 
                    bluetoothInitialized: true,
                    isInitializing: false,
                    bleState: BLE_STATE.READY,
                    initAttempts: 0, // 成功后重置尝试次数
                    operationLocked: false // 解除操作锁定
                  });
                  
                  // 释放状态锁
                  BLE_OPERATION_LOCK.initializing = false;
                  if (BLE_OPERATION_LOCK.initTimeoutId) {
                    clearTimeout(BLE_OPERATION_LOCK.initTimeoutId);
                  }
                  
                  // 针对华为安卓12，多等一会儿再开始搜索
                  const isHuaweiAndroid12 = systemInfo && 
                      systemInfo.brand && 
                      systemInfo.brand.toUpperCase().includes('HUAWEI') &&
                      systemInfo.system && 
                      systemInfo.system.includes('Android 12');
                      
                  const searchDelay = isHuaweiAndroid12 ? 1000 : 500;
                  
                  // 延迟获取蓝牙状态
                  setTimeout(() => {
                    this.getBluetoothAdapterState();
                  }, searchDelay);
                },
                fail: (err) => {
                  // 设置初始化失败状态
                  this.setData({ 
                    bluetoothInitialized: false,
                    isInitializing: false,
                    bleState: BLE_STATE.INIT,
                    operationLocked: false // 解除操作锁定
                  });
                  
                  // 释放状态锁
                  BLE_OPERATION_LOCK.initializing = false;
                  if (BLE_OPERATION_LOCK.initTimeoutId) {
                    clearTimeout(BLE_OPERATION_LOCK.initTimeoutId);
                  }
                  
                  if (err.errCode === 10001) {
                    // 蓝牙未打开
                    this.showBluetoothTips();
                  } else {
                    // 其他错误，可能需要权限
                    // 针对华为设备，可能需要多次尝试
                    if ((systemInfo.brand && systemInfo.brand.toUpperCase().includes('HUAWEI')) && 
                        this.data.initAttempts < (profile.retryTimes || 2)) {
                      
                      const retryDelay = profile.retryDelay || 1500;
                      this.setData({
                        initAttempts: this.data.initAttempts + 1
                      });
                      
                      setTimeout(() => {
                        if (this._pageShowing) {
                          this.initBluetoothAdapter(true);
                        }
                      }, retryDelay);
                      
                      // 对于华为设备，显示正在重试的提示
                      wx.showToast({
                        title: '正在重新初始化蓝牙...',
                        icon: 'none',
                        duration: retryDelay - 300
                      });
                    } else {
                      // 重试次数过多，显示错误
                      wx.showModal({
                        title: '提示',
                        content: '初始化蓝牙失败，请确保已授予蓝牙权限并已打开蓝牙',
                        showCancel: false
                      });
                    }
                  }
                }
              });
            } catch (e) {
              // 处理可能的异常
              this.setData({ 
                isInitializing: false,
                bleState: BLE_STATE.INIT,
                operationLocked: false // 解除操作锁定
              });
              
              // 释放状态锁
              BLE_OPERATION_LOCK.initializing = false;
              if (BLE_OPERATION_LOCK.initTimeoutId) {
                clearTimeout(BLE_OPERATION_LOCK.initTimeoutId);
              }
              
              wx.showToast({
                title: '蓝牙初始化异常，请重试',
                icon: 'none'
              });
            }
          }, initDelay);
        }
      });
    } catch (e) {
      // 处理可能的异常
      this.setData({ 
        isInitializing: false,
        bleState: BLE_STATE.INIT,
        operationLocked: false // 解除操作锁定
      });
      
      // 释放状态锁
      BLE_OPERATION_LOCK.initializing = false;
      if (BLE_OPERATION_LOCK.initTimeoutId) {
        clearTimeout(BLE_OPERATION_LOCK.initTimeoutId);
      }
      
      wx.showToast({
        title: '蓝牙操作异常，请重试',
        icon: 'none'
      });
    }
  },
  
  // 显示蓝牙提示
  showBluetoothTips() {
    wx.showModal({
      title: '提示',
      content: '请打开蓝牙后再试',
      showCancel: false
    });
  },
  
  getBluetoothAdapterState() {
    if (!this.data.bluetoothInitialized) {
      return;
    }
    
    try {
      wx.getBluetoothAdapterState({
        success: (res) => {
          if (res.discovering) {
            this.setData({ bleState: BLE_STATE.SCANNING });
          } else if (res.available) {
            this.setData({ bleState: BLE_STATE.READY });
            this.startBluetoothDevicesDiscovery();
          }
        },
        fail: (err) => {
          // 如果是未初始化错误，尝试重新初始化
          if (err.errCode === 10000 && err.errMsg.includes('not init')) {
            this.setData({ 
              bluetoothInitialized: false,
              bleState: BLE_STATE.INIT
            });
            
            setTimeout(() => {
              this.initBluetoothAdapter();
            }, 1000);
          }
        }
      });
    } catch (e) {
      // 捕获可能的异常
      wx.showToast({
        title: '蓝牙状态获取失败',
        icon: 'none'
      });
    }
  },
  
  startBluetoothDevicesDiscovery() {
    if (!this.data.bluetoothInitialized) {
      this.initBluetoothAdapter();
      return;
    }
    
    // 检查是否正在初始化或扫描中
    if (BLE_OPERATION_LOCK.initializing) {
      console.log('蓝牙正在初始化中，请稍后再试');
      wx.showToast({
        title: '蓝牙初始化中，请稍候',
        icon: 'none',
        duration: 1500
      });
      return;
    }
    
    // 状态锁检查 - 防止重复扫描
    if (BLE_OPERATION_LOCK.scanning) {
      console.log('蓝牙正在扫描中，请勿重复操作');
      return;
    }
    
    var that = this;
    if (this._discoveryStarted) {
      this.stopBluetoothDevicesDiscovery();
      return;
    }
    
    // 先检查蓝牙是否真的开启并可用
    checkBluetoothIsOn().then(isOn => {
      if (!isOn) {
        wx.showToast({
          title: '蓝牙未开启或不可用',
          icon: 'none',
          duration: 2000
        });
        return;
      }
      
      // 清空设备列表
      this.setData({
        devices: []
      });
      
      // 激活扫描状态锁
      BLE_OPERATION_LOCK.scanning = true;
      BLE_OPERATION_LOCK.scanStartTime = Date.now();
      BLE_OPERATION_LOCK.lastOperation = 'startBluetoothDevicesDiscovery';
      BLE_OPERATION_LOCK.operationCount++;
      
      this.setData({ 
        misScanding: true, 
        scandbutName: "停止搜索",
        bleState: BLE_STATE.SCANNING,
        scanningAttempts: this.data.scanningAttempts + 1, // 增加扫描尝试次数
        operationLocked: false // 不锁定操作，允许在搜索过程中连接设备
      });
      
      this._discoveryStarted = true;
      
      // 获取设备配置
      const systemInfo = this.data.systemInfo;
      const profile = this.data.deviceProfile || DEVICE_PROFILES.DEFAULT;
      
      // 设置搜索选项
      let options = {
        allowDuplicatesKey: true
      };
      
      // 根据设备类型调整参数
      if (profile.useEmptyServices) {
        options.services = [];
      }
      
      if (profile.powerLevel) {
        options.powerLevel = profile.powerLevel;
      }
      
      if (profile.searchInterval !== undefined) {
        options.interval = profile.searchInterval;
      }
      
      // 针对华为安卓12的特殊处理
      const isHuaweiAndroid12 = systemInfo && 
          systemInfo.brand && 
          (systemInfo.brand.toUpperCase().includes('HUAWEI') || systemInfo.brand.toUpperCase().includes('HONOR')) &&
          systemInfo.system && 
          (systemInfo.system.includes('Android 12') || systemInfo.system.includes('Android 13'));
          
      if (isHuaweiAndroid12) {
        // 华为安卓12/13上特殊优化
        options.services = [];
        options.interval = 0;
        options.powerLevel = 'high';
        
        wx.showToast({
          title: '华为设备优化搜索中',
          icon: 'loading',
          duration: 2000
        });
        
        // 华为安卓12上第一次扫描可能会失败，设置更短的超时以便快速重试
        const scanningLimit = profile.android12?.scanningLimit || 4000;
        
        // 添加扫描限制时间，避免扫描过久
        setTimeout(() => {
          if (this._discoveryStarted && isHuaweiAndroid12 && this.data.devices.length === 0) {
            console.log('华为设备扫描超时，准备重试');
            this.stopBluetoothDevicesDiscovery();
            
            // 延迟重新扫描
            setTimeout(() => {
              if (this._pageShowing && !this._discoveryStarted) {
                wx.showToast({
                  title: '重新尝试搜索设备',
                  icon: 'none',
                  duration: 1500
                });
                this.startBluetoothDevicesDiscovery();
              }
            }, 1000);
          }
        }, scanningLimit);
      }
      
      // 设置扫描超时自动释放锁
      if (BLE_OPERATION_LOCK.scanTimeoutId) {
        clearTimeout(BLE_OPERATION_LOCK.scanTimeoutId);
      }
      
      // 获取扫描超时时间
      const scanTimeout = isHuaweiAndroid12 
        ? profile.android12?.searchTimeout || 8000 
        : profile.searchTimeout || 20000;
        
      BLE_OPERATION_LOCK.scanTimeoutId = setTimeout(() => {
        if (BLE_OPERATION_LOCK.scanning) {
          console.log('扫描操作超时，自动释放锁');
          BLE_OPERATION_LOCK.scanning = false;
          // 不主动停止扫描，让超时回调处理
        }
      }, scanTimeout + 2000); // 比实际扫描时间多2秒，确保回调已执行
      
      try {
        wx.startBluetoothDevicesDiscovery({
          ...options,
          success: (res) => {
            // 成功开始搜索
            wx.vibrateShort({ type: 'medium' }); // 成功振动提示
            
            // 设置超时，避免一直搜索
            setTimeout(function () {
              if (that._discoveryStarted){
                that.stopBluetoothDevicesDiscovery();
              }
            }, profile.searchTimeout || 20000);
          },
          fail: (err) => {
            this._discoveryStarted = false;
            
            // 释放扫描状态锁
            BLE_OPERATION_LOCK.scanning = false;
            if (BLE_OPERATION_LOCK.scanTimeoutId) {
              clearTimeout(BLE_OPERATION_LOCK.scanTimeoutId);
            }
            
            this.setData({ 
              misScanding: false, 
              scandbutName: "刷新设备列表",
              bleState: BLE_STATE.READY,
              operationLocked: false // 解除操作锁定
            });
            
            // 如果是未初始化错误，尝试重新初始化
            if (err.errCode === 10000 && err.errMsg.includes('not init')) {
              this.setData({ 
                bluetoothInitialized: false,
                bleState: BLE_STATE.INIT
              });
              
              wx.showModal({
                title: '蓝牙异常',
                content: '蓝牙初始化状态异常，需要重新初始化',
                confirmText: '立即重试',
                success: (res) => {
                  if (res.confirm) {
                    setTimeout(() => {
                      this.initBluetoothAdapter(true); // 强制重新初始化
                    }, 500);
                  }
                }
              });
            } else if (err.errCode === 10012 || err.errMsg.includes('already discover')) {
              // 已经在搜索中，先停止再重新开始
              wx.stopBluetoothDevicesDiscovery({
                complete: () => {
                  setTimeout(() => {
                    if (this._pageShowing) {
                      wx.showToast({
                        title: '重新开始搜索',
                        icon: 'none',
                        duration: 1500
                      });
                      this.startBluetoothDevicesDiscovery();
                    }
                  }, 1000);
                }
              });
            } else if (err.errCode === -1) {
              // 处理错误码 -1 的情况
              console.log('搜索设备出现通用错误 -1');
              
              wx.showModal({
                title: '搜索设备失败',
                content: '系统返回错误码 -1，可能是蓝牙硬件异常或权限问题。建议重启蓝牙或设备。',
                confirmText: '修复问题',
                cancelText: '稍后再试',
                success: (res) => {
                  if (res.confirm) {
                    // 使用启发式修复函数
                    this.fixErrorNegativeOne();
                  }
                }
              });
              
              // 记录错误并增加错误计数
              this.setData({
                errorCount: this.data.errorCount + 1
              });
              
              // 尝试获取系统蓝牙状态
              setTimeout(() => {
                this.getBluetoothAdapterState();
              }, 1500);
            } else {
              // 其他错误，对于华为设备可能需要特殊处理
              const isBrandSpecial = systemInfo && 
                  systemInfo.brand && 
                  (systemInfo.brand.toUpperCase().includes('HUAWEI') || 
                  systemInfo.brand.toUpperCase().includes('HONOR') ||
                  systemInfo.brand.toUpperCase().includes('OPPO') ||
                  systemInfo.brand.toUpperCase().includes('VIVO'));
                  
              if (isBrandSpecial && this.data.scanningAttempts < 3) {
                // 特殊品牌设备需要更针对性的提示和重试策略
                let brandName = "您的设备";
                if (systemInfo.brand.toUpperCase().includes('HUAWEI') || 
                    systemInfo.brand.toUpperCase().includes('HONOR')) {
                  brandName = "华为设备";
                } else if (systemInfo.brand.toUpperCase().includes('OPPO')) {
                  brandName = "OPPO设备";
                } else if (systemInfo.brand.toUpperCase().includes('VIVO')) {
                  brandName = "vivo设备";
                }
                
                wx.showToast({
                  title: `${brandName}搜索失败，正在重试...`,
                  icon: 'none',
                  duration: 2000
                });
                
                // 特殊设备需要更长的延迟
                setTimeout(() => {
                  if (!this._discoveryStarted && this._pageShowing) {
                    this.startBluetoothDevicesDiscovery();
                  }
                }, 2000);
              } else {
                // 非特殊设备或尝试次数过多
                const errMsg = err.errMsg || "未知错误";
                const errCode = err.errCode || "未知错误码";
                
                wx.showModal({
                  title: '搜索设备失败',
                  content: `无法搜索附近设备，请检查蓝牙权限和状态。\n错误: ${errCode}`,
                  confirmText: '重试',
                  showCancel: true,
                  cancelText: '知道了',
                  success: (res) => {
                    if (res.confirm) {
                      // 用户选择重试
                      setTimeout(() => {
                        if (this._pageShowing) {
                          // 重置蓝牙后再尝试扫描
                          this.refreshBluetooth();
                        }
                      }, 500);
                    }
                  }
                });
              }
            }
          }
        });
      } catch (e) {
        // 捕获可能的异常
        this._discoveryStarted = false;
        
        // 释放扫描状态锁
        BLE_OPERATION_LOCK.scanning = false;
        if (BLE_OPERATION_LOCK.scanTimeoutId) {
          clearTimeout(BLE_OPERATION_LOCK.scanTimeoutId);
        }
        
        this.setData({ 
          misScanding: false, 
          scandbutName: "刷新设备列表",
          bleState: BLE_STATE.READY,
          operationLocked: false // 解除操作锁定
        });
        
        // 显示异常信息并提供重试选项
        wx.showModal({
          title: '蓝牙搜索异常',
          content: '搜索设备时发生意外错误，是否重试？',
          confirmText: '重试',
          cancelText: '取消',
          success: (res) => {
            if (res.confirm) {
              setTimeout(() => {
                if (this._pageShowing) {
                  this.refreshBluetooth();
                }
              }, 1000);
            }
          }
        });
      }
    }).catch(() => {
      // 检查蓝牙状态出错时
      wx.showToast({
        title: '获取蓝牙状态失败',
        icon: 'none',
        duration: 2000
      });
      
      // 尝试重新初始化
      setTimeout(() => {
        this.refreshBluetooth();
      }, 1000);
    });
  },
  
  stopBluetoothDevicesDiscovery() {
    if (!this.data.bluetoothInitialized) {
      return;
    }
    
    this._discoveryStarted = false;
    wx.stopBluetoothDevicesDiscovery({
      success: () => {
        this.setData({ bleState: BLE_STATE.READY });
      },
      fail: (err) => {
        // 如果是未初始化错误，尝试重新初始化
        if (err.errCode === 10000 && err.errMsg.includes('not init')) {
          this.setData({ 
            bluetoothInitialized: false,
            bleState: BLE_STATE.INIT
          });
          
          if (this._pageShowing) {
            setTimeout(() => {
              this.initBluetoothAdapter();
            }, 1000);
          }
        }
      },
      complete: () => {
        this.setData({ 
          misScanding: false, 
          scandbutName: "刷新设备列表",
          operationLocked: false // 解除操作锁定
        });
        
        // 释放扫描状态锁
        BLE_OPERATION_LOCK.scanning = false;
        if (BLE_OPERATION_LOCK.scanTimeoutId) {
          clearTimeout(BLE_OPERATION_LOCK.scanTimeoutId);
        }
        
        // 如果没有找到设备，3秒后自动重新搜索
        if (this.data.devices.length === 0 && this.data.bluetoothInitialized && this._pageShowing) {
          wx.showToast({
            title: '未找到设备，稍后将重试',
            icon: 'none',
            duration: 2000
          });
          
          setTimeout(() => {
            if (this._pageShowing && !this._discoveryStarted && this.data.devices.length === 0) {
              this.startBluetoothDevicesDiscovery();
            }
          }, 3000);
        } else if (this.data.devices.length > 0) {
          // 找到设备时提示
          wx.showToast({
            title: `已发现${this.data.devices.length}个设备`,
            icon: 'success',
            duration: 1500
          });
        }
      }
    });
  },
  
  goto_Comm(e) {
    // 获取设备信息
    const deviceData = e.currentTarget.dataset;
    const isLockDevice = deviceData.isLock || false;
    
    // 如果设备被锁定，阻止连接
    if (this.data.operationLocked) {
      wx.showToast({
        title: '请等待当前操作完成',
        icon: 'none'
      });
      return;
    }
    
    // 如果不是门锁设备，提示用户确认
    if (!isLockDevice) {
      wx.showModal({
        title: '连接确认',
        content: '该设备可能不是智能门锁，是否继续连接？',
        confirmText: '继续连接',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            this._proceedToConnect(deviceData);
          }
        }
      });
      return;
    }
    
    // 直接连接门锁设备
    this._proceedToConnect(deviceData);
  },
  
  // 处理实际的连接逻辑
  _proceedToConnect(deviceData) {
    app.globalData.ble_device = deviceData;
    
    // 停止搜索但不锁定界面
    if (this._discoveryStarted) {
      this.stopBluetoothDevicesDiscovery();
    }
    
    wx.showLoading({
      title: '正在连接...',
    });
    
    // 设置蓝牙状态为连接中
    this.setData({ bleState: BLE_STATE.CONNECTING });
    
    // 记录连接尝试
    app.globalData.lastConnectAttempt = {
      time: Date.now(),
      deviceId: deviceData.deviceId,
      deviceName: deviceData.name
    };
    
    // 更新设备连接状态
    const devices = [...this.data.devices];
    const idx = inArray(devices, 'deviceId', deviceData.deviceId);
    if (idx !== -1) {
      devices[idx].connecting = true;
      devices[idx].connected = false;
      this.setData({ devices });
    }
    
    setTimeout(() => {
      wx.hideLoading();
      wx.navigateTo({
        url: '/pages/comm/comm',
        success: () => {
          // 连接成功，添加振动反馈
          wx.vibrateShort({ type: 'light' });
        },
        fail: (err) => {
          // 恢复状态
          this.setData({ bleState: BLE_STATE.READY });
          
          // 更新设备连接状态
          const devices = [...this.data.devices];
          const idx = inArray(devices, 'deviceId', deviceData.deviceId);
          if (idx !== -1) {
            devices[idx].connecting = false;
            this.setData({ devices });
          }
          
          wx.showModal({
            title: '连接失败',
            content: '无法打开设备连接页面，请重试',
            showCancel: false
          });
        }
      });
    }, 800);
  },
  
  // 复制设备ID到剪贴板
  copyDeviceId(e) {
    const deviceId = e.currentTarget.dataset.id;
    wx.setClipboardData({
      data: deviceId,
      success: function() {
        wx.showToast({
          title: 'MAC地址已复制',
          icon: 'success',
          duration: 1500
        });
      },
      fail: function(err) {
        wx.showToast({
          title: '复制失败',
          icon: 'none'
        });
      }
    });
  },
  
  closeBluetoothAdapter() {
    wx.closeBluetoothAdapter({
      complete: () => {
        this._discoveryStarted = false;
        this.setData({ 
          bluetoothInitialized: false,
          isInitializing: false,
          bleState: BLE_STATE.INIT
        });
      }
    });
  },
  
  onUnload: function() {
    this._pageShowing = false;
    
    if (this._discoveryStarted) {
      this.stopBluetoothDevicesDiscovery();
    }
    this.closeBluetoothAdapter();
    
    // 确保清除所有定时器
    if (BLE_OPERATION_LOCK.initTimeoutId) {
      clearTimeout(BLE_OPERATION_LOCK.initTimeoutId);
    }
    
    if (BLE_OPERATION_LOCK.scanTimeoutId) {
      clearTimeout(BLE_OPERATION_LOCK.scanTimeoutId);
    }
    
    // 重置状态锁
    BLE_OPERATION_LOCK.initializing = false;
    BLE_OPERATION_LOCK.scanning = false;
  },
  
  // 刷新蓝牙按钮 - 用于用户手动刷新蓝牙
  refreshBluetooth: function() {
    // 如果有操作锁定中，则不允许刷新
    if (BLE_OPERATION_LOCK.initializing || BLE_OPERATION_LOCK.scanning) {
      // 显示提示
      wx.showToast({
        title: '操作太快，请稍后再试',
        icon: 'none',
        duration: 1500
      });
      return;
    }
    
    // 添加动画效果
    wx.showToast({
      title: '正在刷新蓝牙',
      icon: 'loading',
      duration: 1500
    });
    
    // 完全重置蓝牙状态
    this.closeBluetoothAdapter();
    
    // 重置所有状态
    this.setData({
      devices: [],
      connected: false,
      chs: [], 
      misScanding: false,
      scandbutName: "刷新设备列表",
      bluetoothInitialized: false,
      isInitializing: false,
      errorCount: 0,
      bleState: BLE_STATE.INIT,
      initAttempts: 0,
      operationLocked: false, // 重置操作锁定状态
      hasNonSmartLockDevices: false // 重置非智能锁设备标志
    });
    
    // 重置全局状态锁
    BLE_OPERATION_LOCK.initializing = false;
    BLE_OPERATION_LOCK.scanning = false;
    if (BLE_OPERATION_LOCK.initTimeoutId) {
      clearTimeout(BLE_OPERATION_LOCK.initTimeoutId);
    }
    if (BLE_OPERATION_LOCK.scanTimeoutId) {
      clearTimeout(BLE_OPERATION_LOCK.scanTimeoutId);
    }
    
    // 检查蓝牙是否开启
    checkBluetoothIsOn().then(isOn => {
      if (!isOn) {
        // 蓝牙未开启，提示用户开启蓝牙
        wx.showModal({
          title: '蓝牙未开启',
          content: '请先开启设备蓝牙功能',
          showCancel: false,
          success: () => {
            // 延迟重新初始化，等用户有时间开启蓝牙
            setTimeout(() => {
              this.initBluetoothAdapter(true);
            }, 2000);
          }
        });
      } else {
        // 蓝牙已开启，延迟重新初始化
        setTimeout(() => {
          this.initBluetoothAdapter(true); // 强制重新初始化
        }, 1000);
      }
    });
  },
  
  // 打开蓝牙适配器
  openBluetoothAdapter: function() {
    // 如果有操作锁定中，则不允许操作
    if (this.data.operationLocked) {
      // 显示提示
      wx.showToast({
        title: '操作太快，请稍后再试',
        icon: 'none',
        duration: 1500
      });
      return;
    }
    
    // 先检查蓝牙是否开启
    checkBluetoothIsOn().then(isOn => {
      if (!isOn) {
        wx.showModal({
          title: '蓝牙未开启',
          content: '请在设备设置中开启蓝牙功能后再试',
          showCancel: false
        });
        return;
      }
      
      // 如果正在搜索，则停止搜索
      if (this._discoveryStarted) {
        this.stopBluetoothDevicesDiscovery();
      } else {
        // 否则开始搜索
        this.startBluetoothDevicesDiscovery();
      }
    });
  },
  
  // 切换诊断信息显示状态
  toggleDiagnosticInfo: function() {
    this.setData({
      showDiagnosticInfo: !this.data.showDiagnosticInfo
    });
  },
  
  // 检查是否为智能锁设备
  isSmartLockDevice: function(device) {
    if (!device) return false;
    
    const name = device.name || device.localName || '';
    if (!name) return false;
    
    // 检查是否匹配已知的智能锁前缀
    for (let prefix of SMART_LOCK_PREFIXES) {
      if (name.toUpperCase().startsWith(prefix)) {
        return true;
      }
    }
    
    // 检查特定的服务UUID
    if (device.advertisServiceUUIDs && device.advertisServiceUUIDs.length > 0) {
      const uuid = device.advertisServiceUUIDs[0].toUpperCase();
      // 典型的蓝牙门锁服务UUID
      if (uuid.includes('FEE7') || uuid.includes('FFF0') || uuid.includes('FFE0')) {
        return true;
      }
    }
    
    return false;
  },
  
  // 检查是否为干扰设备
  isInterferenceDevice: function(device) {
    if (!device) return false;
    
    const name = device.name || device.localName || '';
    if (!name) return false;
    
    // 检查是否匹配已知的干扰设备
    for (let interferenceDevice of COMMON_INTERFERENCE_DEVICES) {
      if (name.toUpperCase().includes(interferenceDevice.toUpperCase())) {
        return true;
      }
    }
    
    return false;
  },
  
  // 添加启发式修复-1错误的函数
  fixErrorNegativeOne: function() {
    const systemInfo = this.data.systemInfo || getSystemInfo();
    
    // 记录修复尝试
    console.log('尝试修复错误码 -1');
    
    // 1. 完全关闭蓝牙
    wx.closeBluetoothAdapter({
      complete: () => {
        // 2. 等待足够长的时间
        setTimeout(() => {
          // 3. 检查蓝牙是否开启
          checkBluetoothIsOn().then(isOn => {
            if (!isOn) {
              // 蓝牙可能关闭，提示用户
              wx.showModal({
                title: '需要开启蓝牙',
                content: '请确保您已在设备设置中开启蓝牙',
                showCancel: false
              });
              
              return;
            }
            
            // 4. 尝试申请权限
            if (systemInfo.platform === 'android') {
              // Android可能需要位置权限
              wx.getSetting({
                success: (res) => {
                  if (!res.authSetting['scope.userLocation']) {
                    wx.authorize({
                      scope: 'scope.userLocation',
                      success: () => {
                        // 位置权限获取成功，继续初始化蓝牙
                        setTimeout(() => {
                          this.initBluetoothAdapter(true);
                        }, 500);
                      },
                      fail: () => {
                        // 位置权限获取失败，提示用户手动开启
                        wx.showModal({
                          title: '需要位置权限',
                          content: 'Android设备需要位置权限才能使用蓝牙功能，请在设置中开启',
                          confirmText: '去设置',
                          success: (res) => {
                            if (res.confirm) {
                              wx.openSetting();
                            }
                          }
                        });
                      }
                    });
                  } else {
                    // 已有位置权限，继续初始化蓝牙
                    setTimeout(() => {
                      this.initBluetoothAdapter(true);
                    }, 500);
                  }
                }
              });
            } else {
              // iOS或其他平台，直接尝试初始化蓝牙
              setTimeout(() => {
                this.initBluetoothAdapter(true);
              }, 500);
            }
          });
        }, 1500);
      }
    });
  },
})

