<view class="container page-device"><view class="page-background"><image class="background-image" style="{{'opacity:'+(isPageLoaded?1:0)+';'}}" src="https://wuji1112-**********.cos.ap-guangzhou.myqcloud.com/static/编组 <EMAIL>" mode="aspectFill" data-event-opts="{{[['load',[['onBackgroundImageLoaded',['$event']]]]]}}" bindload="__e"></image></view><view class="status-bar safe-area-inset-top"></view><view class="nav-bar"></view><view data-event-opts="{{[['tap',[['handleBannerClick',['$event']]]],['tap',[['handleBannerClick',['$event']]]],['touchstart',[['handleBannerTouch',['$event']]]],['touchend',[['handleBannerTouchEnd',['$event']]]],['touchcancel',[['handleBannerTouchCancel',['$event']]]]]}}" class="banner-container" bindtap="__e" bindtouchstart="__e" bindtouchend="__e" bindtouchcancel="__e"><image class="banner-image" src="https://wuji1112-**********.cos.ap-guangzhou.myqcloud.com/static/<EMAIL>" mode="contain"></image><view class="banner-glow"></view></view><view class="content"><view class="connect-section"><view class="neon-title status-title"><view>支付完成</view><view>门锁自动打开</view></view><block wx:if="{{!isLoggedIn}}"><view class="login-prompt"><view class="login-prompt-text"><text class="material-icons">account_circle</text><text>请先登录以使用设备</text></view><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="login-prompt-button" bindtap="__e"><text>一键登录</text></view></view></block><view class="action-buttons"><view data-event-opts="{{[['tap',[['handleMainAction',['$event']]]]]}}" class="{{['primary-button',mainActionClass]}}" bindtap="__e"><view class="ripple-layer"></view><text class="material-icons">{{mainActionIcon}}</text><text>{{mainActionText}}</text></view></view><view class="{{['connection-status',(isConnected)?'connected':'',(!isConnected&&!isConnecting)?'disconnected':'']}}"><block wx:if="{{isConnected}}"><text class="material-icons">bluetooth_connected</text></block><block wx:else><block wx:if="{{isConnecting}}"><text class="material-icons">bluetooth_searching</text></block><block wx:else><text class="material-icons">bluetooth_disabled</text></block></block><view class="{{['connection-text',(isConnected)?'text-connected':'',(isConnecting)?'text-connecting':'',(!isConnected&&!isConnecting)?'text-disconnected':'']}}">{{''+connectionStatusText+''}}</view></view><view class="connection-tips"><text class="text-tertiary">请确保手机蓝牙已开启，并靠近设备</text><text class="text-agreement">点击开门即视为同意《用户协议》及《隐私协议》</text></view></view></view><block wx:if="{{showPaymentModal}}"><view data-event-opts="{{[['tap',[['closePaymentModal',['$event']]]]]}}" class="modal-overlay" bindtap="__e"></view></block><block wx:if="{{showPaymentModal}}"><view class="payment-modal"><view class="modal-header"><text class="modal-title">支付确认</text><text data-event-opts="{{[['tap',[['closePaymentModal',['$event']]]]]}}" class="material-icons close-icon" bindtap="__e">close</text></view><view class="modal-body"><view class="payment-info"><view class="payment-device-name">{{deviceName||'智能门锁'}}</view><block wx:if="{{deviceInfo&&deviceInfo.shopName}}"><view class="payment-shop-name">{{deviceInfo.shopName}}</view></block><view class="payment-price">{{priceText}}</view></view><view data-event-opts="{{[['tap',[['processPayment',['$event']]]]]}}" class="{{['payment-button',(paymentLoading)?'loading':'']}}" bindtap="__e"><block wx:if="{{!paymentLoading}}"><text>确认支付</text></block><block wx:else><view class="loading-spinner"></view></block></view><view class="payment-tips">点击确认支付后将跳转到微信支付</view><block wx:if="{{!isConnected}}"><view class="payment-tips">支付成功后，您需要连接设备才能开门</view></block></view></view></block><block wx:if="{{showOpenDoorModal}}"><view data-event-opts="{{[['tap',[['closeOpenDoorModal',['$event']]]]]}}" class="modal-overlay" bindtap="__e"></view></block><block wx:if="{{showOpenDoorModal}}"><view class="open-door-modal"><view class="modal-header"><text class="modal-title">开门确认</text><text data-event-opts="{{[['tap',[['closeOpenDoorModal',['$event']]]]]}}" class="material-icons close-icon" bindtap="__e">close</text></view><view class="modal-body"><view class="door-info"><view class="door-icon"><text class="material-icons">lock_open</text></view><view class="door-message">您需要先支付才能开门</view><view class="door-device-name">{{deviceName||'智能门锁'}}</view><block wx:if="{{deviceInfo&&deviceInfo.shopName}}"><view class="door-shop-name">{{deviceInfo.shopName}}</view></block><view class="door-price">{{priceText}}</view></view><view class="door-buttons"><view data-event-opts="{{[['tap',[['closeOpenDoorModal',['$event']]]]]}}" class="door-cancel-button" bindtap="__e"><text>取消</text></view><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="door-pay-button" bindtap="__e"><text>去支付</text></view></view></view></view></block><audio style="display:none;" id="successAudio" src="https://wuji1112-**********.cos.ap-guangzhou.myqcloud.com/static/audio/ttsmaker-file-2025-6-11-20-49-17.mp3"></audio><login-modal vue-id="59373aa7-1" visible="{{showLoginModal}}" data-event-opts="{{[['^close',[['handleCloseLoginModal']]],['^loginSuccess',[['handleLoginSuccess']]],['^loginFail',[['handleLoginFail']]]]}}" bind:close="__e" bind:loginSuccess="__e" bind:loginFail="__e" bind:__l="__l"></login-modal></view>