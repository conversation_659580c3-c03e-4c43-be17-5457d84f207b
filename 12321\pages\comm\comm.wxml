<!--pages/comm/comm.wxml-->
<view class="app-container">
  <!-- 顶部标题和设备信息 -->
  <view class="app-header">
    <view class="app-title">今夜城堡调试器</view>
    <view class="device-connection" id="v1">
      <view class="connection-info">
        <view class="device-mac">
          <text class="mac-label">MAC:</text>
          <text class="mac-value">{{deviceadd}}</text>
          <view class="copy-mac-btn" bindtap="copyDeviceMac">
            <text class="copy-icon">📋</text>
            <text>复制</text>
          </view>
          <view class="qrcode-btn" bindtap="showQRCodeModal">
            <text class="qrcode-icon">🔳</text>
            <text>二维码</text>
          </view>
        </view>
        <view class="connection-status">
          <text class="status-dot {{connectState === '已连接' ? 'connected' : 'disconnected'}}"></text>
          <text class="status-text">{{connectState}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 主要控制区域 -->
  <view class="control-panel" id="v2">
    <!-- 锁状态卡片 -->
    <view class="status-card" id="v8">
      <view class="card-header">
        <text class="card-title">锁状态</text>
        <text class="card-action" bindtap="checkLockStatus">刷新</text>
      </view>
      <view class="card-content lock-status">
        <view class="status-icon {{lockStatus === 0 ? 'unlocked' : lockStatus === 1 ? 'locked' : 'unknown'}}">
          <text>{{lockStatus === 0 ? '🔓' : lockStatus === 1 ? '🔒' : '❓'}}</text>
        </view>
        <view class="status-info">
          <text class="status-label">当前状态</text>
          <text class="status-value {{lockStatus === 0 ? 'unlocked-text' : lockStatus === 1 ? 'locked-text' : 'unknown-text'}}">
            {{lockStatus === 0 ? '已开启' : lockStatus === 1 ? '已锁定' : '未检测'}}
          </text>
        </view>
      </view>
    </view>

    <!-- 电池状态卡片 -->
    <view class="status-card" id="v6">
      <view class="card-header">
        <text class="card-title">电池状态</text>
        <text class="card-action" bindtap="getBatteryLevel">刷新</text>
      </view>
      <view class="card-content battery-status">
        <view class="battery-level-container">
          <view class="battery-level {{batteryLevel > 80 ? 'high' : batteryLevel > 30 ? 'medium' : 'low'}}" style="width: {{batteryLevel}}%;"></view>
        </view>
        <view class="battery-info">
          <view class="battery-percentage">
            <text class="percentage-value">{{batteryLevel}}%</text>
            <text class="percentage-label {{batteryLevel > 80 ? 'high-text' : batteryLevel > 30 ? 'medium-text' : 'low-text'}}">
              {{batteryLevel > 80 ? '电量充足' : batteryLevel > 30 ? '电量正常' : '电量不足'}}
            </text>
          </view>
          <view class="battery-voltage">电压: {{batteryVoltage}}mV</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 快捷操作按钮 -->
  <view class="action-buttons" id="v7">
    <button class="action-btn unlock" bindtap="openLock">
      <text class="btn-icon">🔓</text>
      <text>开锁</text>
    </button>
    <button class="action-btn buzzer" bindtap="controlBuzzer" data-is-on="{{true}}">
      <text class="btn-icon">🔊</text>
      <text>蜂鸣器</text>
    </button>
    <button class="action-btn status" bindtap="checkLockStatus">
      <text class="btn-icon">🔍</text>
      <text>查询状态</text>
    </button>
    <button class="action-btn back" bindtap="gotoback">
      <text class="btn-icon">↩️</text>
      <text>返回</text>
    </button>
  </view>

  <!-- 添加缺失的ID元素 -->
  <view id="v3" style="display:none;"></view>
  <view id="v4" style="display:none;"></view>
  <view id="v5" style="display:none;"></view>

  <!-- 底部提示 -->
  <view class="footer-tip">
    <view class="footer-divider"></view>
    <text>智能锁控制系统 - 安全可靠</text>
  </view>
</view>



<!-- 简化的提示框 -->
<view class="dialog-screen" bindtap="hideModalTips" wx:if="{{showModalStatus}}">
  <view animation="{{animationData}}" class="dialog-box">
    <view class="dialog-content">{{showTips}}</view>
  </view>
</view>

<!-- 二维码模态框 -->
<view class="qrcode-modal-mask" bindtap="hideQRCodeModal" catchtouchmove="preventTouchMove" wx:if="{{showQRCodeModal}}"></view>
<view class="qrcode-modal" wx:if="{{showQRCodeModal}}">
  <view class="qrcode-modal-header">
    <text class="qrcode-modal-title">设备二维码</text>
    <text class="qrcode-modal-close" bindtap="hideQRCodeModal">×</text>
  </view>
  <view class="qrcode-modal-content">
    <view class="qrcode-canvas-container">
      <canvas class="qrcode-canvas" canvas-id="qrcode" style="width: 200px; height: 200px;"></canvas>
    </view>
    <view class="qrcode-device-info">
      <text class="qrcode-device-mac">设备MAC: {{deviceadd}}</text>
      <text class="qrcode-device-url">链接: https://example.com?device={{deviceadd}}</text>
    </view>
  </view>
  <view class="qrcode-modal-footer">
    <button class="qrcode-save-btn" bindtap="saveQRCode">保存二维码</button>
  </view>
</view>