{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/App.vue?7bf7", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/App.vue?b05d", "uni-app:///App.vue", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/App.vue?13f6", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/App.vue?d943"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "<PERSON><PERSON>", "config", "productionTip", "prototype", "$lockService", "lockService", "$blueToothManager", "blueToothManager", "component", "GlobalLogin", "mixin", "mixins", "androidMixin", "methods", "showLoginModal", "$store", "dispatch", "checkLogin", "isLoggedIn", "getters", "App", "mpType", "app", "store", "$mount", "globalData", "isSimulatedMode", "userInfo", "<PERSON><PERSON><PERSON><PERSON>", "isIphoneX", "systemInfo", "onLaunch", "console", "screenHeight", "onShow", "onHide"], "mappings": ";;;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AACA;AAGA;AAMA;AAAkD;AAAA;AAblD;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAAC;EAAA;IAAA;EAAA;AAAA;AAc3DC,YAAG,CAACC,MAAM,CAACC,aAAa,GAAG,KAAK;;AAEhC;AACAF,YAAG,CAACG,SAAS,CAACC,YAAY,GAAGC,kBAAW;AACxCL,YAAG,CAACG,SAAS,CAACG,iBAAiB,GAAGC,uBAAgB;;AAElD;AACAP,YAAG,CAACQ,SAAS,CAAC,cAAc,EAAEC,WAAW,CAAC;;AAE1C;AACAT,YAAG,CAACU,KAAK,CAAC;EACRC,MAAM,EAAE,CAACC,qBAAY,CAAC;EAAE;EACxBC,OAAO,EAAE;IACP;IACAC,cAAc,4BAAG;MACf,IAAI,CAACC,MAAM,CAACC,QAAQ,CAAC,gBAAgB,CAAC;IACxC,CAAC;IACD;IACAC,UAAU,wBAAG;MACX,IAAMC,UAAU,GAAG,IAAI,CAACH,MAAM,CAACI,OAAO,CAACD,UAAU;MACjD,IAAI,CAACA,UAAU,EAAE;QACf,IAAI,CAACH,MAAM,CAACC,QAAQ,CAAC,gBAAgB,CAAC;MACxC;MACA,OAAOE,UAAU;IACnB;EACF;AACF,CAAC,CAAC;AAEFE,YAAG,CAACC,MAAM,GAAG,KAAK;AAGlB,IAAMC,GAAG,GAAG,IAAItB,YAAG;EACjBuB,KAAK,EAALA;AAAK,GACFH,YAAG,EACN;AACF,UAAAE,GAAG,EAACE,MAAM,EAAE,C;;;;;;;;;;;;;ACnDZ;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACuD;AACL;AACa;;;AAG/D;AAC0L;AAC1L,gBAAgB,uMAAU;AAC1B,EAAE,yEAAM;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAmtB,CAAgB,+uBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;ACCvuB;AACA;AACA;AAAA;AAAA;AAAA,eAEA;EACAC;IACAC;IAAA;IACAC;IACAC;IACAC;IACAC;EACA;EACAC;IACAC;IACA;IACA;;IAEA;IACA;IACA;;IAEA;IACA;IACA;IACA;IACA,4FACAC;IACA;EACA;EACAC;IACAF;EACA;EACAG;IACAH;EACA;EACAnB,2BACA;AAEA;AAAA,2B;;;;;;;;;;;;ACvCA;AAAA;AAAA;AAAA;AAAohC,CAAgB,whCAAG,EAAC,C;;;;;;;;;;;ACAxiC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "common/main.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import Vue from 'vue'\nimport App from './App'\nimport './uni.promisify.adaptor'\nimport store from './store'\n\n// 导入蓝牙锁服务\nimport { lockService, blueToothManager } from './utils/index.js'\n\n// 引入全局登录组件\nimport GlobalLogin from './components/global-login/index.vue'\n\n// 引入安卓设备适配混入\nimport androidMixin from './utils/androidMixin.js'\n\nVue.config.productionTip = false\n\n// 挂载蓝牙锁服务到全局\nVue.prototype.$lockService = lockService\nVue.prototype.$blueToothManager = blueToothManager\n\n// 注册全局登录组件\nVue.component('global-login', GlobalLogin)\n\n// 全局混入，提供登录相关方法和安卓设备适配\nVue.mixin({\n  mixins: [androidMixin], // 混入安卓设备适配\n  methods: {\n    // 显示登录弹窗\n    showLoginModal() {\n      this.$store.dispatch('showLoginModal')\n    },\n    // 检查是否已登录，未登录则显示登录弹窗\n    checkLogin() {\n      const isLoggedIn = this.$store.getters.isLoggedIn\n      if (!isLoggedIn) {\n        this.$store.dispatch('showLoginModal')\n      }\n      return isLoggedIn\n    }\n  }\n})\n\nApp.mpType = 'app'\n\n\nconst app = new Vue({\n  store,\n  ...App\n})\napp.$mount()", "var render, staticRenderFns, recyclableRender, components\nvar renderjs\nimport script from \"./App.vue?vue&type=script&lang=js&\"\nexport * from \"./App.vue?vue&type=script&lang=js&\"\nimport style0 from \"./App.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"App.vue\"\nexport default component.exports", "import mod from \"-!../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=script&lang=js&\"", "<script>\n\timport { mapActions } from 'vuex';\n\timport LoginManager from './static/js/login.js';\n\timport { getSystemInfoSync } from './utils/systemInfoCompat.js';\n\n\texport default {\n\t\tglobalData: {\n\t\t\tisSimulatedMode: false, // 是否使用模拟模式\n\t\t\tuserInfo: null,\n\t\t\thasLogin: false,\n\t\t\tisIphoneX: false,\n\t\t\tsystemInfo: null\n\t\t},\n\t\tonLaunch: function() {\n\t\t\tconsole.log('App Launch');\n\t\t\t// 初始化检查登录状态\n\t\t\tthis.checkLoginStatus();\n\n\t\t\t// 获取系统信息 - 使用兼容性工具\n\t\t\tconst systemInfo = getSystemInfoSync();\n\t\t\tthis.globalData.systemInfo = systemInfo;\n\t\t\t\n\t\t\t// 检查是否是iPhoneX系列\n\t\t\tconst model = systemInfo.model;\n\t\t\tconst screenHeight = systemInfo.screenHeight;\n\t\t\tconst screenWidth = systemInfo.screenWidth;\n\t\t\tconst isIphoneX = /iPhone X|iPhone 11|iPhone 12|iPhone 13|iPhone 14|iPhone 15/.test(model) || \n\t\t\t\t(screenHeight / screenWidth > 2 && systemInfo.platform === 'ios');\n\t\t\tthis.globalData.isIphoneX = isIphoneX;\n\t\t},\n\t\tonShow: function() {\n\t\t\tconsole.log('App Show');\n\t\t},\n\t\tonHide: function() {\n\t\t\tconsole.log('App Hide');\n\t\t},\n\t\tmethods: {\n\t\t\t...mapActions(['checkLoginStatus'])\n\t\t}\n\t}\n</script>\n\n<style>\n\t/* 引入通用样式和图标 */\n\t@import './static/css/material-icons.css';\n\t\n\t/*每个页面公共css */\n\tpage {\n\t\tbackground-color: #000000;\n\t\tcolor: #FFFFFF;\n\t\tfont-family: \"Helvetica Neue\", Helvetica, Arial, sans-serif;\n\t\theight: 100%;\n\t\toverflow-x: hidden;\n\t\tbox-sizing: border-box;\n\t}\n\t\n\t/* iOS安全区域适配 */\n\t.safe-area-inset-bottom {\n\t\tpadding-bottom: constant(safe-area-inset-bottom); /* iOS 11.0 */\n\t\tpadding-bottom: env(safe-area-inset-bottom); /* iOS 11.2+ */\n\t}\n\t\n\t.safe-area-inset-top {\n\t\theight: var(--status-bar-height);\n\t\tpadding-top: constant(safe-area-inset-top); /* iOS 11.0 */\n\t\tpadding-top: env(safe-area-inset-top); /* iOS 11.2+ */\n\t}\n\t\n\t.container {\n\t\twidth: 100%;\n\t\tmin-height: 100vh;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tbox-sizing: border-box;\n\t\tposition: relative;\n\t\tpadding-bottom: calc(170rpx + env(safe-area-inset-bottom)); /* 修改底部内边距适配新的导航栏高度 */\n\t}\n\t\n\t/* 自定义颜色变量 */\n\t:root {\n\t\t--primary: #8B5CF6;\n\t\t--primary-light: #A875FF;\n\t\t--neon-pink: #ff36f9;\n\t\t--text-primary: #FFFFFF;\n\t\t--text-secondary: rgba(255, 255, 255, 0.7);\n\t\t--text-tertiary: rgba(255, 255, 255, 0.5);\n\t}\n\t\n\t/* 通用文本颜色 */\n\t.text-primary {\n\t\tcolor: #FFFFFF;\n\t}\n\t\n\t.text-secondary {\n\t\tcolor: rgba(255, 255, 255, 0.7);\n\t}\n\t\n\t.text-tertiary {\n\t\tcolor: rgba(255, 255, 255, 0.5);\n\t}\n\t\n\t/* 通用边距 */\n\t.mt-xs {\n\t\tmargin-top: 10rpx;\n\t}\n\t\n\t.mt-sm {\n\t\tmargin-top: 20rpx;\n\t}\n\t\n\t.mt-md {\n\t\tmargin-top: 30rpx;\n\t}\n\t\n\t.mt-lg {\n\t\tmargin-top: 40rpx;\n\t}\n\t\n\t.mb-xs {\n\t\tmargin-bottom: 10rpx;\n\t}\n\t\n\t.mb-sm {\n\t\tmargin-bottom: 20rpx;\n\t}\n\t\n\t.mb-md {\n\t\tmargin-bottom: 30rpx;\n\t}\n\t\n\t.mb-lg {\n\t\tmargin-bottom: 40rpx;\n\t}\n\t\n\t/* 通用flex布局 */\n\t.flex {\n\t\tdisplay: flex;\n\t}\n\t\n\t.flex-col {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t}\n\t\n\t.justify-between {\n\t\tjustify-content: space-between;\n\t}\n\t\n\t.justify-center {\n\t\tjustify-content: center;\n\t}\n\t\n\t.items-center {\n\t\talign-items: center;\n\t}\n\t\n\t.flex-1 {\n\t\tflex: 1;\n\t}\n\t\n\t.text-center {\n\t\ttext-align: center;\n\t}\n</style>\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n", "import mod from \"-!../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754165306779\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}