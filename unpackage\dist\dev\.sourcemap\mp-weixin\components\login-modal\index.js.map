{"version": 3, "sources": ["webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/components/login-modal/index.vue?fa03", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/components/login-modal/index.vue?c239", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/components/login-modal/index.vue?41a3", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/components/login-modal/index.vue?5976", "uni-app:///components/login-modal/index.vue", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/components/login-modal/index.vue?f9f5", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/components/login-modal/index.vue?c6e4"], "names": ["name", "props", "visible", "type", "default", "data", "loginCode", "created", "methods", "handleClose", "getLoginCode", "uni", "provider", "success", "console", "fail", "onGetPhoneNumber", "title", "mask", "icon", "processLoginWithCode", "completeLogin", "API", "then", "userId", "catch"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AACgM;AAChM,gBAAgB,uMAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAmvB,CAAgB,ivBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACkBvwB;AACA;;;;;;;;;;;;;;;;;;eAEA;EACAA;EACAC;IACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;IACAC;MACA;IACA;IAEA;IACAC;MAAA;MACAC;QACAC;QACAC;UACA;UACAC;QACA;QACAC;UACAD;QACA;MACA;IACA;IAEA;IACAE;MACA;QACA;QACA;QAEAL;UACAM;UACAC;QACA;;QAEA;QACA;MACA;QACA;QACAP;UACAM;UACAE;QACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACAT;QACAC;QACAC;UACA;UACAC;UACA;QACA;QACAC;UACAJ;UACAA;YACAM;YACAE;UACA;UACAL;QACA;MACA;IACA;IAEA;IACAO;MAAA;MACA;MACAC,2DACAC;QACAZ;QACA;;QAEA;UACA;UACAA;UACAA;;UAEA;UACA;YACAA;UACA;YACA;YACA;cACAa;YACA;YACAb;UACA;QACA;QAEAA;UACAM;UACAE;QACA;;QAEA;QACA;MACA,GACAM;QACAd;;QAEA;QACA;UACAG;UACAH;UACA;QACA;;QAEAA;UACAM;UACAE;QACA;QACAL;;QAEA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC3JA;AAAA;AAAA;AAAA;AAAgkC,CAAgB,0hCAAG,EAAC,C;;;;;;;;;;;ACAplC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/login-modal/index.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=73a9de3e&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/login-modal/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=73a9de3e&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"login-modal\" v-if=\"visible\">\r\n\t\t<view class=\"login-modal-content\">\r\n\t\t\t<view class=\"login-modal-header\">\r\n\t\t\t\t<text class=\"login-modal-title\">快速登录</text>\r\n\t\t\t\t<text class=\"material-icons close-icon\" @tap=\"handleClose\">close</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"login-modal-body\">\r\n\t\t\t\t<text class=\"material-icons login-icon\">person</text>\r\n\t\t\t\t<text class=\"login-modal-desc\">登录后可使用所有功能</text>\r\n\t\t\t\t<text class=\"auth-explanation\">•需要验证手机号确保安全\\n• 我们将严格保护您的隐私信息</text>\r\n\t\t\t\t<button class=\"auth-btn\" open-type=\"getPhoneNumber\" @getphonenumber=\"onGetPhoneNumber\">授权登录</button>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport API from '@/static/js/api.js';\r\n\timport LoginManager from '@/static/js/login.js';\r\n\t\r\n\texport default {\r\n\t\tname: 'login-modal',\r\n\t\tprops: {\r\n\t\t\tvisible: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tloginCode: '', // 微信登录code\r\n\t\t\t}\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\t// 预先获取登录code，避免多次调用\r\n\t\t\tthis.getLoginCode();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 关闭弹窗\r\n\t\t\thandleClose() {\r\n\t\t\t\tthis.$emit('close');\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 预先获取登录code\r\n\t\t\tgetLoginCode() {\r\n\t\t\t\tuni.login({\r\n\t\t\t\t\tprovider: 'weixin',\r\n\t\t\t\t\tsuccess: (loginRes) => {\r\n\t\t\t\t\t\tthis.loginCode = loginRes.code;\r\n\t\t\t\t\t\tconsole.log('预先获取登录code成功:', this.loginCode);\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\tconsole.error('预先获取登录code失败:', err);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 获取手机号回调\r\n\t\t\tonGetPhoneNumber(e) {\r\n\t\t\t\tif (e.detail.errMsg && e.detail.errMsg.includes('ok')) {\r\n\t\t\t\t\t// 获取手机号成功\r\n\t\t\t\t\tconst phoneCode = e.detail.code;\r\n\t\t\t\t\t\r\n\t\t\t\t\tuni.showLoading({\r\n\t\t\t\t\t\ttitle: '登录中...',\r\n\t\t\t\t\t\tmask: true\r\n\t\t\t\t\t});\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 处理登录流程\r\n\t\t\t\t\tthis.processLoginWithCode(phoneCode);\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 用户取消了授权，显示提示\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '需要授权手机号才能完成登录',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 处理登录码和手机号\r\n\t\t\tprocessLoginWithCode(phoneCode) {\r\n\t\t\t\t// 每次登录都重新获取code，避免使用过期code\r\n\t\t\t\tuni.login({\r\n\t\t\t\t\tprovider: 'weixin',\r\n\t\t\t\t\tsuccess: (loginRes) => {\r\n\t\t\t\t\t\tthis.loginCode = loginRes.code;\r\n\t\t\t\t\t\tconsole.log('获取新的登录code成功:', this.loginCode);\r\n\t\t\t\t\t\tthis.completeLogin(phoneCode);\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '登录失败，请重试',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tconsole.error('微信登录失败:', err);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 完成登录流程\r\n\t\t\tcompleteLogin(phoneCode) {\r\n\t\t\t\t// 调用后端登录接口\r\n\t\t\t\tAPI.user.login(this.loginCode, '', '', phoneCode)\r\n\t\t\t\t\t.then(res => {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\tthis.handleClose(); // 关闭弹窗\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tif (res && res.data) {\r\n\t\t\t\t\t\t\t// 保存登录信息到本地存储\r\n\t\t\t\t\t\t\tuni.setStorageSync('token', res.data.token);\r\n\t\t\t\t\t\t\tuni.setStorageSync('userId', res.data.userId);\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t// 如果后端返回了用户信息，保存到本地\r\n\t\t\t\t\t\t\tif (res.data.userInfo) {\r\n\t\t\t\t\t\t\t\tuni.setStorageSync('userInfo', res.data.userInfo);\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t// 保存基本用户信息\r\n\t\t\t\t\t\t\t\tconst basicUserInfo = {\r\n\t\t\t\t\t\t\t\t\tuserId: res.data.userId\r\n\t\t\t\t\t\t\t\t};\r\n\t\t\t\t\t\t\t\tuni.setStorageSync('userInfo', basicUserInfo);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '登录成功',\r\n\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 通知父组件登录成功\r\n\t\t\t\t\t\tthis.$emit('login-success', res.data);\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch(err => {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 如果是code无效错误，清除登录状态\r\n\t\t\t\t\t\tif (err.code === 500 && err.message && err.message.includes('无效的code')) {\r\n\t\t\t\t\t\t\tconsole.log('检测到无效code错误，清除token');\r\n\t\t\t\t\t\t\tuni.removeStorageSync('token');\r\n\t\t\t\t\t\t\tthis.loginCode = null; // 清除已保存的code\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '登录失败，请重试',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tconsole.error('登录失败:', err);\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 通知父组件登录失败\r\n\t\t\t\t\t\tthis.$emit('login-fail', err);\r\n\t\t\t\t\t});\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t/* 登录弹窗样式 */\r\n\t.login-modal {\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tbackground-color: rgba(0, 0, 0, 0.6);\r\n\t\tz-index: 10000; /* 提高z-index，确保在TabBar之上 */\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tbackdrop-filter: blur(5px);\r\n\t}\r\n\t\r\n\t.login-modal-content {\r\n\t\twidth: 80%;\r\n\t\tmax-width: 600rpx;\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 20rpx;\r\n\t\tbox-shadow: 0 0 20rpx rgba(0, 0, 0, 0.2);\r\n\t\toverflow: hidden;\r\n\t\tmargin-bottom: 120rpx; /* 添加底部间距，避免遮挡TabBar */\r\n\t}\r\n\t\r\n\t.login-modal-header {\r\n\t\tposition: relative;\r\n\t\tpadding: 30rpx;\r\n\t\ttext-align: center;\r\n\t\tborder-bottom: 1rpx solid #f0f0f0;\r\n\t}\r\n\t\r\n\t.login-modal-title {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333;\r\n\t}\r\n\t\r\n\t.close-icon {\r\n\t\tposition: absolute;\r\n\t\tright: 20rpx;\r\n\t\ttop: 50%;\r\n\t\ttransform: translateY(-50%);\r\n\t\tfont-size: 40rpx;\r\n\t\tcolor: #999;\r\n\t}\r\n\t\r\n\t.login-modal-body {\r\n\t\tpadding: 40rpx 30rpx;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t}\r\n\t\r\n\t.login-icon {\r\n\t\tfont-size: 80rpx;\r\n\t\tcolor: #A875FF; /* 修改为紫色 */\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\t\r\n\t.login-modal-desc {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #666;\r\n\t\tmargin-bottom: 30rpx;\r\n\t\ttext-align: center;\r\n\t}\r\n\t\r\n\t.auth-btn {\r\n\t\twidth: 100%;\r\n\t\theight: 80rpx;\r\n\t\tline-height: 80rpx;\r\n\t\tbackground: linear-gradient(to right, #A875FF, #8B5CF6); /* 修改为紫色渐变 */\r\n\t\tcolor: #fff;\r\n\t\tborder-radius: 40rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tmargin-top: 20rpx;\r\n\t\ttext-align: center;\r\n\t}\r\n\t\r\n\t.auth-btn::after {\r\n\t\tborder: none;\r\n\t}\r\n\t\r\n\t.auth-explanation {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #999;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\ttext-align: center;\r\n\t\tpadding: 0 20rpx;\r\n\t}\r\n\t\r\n\t/* Material Icons 字体 */\r\n\t@font-face {\r\n\t\tfont-family: 'Material Icons';\r\n\t\tfont-style: normal;\r\n\t\tfont-weight: 400;\r\n\t\tsrc: url(https://fonts.gstatic.com/s/materialicons/v139/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format('woff2');\r\n\t\tfont-display: block;\r\n\t}\r\n\r\n\t.material-icons {\r\n\t\tfont-family: 'Material Icons';\r\n\t\tfont-weight: normal;\r\n\t\tfont-style: normal;\r\n\t\tfont-size: 48rpx;\r\n\t\tline-height: 1;\r\n\t\tletter-spacing: normal;\r\n\t\ttext-transform: none;\r\n\t\tdisplay: inline-block;\r\n\t\twhite-space: nowrap;\r\n\t\tword-wrap: normal;\r\n\t\tdirection: ltr;\r\n\t\t-webkit-font-smoothing: antialiased;\r\n\t\t-moz-osx-font-smoothing: grayscale;\r\n\t\ttext-rendering: optimizeLegibility;\r\n\t}\r\n</style> ", "import mod from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754165306730\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}