





















































































































/* CSS变量定义 */
page {
	--primary-light: #A875FF;
	--neon-pink: #ff36f9;
	--success-color: #4CAF50;
	--error-color: #F44336;
}
/* 页面基础样式 */
.page-payment-result {
	padding-top: 180rpx;
	padding-bottom: calc(170rpx + env(safe-area-inset-bottom));
	color: #ffffff;
	height: 100vh;
	min-height: 100vh;
	box-sizing: border-box;
	position: relative;
	overflow: hidden;
}
/* 页面背景样式 */
.page-background {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 0;
}
/* 背景图片样式 */
.background-image {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 0;
	object-fit: cover;
}
.gradient-overlay {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: linear-gradient(to bottom, 
		rgba(18, 18, 18, 0.7) 0%, 
		rgba(18, 18, 18, 0.6) 50%,
		rgba(18, 18, 18, 0.7) 100%);
	z-index: 1;
}
/* 顶部状态栏和导航栏 */
.status-bar {
	width: 100%;
	background: transparent;
	backdrop-filter: none;
	-webkit-backdrop-filter: none;
	position: fixed;
	top: 0;
	left: 0;
	z-index: 100;
}
/* 导航栏 */
.nav-bar {
	position: fixed;
	top: calc(env(safe-area-inset-top) + 60rpx);
	left: 15rpx;
	right: 15rpx;
	height: 110rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background: transparent;
	backdrop-filter: none;
	-webkit-backdrop-filter: none;
	z-index: 100;
	padding: 0 30rpx;
	border-bottom: none;
	box-shadow: none;
	border-radius: 0 0 30rpx 30rpx;
}
.nav-back {
	position: absolute;
	left: 30rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	width: 70rpx;
	height: 70rpx;
	border-radius: 50%;
	background-color: rgba(168, 117, 255, 0.15);
	border: 1px solid rgba(168, 117, 255, 0.3);
}
.nav-back .material-icons {
	font-size: 44rpx;
	color: rgba(255, 255, 255, 0.9);
	text-shadow: 0 0 8rpx rgba(168, 117, 255, 0.5);
}
.nav-title {
	font-size: 38rpx;
	font-weight: 600;
	color: #ffffff;
	text-shadow: 0 0 10rpx rgba(168, 117, 255, 0.5);
	letter-spacing: 2rpx;
}
/* 结果内容区域 */
.result-content {
	position: relative;
	z-index: 2;
	padding: 0 40rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-top: 60rpx;
}
/* 结果图标 */
.result-icon {
	width: 160rpx;
	height: 160rpx;
	border-radius: 50%;
	background-color: rgba(244, 67, 54, 0.15);
	border: 1px solid rgba(244, 67, 54, 0.3);
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 40rpx;
	box-shadow: 0 0 30rpx rgba(244, 67, 54, 0.3);
}
.result-icon.success {
	background-color: rgba(76, 175, 80, 0.15);
	border: 1px solid rgba(76, 175, 80, 0.3);
	box-shadow: 0 0 30rpx rgba(76, 175, 80, 0.3);
}
.result-icon .material-icons {
	font-size: 100rpx;
	color: var(--error-color);
}
.result-icon.success .material-icons {
	color: var(--success-color);
}
/* 结果标题 */
.result-title {
	font-size: 48rpx;
	font-weight: 700;
	color: #ffffff;
	margin-bottom: 20rpx;
	text-shadow: 0 0 10rpx rgba(168, 117, 255, 0.5);
}
/* 结果信息 */
.result-info {
	font-size: 32rpx;
	color: rgba(255, 255, 255, 0.8);
	text-align: center;
	margin-bottom: 40rpx;
	max-width: 600rpx;
	line-height: 1.5;
}
/* 订单信息 */
.order-info {
	width: 100%;
	background-color: rgba(30, 30, 30, 0.5);
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 60rpx;
	border: 1px solid rgba(168, 117, 255, 0.2);
}
.info-item {
	display: flex;
	margin-bottom: 20rpx;
}
.info-item:last-child {
	margin-bottom: 0;
}
.info-label {
	font-size: 28rpx;
	color: rgba(255, 255, 255, 0.6);
	width: 160rpx;
}
.info-value {
	font-size: 28rpx;
	color: rgba(255, 255, 255, 0.9);
	flex: 1;
	word-break: break-all;
}
/* 操作按钮 */
.action-buttons {
	display: flex;
	justify-content: space-between;
	width: 100%;
	margin-top: 20rpx;
}
.primary-button {
	flex: 1;
	height: 90rpx;
	background: linear-gradient(145deg, rgba(168, 117, 255, 0.7), rgba(168, 117, 255, 0.9));
	border-radius: 45rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 8rpx 20rpx rgba(168, 117, 255, 0.3);
	margin: 0 20rpx;
}
.primary-button text {
	font-size: 32rpx;
	font-weight: 600;
	color: #ffffff;
}
.retry-button {
	background: linear-gradient(145deg, rgba(255, 152, 0, 0.7), rgba(255, 152, 0, 0.9));
	box-shadow: 0 8rpx 20rpx rgba(255, 152, 0, 0.3);
}
/* Material Icons 字体 */
@font-face {
	font-family: 'Material Icons';
	font-style: normal;
	font-weight: 400;
	src: url(https://fonts.gstatic.com/s/materialicons/v139/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format('woff2');
}
.material-icons {
	font-family: 'Material Icons';
	font-weight: normal;
	font-style: normal;
	font-size: 48rpx;
	line-height: 1;
	letter-spacing: normal;
	text-transform: none;
	display: inline-block;
	white-space: nowrap;
	word-wrap: normal;
	direction: ltr;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

