{"version": 3, "sources": ["webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/custom-tab-bar/index.vue?718e", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/custom-tab-bar/index.vue?f175", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/custom-tab-bar/index.vue?c97e", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/custom-tab-bar/index.vue?3f28", "uni-app:///custom-tab-bar/index.vue", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/custom-tab-bar/index.vue?9217", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/custom-tab-bar/index.vue?1090"], "names": ["name", "data", "currentPath", "hasBottomSafeArea", "isAndroid", "systemInfo", "tabBarStyle", "opacity", "list", "pagePath", "text", "icon", "isMpWeixin", "pathCheckTimer", "created", "console", "screenHeight", "uni", "setTimeout", "onShow", "onHide", "<PERSON><PERSON><PERSON><PERSON>", "methods", "startPathCheck", "stopPathCheck", "clearInterval", "updateCurrentPath", "isActive", "switchTab", "animation", "url", "success", "fail", "setSelected"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACqC;;;AAGzF;AAC6L;AAC7L,gBAAgB,uMAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACxBA;AAAA;AAAA;AAAA;AAAouB,CAAgB,ivBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCsBxvB;EACAA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;MACA;;MACAC,OACA;QACAC;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,EACA;MACA;MACAC;MACA;MACAC;IACA;EACA;EACAC;IAAA;IACA;;IAEA;;IAGA;IACA;MACA;MACA;MACAC;;MAEA;MACA;;MAEA;MACA;MACA;QACA;MACA;QACA;QACA;QACA;;QAEA;QACA;UACA;UACA;UACA;UACA,qGACAC;;UAEA;UACAC;QACA;MACA;MACAF;MACAA;IACA;MACAA;IACA;;IAEA;IACAG;MACA;MACA;;MAEA;MACA;IACA;EACA;EACAC;IACA;IACA;;IAEA;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;IACAC;MAAA;MACA;MACA;;MAEA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACAC;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;UACA;UACA;;UAEA;UACA;YACAX;YACA;UACA;QACA;MACA;QACAA;MACA;IACA;IAEA;IACAY;MACA;MACA;;MAEA;MACA;QACA;MACA;;MAEA;MACA;MACA;QACA;MACA;;MAEA;MACA;MACA;MACA;QACA;MACA;;MAEA;MACA;QACA;UACA;QACA;MACA;MAEA;IACA;IAEAC;MAAA;MACA;MACA;;MAEA;MACA;;MAEA;MACA;MACA;;MAEA;MACA;QACA;QACAX;UACAY;QACA;MACA;MAEAd;;MAEA;MACAG;QACAD;UACAa;UACAC;YACAhB;;YAEA;YACAG;cACA;;cAEA;cACAA;gBACA;cACA;YACA;UACA;UACAc;YACAjB;YACA;YACAE;cACAa;cACAC;gBACA;gBACAb;kBACA;;kBAEA;kBACAA;oBACA;kBACA;gBACA;cACA;YACA;UACA;QACA;MACA;IACA;IACA;IACAe;MACA;MACA;MACA;QACA;QACA;QACA;;QAEA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACtQA;AAAA;AAAA;AAAA;AAAmkC,CAAgB,kjCAAG,EAAC,C;;;;;;;;;;;ACAvlC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "custom-tab-bar/index.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=47b4a9d0&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=47b4a9d0&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"47b4a9d0\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"custom-tab-bar/index.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=47b4a9d0&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.list, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var m0 = _vm.isActive(item.pagePath)\n    return {\n      $orig: $orig,\n      m0: m0,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"tab-bar\"\n\t\t:class=\"{\n\t\t\t'safe-area-inset-bottom': hasBottomSafeArea,\n\t\t\t'android-tab-bar': isAndroid\n\t\t}\"\n\t\t:style=\"tabBarStyle\">\n\t\t<view class=\"tab-bar-border\"></view>\n\t\t<view v-for=\"(item, index) in list\" :key=\"index\"\n\t\t\tclass=\"tab-bar-item\"\n\t\t\t:class=\"{\n\t\t\t\t'active': isActive(item.pagePath),\n\t\t\t\t'android-tab-item': isAndroid\n\t\t\t}\"\n\t\t\t@tap=\"switchTab(item.pagePath)\">\n\t\t\t<text class=\"material-icons\" :class=\"{'android-icon': isAndroid}\">{{ item.icon }}</text>\n\t\t\t<text class=\"tab-text\" :class=\"{'android-text': isAndroid}\">{{ item.text }}</text>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t\tname: \"custom-tab-bar\",\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tcurrentPath: '',\n\t\t\t\thasBottomSafeArea: false,\n\t\t\t\tisAndroid: false,\n\t\t\t\tsystemInfo: null,\n\t\t\t\ttabBarStyle: {\n\t\t\t\t\topacity: 0 // 初始隐藏，避免闪烁\n\t\t\t\t},\n\t\t\t\tlist: [\n\t\t\t\t\t{\n\t\t\t\t\t\tpagePath: \"/pages/index/index\",\n\t\t\t\t\t\ttext: \"首页\",\n\t\t\t\t\t\ticon: \"meeting_room\"\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tpagePath: \"/pages/profile/profile\",\n\t\t\t\t\t\ttext: \"我的\",\n\t\t\t\t\t\ticon: \"diamond\"\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\t// 标记是否为微信小程序环境\n\t\t\t\tisMpWeixin: false,\n\t\t\t\t// 路径检查定时器\n\t\t\t\tpathCheckTimer: null\n\t\t\t}\n\t\t},\n\t\tcreated() {\n\t\t\t// 检测当前环境\n\t\t\t// #ifdef MP-WEIXIN\n\t\t\tthis.isMpWeixin = true;\n\t\t\t// #endif\n\t\t\t\n\t\t\t// 初始化系统信息和安全区域检查\n\t\t\ttry {\n\t\t\t\t// 获取系统信息\n\t\t\t\tthis.systemInfo = uni.getSystemInfoSync();\n\t\t\t\tconsole.log('TabBar - 系统信息:', this.systemInfo);\n\n\t\t\t\t// 检测是否为安卓设备\n\t\t\t\tthis.isAndroid = this.systemInfo.platform === 'android';\n\n\t\t\t\t// 尝试从App实例获取\n\t\t\t\tconst app = getApp();\n\t\t\t\tif (app && app.globalData) {\n\t\t\t\t\tthis.hasBottomSafeArea = app.globalData.isIphoneX;\n\t\t\t\t} else {\n\t\t\t\t\t// 回退到本地存储\n\t\t\t\t\tconst isIPhoneX = uni.getStorageSync('isIPhoneX');\n\t\t\t\t\tthis.hasBottomSafeArea = isIPhoneX;\n\n\t\t\t\t\t// 如果仍然没有，尝试检测系统信息\n\t\t\t\t\tif (this.hasBottomSafeArea === '') {\n\t\t\t\t\t\tconst model = this.systemInfo.model;\n\t\t\t\t\t\tconst screenHeight = this.systemInfo.screenHeight;\n\t\t\t\t\t\tconst screenWidth = this.systemInfo.screenWidth;\n\t\t\t\t\t\tthis.hasBottomSafeArea = /iPhone X|iPhone 11|iPhone 12|iPhone 13|iPhone 14|iPhone 15/.test(model) ||\n\t\t\t\t\t\t\t(screenHeight / screenWidth > 2 && this.systemInfo.platform === 'ios');\n\n\t\t\t\t\t\t// 存储结果供后续使用\n\t\t\t\t\t\tuni.setStorageSync('isIPhoneX', this.hasBottomSafeArea);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tconsole.log('底部安全区域检测结果:', this.hasBottomSafeArea);\n\t\t\t\tconsole.log('是否为安卓设备:', this.isAndroid);\n\t\t\t} catch(e) {\n\t\t\t\tconsole.error('获取安全区域信息失败', e);\n\t\t\t}\n\t\t\t\n\t\t\t// 延迟显示TabBar，避免闪烁\n\t\t\tsetTimeout(() => {\n\t\t\t\tthis.tabBarStyle.opacity = 1;\n\t\t\t\tthis.updateCurrentPath();\n\t\t\t\t\n\t\t\t\t// 启动定时器，持续监测页面路径变化\n\t\t\t\tthis.startPathCheck();\n\t\t\t}, 200);\n\t\t},\n\t\tonShow() {\n\t\t\t// 页面显示时更新当前路径\n\t\t\tthis.updateCurrentPath();\n\t\t\t\n\t\t\t// 确保定时器在显示时启动\n\t\t\tthis.startPathCheck();\n\t\t},\n\t\tonHide() {\n\t\t\t// 页面隐藏时停止定时器\n\t\t\tthis.stopPathCheck();\n\t\t},\n\t\tbeforeDestroy() {\n\t\t\t// 组件销毁前停止定时器\n\t\t\tthis.stopPathCheck();\n\t\t},\n\t\tmethods: {\n\t\t\t// 启动路径检查定时器\n\t\t\tstartPathCheck() {\n\t\t\t\t// 先清除可能存在的定时器\n\t\t\t\tthis.stopPathCheck();\n\t\t\t\t\n\t\t\t\t// 设置新的定时器，每200ms检查一次路径\n\t\t\t\tthis.pathCheckTimer = setInterval(() => {\n\t\t\t\t\tthis.updateCurrentPath();\n\t\t\t\t}, 200);\n\t\t\t},\n\t\t\t\n\t\t\t// 停止路径检查定时器\n\t\t\tstopPathCheck() {\n\t\t\t\tif (this.pathCheckTimer) {\n\t\t\t\t\tclearInterval(this.pathCheckTimer);\n\t\t\t\t\tthis.pathCheckTimer = null;\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 更新当前页面路径\n\t\t\tupdateCurrentPath() {\n\t\t\t\ttry {\n\t\t\t\t\tconst pages = getCurrentPages();\n\t\t\t\t\tif (pages && pages.length > 0) {\n\t\t\t\t\t\tconst currentPage = pages[pages.length - 1];\n\t\t\t\t\t\tconst route = '/' + currentPage.route;\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 只有当路径变化时才记录和更新\n\t\t\t\t\t\tif (this.currentPath !== route) {\n\t\t\t\t\t\t\tconsole.log('TabBar更新当前路径:', route);\n\t\t\t\t\t\t\tthis.currentPath = route;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t} catch (e) {\n\t\t\t\t\tconsole.error('获取当前页面路径失败', e);\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 判断是否为当前活动项\n\t\t\tisActive(pagePath) {\n\t\t\t\t// 首先尝试刷新当前路径\n\t\t\t\tthis.updateCurrentPath();\n\t\t\t\t\n\t\t\t\t// 完全匹配路径\n\t\t\t\tif (this.currentPath === pagePath) {\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 兼容不带斜杠的路径\n\t\t\t\tconst normalizedPath = pagePath.replace(/^\\//, '');\n\t\t\t\tif (this.currentPath === normalizedPath || this.currentPath === '/' + normalizedPath) {\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 页面路径的简化比较（忽略查询参数等）\n\t\t\t\tconst currentSimple = this.currentPath.split('?')[0];\n\t\t\t\tconst targetSimple = pagePath.split('?')[0];\n\t\t\t\tif (currentSimple === targetSimple) {\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 特殊处理招商页面 - 在招商页面时仍然高亮首页Tab\n\t\t\t\tif (this.currentPath.includes('/packageA/pages/zhaoshang/')) {\n\t\t\t\t\tif (pagePath === '/pages/index/index') {\n\t\t\t\t\t\treturn true;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\treturn false;\n\t\t\t},\n\t\t\t\n\t\t\tswitchTab(path) {\n\t\t\t\t// 如果已经在当前页面，不做任何操作\n\t\t\t\tif (this.isActive(path)) return;\n\t\t\t\t\n\t\t\t\t// 切换前先设置透明度为0，避免闪烁\n\t\t\t\tthis.tabBarStyle.opacity = 0;\n\t\t\t\t\n\t\t\t\t// 立即更新当前路径\n\t\t\t\tconst normalizedPath = path.replace(/^\\//, '');\n\t\t\t\tthis.currentPath = '/' + normalizedPath;\n\t\t\t\t\n\t\t\t\t// 微信小程序环境下不调用hideTabBar\n\t\t\t\tif (!this.isMpWeixin) {\n\t\t\t\t\t// 先隐藏TabBar，避免闪烁\n\t\t\t\t\tuni.hideTabBar({\n\t\t\t\t\t\tanimation: false\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tconsole.log('切换Tab到:', path);\n\t\t\t\t\n\t\t\t\t// 延迟一小段时间再跳转，确保状态更新\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tuni.switchTab({\n\t\t\t\t\t\turl: `/${normalizedPath}`,\n\t\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\t\tconsole.log('Tab切换成功');\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 强制更新当前路径\n\t\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\t\tthis.updateCurrentPath();\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t// 恢复TabBar透明度\n\t\t\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\t\t\tthis.tabBarStyle.opacity = 1;\n\t\t\t\t\t\t\t\t}, 50);\n\t\t\t\t\t\t\t}, 50);\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\tconsole.error('Tab切换失败', err);\n\t\t\t\t\t\t\t// 如果是非tabBar页面，尝试使用navigateTo\n\t\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\t\turl: path,\n\t\t\t\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\t\t\t\t// 强制更新当前路径\n\t\t\t\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\t\t\t\tthis.updateCurrentPath();\n\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t// 恢复TabBar透明度\n\t\t\t\t\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\t\t\t\t\tthis.tabBarStyle.opacity = 1;\n\t\t\t\t\t\t\t\t\t\t}, 50);\n\t\t\t\t\t\t\t\t\t}, 50);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}, 50);\n\t\t\t},\n\t\t\t// 更新选中状态的方法，由页面调用（为了向后兼容）\n\t\t\tsetSelected(index) {\n\t\t\t\t// 获取对应路径\n\t\t\t\tconst path = this.list[index] ? this.list[index].pagePath : '';\n\t\t\t\tif (path) {\n\t\t\t\t\t// 强制更新当前路径\n\t\t\t\t\tconst normalizedPath = path.replace(/^\\//, '');\n\t\t\t\t\tthis.currentPath = '/' + normalizedPath;\n\t\t\t\t\t\n\t\t\t\t\t// 触发视图更新\n\t\t\t\t\tthis.$forceUpdate();\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style scoped>\n\t/* Material Icons 字体 */\n\t@font-face {\n\t\tfont-family: 'Material Icons';\n\t\tfont-style: normal;\n\t\tfont-weight: 400;\n\t\tsrc: url(https://fonts.gstatic.com/s/materialicons/v139/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format('woff2'),\n\t\t\t url(https://fonts.googleapis.com/icon?family=Material+Icons) format('truetype');\n\t\tfont-display: block;\n\t}\n\t\n\t:root {\n\t\t--primary: #8B5CF6;\n\t\t--primary-dark: #8B5CF6;\n\t\t--primary-light: #A875FF;\n\t}\n\t\n\t.tab-bar {\n\t\tposition: fixed;\n\t\tbottom: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\theight: 120rpx; /* 增加基础高度，适配安卓设备 */\n\t\tbackground: #FFFFFF;\n\t\tdisplay: flex;\n\t\tflex-wrap: nowrap; /* 不换行 */\n\t\tz-index: 999;\n\t\tbackdrop-filter: blur(10px);\n\t\t-webkit-backdrop-filter: blur(10px);\n\t\tbox-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);\n\t\tborder-top: 1px solid rgba(200, 200, 200, 0.3);\n\t\ttransition: all 0.3s ease, opacity 0.3s ease;\n\t\twidth: 100%; /* 确保宽度为100% */\n\t\tjustify-content: space-around; /* 使用space-around而不是默认的stretch，减少中间空白 */\n\t\tpadding-bottom: constant(safe-area-inset-bottom);\n\t\tpadding-bottom: env(safe-area-inset-bottom, 20rpx); /* 安卓下默认20rpx */\n\t\tmin-height: 120rpx; /* 确保最小高度 */\n\t}\n\n\t/* 安卓设备专属样式优化 */\n\t.android-tab-bar {\n\t\theight: 160rpx !important; /* 安卓设备增加高度 */\n\t\tpadding-bottom: 40rpx !important; /* 安卓设备增加底部内边距 */\n\t\tmin-height: 160rpx !important; /* 确保安卓设备最小高度 */\n\t}\n\t\n\t/* 微信小程序环境下的安卓设备优化 */\n\t/* #ifdef MP-WEIXIN */\n\t.android-tab-bar {\n\t\theight: 170rpx !important; /* 微信小程序中安卓设备需要更大高度 */\n\t\tpadding-bottom: 45rpx !important; /* 微信小程序中安卓设备需要更大底部内边距 */\n\t\tmin-height: 170rpx !important; /* 确保微信小程序中安卓设备最小高度 */\n\t}\n\n\t.android-tab-item {\n\t\theight: 170rpx !important; /* 微信小程序中安卓设备项目高度 */\n\t\tmargin-top: 25rpx !important; /* 微信小程序中安卓设备上边距 */\n\t\tpadding-bottom: 20rpx !important; /* 微信小程序中安卓设备底部内边距 */\n\t}\n\n\t.android-icon {\n\t\tfont-size: 60rpx !important; /* 微信小程序中安卓设备图标尺寸 */\n\t\tmargin-top: 20rpx !important; /* 微信小程序中安卓设备图标上边距 */\n\t}\n\n\t.android-text {\n\t\tfont-size: 32rpx !important; /* 微信小程序中安卓设备文字尺寸 */\n\t\tmargin-top: 10rpx !important; /* 微信小程序中安卓设备文字上边距 */\n\t\tfont-weight: 600 !important; /* 微信小程序中安卓设备文字加粗 */\n\t}\n\t/* #endif */\n\n\t.tab-bar.safe-area-inset-bottom {\n\t\tpadding-bottom: env(safe-area-inset-bottom); /* 添加安全区域内边距 */\n\t}\n\t\n\t.safe-area-placeholder {\n\t\twidth: 100%;\n\t\theight: env(safe-area-inset-bottom);\n\t\tbackground: #FFFFFF !important;\n\t}\n\t\n\t.tab-bar-border {\n\t\tposition: absolute;\n\t\tleft: 0;\n\t\ttop: 0;\n\t\twidth: 100%;\n\t\theight: 1px;\n\t\tbackground: rgba(200, 200, 200, 0.3);\n\t\ttransform: scaleY(0.5);\n\t}\n\t\n\t.tab-bar-item {\n\t\tflex: 1 1 0; /* 平均分配宽度 */\n\t\tmin-width: 120rpx; /* 设置最小宽度 */\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tcolor: rgba(100, 100, 100, 0.8);\n\t\ttransition: all 0.3s cubic-bezier(0.19, 1, 0.22, 1);\n\t\tposition: relative;\n\t\tpadding-top: 0;\n\t\tpadding-bottom: 0;\n\t\tmargin-top: 15rpx; /* 增加上边距，适配更高的tab-bar */\n\t\theight: 120rpx; /* 增加项目高度与tab-bar一致 */\n\t\tmin-height: 120rpx; /* 确保最小高度 */\n\t}\n\n\t/* 安卓设备TabBar项目样式优化 */\n\t.android-tab-item {\n\t\theight: 140rpx !important; /* 安卓设备增加项目高度 */\n\t\tmargin-top: 20rpx !important; /* 安卓设备增加上边距 */\n\t\tpadding-bottom: 12rpx !important; /* 安卓设备增加底部内边距 */\n\t}\n\t\n\t.material-icons {\n\t\tfont-family: 'Material Icons';\n\t\tfont-weight: normal;\n\t\tfont-style: normal;\n\t\tfont-size: 46rpx;\n\t\tline-height: 1;\n\t\tletter-spacing: normal;\n\t\ttext-transform: none;\n\t\tdisplay: block;\n\t\twhite-space: nowrap;\n\t\tword-wrap: normal;\n\t\tdirection: ltr;\n\t\t-webkit-font-smoothing: antialiased;\n\t\t-moz-osx-font-smoothing: grayscale;\n\t\ttransition: all 0.3s ease;\n\t\tmargin: 0 auto;\n\t\ttext-align: center;\n\t\ttext-shadow: 0 0 5rpx rgba(255, 255, 255, 0.3);\n\t\tmargin-top: 8rpx; /* 调整图标上边距 */\n\t}\n\n\t/* 安卓设备图标样式优化 */\n\t.android-icon {\n\t\tfont-size: 56rpx !important; /* 安卓设备增大图标尺寸 */\n\t\tmargin-top: 16rpx !important; /* 安卓设备增加图标上边距 */\n\t}\n\t\n\t.tab-text {\n\t\tfont-size: 24rpx; /* 减小字体大小 */\n\t\tmargin-top: 0; /* 减少与图标的间距 */\n\t\ttransition: all 0.3s ease;\n\t\ttext-align: center;\n\t\twidth: 100%;\n\t\tfont-weight: 500;\n\t\ttext-shadow: 0 0 5rpx rgba(255, 255, 255, 0.3);\n\t}\n\n\t/* 安卓设备文字样式优化 */\n\t.android-text {\n\t\tfont-size: 28rpx !important; /* 安卓设备增大文字尺寸 */\n\t\tmargin-top: 6rpx !important; /* 安卓设备增加与图标的间距 */\n\t\tfont-weight: 600 !important; /* 安卓设备增加字重 */\n\t}\n\t\n\t.tab-bar-item.active {\n\t\tcolor: var(--primary-light, #A875FF);\n\t\ttransform: translateY(0);\n\t}\n\t\n\t.tab-bar-item.active .material-icons {\n\t\ttext-shadow: 0 0 10rpx rgba(168, 117, 255, 0.4);\n\t\ttransform: scale(1.1);\n\t}\n\t\n\t.tab-bar-item.active .tab-text {\n\t\tfont-weight: 600;\n\t}\n\t\n\t/* 添加按下效果 */\n\t.tab-bar-item:active {\n\t\topacity: 0.8;\n\t}\n</style> ", "import mod from \"-!../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=47b4a9d0&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=47b4a9d0&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754165306726\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}