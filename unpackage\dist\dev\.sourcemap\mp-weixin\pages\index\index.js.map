{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/index/index.vue?72f8", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/index/index.vue?d9d8", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/index/index.vue?72e6", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/index/index.vue?cc88", "uni-app:///pages/index/index.vue", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/index/index.vue?6ad4", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/index/index.vue?65bc"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "customTabBar", "data", "title", "isNavigating", "bluetoothInitialized", "bluetoothInitializing", "computed", "isLoggedIn", "userInfo", "onLoad", "console", "onShow", "onReady", "setTimeout", "methods", "updateTabBar", "navigateTo", "uni", "url", "success", "fail", "complete", "navigateToZhaoshang", "autoShowLogin", "scanQRCode", "showFunctionIntroduction", "startScan", "QRScanner", "onlyFromCamera", "showLoading", "then", "catch", "icon", "content", "confirmText", "cancelText", "scanType", "checkLastScanParams", "disconnectBluetooth", "blueToothManager", "res", "deviceId", "initBluetooth", "systemInfo", "global", "duration", "message", "simulated", "_initBLEController", "resolve", "reject", "navigateToLock", "_checkBluetoothAvailable", "showCancel"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AACgM;AAChM,gBAAgB,uMAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAmvB,CAAgB,ivBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;AC8DvwB;AACA;AACA;AACA;AAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAEA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MAAA;MACAC;MACAC;IACA;EACA;EACAC,4BACA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;EACA,GACA;EACAC;IACAC;IACA;IACA;;IAEA;IACA;EACA;EACAC;IACA;IACA;;IAEA;IACA;;IAEA;IACA;;IAEA;IACA;;IAEA;IACA;;IAEA;IACA;EACA;EACAC;IAAA;IACA;IACAC;MACA;IACA;EACA;EACAC,yCACA,uBACA,oBACA,kBACA,iBACA;IAEA;IACAC;MACA;QACA;UACA;UACA;YACA;UACA;;UAEA;UACA;YACA;UACA;;UAEA;UACA;YACA;UACA;QACA;UACAL;QACA;MACA;IACA;IACAM;MAAA;MACA;MACA;MAEA;;MAEA;MACAH;QACAI;UACAC;UACAC;YACAT;UACA;UACAU;YACAV;YACA;UACA;UACAW;YACA;YACAR;cACA;YACA;UACA;QACA;MACA;IACA;IACA;IACAS;MAAA;MACA;MACA;MAEA;MAEAT;QACAI;UACAC;UACAC;YACAT;UACA;UACAU;YACAV;YACA;;YAEA;YACA;cACAA;cACAO;gBACAC;gBACAG;kBACAR;oBACAI;sBACAC;oBACA;kBACA;gBACA;cACA;YACA;UACA;UACAG;YACA;YACAR;cACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAU;MACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;;MAEA;MACA;QACA;QACA;QACA;MACA;;MAEA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;;QAEA;QACA;QAEAhB;;QAEA;QACAiB;UACAC;UACAC;UAAA;UACAV;YACAT;;YAEA;YACAiB,4CACAG;cACApB;YACA,GACAqB;cACArB;;cAEA;cACAO;gBACAf;gBACA8B;cACA;YACA;UACA;UACAZ;YACAV;;YAEA;YACA;cACAA;cACA;YACA;;YAEA;YACA;cACAA;;cAEA;cACAO;gBACAf;gBACA+B;gBACAC;gBACAC;gBACAhB;kBACA;oBACA;oBACAN;sBACA;sBACAI;wBACAW;wBACAQ;wBACAjB;0BACAT;0BACA;0BACAiB;wBACA;wBACAP;0BACAV;0BACAO;4BACAf;4BACA8B;0BACA;wBACA;sBACA;oBACA;kBACA;gBACA;cACA;cACA;YACA;;YAEA;YACAf;cACAf;cACA8B;YACA;UACA;QACA;MACA;QACA;QACAtB;QACAO;QACAA;UACAf;UACA8B;QACA;MACA;IACA;IAEA;IACAK;MACA;MACA;MACA;QACA;MACA;;MAEA;MACA;MACA;QACA;QACA;QACA;QAEA;UAAA;UACA3B;;UAEA;UACAO;;UAEA;UACA;UACA;YACAC;UACA;UACA;YACAA;UACA;;UAEA;UACA;YACAA;UACA;;UAEA;UACAL;YACAI;cACAC;cACAE;gBACAV;cACA;YACA;UACA;QACA;UACA;UACAO;QACA;MACA;IACA;IAEA;AACA;AACA;IACAqB;MACA5B;;MAEA;MACA;QACA;UACA6B;YACA7B;UACA;QACA;UACAA;QACA;MACA;QACAA;;QAEA;QACA;UACA;UACAO;YACAE;cACA;gBACA;gBACAqB;kBACAvB;oBACAwB;oBACAtB;sBACAT;oBACA;oBACAU;sBACAV;oBACA;kBACA;gBACA;cACA;YACA;YACAU;cACAV;YACA;UACA;QACA;UACAA;QACA;MACA;IACA;IAEA;AACA;AACA;IACAgC;MAAA;MACAhC;;MAEA;MACA;QACA;QACAA;QAEA;QACA;QACA;QACA,qDACAiC,8DACAA;;QAEA;QACA;UACAjC;UACA;UACA;;UAEA;UACA;YACAkC;UACA;;UAEA;UACA3B;YACAf;YACA8B;YACAa;UACA;UAEA;YACA1B;YACA2B;YACAC;UACA;QACA;MACA;QACArC;QACA;QACA;QACA;QAEA;UACAS;UACA2B;UACAC;QACA;MACA;;MAEA;MACA,iCACAjB;QACApB;QACA;;QAEA;QACA;UACA;UACAA;;UAEA;UACA;YACAkC;UACA;QACA;QAEA;MACA,GACAb;QACArB;;QAEA;QACA;QACA;;QAEA;QACA;UACAkC;QACA;QAEAlC;;QAEA;QACAO;UACAf;UACA8B;UACAa;QACA;QAEA;UACA1B;UACA2B;UACAC;QACA;MACA;IACA;IAEA;AACA;AACA;AACA;AACA;IACAC;MACA;QACA;UACA;UACA;YACAtC;YACA;UACA;;UAEA;UACA;YACA6B;YACAA;UACA;;UAEA;UACAK;UAEAlC;UACAuC;QACA;UACAvC;UACAwC;QACA;MACA;IACA;IAEA;AACA;AACA;IACAC;MAAA;MACA;MACA;MAEA;;MAEA;MACAlC;QACAC;QACAG;UACA;UACAR;YACA;UACA;QACA;MACA;IACA;IACA;AACA;AACA;AACA;AACA;IACAuC;MACA;QACA;QACAnC;UACAE;YACAT;;YAEA;YACAO;cACAE;gBACAT;gBAEA;kBACA;kBACAO;oBACAf;oBACA+B;oBACAoB;oBACAlC;sBACA+B;oBACA;kBACA;kBACA;gBACA;gBAEAD;cACA;cACA7B;gBACAV;gBACAwC;cACA;YACA;UACA;UACA9B;YACAV;;YAEA;YACA;cACA;cACAO;gBACAf;gBACA+B;gBACAoB;gBACAlC;kBACA+B;gBACA;cACA;YACA;cACA;cACAjC;gBACAf;gBACA+B;gBACAoB;gBACAlC;kBACA;kBACAF;oBACAE;sBACAT;oBACA;oBACAU;sBACAV;oBACA;kBACA;kBAEAwC;gBACA;cACA;YACA;cACAA;YACA;UACA;QACA;MACA;IACA;EAAA;AAEA;AAAA,2B;;;;;;;;;;;;;ACvqBA;AAAA;AAAA;AAAA;AAAgkC,CAAgB,0hCAAG,EAAC,C;;;;;;;;;;;ACAplC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=57280228&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=57280228&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"container page-index\" @touchmove.prevent>\n\t\t<!-- 页面背景 -->\n\t\t<view class=\"page-background\">\n\t\t\t<!-- 背景图片 -->\n\t\t\t<image class=\"background-image\" src=\"https://wuji1112-1312674791.cos.ap-guangzhou.myqcloud.com/static/编组 <EMAIL>\" mode=\"aspectFill\"></image>\n\t\t</view>\n\t\t\n\t\t<!-- 顶部状态栏占位 -->\n\t\t<view class=\"status-bar safe-area-inset-top\"></view>\n\t\t\n\t\t<!-- 页面内容 -->\n\t\t<view class=\"content\" @touchmove.stop>\n\t\t\t<!-- 顶部欢迎区域 -->\n\t\t\t<view class=\"welcome-section\">\n\t\t\t\t<!-- 添加顶部banner -->\n\t\t\t\t<view class=\"banner-container\" @tap=\"navigateToZhaoshang\">\n\t\t\t\t\t<image class=\"banner-image\" src=\"https://wuji1112-1312674791.cos.ap-guangzhou.myqcloud.com/static/<EMAIL>\" mode=\"contain\"></image>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 移除logo部分，因为背景图片已包含logo -->\n\t\t\t\t<view class=\"welcome-text\">\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 快捷功能区域 -->\n\t\t\t<view class=\"quick-actions\">\n\t\t\t\t<!-- 标题移到中部位置 -->\n\t\t\t\t<view class=\"scan-title-container\">\n\t\t\t\t\t<view class=\"neon-title\">点击爱心</view>\n\t\t\t\t\t<view class=\"neon-title mt-xs\">扫码开门</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 显示扫码按钮，无论是否登录 -->\n\t\t\t\t<view class=\"action-container\">\n\t\t\t\t\t<view class=\"scan-button-wrapper\" @tap=\"scanQRCode\">\n\t\t\t\t\t\t<view class=\"scan-icon-container\">\n\t\t\t\t\t\t\t<text class=\"material-icons heart-icon\">favorite</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 添加协议提示文字 -->\n\t\t\t\t<view class=\"agreement-tip text-tertiary\">\n\t\t\t\t\t<text>点击开门即视为同意《用户协议》及《隐私协议》</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 底部空白区域，确保内容不被遮挡 -->\n\t\t\t<view class=\"bottom-space\"></view>\n\t\t</view>\n\t\t\n\t\t<!-- 自定义TabBar -->\n\t\t<custom-tab-bar ref=\"tabBar\"></custom-tab-bar>\n\t\t\n\t\t<!-- 全局登录组件 -->\n\t\t<global-login ref=\"globalLogin\"></global-login>\n\t</view>\n</template>\n\n<script>\n\timport customTabBar from \"@/custom-tab-bar/index.vue\"\n\timport QRScanner from '@/static/js/qrScanner.js';\n\timport API from '@/static/js/api.js';\n\timport { lockService, blueToothManager } from '@/utils/index.js';\n\timport { mapState, mapMutations, mapActions } from 'vuex';\n\t\n\texport default {\n\t\tcomponents: {\n\t\t\tcustomTabBar\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\ttitle: '首页',\n\t\t\t\tisNavigating: false, // 防止重复导航\n\t\t\t\tbluetoothInitialized: false,\n\t\t\t\tbluetoothInitializing: false\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\t...mapState({\n\t\t\t\tisLoggedIn: state => state.isLoggedIn,\n\t\t\t\tuserInfo: state => state.userInfo\n\t\t\t})\n\t\t},\n\t\tonLoad() {\n\t\t\tconsole.log('首页加载');\n\t\t\t// 检查登录状态\n\t\t\tthis.checkLoginStatus();\n\t\t\t\n\t\t\t// 在页面加载时初始化蓝牙环境\n\t\t\tthis.initBluetooth();\n\t\t},\n\t\tonShow() {\n\t\t\t// 重置导航状态\n\t\t\tthis.isNavigating = false;\n\n\t\t\t// 确保TabBar正确显示当前页面\n\t\t\tthis.updateTabBar();\n\n\t\t\t// 检查登录状态\n\t\t\tthis.checkLoginStatus();\n\n\t\t\t// 移除自动弹出登录框的逻辑，让用户先体验功能\n\t\t\t// 符合微信小程序审核规范：用户应先浏览体验功能，再选择是否登录\n\n\t\t\t// 检查是否有存储的扫码参数\n\t\t\tthis.checkLastScanParams();\n\n\t\t\t// 断开可能存在的蓝牙连接，避免在首页仍然尝试连接设备\n\t\t\tthis.disconnectBluetooth();\n\t\t},\n\t\tonReady() {\n\t\t\t// 页面渲染完成后，再次更新TabBar\n\t\t\tsetTimeout(() => {\n\t\t\t\tthis.updateTabBar();\n\t\t\t}, 100);\n\t\t},\n\t\tmethods: {\n\t\t\t...mapActions([\n\t\t\t\t'checkLoginStatus',\n\t\t\t\t'showLoginModal',\n\t\t\t\t'hideLoginModal'\n\t\t\t]),\n\t\t\t\n\t\t\t// 更新TabBar状态\n\t\t\tupdateTabBar() {\n\t\t\t\tif (this.$refs.tabBar) {\n\t\t\t\t\ttry {\n\t\t\t\t\t\t// 如果有updateCurrentPath方法，则调用\n\t\t\t\t\t\tif (typeof this.$refs.tabBar.updateCurrentPath === 'function') {\n\t\t\t\t\t\t\tthis.$refs.tabBar.updateCurrentPath();\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 如果有startPathCheck方法，则调用\n\t\t\t\t\t\tif (typeof this.$refs.tabBar.startPathCheck === 'function') {\n\t\t\t\t\t\t\tthis.$refs.tabBar.startPathCheck();\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 强制重新渲染TabBar\n\t\t\t\t\t\tif (typeof this.$refs.tabBar.$forceUpdate === 'function') {\n\t\t\t\t\t\t\tthis.$refs.tabBar.$forceUpdate();\n\t\t\t\t\t\t}\n\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\tconsole.error('更新TabBar状态失败:', error);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t\tnavigateTo(url) {\n\t\t\t\t// 防止重复导航\n\t\t\t\tif (this.isNavigating) return;\n\t\t\t\t\n\t\t\t\tthis.isNavigating = true;\n\t\t\t\t\n\t\t\t\t// 使用setTimeout确保导航完成后才重置状态\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl: url,\n\t\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\t\tconsole.log('导航成功:', url);\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\tconsole.error('导航失败:', err);\n\t\t\t\t\t\t\tthis.isNavigating = false;\n\t\t\t\t\t\t},\n\t\t\t\t\t\tcomplete: () => {\n\t\t\t\t\t\t\t// 导航完成后延迟重置状态，避免快速点击\n\t\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\t\tthis.isNavigating = false;\n\t\t\t\t\t\t\t}, 500);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}, 50);\n\t\t\t},\n\t\t\t// 导航到招商页面\n\t\t\tnavigateToZhaoshang() {\n\t\t\t\t// 防止重复导航\n\t\t\t\tif (this.isNavigating) return;\n\t\t\t\t\n\t\t\t\tthis.isNavigating = true;\n\t\t\t\t\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl: '/packageA/pages/zhaoshang/index',\n\t\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\t\tconsole.log('导航到招商页面成功');\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\tconsole.error('导航到招商页面失败:', err);\n\t\t\t\t\t\t\tthis.isNavigating = false;\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 如果导航失败，尝试使用switchTab\n\t\t\t\t\t\t\tif (err.errMsg && err.errMsg.includes('routeDone')) {\n\t\t\t\t\t\t\t\tconsole.log('尝试使用switchTab导航');\n\t\t\t\t\t\t\t\tuni.switchTab({\n\t\t\t\t\t\t\t\t\turl: '/pages/index/index',\n\t\t\t\t\t\t\t\t\tcomplete: () => {\n\t\t\t\t\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\t\t\t\t\t\turl: '/packageA/pages/zhaoshang/index'\n\t\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t\t}, 300);\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t},\n\t\t\t\t\t\tcomplete: () => {\n\t\t\t\t\t\t\t// 导航完成后延迟重置状态\n\t\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\t\tthis.isNavigating = false;\n\t\t\t\t\t\t\t}, 500);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}, 50);\n\t\t\t},\n\t\t\t\n\t\t\t// 自动显示登录框\n\t\t\tautoShowLogin() {\n\t\t\t\t// 如果未登录，自动弹出登录框\n\t\t\t\tif (!this.isLoggedIn) {\n\t\t\t\t\tthis.showLoginModal();\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 扫描二维码\n\t\t\tscanQRCode() {\n\t\t\t\t// 防止重复操作\n\t\t\t\tif (this.isNavigating) return;\n\n\t\t\t\t// 检查是否已登录\n\t\t\t\tif (!this.isLoggedIn) {\n\t\t\t\t\t// 未登录，先显示功能说明，让用户了解价值后再选择是否登录\n\t\t\t\t\tthis.showFunctionIntroduction();\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\t// 已登录，直接扫码\n\t\t\t\tthis.startScan();\n\t\t\t},\n\n\t\t\t// 显示功能介绍，符合微信审核规范\n\t\t\tshowFunctionIntroduction() {\n\t\t\t\t// 直接显示登录框，但保留用户选择权\n\t\t\t\tthis.showLoginModal();\n\t\t\t},\n\t\t\t\n\t\t\t// 开始扫码\n\t\t\tstartScan() {\n\t\t\t\ttry {\n\t\t\t\t\t// 获取系统信息，检查平台类型\n\t\t\t\t\tconst systemInfo = uni.getSystemInfoSync();\n\t\t\t\t\tconst platform = systemInfo.platform;\n\t\t\t\t\t\n\t\t\t\t\t// 判断是否是鸿蒙系统\n\t\t\t\t\tconst isHarmonyOS = platform === 'ohos' || (systemInfo.system && systemInfo.system.toLowerCase().includes('harmony'));\n\t\t\t\t\t\n\t\t\t\t\tconsole.log('系统平台:', platform, '是否鸿蒙系统:', isHarmonyOS);\n\t\t\t\t\t\n\t\t\t\t\t// 使用QRScanner工具扫码，不在这里显示loading，而是通过参数传递给QRScanner\n\t\t\t\t\tQRScanner.scan({\n\t\t\t\t\t\tonlyFromCamera: true,\n\t\t\t\t\t\tshowLoading: true, // 通过参数控制是否显示loading\n\t\t\t\t\t\tsuccess: (result) => {\n\t\t\t\t\t\t\tconsole.log('扫码成功:', result);\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 处理扫码结果\n\t\t\t\t\t\t\tQRScanner.handleScanResult(result)\n\t\t\t\t\t\t\t\t.then(res => {\n\t\t\t\t\t\t\t\t\tconsole.log('处理扫码结果成功:', res);\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\t.catch(err => {\n\t\t\t\t\t\t\t\t\tconsole.error('处理扫码结果失败:', err);\n\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t// 显示错误提示\n\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\ttitle: '无法识别的二维码',\n\t\t\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\tconsole.log('扫码失败:', err);\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 用户取消扫码，不显示错误提示\n\t\t\t\t\t\t\tif (err.errMsg && err.errMsg.includes('cancel')) {\n\t\t\t\t\t\t\t\tconsole.log('用户取消了扫码');\n\t\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 对于鸿蒙系统的特殊处理\n\t\t\t\t\t\t\tif (isHarmonyOS && err.errMsg === 'scanCode:fail') {\n\t\t\t\t\t\t\t\tconsole.log('鸿蒙系统扫码失败，可能是权限问题，尝试使用替代方案');\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t// 提示用户\n\t\t\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\t\t\ttitle: '提示',\n\t\t\t\t\t\t\t\t\tcontent: '扫码失败，请确保已授予相机权限',\n\t\t\t\t\t\t\t\t\tconfirmText: '重试',\n\t\t\t\t\t\t\t\t\tcancelText: '取消',\n\t\t\t\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\t\t\t\t\t// 用户点击重试，延迟一下再次尝试扫码\n\t\t\t\t\t\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\t\t\t\t\t\t// 第二次尝试时，使用不同的配置\n\t\t\t\t\t\t\t\t\t\t\t\tuni.scanCode({\n\t\t\t\t\t\t\t\t\t\t\t\t\tonlyFromCamera: true,\n\t\t\t\t\t\t\t\t\t\t\t\t\tscanType: ['qrCode'],\n\t\t\t\t\t\t\t\t\t\t\t\t\tsuccess: (scanRes) => {\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tconsole.log('重试扫码成功:', scanRes);\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tconst result = QRScanner.parseQRResult(scanRes.result);\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tQRScanner.handleScanResult(result);\n\t\t\t\t\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t\t\t\t\tfail: (scanErr) => {\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tconsole.error('重试扫码失败:', scanErr);\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttitle: '扫码失败，请稍后再试',\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t\t\t}, 500);\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 其他错误才显示提示\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '扫码失败，请重试',\n\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t} catch (error) {\n\t\t\t\t\t// 捕获可能的异常\n\t\t\t\t\tconsole.error('扫码过程发生异常:', error);\n\t\t\t\t\tuni.hideLoading(); // 确保loading被关闭\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '扫码功能异常，请重试',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 新增：检查上次扫码参数\n\t\t\tcheckLastScanParams() {\n\t\t\t\t// 检查登录状态\n\t\t\t\tconst token = uni.getStorageSync('token');\n\t\t\t\tif (!token) {\n\t\t\t\t\treturn; // 未登录不处理\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 获取上次扫码参数\n\t\t\t\tconst lastScanParams = uni.getStorageSync('lastScanParams');\n\t\t\t\tif (lastScanParams) {\n\t\t\t\t\t// 检查时间戳，如果超过10分钟则不处理\n\t\t\t\t\tconst now = Date.now();\n\t\t\t\t\tconst scanTime = lastScanParams.timestamp || 0;\n\t\t\t\t\t\n\t\t\t\t\tif (now - scanTime < 10 * 60 * 1000) { // 10分钟内的扫码记录\n\t\t\t\t\t\tconsole.log('检测到上次扫码参数，准备跳转:', lastScanParams);\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 清除扫码参数，避免重复跳转\n\t\t\t\t\t\tuni.removeStorageSync('lastScanParams');\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 构建跳转URL\n\t\t\t\t\t\tlet url = '/pages/scan/device?';\n\t\t\t\t\t\tif (lastScanParams.mac) {\n\t\t\t\t\t\t\turl += `mac=${lastScanParams.mac}&`;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (lastScanParams.qrCode) {\n\t\t\t\t\t\t\turl += `qrCode=${encodeURIComponent(lastScanParams.qrCode)}&`;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 去掉最后一个&\n\t\t\t\t\t\tif (url.endsWith('&')) {\n\t\t\t\t\t\t\turl = url.slice(0, -1);\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 延迟跳转，确保页面已完全加载\n\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\t\turl: url,\n\t\t\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\t\t\tconsole.error('跳转到设备页面失败:', err);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}, 500);\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// 超时的扫码记录，清除\n\t\t\t\t\t\tuni.removeStorageSync('lastScanParams');\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t/**\n\t\t\t * 断开蓝牙连接\n\t\t\t */\n\t\t\tdisconnectBluetooth() {\n\t\t\t\tconsole.log('尝试断开蓝牙连接 - disconnectDevice方法');\n\t\t\t\t\n\t\t\t\t// 使用blueToothManager断开连接\n\t\t\t\tif (blueToothManager && typeof blueToothManager.disconnect === 'function') {\n\t\t\t\t\ttry {\n\t\t\t\t\t\tblueToothManager.disconnect().catch(err => {\n\t\t\t\t\t\t\tconsole.warn('断开蓝牙连接失败:', err);\n\t\t\t\t\t\t});\n\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\tconsole.error('调用blueToothManager.disconnect出错:', e);\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tconsole.log('blueToothManager不可用或没有disconnect方法');\n\t\t\t\t\t\n\t\t\t\t\t// 如果两种方法都不可用，尝试使用uni API直接断开\n\t\t\t\t\ttry {\n\t\t\t\t\t\t// 获取已连接的蓝牙设备\n\t\t\t\t\t\tuni.getConnectedBluetoothDevices({\n\t\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\t\tif (res.devices && res.devices.length > 0) {\n\t\t\t\t\t\t\t\t\t// 断开所有连接的设备\n\t\t\t\t\t\t\t\t\tres.devices.forEach(device => {\n\t\t\t\t\t\t\t\t\t\tuni.closeBLEConnection({\n\t\t\t\t\t\t\t\t\t\t\tdeviceId: device.deviceId,\n\t\t\t\t\t\t\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\t\t\t\t\t\t\tconsole.log('成功断开设备连接:', device.deviceId);\n\t\t\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\t\t\t\t\t\tconsole.warn('断开设备连接失败:', err);\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\t\tconsole.warn('获取已连接的蓝牙设备失败:', err);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\tconsole.error('使用uni API断开蓝牙连接失败:', e);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t/**\n\t\t\t * 初始化蓝牙环境\n\t\t\t */\n\t\t\tinitBluetooth() {\n\t\t\t\tconsole.log('初始化蓝牙环境');\n\t\t\t\t\n\t\t\t\t// 检查当前平台是否为Windows开发环境\n\t\t\t\ttry {\n\t\t\t\t\tconst systemInfo = wx.getAppBaseInfo ? wx.getAppBaseInfo() : uni.getSystemInfoSync();\n\t\t\t\t\tconsole.log('首页 - 系统信息:', systemInfo);\n\t\t\t\t\t\n\t\t\t\t\tconst isDevTools = systemInfo.platform === 'devtools';\n\t\t\t\t\tconst isMac = systemInfo.platform === 'mac';\n\t\t\t\t\t// 修复system可能为undefined的问题\n\t\t\t\t\tconst isWindows = systemInfo.platform === 'windows' || \n\t\t\t\t\t                 (systemInfo.system && typeof systemInfo.system === 'string' && \n\t\t\t\t\t                  systemInfo.system.toLowerCase().includes('windows'));\n\t\t\t\t\t\n\t\t\t\t\t// 在非Mac开发环境下自动切换到模拟模式\n\t\t\t\t\tif (isDevTools && !isMac) {\n\t\t\t\t\t\tconsole.log('首页 - 非Mac开发环境自动切换到模拟模式');\n\t\t\t\t\t\tthis.isBluetoothAvailable = true;\n\t\t\t\t\t\tthis.isSimulatedMode = true;\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 设置全局模拟模式标志\n\t\t\t\t\t\tif (typeof global !== 'undefined') {\n\t\t\t\t\t\t\tglobal.isSimulatedMode = true;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 显示提示\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '当前平台不支持蓝牙调试，已切换到模拟模式',\n\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\tduration: 3000\n\t\t\t\t\t\t});\n\t\t\t\t\t\t\n\t\t\t\t\t\treturn Promise.resolve({\n\t\t\t\t\t\t\tsuccess: true,\n\t\t\t\t\t\t\tmessage: '蓝牙环境初始化成功(模拟模式)',\n\t\t\t\t\t\t\tsimulated: true\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t} catch (e) {\n\t\t\t\t\tconsole.error('获取系统信息失败:', e);\n\t\t\t\t\t// 出错时也切换到模拟模式\n\t\t\t\t\tthis.isBluetoothAvailable = true;\n\t\t\t\t\tthis.isSimulatedMode = true;\n\t\t\t\t\t\n\t\t\t\t\treturn Promise.resolve({\n\t\t\t\t\t\tsuccess: true,\n\t\t\t\t\t\tmessage: '蓝牙环境初始化成功(模拟模式-错误恢复)',\n\t\t\t\t\t\tsimulated: true\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 调用锁服务初始化\n\t\t\t\treturn lockService.init()\n\t\t\t\t\t.then(res => {\n\t\t\t\t\t\tconsole.log('蓝牙初始化成功:', res);\n\t\t\t\t\t\tthis.isBluetoothAvailable = true;\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 如果是模拟模式，设置标志\n\t\t\t\t\t\tif (res.simulated) {\n\t\t\t\t\t\t\tthis.isSimulatedMode = true;\n\t\t\t\t\t\t\tconsole.log('使用模拟模式');\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 设置全局模拟模式标志\n\t\t\t\t\t\t\tif (typeof global !== 'undefined') {\n\t\t\t\t\t\t\t\tglobal.isSimulatedMode = true;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\treturn res;\n\t\t\t\t\t})\n\t\t\t\t\t.catch(err => {\n\t\t\t\t\t\tconsole.error('蓝牙不可用:', err);\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 即使蓝牙不可用，也设置为可用并使用模拟模式\n\t\t\t\t\t\tthis.isBluetoothAvailable = true;\n\t\t\t\t\t\tthis.isSimulatedMode = true;\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 设置全局模拟模式标志\n\t\t\t\t\t\tif (typeof global !== 'undefined') {\n\t\t\t\t\t\t\tglobal.isSimulatedMode = true;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\tconsole.log('切换到模拟模式');\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 不显示错误提示，而是显示已切换到模拟模式\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '已切换到模拟模式',\n\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\tduration: 3000\n\t\t\t\t\t\t});\n\t\t\t\t\t\t\n\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\tsuccess: true,\n\t\t\t\t\t\t\tmessage: '蓝牙环境初始化成功(模拟模式-错误恢复)',\n\t\t\t\t\t\t\tsimulated: true\n\t\t\t\t\t\t};\n\t\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t/**\n\t\t\t * 初始化BLELockController\n\t\t\t * @returns {Promise} 初始化结果\n\t\t\t * @private\n\t\t\t */\n\t\t\t_initBLEController() {\n\t\t\t\treturn new Promise((resolve, reject) => {\n\t\t\t\t\ttry {\n\t\t\t\t\t\t// 使用已导入的blueToothManager\n\t\t\t\t\t\tif (!blueToothManager) {\n\t\t\t\t\t\t\tconsole.error('blueToothManager不可用');\n\t\t\t\t\t\t\treturn reject(new Error('blueToothManager不可用'));\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 初始化blueToothManager\n\t\t\t\t\t\tif (typeof blueToothManager.init === 'function' && !blueToothManager.isInitialized) {\n\t\t\t\t\t\t\tblueToothManager.init();\n\t\t\t\t\t\t\tblueToothManager.isInitialized = true;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 保存到全局对象\n\t\t\t\t\t\tglobal.blueToothManager = blueToothManager;\n\t\t\t\t\t\t\n\t\t\t\t\t\tconsole.log('blueToothManager初始化成功');\n\t\t\t\t\t\tresolve(blueToothManager);\n\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\tconsole.error('初始化blueToothManager失败:', e);\n\t\t\t\t\t\treject(new Error('初始化blueToothManager失败: ' + e.message));\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t/**\n\t\t\t * 跳转到智能锁页面\n\t\t\t */\n\t\t\tnavigateToLock() {\n\t\t\t\t// 防止重复点击\n\t\t\t\tif (this.isNavigating) return;\n\t\t\t\t\n\t\t\t\tthis.isNavigating = true;\n\t\t\t\t\n\t\t\t\t// 跳转到扫码页面（原来是锁页面，但锁页面不存在，所以改为扫码页面）\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/scan/scan',\n\t\t\t\t\tcomplete: () => {\n\t\t\t\t\t\t// 重置导航状态\n\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\tthis.isNavigating = false;\n\t\t\t\t\t\t}, 500);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t/**\n\t\t\t * 检查蓝牙是否可用\n\t\t\t * @returns {Promise} 蓝牙可用状态\n\t\t\t * @private\n\t\t\t */\n\t\t\t_checkBluetoothAvailable() {\n\t\t\t\treturn new Promise((resolve, reject) => {\n\t\t\t\t\t// 初始化蓝牙适配器\n\t\t\t\t\tuni.openBluetoothAdapter({\n\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\tconsole.log('蓝牙适配器初始化成功:', res);\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 获取蓝牙适配器状态\n\t\t\t\t\t\t\tuni.getBluetoothAdapterState({\n\t\t\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\t\t\tconsole.log('蓝牙适配器状态:', res);\n\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\tif (!res.available) {\n\t\t\t\t\t\t\t\t\t\t// 提示用户开启蓝牙\n\t\t\t\t\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\t\t\t\t\ttitle: '提示',\n\t\t\t\t\t\t\t\t\t\t\tcontent: '请开启蓝牙后重试',\n\t\t\t\t\t\t\t\t\t\t\tshowCancel: false,\n\t\t\t\t\t\t\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\t\t\t\t\t\t\treject(new Error('蓝牙不可用'));\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\tresolve();\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\t\t\tconsole.error('获取蓝牙适配器状态失败:', err);\n\t\t\t\t\t\t\t\t\treject(err);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\tconsole.error('蓝牙适配器初始化失败:', err);\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 检查错误类型\n\t\t\t\t\t\t\tif (err.errCode === 10001) {\n\t\t\t\t\t\t\t\t// 蓝牙未开启\n\t\t\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\t\t\ttitle: '提示',\n\t\t\t\t\t\t\t\t\tcontent: '请开启蓝牙后重试',\n\t\t\t\t\t\t\t\t\tshowCancel: false,\n\t\t\t\t\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\t\t\t\t\treject(new Error('蓝牙未开启'));\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t} else if (err.errCode === 10002) {\n\t\t\t\t\t\t\t\t// 没有蓝牙权限\n\t\t\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\t\t\ttitle: '提示',\n\t\t\t\t\t\t\t\t\tcontent: '请授予蓝牙权限后重试',\n\t\t\t\t\t\t\t\t\tshowCancel: false,\n\t\t\t\t\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\t\t\t\t\t// 打开应用设置页面\n\t\t\t\t\t\t\t\t\t\tuni.openSetting({\n\t\t\t\t\t\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\t\t\t\t\t\tconsole.log('打开设置页面成功:', res);\n\t\t\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\t\t\t\t\t\tconsole.error('打开设置页面失败:', err);\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\treject(new Error('没有蓝牙权限'));\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\treject(err);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t};\n</script>\n\n<style>\n\t/* CSS变量定义 */\n\tpage {\n\t\t--primary-light: #A875FF;\n\t\t--neon-pink: #ff36f9;\n\t\theight: 100%;\n\t\toverflow: hidden;\n\t}\n\t\n\t/* 页面基础样式 */\n\t.page-index {\n\t\tpadding-top: 100rpx; /* 减少顶部间距 */\n\t\tpadding-bottom: calc(120rpx + env(safe-area-inset-bottom)); /* 增加底部padding，为TabBar留出空间 */\n\t\tcolor: #ffffff;\n\t\theight: 100vh;\n\t\tmin-height: 100vh;\n\t\tbox-sizing: border-box;\n\t\tposition: relative;\n\t\toverflow: hidden; /* 防止页面滚动 */\n\t\ttouch-action: none; /* 禁止触摸操作 */\n\t}\n\t\n\t/* 页面背景样式 */\n\t.page-background {\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tz-index: 0;\n\t}\n\t\n\t/* 背景图片样式 */\n\t.background-image {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tz-index: 0;\n\t\tobject-fit: cover; /* 确保图片覆盖整个容器 */\n\t}\n\t\n\t.status-bar {\n\t\twidth: 100%;\n\t\tbackground: transparent; /* 使顶部安全区域透明，与页面背景一致 */\n\t\tbackdrop-filter: none;\n\t\t-webkit-backdrop-filter: none;\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tz-index: 100;\n\t}\n\t\n\t.content {\n\t\tpadding: 20rpx 30rpx;\n\t\tposition: relative;\n\t\tz-index: 2;\n\t\theight: calc(100vh - 100rpx - 120rpx - env(safe-area-inset-bottom)); /* 修改高度计算，为TabBar留出空间 */\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\toverflow: hidden; /* 防止内容滚动 */\n\t\ttouch-action: none; /* 禁止触摸操作 */\n\t}\n\t\n\t/* 欢迎区域 */\n\t.welcome-section {\n\t\tmargin-bottom: 0; /* 减少底部间距 */\n\t\tmargin-top: 0; /* 减少顶部间距 */\n\t}\n\t\n\t/* 顶部banner样式 */\n\t.banner-container {\n\t\tposition: relative;\n\t\tz-index: 2;\n\t\tmargin-bottom: 10rpx; /* 减小底部间距 */\n\t\tmargin-top: 30rpx; /* 增加顶部间距，让banner往下移 */\n\t\twidth: 100%;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\toverflow: visible;\n\t\tpadding: 0;\n\t}\n\t\n\t.banner-image {\n\t\twidth: 100%;\n\t\tmax-width: 1000rpx;\n\t\theight: 180rpx;\n\t\tobject-fit: contain;\n\t}\n\t\n\t.welcome-text {\n\t\tmargin-left: 10rpx;\n\t}\n\t\n\t.user-info {\n\t\tposition: relative;\n\t\tz-index: 2;\n\t\tmargin-bottom: 20rpx;\n\t}\n\t\n\t.user-summary {\n\t\tmargin-top: 30rpx;\n\t\tpadding: 30rpx;\n\t\tbackground-color: rgba(255, 255, 255, 0.08);\n\t\tborder-radius: 20rpx;\n\t\tbackdrop-filter: blur(10px);\n\t\t-webkit-backdrop-filter: blur(10px);\n\t\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);\n\t}\n\t\n\t/* 快捷功能区域 */\n\t.quick-actions {\n\t\tmargin-top: 530rpx; /* 从500rpx增加到530rpx，使整体下移 */\n\t\tmargin-bottom: 40rpx;\n\t\tflex: 1;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tjustify-content: flex-start; /* 从顶部开始排列 */\n\t}\n\t\n\t/* 标题容器 */\n\t.scan-title-container {\n\t\tmargin-bottom: 40rpx;\n\t\ttext-align: center;\n\t\twidth: 100%;\n\t\tposition: relative;\n\t\ttop: 50rpx; /* 从30rpx增加到50rpx，使标题进一步向下移动 */\n\t}\n\t\n\t/* 增加标题之间的间距 */\n\t.neon-title.mt-xs {\n\t\tmargin-top: 15rpx; /* 增加两行标题之间的间距 */\n\t}\n\t\n\t/* 标题下方的爱心图标容器 */\n\t.heart-icon-container {\n\t\tmargin-top: 20rpx;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t}\n\t\n\t/* 标题下方的爱心图标 */\n\t.title-heart-icon {\n\t\tfont-size: 80rpx;\n\t\tcolor: #d4a9ff;\n\t\ttext-shadow: \n\t\t\t0 0 15rpx rgba(212, 169, 255, 0.7),\n\t\t\t0 0 30rpx rgba(212, 169, 255, 0.5);\n\t\tanimation: title-heart-pulse 2s ease-in-out infinite;\n\t}\n\t\n\t@keyframes title-heart-pulse {\n\t\t0%, 100% {\n\t\t\ttransform: scale(1);\n\t\t\topacity: 0.9;\n\t\t}\n\t\t50% {\n\t\t\ttransform: scale(1.1);\n\t\t\topacity: 1;\n\t\t}\n\t}\n\t\n\t/* 霓虹灯标题效果增强 */\n\t.neon-title {\n\t\tfont-size: 50rpx; /* 增大字体大小 */\n\t\tline-height: 1.3;\n\t\tfont-weight: 500;\n\t\ttext-align: center;\n\t\tcolor: #fff;\n\t\tfont-family: \"YouYuan\", \"幼圆\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"微软雅黑\", sans-serif; /* 添加幼圆字体 */\n\t\ttext-shadow: \n\t\t\t0 0 5rpx rgba(255, 255, 255, 0.6),\n\t\t\t0 0 10rpx rgba(255, 255, 255, 0.4), \n\t\t\t0 0 15rpx rgba(168, 117, 255, 0.5),\n\t\t\t0 0 25rpx rgba(168, 117, 255, 0.4);\n\t\tletter-spacing: 8rpx; /* 增加字间距 */\n\t\tanimation: soft-flicker 4s infinite alternate;\n\t\ttransform: scale(1, 0.95); /* 稍微压扁字体，模拟圆体效果 */\n\t}\n\t\n\t/* 添加霓虹灯闪烁动画 */\n\t@keyframes soft-flicker {\n\t\t0%, 100% {\n\t\t\ttext-shadow: \n\t\t\t\t0 0 5rpx rgba(255, 255, 255, 0.6),\n\t\t\t\t0 0 10rpx rgba(255, 255, 255, 0.4), \n\t\t\t\t0 0 15rpx rgba(168, 117, 255, 0.5),\n\t\t\t\t0 0 25rpx rgba(168, 117, 255, 0.4);\n\t\t}\n\t\t50% {\n\t\t\ttext-shadow: \n\t\t\t\t0 0 8rpx rgba(255, 255, 255, 0.7),\n\t\t\t\t0 0 15rpx rgba(255, 255, 255, 0.5), \n\t\t\t\t0 0 20rpx rgba(168, 117, 255, 0.6),\n\t\t\t\t0 0 30rpx rgba(168, 117, 255, 0.5);\n\t\t}\n\t}\n\t\n\t/* 扫码按钮容器 */\n\t.action-container {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tmargin-top: -10rpx; /* 从10rpx改为-10rpx，使按钮往上移 */\n\t\tmargin-bottom: 10rpx; /* 从20rpx改为10rpx，减少底部间距 */\n\t\twidth: 100%;\n\t\tposition: relative; /* 添加相对定位 */\n\t\tleft: 0; /* 确保不偏移 */\n\t}\n\t\n\t/* 主扫码按钮样式 */\n\t.scan-button-wrapper {\n\t\tposition: relative;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\twidth: 320rpx; /* 从300rpx增加到320rpx */\n\t\theight: 320rpx; /* 从300rpx增加到320rpx */\n\t\tmargin-top: 0; /* 移除顶部间距 */\n\t\tpadding: 0; /* 移除内边距 */\n\t\ttransition: transform 0.3s, box-shadow 0.3s;\n\t\tmargin: 0 auto; /* 确保按钮居中 */\n\t\tleft: 0; /* 确保不偏移 */\n\t\tright: 0; /* 确保不偏移 */\n\t}\n\t\n\t.scan-button-wrapper:active {\n\t\ttransform: scale(0.96);\n\t}\n\t\n\t/* 扫码按钮内层 */\n\t.scan-button-inner {\n\t\tposition: relative;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\twidth: 220rpx;\n\t\theight: 220rpx;\n\t\tborder-radius: 50%;\n\t\tbackground: rgba(168, 117, 255, 0.12);\n\t\tbox-shadow: \n\t\t\t0 12rpx 36rpx rgba(0, 0, 0, 0.35),\n\t\t\t0 20rpx 50rpx rgba(0, 0, 0, 0.2),\n\t\t\tinset 0 2rpx 10rpx rgba(255, 255, 255, 0.5),\n\t\t\tinset 0 -5rpx 15rpx rgba(0, 0, 0, 0.3);\n\t\tz-index: 2;\n\t\toverflow: visible;\n\t\tposition: relative;\n\t\tanimation: circle-breathing 4s cubic-bezier(0.4, 0, 0.6, 1) infinite; /* 改为4秒，使用更平滑的缓动函数 */\n\t}\n\t\n\t/* 修改背景圆圈呼吸灯效果，显著增强效果并与爱心同步 */\n\t@keyframes circle-breathing {\n\t\t0% {\n\t\t\tbackground: rgba(168, 117, 255, 0.08); /* 降低初始亮度 */\n\t\t\tbox-shadow: \n\t\t\t\t0 12rpx 36rpx rgba(0, 0, 0, 0.35),\n\t\t\t\t0 20rpx 50rpx rgba(0, 0, 0, 0.2),\n\t\t\t\tinset 0 2rpx 10rpx rgba(255, 255, 255, 0.4),\n\t\t\t\tinset 0 -5rpx 15rpx rgba(0, 0, 0, 0.3);\n\t\t}\n\t\t25% { /* 放大过程占25% */\n\t\t\tbackground: rgba(168, 117, 255, 0.45); /* 显著增强亮度 */\n\t\t\tbox-shadow: \n\t\t\t\t0 12rpx 36rpx rgba(168, 117, 255, 0.4), /* 添加紫色外发光 */\n\t\t\t\t0 20rpx 50rpx rgba(168, 117, 255, 0.25),\n\t\t\t\tinset 0 2rpx 20rpx rgba(255, 255, 255, 0.9), /* 增强内发光 */\n\t\t\t\tinset 0 -5rpx 15rpx rgba(0, 0, 0, 0.2);\n\t\t}\n\t\t50% { /* 保持最大状态占25% */\n\t\t\tbackground: rgba(168, 117, 255, 0.45); /* 保持增强的亮度 */\n\t\t\tbox-shadow: \n\t\t\t\t0 12rpx 36rpx rgba(168, 117, 255, 0.4), /* 添加紫色外发光 */\n\t\t\t\t0 20rpx 50rpx rgba(168, 117, 255, 0.25),\n\t\t\t\tinset 0 2rpx 20rpx rgba(255, 255, 255, 0.9), /* 增强内发光 */\n\t\t\t\tinset 0 -5rpx 15rpx rgba(0, 0, 0, 0.2);\n\t\t}\n\t\t75% { /* 缩小过程占25% */\n\t\t\tbackground: rgba(168, 117, 255, 0.08); /* 降低初始亮度 */\n\t\t\tbox-shadow: \n\t\t\t\t0 12rpx 36rpx rgba(0, 0, 0, 0.35),\n\t\t\t\t0 20rpx 50rpx rgba(0, 0, 0, 0.2),\n\t\t\t\tinset 0 2rpx 10rpx rgba(255, 255, 255, 0.4),\n\t\t\t\tinset 0 -5rpx 15rpx rgba(0, 0, 0, 0.3);\n\t\t}\n\t\t100% {\n\t\t\tbackground: rgba(168, 117, 255, 0.08); /* 降低初始亮度 */\n\t\t\tbox-shadow: \n\t\t\t\t0 12rpx 36rpx rgba(0, 0, 0, 0.35),\n\t\t\t\t0 20rpx 50rpx rgba(0, 0, 0, 0.2),\n\t\t\t\tinset 0 2rpx 10rpx rgba(255, 255, 255, 0.4),\n\t\t\t\tinset 0 -5rpx 15rpx rgba(0, 0, 0, 0.3);\n\t\t}\n\t}\n\t\n\t/* 图标样式 */\n\t.scan-icon-container {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\twidth: 160rpx; /* 从140rpx增加到160rpx */\n\t\theight: 160rpx; /* 从140rpx增加到160rpx */\n\t\tz-index: 3;\n\t\tposition: relative; /* 添加相对定位 */\n\t\tmargin: 0 auto; /* 确保容器居中 */\n\t\tleft: 0; /* 确保不偏移 */\n\t\tright: 0; /* 确保不偏移 */\n\t}\n\t\n\t.scan-icon-container .material-icons.heart-icon {\n\t\tfont-size: 160rpx;\n\t\tcolor: #c49aef; /* 使用更深的紫色 */\n\t\tbackground: none;\n\t\t-webkit-background-clip: initial;\n\t\tbackground-clip: initial;\n\t\ttext-shadow: none;\n\t\tanimation: heart-natural-grow 4s cubic-bezier(0.4, 0, 0.6, 1) infinite; /* 改为4秒，使用更平滑的缓动函数 */\n\t\ttransform-style: preserve-3d;\n\t\tfilter: none;\n\t\tposition: absolute;\n\t\ttop: 50%;\n\t\tleft: 50%;\n\t\ttransform: translate(-50%, -50%); /* 保持居中 */\n\t\ttransform-origin: center center;\n\t\twidth: 100%; /* 确保图标占满容器宽度 */\n\t\ttext-align: center; /* 确保文本居中 */\n\t\tdisplay: flex; /* 添加flex布局 */\n\t\tjustify-content: center; /* 水平居中 */\n\t\talign-items: center; /* 垂直居中 */\n\t\theight: 100%; /* 确保高度占满容器 */\n\t\tbox-sizing: border-box; /* 确保盒模型计算正确 */\n\t}\n\t\n\t/* 重新设计爱心放大动画 - 自然放大效果，放大和缩小速度一致且更慢 */\n\t@keyframes heart-natural-grow {\n\t\t0% {\n\t\t\ttransform: translate(-50%, -50%) scale(1);\n\t\t\tcolor: #c49aef;\n\t\t}\n\t\t25% { /* 放大过程占25% */\n\t\t\ttransform: translate(-50%, -50%) scale(1.35);\n\t\t\tcolor: #e5bcf7;\n\t\t}\n\t\t50% { /* 保持最大状态占25% */\n\t\t\ttransform: translate(-50%, -50%) scale(1.35);\n\t\t\tcolor: #e5bcf7;\n\t\t}\n\t\t75% { /* 缩小过程占25% */\n\t\t\ttransform: translate(-50%, -50%) scale(1);\n\t\t\tcolor: #c49aef;\n\t\t}\n\t\t100% { /* 保持原始状态 */\n\t\t\ttransform: translate(-50%, -50%) scale(1);\n\t\t\tcolor: #c49aef;\n\t\t}\n\t}\n\t\n\t/* 文本容器 */\n\t.scan-text {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tmargin-top: 40rpx;\n\t}\n\t\n\t.scan-text .action-title {\n\t\tfont-weight: 600;\n\t\tfont-size: 36rpx;\n\t\tmargin-bottom: 10rpx;\n\t\tcolor: rgba(255, 255, 255, 0.9);\n\t\tletter-spacing: 2rpx;\n\t}\n\t\n\t.scan-text .text-tertiary {\n\t\tfont-size: 28rpx;\n\t\tcolor: rgba(255, 255, 255, 0.5);\n\t}\n\t\n\t/* Material Icons 字体 */\n\t@font-face {\n\t\tfont-family: 'Material Icons';\n\t\tfont-style: normal;\n\t\tfont-weight: 400;\n\t\tsrc: url(https://fonts.gstatic.com/s/materialicons/v139/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format('woff2');\n\t\tfont-display: block;\n\t}\n\n\t.material-icons {\n\t\tfont-family: 'Material Icons';\n\t\tfont-weight: normal;\n\t\tfont-style: normal;\n\t\tfont-size: 48rpx;\n\t\tline-height: 1;\n\t\tletter-spacing: normal;\n\t\ttext-transform: none;\n\t\tdisplay: inline-block;\n\t\twhite-space: nowrap;\n\t\tword-wrap: normal;\n\t\tdirection: ltr;\n\t\t-webkit-font-smoothing: antialiased;\n\t\t-moz-osx-font-smoothing: grayscale;\n\t\ttext-rendering: optimizeLegibility;\n\t}\n\t\n\t.material-icons.primary-light {\n\t\tcolor: var(--primary-light, #A875FF);\n\t}\n\t\n\t/* 状态标签 */\n\t.status {\n\t\tpadding: 8rpx 20rpx;\n\t\tborder-radius: 30rpx;\n\t\tfont-size: 24rpx;\n\t}\n\t\n\t.status.active {\n\t\tbackground-color: rgba(168, 117, 255, 0.2);\n\t\tcolor: var(--primary-light, #A875FF);\n\t}\n\t\n\t/* 辅助类 */\n\t.flex {\n\t\tdisplay: flex;\n\t}\n\t\n\t.flex-col {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t}\n\t\n\t.justify-between {\n\t\tjustify-content: space-between;\n\t}\n\t\n\t.items-center {\n\t\talign-items: center;\n\t}\n\t\n\t.text-center {\n\t\ttext-align: center;\n\t}\n\t\n\t.mt-xs {\n\t\tmargin-top: 8rpx;\n\t}\n\t\n\t.mt-sm {\n\t\tmargin-top: 16rpx;\n\t}\n\t\n\t.mt-md {\n\t\tmargin-top: 30rpx;\n\t}\n\t\n\t.mt-lg {\n\t\tmargin-top: 40rpx; /* 减小顶部大间距 */\n\t}\n\t\n\t.mb-sm {\n\t\tmargin-bottom: 16rpx;\n\t}\n\t\n\t.gap-md {\n\t\tgap: 24rpx;\n\t}\n\t\n\t.flex-1 {\n\t\tflex: 1;\n\t}\n\t\n\t/* 标题和文本样式 */\n\t.title-lg {\n\t\tfont-size: 46rpx;\n\t\tfont-weight: 600;\n\t\tcolor: #ffffff;\n\t\tmargin-bottom: 10rpx;\n\t}\n\t\n\t.title-md {\n\t\tfont-size: 38rpx;\n\t\tfont-weight: 600;\n\t\tcolor: #ffffff;\n\t}\n\t\n\t.title-sm {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: 500;\n\t\tcolor: #ffffff;\n\t\tmargin-bottom: 8rpx;\n\t}\n\t\n\t.text-secondary {\n\t\tcolor: rgba(255, 255, 255, 0.7);\n\t}\n\t\n\t.text-tertiary {\n\t\tcolor: rgba(255, 255, 255, 0.5);\n\t}\n\t\n\t.primary-light {\n\t\tcolor: var(--primary-light, #A875FF);\n\t}\n\t\n\t/* 底部空间 */\n\t.bottom-space {\n\t\theight: 20rpx;\n\t}\n\t\n\t/* 卡片样式 */\n\t.card {\n\t\tborder-radius: 20rpx;\n\t\tbackground-color: rgba(255, 255, 255, 0.08);\n\t\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);\n\t}\n\t\n\t/* 协议提示文字 */\n\t.agreement-tip {\n\t\tmargin-top: 20rpx; /* 从30rpx减少到20rpx，减少与按钮的间距 */\n\t\ttext-align: center;\n\t\tfont-size: 24rpx;\n\t\tcolor: rgba(255, 255, 255, 0.6);\n\t}\n</style>", "import mod from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754165306708\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}