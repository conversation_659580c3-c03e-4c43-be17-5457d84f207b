<template>
	<view class="login-modal" v-if="visible">
		<view class="login-modal-content">
			<view class="login-modal-header">
				<text class="login-modal-title">快速登录</text>
				<text class="material-icons close-icon" @tap="handleClose">close</text>
			</view>
			<view class="login-modal-body">
				<text class="material-icons login-icon">person</text>
				<text class="login-modal-desc">登录后可使用所有功能</text>
				<text class="auth-explanation">•需要验证手机号确保安全\n• 我们将严格保护您的隐私信息</text>
				<button class="auth-btn" open-type="getPhoneNumber" @getphonenumber="onGetPhoneNumber">授权登录</button>
			</view>
		</view>
	</view>
</template>

<script>
	import API from '@/static/js/api.js';
	import LoginManager from '@/static/js/login.js';
	
	export default {
		name: 'login-modal',
		props: {
			visible: {
				type: Boolean,
				default: false
			}
		},
		data() {
			return {
				loginCode: '', // 微信登录code
			}
		},
		created() {
			// 预先获取登录code，避免多次调用
			this.getLoginCode();
		},
		methods: {
			// 关闭弹窗
			handleClose() {
				this.$emit('close');
			},
			
			// 预先获取登录code
			getLoginCode() {
				uni.login({
					provider: 'weixin',
					success: (loginRes) => {
						this.loginCode = loginRes.code;
						console.log('预先获取登录code成功:', this.loginCode);
					},
					fail: (err) => {
						console.error('预先获取登录code失败:', err);
					}
				});
			},
			
			// 获取手机号回调
			onGetPhoneNumber(e) {
				if (e.detail.errMsg && e.detail.errMsg.includes('ok')) {
					// 获取手机号成功
					const phoneCode = e.detail.code;
					
					uni.showLoading({
						title: '登录中...',
						mask: true
					});
					
					// 处理登录流程
					this.processLoginWithCode(phoneCode);
				} else {
					// 用户取消了授权，显示提示
					uni.showToast({
						title: '需要授权手机号才能完成登录',
						icon: 'none'
					});
				}
			},
			
			// 处理登录码和手机号
			processLoginWithCode(phoneCode) {
				// 每次登录都重新获取code，避免使用过期code
				uni.login({
					provider: 'weixin',
					success: (loginRes) => {
						this.loginCode = loginRes.code;
						console.log('获取新的登录code成功:', this.loginCode);
						this.completeLogin(phoneCode);
					},
					fail: (err) => {
						uni.hideLoading();
						uni.showToast({
							title: '登录失败，请重试',
							icon: 'none'
						});
						console.error('微信登录失败:', err);
					}
				});
			},
			
			// 完成登录流程
			completeLogin(phoneCode) {
				// 调用后端登录接口
				API.user.login(this.loginCode, '', '', phoneCode)
					.then(res => {
						uni.hideLoading();
						this.handleClose(); // 关闭弹窗
						
						if (res && res.data) {
							// 保存登录信息到本地存储
							uni.setStorageSync('token', res.data.token);
							uni.setStorageSync('userId', res.data.userId);
							
							// 如果后端返回了用户信息，保存到本地
							if (res.data.userInfo) {
								uni.setStorageSync('userInfo', res.data.userInfo);
							} else {
								// 保存基本用户信息
								const basicUserInfo = {
									userId: res.data.userId
								};
								uni.setStorageSync('userInfo', basicUserInfo);
							}
						}
						
						uni.showToast({
							title: '登录成功',
							icon: 'success'
						});
						
						// 通知父组件登录成功
						this.$emit('login-success', res.data);
					})
					.catch(err => {
						uni.hideLoading();
						
						// 如果是code无效错误，清除登录状态
						if (err.code === 500 && err.message && err.message.includes('无效的code')) {
							console.log('检测到无效code错误，清除token');
							uni.removeStorageSync('token');
							this.loginCode = null; // 清除已保存的code
						}
						
						uni.showToast({
							title: '登录失败，请重试',
							icon: 'none'
						});
						console.error('登录失败:', err);
						
						// 通知父组件登录失败
						this.$emit('login-fail', err);
					});
			}
		}
	}
</script>

<style>
	/* 登录弹窗样式 */
	.login-modal {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-color: rgba(0, 0, 0, 0.6);
		z-index: 10000; /* 提高z-index，确保在TabBar之上 */
		display: flex;
		justify-content: center;
		align-items: center;
		backdrop-filter: blur(5px);
	}
	
	.login-modal-content {
		width: 80%;
		max-width: 600rpx;
		background-color: #fff;
		border-radius: 20rpx;
		box-shadow: 0 0 20rpx rgba(0, 0, 0, 0.2);
		overflow: hidden;
		margin-bottom: 120rpx; /* 添加底部间距，避免遮挡TabBar */
	}
	
	.login-modal-header {
		position: relative;
		padding: 30rpx;
		text-align: center;
		border-bottom: 1rpx solid #f0f0f0;
	}
	
	.login-modal-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
	}
	
	.close-icon {
		position: absolute;
		right: 20rpx;
		top: 50%;
		transform: translateY(-50%);
		font-size: 40rpx;
		color: #999;
	}
	
	.login-modal-body {
		padding: 40rpx 30rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
	}
	
	.login-icon {
		font-size: 80rpx;
		color: #A875FF; /* 修改为紫色 */
		margin-bottom: 20rpx;
	}
	
	.login-modal-desc {
		font-size: 28rpx;
		color: #666;
		margin-bottom: 30rpx;
		text-align: center;
	}
	
	.auth-btn {
		width: 100%;
		height: 80rpx;
		line-height: 80rpx;
		background: linear-gradient(to right, #A875FF, #8B5CF6); /* 修改为紫色渐变 */
		color: #fff;
		border-radius: 40rpx;
		font-size: 28rpx;
		margin-top: 20rpx;
		text-align: center;
	}
	
	.auth-btn::after {
		border: none;
	}
	
	.auth-explanation {
		font-size: 24rpx;
		color: #999;
		margin-bottom: 20rpx;
		text-align: center;
		padding: 0 20rpx;
	}
	
	/* Material Icons 字体 */
	@font-face {
		font-family: 'Material Icons';
		font-style: normal;
		font-weight: 400;
		src: url(https://fonts.gstatic.com/s/materialicons/v139/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format('woff2');
		font-display: block;
	}

	.material-icons {
		font-family: 'Material Icons';
		font-weight: normal;
		font-style: normal;
		font-size: 48rpx;
		line-height: 1;
		letter-spacing: normal;
		text-transform: none;
		display: inline-block;
		white-space: nowrap;
		word-wrap: normal;
		direction: ltr;
		-webkit-font-smoothing: antialiased;
		-moz-osx-font-smoothing: grayscale;
		text-rendering: optimizeLegibility;
	}
</style> 