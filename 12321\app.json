{"pages": ["pages/index/index", "pages/logs/logs", "pages/comm/comm", "pages/protocol/protocol"], "window": {"backgroundTextStyle": "light", "navigationBarBackgroundColor": "#fff", "navigationBarTitleText": "今夜城堡", "navigationBarTextStyle": "black"}, "style": "v2", "sitemapLocation": "sitemap.json", "requiredBackgroundModes": ["bluetooth"], "permission": {"scope.bluetooth": {"desc": "需要使用蓝牙功能连接智能门锁"}, "scope.userLocation": {"desc": "搜索蓝牙设备需要使用位置权限"}, "scope.writePhotosAlbum": {"desc": "需要保存二维码到您的相册"}}, "requiredPrivateInfos": ["<PERSON><PERSON><PERSON><PERSON>", "chooseLocation", "choosePoi", "getLocation", "onLocationChange", "startLocationUpdate", "startLocationUpdateBackground"], "networkTimeout": {"request": 10000, "connectSocket": 10000, "uploadFile": 10000, "downloadFile": 10000}}