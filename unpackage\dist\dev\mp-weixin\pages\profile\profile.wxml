<view class="container page-profile"><view class="page-background"><image class="background-image" src="https://wuji1112-1312674791.cos.ap-guangzhou.myqcloud.com/static/shouye3.png" mode="aspectFill"></image><view class="frosted-overlay"></view></view><view class="status-bar safe-area-inset-top"></view><view class="content"><view class="profile-header"><view class="avatar-container"><view class="avatar-wrapper"><image class="avatar" src="{{userInfo.avatar||'../../static/images/avatar.svg'}}" mode="aspectFill"></image></view></view><view class="user-info"><view class="user-name-row"><text class="title-md">{{userInfo.nickname||'隐私用户'}}</text><view class="{{['status',(isLoggedIn)?'active':'']}}">{{isLoggedIn?'已登录':'未登录'}}</view></view><view class="text-tertiary">{{"用户ID："+(userInfo.userId||'未登录')}}</view></view></view><view class="menu-section mt-md"><view class="menu-list"><view data-event-opts="{{[['tap',[['navigateTo',['/pages/orders/orders']]]]]}}" class="menu-item" bindtap="__e"><view class="menu-icon"><text class="material-icons neon-pink">receipt_long</text></view><view class="menu-content"><view class="menu-title">我的订单</view><view class="text-tertiary">{{"已有"+(userInfo.orderCount||0)+"个订单记录"}}</view></view><view class="menu-arrow"><text class="material-icons text-tertiary">arrow_forward_ios</text></view></view><view data-event-opts="{{[['tap',[['navigateTo',['/packageA/pages/zhaoshang/index']]]]]}}" class="menu-item" bindtap="__e"><view class="menu-icon"><text class="material-icons neon-blue">group_add</text></view><view class="menu-content"><view class="menu-title">招募合作</view><view class="text-tertiary">0元加盟成为合伙人</view></view><view class="menu-arrow"><text class="material-icons text-tertiary">arrow_forward_ios</text></view></view><view data-event-opts="{{[['tap',[['goToReportPage',['$event']]]]]}}" class="menu-item" bindtap="__e"><view class="menu-icon"><text class="material-icons neon-yellow">build</text></view><view class="menu-content"><view class="menu-title">设备异常反馈</view><view class="text-tertiary">报告设备使用问题</view></view><view class="menu-arrow"><text class="material-icons text-tertiary">arrow_forward_ios</text></view></view></view></view></view><custom-tab-bar class="vue-ref" vue-id="23b2e5ec-1" data-ref="tabBar" bind:__l="__l"></custom-tab-bar></view>