<template>
	<view class="container page-report">
		<!-- 页面背景 -->
		<view class="page-background">
			<!-- 背景图片 -->
			<image class="background-image" src="https://wuji1112-1312674791.cos.ap-guangzhou.myqcloud.com/static/shouye3.png" mode="aspectFill"></image>
			
			<!-- 深磨砂效果叠加层 -->
			<view class="frosted-overlay"></view>
		</view>
		
		<!-- 导航栏 -->
		<view class="navbar" :style="navbarStyle">
			<view class="navbar-left" @click="goBack">
				<text class="material-icons md-24 text-primary">arrow_back</text>
			</view>
			<view class="navbar-title">设备异常反馈</view>
			<view class="navbar-right"></view>
		</view>
		
		<!-- 页面内容 -->
		<view class="content">
				<!-- 表单内容 -->
				<view class="form-body">
					<!-- 订单选择 -->
					<view class="form-group" style="margin-top: -20rpx;">
						<view class="form-label">选择订单</view>
						<picker @change="onOrderChange" :value="orderIndex" :range="orderDisplays">
							<view class="form-picker">
								<text>{{orderDisplays[orderIndex] || '请选择订单'}}</text>
								<text class="material-icons md-24 text-tertiary">arrow_drop_down</text>
							</view>
						</picker>
					</view>
					
					<!-- 异常类型 -->
					<view class="form-group mt-lg">
					<view class="form-label">
						异常类型 <text class="text-tertiary type-hint">(可多选)</text>
					</view>
						<view class="issue-types">
							<view 
								v-for="(item, index) in issueTypes" 
								:key="index" 
								class="issue-type-item" 
								:class="{'active': selectedIssueTypes.includes(item.value)}"
								@click="toggleIssueType(item.value)"
							>
							<text class="material-icons issue-icon">{{item.icon}}</text>
								<text>{{item.label}}</text>
							</view>
						</view>
					</view>
					
					<!-- 图片上传 -->
				<view class="form-group mt-md">
						<view class="form-label">上传图片 <text class="text-tertiary">(最多3张)</text></view>
						<view class="upload-container">
							<view class="upload-items">
								<view 
									v-for="(item, index) in uploadedImages" 
									:key="index" 
									class="upload-item"
								>
									<image :src="item" mode="aspectFill"></image>
									<view class="delete-btn" @click="removeImage(index)">
										<text class="material-icons md-18">close</text>
									</view>
								</view>
								
								<!-- 上传按钮 -->
								<view class="upload-btn" v-if="uploadedImages.length < 3" @click="chooseImage">
									<text class="material-icons md-36 text-tertiary">add_photo_alternate</text>
								</view>
							</view>
						</view>
					</view>
				
				<!-- 问题描述 -->
				<view class="form-group mt-md">
					<view class="form-label">问题描述 <text class="text-tertiary">(最少10个字符)</text></view>
					<textarea 
						class="form-textarea" 
						v-model="description" 
						placeholder="请详细描述您遇到的问题，以便我们更好地解决..." 
						maxlength="200"
					></textarea>
					<view class="text-count text-tertiary">{{description.length}}/200</view>
				</view>
					
					<!-- 提交按钮 -->
					<view class="form-group" style="margin-top: 10rpx;">
					<view class="btn-wrapper" @tap.stop="handleSubmit">
						<button 
							class="btn btn-submit" 
							:class="{'btn-active': isFormValid && !submitting, 'btn-disabled': !isFormValid || submitting}"
							:disabled="!isFormValid || submitting"
							:style="{ opacity: isFormValid ? 1 : 0.5 }">
							<text class="btn-text">{{ submitting ? '提交中...' : '提交' }}</text>
						</button>
					</view>
					</view>
					
					<!-- 隐私提示 -->
					<view class="privacy-tip text-tertiary mt-md">
						<text>提交即表示您同意我们收集此信息以解决您的问题。</text>
				</view>
			</view>
			
			<!-- 底部空白区域，确保内容不被遮挡 -->
			<view class="bottom-space"></view>
		</view>

		<!-- 加载状态 -->
		<view class="loading-container" v-if="loading">
			<view class="loading-spinner"></view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				loading: true,
				submitting: false,

				// iOS适配相关
				systemInfo: {},
				navbarStyle: {},
				isIOS: false,

				// 设备平台
				platform: '',
				
				// 订单相关
				orderIndex: 0,
				orderList: [],
				orderDisplays: ['请选择订单'],
				
				// 问题类型
				issueTypes: [
					{ label: '机柜损坏', value: 'cabinet_damaged', icon: 'door_front', type: 1 },
					{ label: '门锁损坏', value: 'lock_damaged', icon: 'lock_open', type: 1 },
					{ label: '娃娃损坏', value: 'doll_damaged', icon: 'girl', type: 1 },
					{ label: '无法对话', value: 'no_dialogue', icon: 'mic_off', type: 1 },
					{ label: '清洁问题', value: 'cleaning_issue', icon: 'cleaning_services', type: 1 },
					{ label: '其他问题', value: 'other', icon: 'help_outline', type: 3 }
				],
				selectedIssueTypes: [],
				
				// 问题描述
				description: '',
				
				// 上传图片
				uploadedImages: [],
				uploadedImageUrls: [],
				
				// 从订单详情页传入的参数
				orderId: null,
				deviceId: null
			}
		},
		onLoad(options) {
			console.log('report.vue onLoad options:', options);

			// 获取设备平台信息
			this.detectPlatform();

			// 接收从订单详情页传入的参数
			if (options.orderId) {
				this.orderId = options.orderId;
				console.log('接收到订单ID:', this.orderId);
			}
			
			if (options.deviceId) {
				this.deviceId = options.deviceId;
				console.log('接收到设备ID:', this.deviceId);
			}
			
			// 获取订单列表
			this.getOrderList();
		},
		onReady() {
			// 获取系统信息并计算iOS适配参数
			this.calculateIOSAdaptation();
		},
		computed: {
			isFormValid() {
				// 表单验证
				// 修改为必须选择订单（orderIndex > 0），除非有传入的orderId
				const orderSelected = (this.orderIndex > 0) || this.orderId; 
				const issueTypeSelected = this.selectedIssueTypes.length > 0;
				const descriptionValid = this.description && this.description.trim().length >= 10;
				
				console.log('表单验证详情:', {
					orderSelected,
					orderIndex: this.orderIndex,
					orderId: this.orderId,
					issueTypeSelected,
					selectedTypes: this.selectedIssueTypes,
					descriptionValid,
					descriptionLength: this.description ? this.description.trim().length : 0
				});
				
				return orderSelected && issueTypeSelected && descriptionValid;
			},
			selectedOrderId() {
				// 如果已有orderId，直接返回
				if (this.orderId) {
					return this.orderId;
				}
				
				// 否则从选择的订单中获取
				if (this.orderIndex > 0 && this.orderList && this.orderList.length > 0 && this.orderIndex <= this.orderList.length) {
					const selectedOrder = this.orderList[this.orderIndex - 1];
					if (selectedOrder) {
						return selectedOrder.orderId || selectedOrder.id || null;
					}
				}
				return null;
			},
			// 获取选中的问题类型
			getSelectedIssueType() {
				if (this.selectedIssueTypes.length === 0) {
					return 1; // 默认为设备问题
				}
				
				// 如果选中了多个问题类型，优先级：其他问题 > 订单问题 > 设备问题
				if (this.selectedIssueTypes.includes('other')) {
					return 3; // 其他问题
				}
				
				// 默认为设备问题
				return 1;
			}
		},
		methods: {
			// 计算iOS适配参数
			calculateIOSAdaptation() {
				try {
					this.systemInfo = uni.getSystemInfoSync();
					this.isIOS = this.systemInfo.platform === 'ios';

					console.log('举报页面 - 系统信息:', this.systemInfo);
					console.log('举报页面 - 是否iOS:', this.isIOS);

					if (this.isIOS) {
						const statusBarHeight = this.systemInfo.statusBarHeight || 44;
						const model = this.systemInfo.model || '';
						const safeAreaTop = this.systemInfo.safeArea ? this.systemInfo.safeArea.top : statusBarHeight;

						console.log('举报页面 - 状态栏高度:', statusBarHeight);
						console.log('举报页面 - 设备型号:', model);
						console.log('举报页面 - 安全区域顶部:', safeAreaTop);

						let finalTopPosition;

						// 使用与订单页面相同的超激进适配策略
						if (model.includes('iPhone 16 Pro')) {
							// 方案1：直接使用状态栏高度，无额外间距
							finalTopPosition = statusBarHeight;
							console.log('举报页面 - iPhone 16 Pro - 方案1（状态栏高度）:', finalTopPosition);

							// 方案2：如果还是太靠下，尝试更小的值
							if (finalTopPosition > 50) {
								finalTopPosition = 44; // 使用标准状态栏高度
								console.log('举报页面 - iPhone 16 Pro - 方案2（标准高度）:', finalTopPosition);
							}

							// 方案3：如果还是太靠下，尝试负值
							if (finalTopPosition > 45) {
								finalTopPosition = statusBarHeight - 10; // 负偏移
								console.log('举报页面 - iPhone 16 Pro - 方案3（负偏移）:', finalTopPosition);
							}
						} else if (model.includes('iPhone 15 Pro') || model.includes('iPhone 14 Pro')) {
							finalTopPosition = statusBarHeight;
						} else if (model.includes('iPhone X') || model.includes('iPhone 11') ||
								  model.includes('iPhone 12') || model.includes('iPhone 13')) {
							finalTopPosition = statusBarHeight;
						} else {
							finalTopPosition = statusBarHeight;
						}

						this.navbarStyle = {
							marginTop: finalTopPosition + 'px',
							position: 'relative',
							top: '0px'
						};

						console.log('举报页面 - 超激进适配 - 最终顶部位置:', finalTopPosition + 'px');
						console.log('举报页面 - 超激进适配 - 导航栏样式:', this.navbarStyle);
					} else {
						this.navbarStyle = {};
					}
				} catch (error) {
					console.error('举报页面 - 计算iOS适配参数失败:', error);
					this.navbarStyle = {};
				}
			},
			goBack() {
				uni.navigateBack();
			},
			// 获取订单列表
			getOrderList() {
				try {
					this.loading = true;
					
					// 显示加载中
					uni.showLoading({
						title: '加载中...'
					});
					
					// 直接获取所有设备订单列表
					this.getActiveOrders();
					
					// 如果已经有传入的orderId，则尝试获取该订单的详情
					if (this.orderId) {
						// 检查API是否可用
						if (!this.$api || !this.$api.order || typeof this.$api.order.getDetail !== 'function') {
							console.error('API对象不存在或order.getDetail方法不可用');
							return;
						}
						
						this.$api.order.getDetail(this.orderId)
							.then(res => {
								if (res && res.data) {
									console.log('获取到指定订单详情:', res.data);
									
									// 确保orderList已初始化
									if (!this.orderList) {
										this.orderList = [];
									}
									
									// 检查当前订单列表是否已包含该订单
									const existingOrderIndex = this.orderList.findIndex(order => 
										order && (order.orderId === this.orderId || order.id === this.orderId)
									);
									
									if (existingOrderIndex === -1) {
										// 如果订单列表中不包含指定的订单，则添加到列表中
										this.orderList.unshift(res.data);
										console.log('将指定订单添加到列表首位');
										
										// 重新生成用于显示的订单列表
										this.generateOrderDisplays();
										
										// 默认选中该订单
										this.orderIndex = 1;
									} else {
										// 如果已包含，则选中该订单
										this.orderIndex = existingOrderIndex + 1; // +1 是因为第一项是"请选择订单"
										console.log('订单列表中已包含指定订单，选中索引:', this.orderIndex);
									}
								}
							})
							.catch(err => {
								console.error('获取指定订单详情失败:', err);
								uni.showToast({
									title: '获取订单详情失败',
									icon: 'none'
								});
							});
					}
				} catch (error) {
					console.error('getOrderList执行出错:', error);
					uni.hideLoading();
					this.loading = false;
					
					uni.showToast({
						title: '获取订单列表出错',
						icon: 'none'
					});
				}
			},
			
			// 获取所有设备订单列表
			getActiveOrders() {
				try {
					if (!this.$api || !this.$api.order || typeof this.$api.order.getList !== 'function') {
						console.error('API对象不存在或order.getList方法不可用');
						uni.hideLoading();
						this.loading = false;
						
						// 显示错误提示
						uni.showToast({
							title: '获取订单列表失败，API不可用',
							icon: 'none'
						});
						return;
					}
					
					// 直接获取所有设备订单列表，不限制状态
					this.$api.order.getList(0, 1, 20) // 获取所有订单，第一页，每页20条
						.then(res => {
							uni.hideLoading();
							this.loading = false;
							
							// 处理返回数据，增强兼容性
							let orderList = [];
							
							try {
								// 兼容不同的返回格式
								if (res && res.data) {
									if (Array.isArray(res.data)) {
										// 直接返回数组的情况
										orderList = res.data;
									} else if (res.data && Array.isArray(res.data.list)) {
										// 包含list数组的情况
										orderList = res.data.list;
									} else if (res.data && Array.isArray(res.data.records)) {
										// 包含records数组的情况
										orderList = res.data.records;
									} else if (res.data && typeof res.data === 'object') {
										// 其他情况，尝试将对象转为数组
										orderList = [res.data];
									}
								}
							} catch (error) {
								console.error('处理订单数据出错:', error);
							}
							
							console.log('获取到订单列表:', orderList);
							
							if (orderList && orderList.length > 0) {
								// 使用获取到的订单列表
								this.orderList = orderList;
								
								// 生成用于显示的订单列表
								this.generateOrderDisplays();
								
								// 不再默认选择第一个订单，保持orderIndex为0，即"请选择订单"
								// 只有在有传入orderId的情况下，才会在getOrderList方法中设置选中项
							} else {
								console.log('没有获取到订单列表或列表为空');
								// 如果没有订单
								if (!this.orderId) {
									uni.showModal({
										title: '提示',
										content: '您当前没有可用订单，无法上报设备异常',
										showCancel: false,
										success: () => {
											uni.navigateBack();
										}
									});
								}
							}
						})
						.catch(err => {
							console.error('获取订单列表失败:', err);
							uni.hideLoading();
							this.loading = false;
							
							uni.showToast({
								title: '获取订单列表失败',
								icon: 'none'
							});
							
							// 如果获取列表失败且没有传入的orderId，则提示用户
							if (!this.orderId) {
								uni.showModal({
									title: '提示',
									content: '获取订单列表失败，请稍后重试',
									showCancel: false,
									success: () => {
										uni.navigateBack();
									}
								});
							}
						});
				} catch (error) {
					console.error('getActiveOrders执行出错:', error);
					uni.hideLoading();
					this.loading = false;
					
					// 显示错误提示
					uni.showToast({
						title: '获取订单列表出错',
						icon: 'none'
					});
				}
			},
			
			// 格式化日期字符串，兼容iOS
			formatDateString(dateStr) {
				if (!dateStr) return null;
				
				try {
					// 尝试直接解析（兼容标准格式）
					const directDate = new Date(dateStr);
					// 检查是否为有效日期
					if (!isNaN(directDate.getTime())) {
						return directDate;
					}
					
					// 处理 "yyyy-MM-dd HH:mm:ss" 格式
					if (dateStr.includes('-') && dateStr.includes(':')) {
						// 将 "yyyy-MM-dd HH:mm:ss" 转换为 "yyyy/MM/dd HH:mm:ss"
						const formattedStr = dateStr.replace(/-/g, '/');
						return new Date(formattedStr);
					}
					
					// 处理其他可能的格式
					// 提取日期部分
					const dateParts = dateStr.match(/(\d{4})[-\/](\d{1,2})[-\/](\d{1,2})/);
					if (dateParts) {
						const year = parseInt(dateParts[1]);
						const month = parseInt(dateParts[2]) - 1; // 月份从0开始
						const day = parseInt(dateParts[3]);
						
						// 提取时间部分
						const timeParts = dateStr.match(/(\d{1,2}):(\d{1,2}):(\d{1,2})/);
						if (timeParts) {
							const hour = parseInt(timeParts[1]);
							const minute = parseInt(timeParts[2]);
							const second = parseInt(timeParts[3]);
							return new Date(year, month, day, hour, minute, second);
						} else {
							return new Date(year, month, day);
						}
					}
					
					// 如果无法解析，返回当前日期
					console.error('无法解析日期:', dateStr);
					return new Date();
				} catch (e) {
					console.error('日期解析错误:', e, dateStr);
					return new Date(); // 出错时返回当前日期
				}
			},
			
			// 生成订单显示列表
			generateOrderDisplays() {
				try {
					// 订单状态映射
					const statusMap = {
						0: '未支付',
						1: '进行中',
						2: '已完成',
						3: '已取消'
					};
					
					// 确保orderList存在且为数组
					if (!this.orderList || !Array.isArray(this.orderList)) {
						console.error('订单列表不是有效数组:', this.orderList);
						this.orderDisplays = ['请选择订单'];
						return;
					}
					
					this.orderDisplays = ['请选择订单', ...this.orderList.map(order => {
						if (!order) return '未知订单';
						
						try {
							// 获取日期（使用兼容iOS的方法）
							const dateTimeStr = order.createTime || order.startTime || '';
							const date = this.formatDateString(dateTimeStr);
							const dateStr = date ? `${date.getMonth() + 1}月${date.getDate()}日` : '未知日期';
							
							// 获取房间号/设备号
							const roomNumber = order.roomNumber || order.deviceNo || order.deviceId || '';
							
							// 获取状态
							const status = order.payStatus === 1 ? '已完成' : (statusMap[order.status || 0] || '未知状态');
							
							// 获取门店名称
							const storeName = this.getStoreName(order.storeName || order.shopName || '未知门店');
							
							// 返回格式化的订单显示文本
							return `${dateStr} - ${storeName} (${roomNumber}) [${status}]`;
						} catch (innerError) {
							console.error('处理单个订单数据出错:', innerError, order);
							return '订单数据处理错误';
						}
					})];
				} catch (error) {
					console.error('生成订单显示列表出错:', error);
					this.orderDisplays = ['请选择订单'];
				}
			},
			
			// 处理门店名称，移除"今夜城堡 - "前缀
			getStoreName(fullName) {
				if (!fullName) return '未知门店';
				return String(fullName).replace('今夜城堡 - ', '');
			},
			onOrderChange(e) {
				this.orderIndex = e.detail.value;
				// 如果选择了新订单，清除之前传入的orderId
				if (this.orderIndex > 0) {
					this.orderId = null;
				}
			},
			toggleIssueType(value) {
				const index = this.selectedIssueTypes.indexOf(value);
				if (index !== -1) {
					this.selectedIssueTypes.splice(index, 1);
				} else {
					this.selectedIssueTypes.push(value);
				}
			},
			chooseImage() {
				uni.chooseImage({
					count: 3 - this.uploadedImages.length,
					sizeType: ['compressed'],
					sourceType: ['album', 'camera'],
					success: (res) => {
						// 添加选择的图片
						this.uploadedImages = [...this.uploadedImages, ...res.tempFilePaths];
					}
				});
			},
			removeImage(index) {
				this.uploadedImages.splice(index, 1);
			},
			// 上传图片（带备用方案）
			uploadImages() {
				return new Promise((resolve, reject) => {
					try {
						// 如果没有图片需要上传，直接返回空数组
						if (this.uploadedImages.length === 0) {
							resolve([]);
							return;
						}
						
						// 检查API是否可用
						if (!this.$api || !this.$api.report || typeof this.$api.report.uploadImage !== 'function') {
							console.error('API对象不存在或report.uploadImage方法不可用，尝试备用上传方法');
							// 如果主API不可用，尝试使用备用方法上传所有图片
							const fallbackPromises = this.uploadedImages.map(imagePath => this.uploadImageFallback(imagePath));
							
							Promise.all(fallbackPromises)
								.then(imageUrls => {
									console.log('所有图片通过备用方法上传成功:', imageUrls);
									this.uploadedImageUrls = imageUrls;
									resolve(imageUrls);
								})
								.catch(err => {
									console.error('备用上传方法失败:', err);
									reject(err);
								});
							return;
						}
						
						const uploadPromises = this.uploadedImages.map(imagePath => {
							// 先尝试使用专用的反馈图片上传API
							return this.$api.report.uploadImage(imagePath)
								.then(res => {
									console.log('图片上传响应:', res);
									// 检查响应是否有效
									if (!res || !res.data) {
										throw new Error('上传图片响应无效');
									}
									
									// 从响应中获取上传后的图片URL
									// 适配不同的返回格式
									if (res.data && res.data.url) {
										return res.data.url;
									} else if (res.data && res.data.imageUrl) {
										return res.data.imageUrl;
									} else if (typeof res.data === 'string') {
										// 如果直接返回了URL字符串
										return res.data;
									} else {
										console.error('上传图片返回格式异常:', res);
										throw new Error('上传图片失败，返回数据格式不正确');
									}
								})
								.catch(err => {
									console.error('专用API上传失败，尝试通用上传API:', err);
									// 如果专用API失败，尝试使用通用上传API
									return this.uploadImageFallback(imagePath);
								});
						});
						
						Promise.all(uploadPromises)
							.then(imageUrls => {
								console.log('所有图片上传成功:', imageUrls);
								this.uploadedImageUrls = imageUrls;
								resolve(imageUrls);
							})
							.catch(err => {
								console.error('上传图片失败:', err);
								reject(err);
							});
					} catch (error) {
						console.error('上传图片过程中发生未捕获的错误:', error);
						reject(error);
					}
				});
			},
			
			// 备用上传方法
			uploadImageFallback(imagePath) {
				return new Promise((resolve, reject) => {
					try {
						if (!imagePath) {
							console.error('备用上传方法：图片路径无效');
							reject(new Error('图片路径无效'));
							return;
						}
						
						// 尝试使用通用上传API
						if (this.$api && this.$api.upload && typeof this.$api.upload.file === 'function') {
							console.log('尝试使用通用上传API');
							this.$api.upload.file(imagePath, 'feedback')
								.then(res => {
									console.log('通用API上传响应:', res);
									if (!res || !res.data) {
										throw new Error('通用上传API响应无效');
									}
									
									if (res.data && res.data.url) {
										return resolve(res.data.url);
									} else if (res.data && res.data.imageUrl) {
										return resolve(res.data.imageUrl);
									} else if (typeof res.data === 'string') {
										return resolve(res.data);
									} else {
										throw new Error('通用上传API返回格式不正确');
									}
								})
								.catch(err => {
									console.error('通用上传API也失败:', err);
									// 最后尝试使用uni.uploadFile直接上传
									this.uploadImageDirect(imagePath)
										.then(resolve)
										.catch(reject);
								});
						} else {
							// 如果没有通用上传API，直接使用uni.uploadFile
							console.log('没有通用上传API，直接使用uni.uploadFile');
							this.uploadImageDirect(imagePath)
								.then(resolve)
								.catch(reject);
						}
					} catch (error) {
						console.error('备用上传方法中发生未捕获的错误:', error);
						// 尝试最后的直接上传方法
						try {
							this.uploadImageDirect(imagePath)
								.then(resolve)
								.catch(reject);
						} catch (finalError) {
							console.error('所有上传方法都失败:', finalError);
							reject(finalError);
						}
					}
				});
			},
			
			// 直接使用uni.uploadFile上传
			uploadImageDirect(imagePath) {
				return new Promise((resolve, reject) => {
					try {
						if (!imagePath) {
							console.error('直接上传方法：图片路径无效');
							reject(new Error('图片路径无效'));
							return;
						}
						
						console.log('尝试直接使用uni.uploadFile上传');
						
						// 检查uni对象是否可用
						if (typeof uni === 'undefined' || typeof uni.uploadFile !== 'function') {
							console.error('uni对象不存在或uploadFile方法不可用');
							reject(new Error('上传功能不可用'));
							return;
						}
						
						uni.uploadFile({
							url: '/api/upload/image',
							filePath: imagePath,
							name: 'file',
							formData: {
								type: 'feedback'
							},
							success: (res) => {
								console.log('直接上传响应:', res);
								try {
									if (!res || !res.data) {
										throw new Error('上传响应无效');
									}
									
									const data = JSON.parse(res.data);
									if (data.code === 200 && data.data) {
										if (data.data.url) {
											resolve(data.data.url);
										} else if (data.data.imageUrl) {
											resolve(data.data.imageUrl);
										} else if (typeof data.data === 'string') {
											resolve(data.data);
										} else {
											reject(new Error('直接上传返回格式不正确'));
										}
									} else {
										reject(new Error(data.message || '直接上传失败'));
									}
								} catch (e) {
									console.error('解析上传响应失败:', e);
									reject(new Error('解析上传响应失败'));
								}
							},
							fail: (err) => {
								console.error('直接上传失败:', err);
								reject(err);
							}
						});
					} catch (error) {
						console.error('直接上传方法中发生未捕获的错误:', error);
						reject(error);
					}
				});
			},
			// 检查网络状态
			checkNetworkStatus() {
				return new Promise((resolve, reject) => {
					uni.getNetworkType({
						success: (res) => {
							console.log('当前网络状态:', res);
							if (res.networkType === 'none') {
								uni.showToast({
									title: '网络连接不可用，请检查网络设置',
									icon: 'none',
									duration: 3000
								});
								reject(new Error('网络连接不可用'));
							} else {
								resolve(res.networkType);
							}
						},
						fail: (err) => {
							console.error('获取网络状态失败:', err);
							// 即使获取网络状态失败，也继续尝试提交
							resolve('unknown');
						}
					});
				});
			},
			// 提交异常报告
			submitReport() {
				try {
					console.log('开始提交异常报告');
					
					if (!this.isFormValid) {
						console.log('表单验证失败，无法提交');
						uni.showToast({
							title: '请完善表单信息',
							icon: 'none'
						});
						return;
					}
					
					// 检查API是否可用
					if (!this.$api || !this.$api.report || typeof this.$api.report.submit !== 'function') {
						console.error('API对象不存在或report.submit方法不可用');
						uni.showToast({
							title: 'API不可用，无法提交',
							icon: 'none'
						});
						return;
					}
					
					// 设置提交状态
					this.submitting = true;
					
					// 显示加载中
					uni.showLoading({
						title: '提交中...',
						mask: true // 添加遮罩防止重复点击
					});
					
					// 先检查网络状态
					this.checkNetworkStatus()
						.then(() => {
							console.log('网络状态检查通过，开始上传图片');
							// 网络正常，上传图片
							return this.uploadImages();
						})
						.then(imageUrls => {
							console.log('图片上传成功，准备提交数据', imageUrls);
							// 获取选中的问题类型
							let issueType = 1; // 默认为设备问题
							
							// 如果选中了多个问题类型，优先级：其他问题 > 订单问题 > 设备问题
							if (this.selectedIssueTypes.includes('other')) {
								issueType = 3; // 其他问题
							}
							
							// 获取当前选中的订单信息
							let currentOrder = null;
							if (this.orderIndex > 0 && this.orderList && this.orderList.length > 0 && this.orderIndex <= this.orderList.length) {
								currentOrder = this.orderList[this.orderIndex - 1];
								if (!currentOrder) {
									console.warn('选中的订单索引有效，但订单对象为空');
								}
							}
							
							// 获取订单ID，优先使用传入的orderId，其次使用选中的订单ID
							const orderId = this.orderId || (currentOrder ? (currentOrder.orderId || currentOrder.id || '') : '');
							
							// 获取设备ID，优先使用传入的deviceId，其次使用订单中的deviceId或deviceNo
							let deviceId = this.deviceId;
							if (!deviceId && currentOrder) {
								if (currentOrder.deviceId) {
									// 如果是数字字符串，转换为数字
									deviceId = /^\d+$/.test(currentOrder.deviceId) ? parseInt(currentOrder.deviceId) : currentOrder.deviceId;
								} else if (currentOrder.deviceNo) {
									deviceId = /^\d+$/.test(currentOrder.deviceNo) ? parseInt(currentOrder.deviceNo) : currentOrder.deviceNo;
								}
							}
							
							// 获取订单状态
							let orderStatus = '';
							if (currentOrder) {
								const statusMap = {
									0: '未支付',
									1: '进行中',
									2: '已完成',
									3: '已取消'
								};
								orderStatus = currentOrder.payStatus === 1 ? '已完成' : (statusMap[currentOrder.status || 0] || '未知状态');
							}
							
							console.log('提交异常报告 - 当前订单:', currentOrder);
							console.log('提交异常报告 - 使用订单ID:', orderId);
							console.log('提交异常报告 - 使用设备ID:', deviceId);
							
							// 准备异常报告数据
							const reportData = {
								orderId: orderId,
								deviceId: deviceId,
								issueType: issueType,
								content: `问题类型：${this.selectedIssueTypes.map(type => {
									const issueType = this.issueTypes.find(item => item.value === type);
									return issueType ? issueType.label : '';
								}).join('、')}\n\n${orderStatus ? `订单状态：${orderStatus}\n\n` : ''}${this.description}`,
								images: imageUrls,
								contactInfo: uni.getStorageSync('userInfo') ? uni.getStorageSync('userInfo').phone || '' : ''
							};
							
							console.log('提交异常报告数据:', reportData);
							
							// 提交异常报告
							return this.$api.report.submit(reportData);
						})
						.then(res => {
							uni.hideLoading();
							
							console.log('异常报告提交响应:', res);
							
							// 检查返回结果
							if (res.code && res.code !== 200) {
								throw new Error(res.message || '提交失败');
							}
							
							// 显示成功提示
							uni.showModal({
								title: '提交成功',
								content: '感谢您的反馈，我们会尽快处理您的问题。客服将在15分钟内与您联系，请保持电话畅通。',
								showCancel: false,
								confirmText: '确定',
								success: (res) => {
									if (res.confirm) {
										// 返回上一页
										uni.navigateBack();
									}
								}
							});
						})
						.catch(err => {
							console.error('提交异常报告失败:', err);
							uni.hideLoading();
							
							// 显示错误信息
							let errorMsg = '提交失败，请稍后重试';
							if (err && err.message) {
								if (err.message.includes('上传图片失败')) {
									errorMsg = '图片上传失败，请重新选择图片';
								} else if (err.message.includes('网络连接不可用')) {
									errorMsg = '网络连接不可用，请检查网络设置后重试';
								} else if (typeof err.message === 'string') {
									errorMsg = err.message;
								}
							}
							
							// 检查网络连接问题
							if (err && err.errMsg && err.errMsg.includes('request:fail')) {
								errorMsg = '网络连接失败，请检查网络设置后重试';
							}
							
							// 检查服务器错误
							if (err && err.statusCode) {
								if (err.statusCode === 401) {
									errorMsg = '登录已过期，请重新登录';
									// 跳转到登录页
									uni.navigateTo({
										url: '/pages/login/login'
									});
								} else if (err.statusCode >= 500) {
									errorMsg = '服务器错误，请稍后重试';
								}
							}
							
							uni.showToast({
								title: errorMsg,
								icon: 'none',
								duration: 3000
							});
						})
						.finally(() => {
							this.submitting = false;
						});
				} catch (error) {
					console.error('提交过程中发生未捕获的错误:', error);
					uni.hideLoading();
					this.submitting = false;
					uni.showToast({
						title: '提交过程发生错误，请稍后重试',
						icon: 'none',
						duration: 3000
					});
				}
			},
			// 处理提交按钮点击事件
			handleSubmit(e) {
				// 阻止事件冒泡
				if (e && typeof e.stopPropagation === 'function') {
					e.stopPropagation();
				}
				
				console.log('提交按钮被点击', new Date().toISOString());
				console.log('表单验证状态:', this.isFormValid);
				console.log('提交中状态:', this.submitting);
				console.log('当前平台:', this.platform);
				console.log('订单选择:', this.orderIndex > 0 ? `已选择第${this.orderIndex}个订单` : '未选择订单');
				console.log('问题类型:', this.selectedIssueTypes);
				console.log('描述长度:', this.description ? this.description.trim().length : 0);
				
				// 如果正在提交中，阻止重复提交
				if (this.submitting) {
					console.log('正在提交中，阻止重复提交');
					uni.showToast({
						title: '正在提交中，请稍候...',
						icon: 'none'
					});
					return;
				}
				
				// 表单验证
				if (!this.isFormValid) {
					let errorMsg = '请完善表单信息';
					
					if (this.orderIndex <= 0 && !this.orderId) {
						errorMsg = '请先选择一个订单';
					} else if (this.selectedIssueTypes.length === 0) {
						errorMsg = '请选择至少一种异常类型';
					} else if (!this.description || this.description.trim().length < 10) {
						errorMsg = '问题描述至少需要10个字符';
					}
					
					console.log('表单验证失败，原因:', errorMsg);
					
					uni.showToast({
						title: errorMsg,
						icon: 'none',
						duration: 2000
					});
					return;
				}
				
				// 根据平台选择不同的提交方式
				console.log('开始调用提交方法');
				if (this.platform === 'ios') {
					// iOS平台使用特殊处理
					this.submitReportForIOS();
				} else {
					// 其他平台使用标准方法
					setTimeout(() => {
						this.submitReport();
					}, 100); // 延迟100ms调用，避免可能的事件冲突
				}
			},
			// iOS平台特定的提交方法
			submitReportForIOS() {
				try {
					console.log('iOS平台特定提交方法');
					
					// 检查API是否可用
					if (!this.$api || !this.$api.report || typeof this.$api.report.submit !== 'function') {
						console.error('API对象不存在或report.submit方法不可用');
						uni.showToast({
							title: 'API不可用，无法提交',
							icon: 'none'
						});
						return;
					}
					
					// 设置提交状态
					this.submitting = true;
					
					// 显示加载中
					uni.showLoading({
						title: '提交中...',
						mask: true // 添加遮罩防止重复点击
					});
					
					// 先检查网络状态
					uni.getNetworkType({
						success: (res) => {
							if (res.networkType === 'none') {
								uni.hideLoading();
								this.submitting = false;
								uni.showToast({
									title: '网络连接不可用，请检查网络设置',
									icon: 'none',
									duration: 3000
								});
								return;
							}
							
							// 网络正常，上传图片
							this.uploadImages()
								.then(imageUrls => {
									console.log('图片上传成功，准备提交数据', imageUrls);
									
									// 获取选中的问题类型
									let issueType = 1; // 默认为设备问题
									if (this.selectedIssueTypes.includes('other')) {
										issueType = 3; // 其他问题
									}
									
									// 获取当前选中的订单信息
									let currentOrder = null;
									if (this.orderIndex > 0 && this.orderList && this.orderList.length > 0 && this.orderIndex <= this.orderList.length) {
										currentOrder = this.orderList[this.orderIndex - 1];
										if (!currentOrder) {
											console.warn('iOS方法中：选中的订单索引有效，但订单对象为空');
										}
									}
									
									// 获取订单ID
									const orderId = this.orderId || (currentOrder ? (currentOrder.orderId || currentOrder.id || '') : '');
									
									// 获取设备ID
									let deviceId = this.deviceId;
									if (!deviceId && currentOrder) {
										if (currentOrder.deviceId) {
											deviceId = /^\d+$/.test(currentOrder.deviceId) ? parseInt(currentOrder.deviceId) : currentOrder.deviceId;
										} else if (currentOrder.deviceNo) {
											deviceId = /^\d+$/.test(currentOrder.deviceNo) ? parseInt(currentOrder.deviceNo) : currentOrder.deviceNo;
										}
									}
									
									// 获取订单状态
									let orderStatus = '';
									if (currentOrder) {
										const statusMap = {
											0: '未支付',
											1: '进行中',
											2: '已完成',
											3: '已取消'
										};
										orderStatus = currentOrder.payStatus === 1 ? '已完成' : (statusMap[currentOrder.status || 0] || '未知状态');
									}
									
									// 准备异常报告数据
									const reportData = {
										orderId: orderId,
										deviceId: deviceId,
										issueType: issueType,
										content: `问题类型：${this.selectedIssueTypes.map(type => {
											const issueType = this.issueTypes.find(item => item.value === type);
											return issueType ? issueType.label : '';
										}).join('、')}\n\n${orderStatus ? `订单状态：${orderStatus}\n\n` : ''}${this.description}`,
										images: imageUrls,
										contactInfo: uni.getStorageSync('userInfo') ? uni.getStorageSync('userInfo').phone || '' : ''
									};
									
									console.log('提交异常报告数据:', reportData);
									
									// 提交异常报告
									this.$api.report.submit(reportData)
										.then(res => {
											uni.hideLoading();
											console.log('异常报告提交响应:', res);
											
											// 检查返回结果
											if (res.code && res.code !== 200) {
												throw new Error(res.message || '提交失败');
											}
											
											// 显示成功提示
											uni.showModal({
												title: '提交成功',
												content: '感谢您的反馈，我们会尽快处理您的问题。客服将在15分钟内与您联系，请保持电话畅通。',
												showCancel: false,
												confirmText: '确定',
												success: (res) => {
													if (res.confirm) {
														// 返回上一页
														uni.navigateBack();
													}
												}
											});
										})
										.catch(err => {
											this.handleSubmitError(err);
										})
										.finally(() => {
											this.submitting = false;
										});
								})
								.catch(err => {
									this.handleSubmitError(err);
									this.submitting = false;
								});
						},
						fail: () => {
							uni.hideLoading();
							this.submitting = false;
							uni.showToast({
								title: '网络状态检查失败，请稍后重试',
								icon: 'none',
								duration: 3000
							});
						}
					});
				} catch (error) {
					console.error('iOS提交过程中发生未捕获的错误:', error);
					uni.hideLoading();
					this.submitting = false;
					uni.showToast({
						title: '提交过程发生错误，请稍后重试',
						icon: 'none',
						duration: 3000
					});
				}
			},
			// 处理提交错误
			handleSubmitError(err) {
				try {
					console.error('提交异常报告失败:', err);
					
					// 确保加载状态被清除
					uni.hideLoading();
					
					// 显示错误信息
					let errorMsg = '提交失败，请稍后重试';
					
					// 处理不同类型的错误
					if (err) {
						// 处理Error对象或带message属性的对象
						if (err.message) {
							if (err.message.includes('上传图片失败')) {
								errorMsg = '图片上传失败，请重新选择图片';
							} else if (err.message.includes('网络连接不可用')) {
								errorMsg = '网络连接不可用，请检查网络设置后重试';
							} else if (err.message.includes('API不可用')) {
								errorMsg = '服务暂时不可用，请稍后重试';
							} else if (typeof err.message === 'string') {
								// 限制错误消息长度，避免过长的错误信息
								errorMsg = err.message.length > 50 ? err.message.substring(0, 50) + '...' : err.message;
							}
						}
						
						// 处理网络请求错误
						if (err.errMsg) {
							if (err.errMsg.includes('request:fail')) {
								errorMsg = '网络连接失败，请检查网络设置后重试';
							} else if (err.errMsg.includes('timeout')) {
								errorMsg = '请求超时，请稍后重试';
							}
						}
						
						// 处理HTTP状态码错误
						if (err.statusCode) {
							if (err.statusCode === 401) {
								errorMsg = '登录已过期，请重新登录';
								// 跳转到登录页
								setTimeout(() => {
									uni.navigateTo({
										url: '/pages/login/login'
									});
								}, 1500);
							} else if (err.statusCode === 403) {
								errorMsg = '您没有权限执行此操作';
							} else if (err.statusCode === 404) {
								errorMsg = '请求的资源不存在';
							} else if (err.statusCode >= 500) {
								errorMsg = '服务器错误，请稍后重试';
							}
						}
						
						// 处理响应数据中的错误信息
						if (err.data) {
							if (err.data.message) {
								errorMsg = err.data.message;
							} else if (err.data.msg) {
								errorMsg = err.data.msg;
							} else if (typeof err.data === 'string' && err.data.length < 50) {
								errorMsg = err.data;
							}
						}
					}
					
					// 显示错误提示
					uni.showToast({
						title: errorMsg,
						icon: 'none',
						duration: 3000
					});
				} catch (error) {
					console.error('处理错误信息时发生异常:', error);
					uni.showToast({
						title: '提交失败，请稍后重试',
						icon: 'none',
						duration: 3000
					});
				}
			},
			// 检测设备平台
			detectPlatform() {
				try {
					// 在微信小程序环境中
					if (typeof wx !== 'undefined') {
						// 优先使用getSystemInfoSync
						try {
							const sysInfo = wx.getSystemInfoSync();
							this.platform = sysInfo.platform;
							console.log('当前设备平台(getSystemInfoSync):', this.platform);
							return;
						} catch (e) {
							console.error('getSystemInfoSync失败:', e);
						}
						
						// 尝试使用getAppBaseInfo
						try {
							if (wx.getAppBaseInfo) {
								const info = wx.getAppBaseInfo();
								this.platform = info.platform;
								console.log('当前设备平台(getAppBaseInfo):', this.platform);
								return;
							}
						} catch (e) {
							console.error('getAppBaseInfo失败:', e);
						}
					}
					
					// 使用uni-app的API作为备选
					uni.getSystemInfo({
						success: (res) => {
							this.platform = res.platform || 'unknown';
							console.log('当前设备平台(uni.getSystemInfo):', this.platform);
						},
						fail: (err) => {
							console.error('获取系统信息失败:', err);
							this.platform = 'unknown';
							console.log('设置默认平台: unknown');
						}
					});
				} catch (err) {
					console.error('检测平台出错:', err);
					this.platform = 'unknown';
					console.log('设置默认平台: unknown');
				}
			}
		}
	}
</script>

<style>
	/* CSS变量定义 */
	page {
		--primary-light: #A875FF;
		--primary-dark: #8C29FF;
		--neon-pink: #ff36f9;
		--neon-blue: #00BFFF;
		--neon-yellow: #FFD700;
		--neon-red: #FF454A;
		overflow: hidden; /* 禁用页面滚动 */
	}
	
	/* iOS适配 */
	.page-report {
		/* 适配iOS设备的安全区域和灵动岛 */
		padding-top: env(safe-area-inset-top);
	}

	/* 页面基础样式 */
	.page-report {
		color: #ffffff;
		height: 100vh;
		min-height: 100vh;
		box-sizing: border-box;
		position: relative;
		overflow: hidden; /* 禁用容器滚动 */
		display: flex;
		flex-direction: column;
	}
	
	/* 页面背景样式 */
	.page-background {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		z-index: 0;
	}
	
	/* 背景图片样式 */
	.background-image {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		z-index: 0;
		object-fit: cover;
	}
	
	/* 深磨砂效果叠加层 */
	.frosted-overlay {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-color: rgba(18, 18, 18, 0.7); /* 深色半透明背景 */
		backdrop-filter: blur(8px); /* 较强模糊效果 */
		-webkit-backdrop-filter: blur(8px);
		z-index: 1;
	}
	
	/* 顶部导航样式 */
	.navbar {
		height: 90rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 30rpx;
		/* 完全移除CSS的margin-top，只使用JavaScript动态设置 */
		margin-top: 0;
		position: relative;
		z-index: 100;
	}
	
	.navbar-title {
		font-size: 40rpx;
		font-weight: 600;
	}
	
	.navbar-left, .navbar-right {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	/* 内容区域 */
	.content {
		flex: 1;
		padding: 30rpx 30rpx;
		position: relative;
		z-index: 5;
		width: 100%;
		box-sizing: border-box;
		overflow: hidden; /* 修改为hidden，禁用滚动 */
	}
	
	/* 表单容器 */
	.form-body {
		padding: 30rpx;
	}
	
	.form-group {
		margin-bottom: 30rpx;
		width: 100%;
	}
	
	.form-group.mt-lg {
		margin-top: 50rpx;
	}
	
	.form-group.mt-md {
		margin-top: 40rpx;
	}
	
	.form-label {
		font-size: 36rpx;
		font-weight: 500;
		margin-bottom: 16rpx;
		color: #c18fff;
		text-shadow: 0 0 10rpx rgba(193, 143, 255, 0.3);
	}
	
	.type-hint {
		margin-left: 15rpx;
	}
	
	.form-picker {
		background-color: rgba(255, 255, 255, 0.08);
		border: 1px solid rgba(168, 117, 255, 0.2);
		border-radius: 15rpx;
		padding: 20rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		color: var(--text-primary, #FFFFFF);
		transition: all 0.3s ease;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
	}
	
	.form-picker:active {
		background-color: rgba(255, 255, 255, 0.12);
		transform: scale(0.99);
	}
	
	.issue-types {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		gap: 10rpx;
		margin-top: 10rpx;
		width: 100%;
	}
	
	.issue-type-item {
		display: flex;
		align-items: center;
		padding: 8rpx 12rpx;
		background-color: rgba(255, 255, 255, 0.08);
		border: 1px solid rgba(168, 117, 255, 0.2);
		border-radius: 16rpx;
		font-size: 26rpx;
		transition: all 0.3s ease;
		height: 60rpx;
	}
	
	.issue-type-item .issue-icon {
		font-size: 34rpx;
		margin-right: 6rpx;
		position: relative;
		top: 1rpx;
	}
	
	.issue-type-item.active {
		background-color: rgba(168, 117, 255, 0.15);
		border-color: var(--primary-light, #A875FF);
		color: var(--primary-light, #A875FF);
		box-shadow: 0 0 10rpx rgba(168, 117, 255, 0.3);
	}
	
	.issue-type-item.active .issue-icon {
		color: var(--primary-light, #A875FF);
	}
	
	.issue-type-item:active {
		transform: scale(0.95);
	}
	
	.form-textarea {
		background-color: rgba(255, 255, 255, 0.08);
		border: 1px solid rgba(168, 117, 255, 0.2);
		border-radius: 15rpx;
		padding: 20rpx;
		width: 100%;
		height: 150rpx;
		box-sizing: border-box;
		color: var(--text-primary, #FFFFFF);
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
		transition: border-color 0.3s ease, box-shadow 0.3s ease;
		font-size: 28rpx;
	}
	
	.form-textarea:focus {
		border-color: rgba(168, 117, 255, 0.5);
		box-shadow: 0 0 12rpx rgba(168, 117, 255, 0.2);
	}
	
	.form-input {
		background-color: rgba(255, 255, 255, 0.08);
		border: 1px solid rgba(168, 117, 255, 0.2);
		border-radius: 15rpx;
		padding: 28rpx 20rpx;
		width: 100%;
		box-sizing: border-box;
		color: var(--text-primary, #FFFFFF);
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
		transition: border-color 0.3s ease, box-shadow 0.3s ease;
		font-size: 30rpx;
		height: 90rpx;
		line-height: 1.5;
	}
	
	.form-input:focus {
		border-color: rgba(168, 117, 255, 0.5);
		box-shadow: 0 0 12rpx rgba(168, 117, 255, 0.2);
	}
	
	.text-count {
		text-align: right;
		font-size: 24rpx;
		margin-top: 10rpx;
		color: rgba(255, 255, 255, 0.4);
	}
	
	.upload-container {
		margin-top: 10rpx;
		width: 100%;
	}
	
	.upload-items {
		display: flex;
		flex-wrap: wrap;
		gap: 20rpx;
		width: 100%;
	}
	
	.upload-item {
		width: 180rpx;
		height: 180rpx;
		position: relative;
		border-radius: 15rpx;
		overflow: hidden;
		box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.2);
		border: 1px solid rgba(168, 117, 255, 0.2);
		transition: transform 0.3s ease, box-shadow 0.3s ease;
	}
	
	.upload-item:active {
		transform: scale(0.95);
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
	}
	
	.upload-item image {
		width: 100%;
		height: 100%;
	}
	
	.delete-btn {
		position: absolute;
		top: 8rpx;
		right: 8rpx;
		width: 36rpx;
		height: 36rpx;
		background-color: rgba(0, 0, 0, 0.6);
		border-radius: 18rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		color: #fff;
		transition: all 0.3s ease;
	}
	
	.delete-btn:active {
		transform: scale(0.9);
		background-color: rgba(255, 69, 58, 0.8);
	}
	
	.upload-btn {
		width: 180rpx;
		height: 180rpx;
		border: 1px dashed rgba(168, 117, 255, 0.4);
		border-radius: 15rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: rgba(168, 117, 255, 0.05);
		transition: all 0.3s ease;
	}
	
	.upload-btn:active {
		background-color: rgba(168, 117, 255, 0.15);
		transform: scale(0.95);
	}
	
	.privacy-tip {
		font-size: 24rpx;
		text-align: center;
		color: rgba(255, 255, 255, 0.4);
	}
	
	/* 按钮包装器，增加点击区域 */
	.btn-wrapper {
		width: 100%;
		padding: 10rpx 0;
		display: flex;
		justify-content: center;
	}
	
	/* 按钮样式 */
	.btn {
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 15rpx;
		padding: 25rpx;
		font-size: 30rpx;
		font-weight: 600;
		transition: all 0.3s ease;
	}
	
	.btn-submit {
		background: linear-gradient(135deg, #c18fff, #a875ff);
		color: #FFFFFF;
		box-shadow: 0 4rpx 20rpx rgba(193, 143, 255, 0.6);
		border: none;
		width: 60%;
		margin: 0 auto;
		padding: 16rpx;
		border-radius: 50rpx;
		position: relative;
		overflow: hidden;
	}
	
	.btn-active:active {
		transform: scale(0.95);
		background: linear-gradient(135deg, #b06aff, #9c63ff);
		box-shadow: 0 2rpx 10rpx rgba(193, 143, 255, 0.5);
	}
	
	.btn-disabled {
		background: linear-gradient(135deg, #c18fff99, #a875ff99);
		box-shadow: none;
	}
	
	.btn-text {
		font-size: 28rpx;
		font-weight: 700;
		letter-spacing: 3rpx;
		text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
		color: #FFFFFF;
		z-index: 1;
	}
	
	/* 底部空间 */
	.bottom-space {
		height: 60rpx;
	}
	
	/* Material Icons 字体 */
	@font-face {
		font-family: 'Material Icons';
		font-style: normal;
		font-weight: 400;
		src: url(https://fonts.gstatic.com/s/materialicons/v139/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format('woff2');
	}

	.material-icons {
		font-family: 'Material Icons';
		font-weight: normal;
		font-style: normal;
		font-size: 24rpx;
		line-height: 1;
		letter-spacing: normal;
		text-transform: none;
		display: inline-block;
		white-space: nowrap;
		word-wrap: normal;
		direction: ltr;
		-webkit-font-smoothing: antialiased;
		-moz-osx-font-smoothing: grayscale;
	}
	
	.material-icons.md-18 {
		font-size: 36rpx;
	}
	
	.material-icons.md-24 {
		font-size: 48rpx;
	}
	
	.material-icons.md-36 {
		font-size: 72rpx;
	}
	
	.material-icons.text-primary {
		color: var(--text-primary, #FFFFFF);
	}
	
	.material-icons.text-tertiary {
		color: var(--text-tertiary, rgba(255, 255, 255, 0.5));
	}
	
	.mr-sm {
		margin-right: 10rpx;
	}
	
	.mt-sm {
		margin-top: 16rpx;
	}
	
	.mt-md {
		margin-top: 30rpx;
	}
	
	.mt-lg {
		margin-top: 60rpx;
	}
	
	/* 文本样式 */
	.title-sm {
		font-size: 32rpx;
		font-weight: 600;
		color: #ffffff;
		margin-bottom: 6rpx;
	}
	
	.text-secondary {
		color: rgba(255, 255, 255, 0.7);
		font-size: 28rpx;
	}
	
	.text-tertiary {
		color: rgba(255, 255, 255, 0.5);
		font-size: 26rpx;
	}
	
	.text-primary {
		color: #FFFFFF;
		font-size: 28rpx;
	}

	/* 移除所有CSS适配，完全依赖JavaScript动态设置 */
</style>