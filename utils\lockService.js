/**
 * 智能锁服务类
 * 封装蓝牙锁管理器，提供更高级的业务功能，并与API对接
 */
import blueToothManager from './blueToothManager.js';
import { request, post, get } from './request.js';
import API from '../static/js/api.js';

/**
 * 获取API基础URL
 * @returns {string} API基础URL
 */
const getAPIBaseUrl = () => {
  let baseUrl = 'https://api.jycb888.com';
  if (API && API.baseUrl) {
    baseUrl = API.baseUrl;
    console.log('使用API配置的baseUrl:', baseUrl);
  } else {
    console.log('API.baseUrl不存在，使用默认baseUrl:', baseUrl);
  }

  // 确保baseUrl不以斜杠结尾
  if (baseUrl.endsWith('/')) {
    baseUrl = baseUrl.slice(0, -1);
    console.log('移除尾部斜杠后的baseUrl:', baseUrl);
  }

  return baseUrl;
};

/**
 * 构建API URL，确保没有双斜杠问题
 * @param {string} path API路径
 * @returns {string} 完整的API URL
 */
const buildAPIUrl = (path) => {
  const baseUrl = getAPIBaseUrl();

  // 确保path以斜杠开头
  const apiPath = path.startsWith('/') ? path : `/${path}`;

  return `${baseUrl}${apiPath}`;
};

/**
 * 锁服务类
 */
class LockService {
  constructor() {
    // 设备信息
    this.deviceInfo = {
      deviceId: '',
      mac: '',
      name: 'D30智能锁',
      connected: false,
      batteryLevel: 0,
      lockStatus: {
        isOpen: false
      }
    };
    
    // 订单信息
    this.orderInfo = {
      orderId: '',
      status: -1, // -1未创建，0未支付，1已支付，2已完成，3已取消
      isPaid: false,
      amount: 0,
      duration: 0
    };
    
    // 回调函数
    this.callbacks = {
      onConnected: null,
      onDisconnected: null,
      onLockStatusChange: null,
      onBatteryUpdate: null,
      onError: null
    };
    
    // 初始化蓝牙管理器回调
    this._initCallbacks();
  }
  
  /**
   * 设置回调函数
   * @param {Object} callbacks 回调函数对象
   */
  setCallbacks(callbacks) {
    this.callbacks = {
      ...this.callbacks,
      ...callbacks
    };
    return this;
  }
  
  /**
   * 初始化蓝牙管理器回调
   * @private
   */
  _initCallbacks() {
    blueToothManager.setCallbacks({
      onConnected: (info) => {
        console.log('锁服务 - 设备连接成功:', info);
        this.deviceInfo.connected = true;
        this.deviceInfo.deviceId = info.deviceId;
        
        // 查询设备信息
        this._queryDeviceInfo().catch(err => {
          console.error('查询设备信息失败:', err);
        });
        
        // 连接成功后立即上报设备状态
        if (this.orderInfo.orderId) {
          console.log('锁服务 - 连接成功，上报设备状态');
          this._reportDeviceStatus({
            operationType: 1, // 1-开锁
            deviceStatus: 'normal'
          });
        }
        
        // 调用连接成功回调
        if (typeof this.callbacks.onConnected === 'function') {
          this.callbacks.onConnected(this.deviceInfo);
        }
      },
      
      onDisconnected: (info) => {
        console.log('锁服务 - 设备断开连接:', info);
        this.deviceInfo.connected = false;
        
        // 调用断开连接回调
        if (typeof this.callbacks.onDisconnected === 'function') {
          this.callbacks.onDisconnected(info);
        }
      },
      
      onLockStatusChange: (status) => {
        console.log('锁服务 - 锁状态变化:', status);

        // 更新设备信息中的锁状态
        if (!this.deviceInfo.lockStatus) {
          this.deviceInfo.lockStatus = {};
        }
        this.deviceInfo.lockStatus.isOpen = status.isOpen;

        // 调用锁状态变化回调
        if (typeof this.callbacks.onLockStatusChange === 'function') {
          this.callbacks.onLockStatusChange(status);
        }
        
        // 如果有订单ID，上报锁状态
        if (this.orderInfo.orderId) {
          this._reportDeviceStatus({
            operationType: status.isOpen ? 1 : 2, // 1-开锁，2-关锁
            deviceStatus: status.isOpen ? 'open' : 'closed'
          });
        }
      },
      
      onBatteryUpdate: (info) => {
        console.log('锁服务 - 电池电量更新:', info);
        this.deviceInfo.batteryLevel = info.batteryLevel;
        
        // 调用电池更新回调
        if (typeof this.callbacks.onBatteryUpdate === 'function') {
          this.callbacks.onBatteryUpdate(info);
        }
        
        // 如果有订单ID，上报电池状态
        if (this.orderInfo.orderId) {
          this._reportDeviceStatus({
            operationType: 1, // 默认为开锁操作
            deviceStatus: 'normal',
            battery: info.batteryLevel
          });
        }
      },
      
      onError: (error) => {
        console.error('锁服务 - 错误:', error);
        
        // 调用错误回调
        if (typeof this.callbacks.onError === 'function') {
          this.callbacks.onError(error);
        }
      }
    });
  }
  
  /**
   * 初始化蓝牙环境
   * @returns {Promise} 初始化结果
   */
  init() {
    console.log('锁服务 - 初始化蓝牙环境');
    
    // 先检查当前平台是否为Windows开发环境
    try {
      const systemInfo = wx.getAppBaseInfo ? wx.getAppBaseInfo() : uni.getSystemInfoSync();
      console.log('锁服务 - 系统信息:', systemInfo);
      
      const isDevTools = systemInfo.platform === 'devtools';
      // 修复system可能为undefined的问题
      const isWindows = systemInfo.platform === 'windows' || 
                       (systemInfo.system && typeof systemInfo.system === 'string' && 
                        systemInfo.system.toLowerCase().includes('windows'));
      const isMac = systemInfo.platform === 'mac';
      
      // 在非Mac开发环境下自动切换到模拟模式
      if (isDevTools && !isMac) {
        console.log('锁服务 - 非Mac开发环境自动切换到模拟模式');
        this.isSimulatedMode = true;
        this.isInitialized = true;
        
        // 显示提示
        uni.showToast({
          title: '当前平台不支持蓝牙调试，已切换到模拟模式',
          icon: 'none',
          duration: 3000
        });
        
        return Promise.resolve({
          success: true,
          message: '蓝牙环境初始化成功(模拟模式)',
          simulated: true
        });
      }
    } catch (e) {
      console.error('获取系统信息失败:', e);
      // 出错时也切换到模拟模式
      this.isSimulatedMode = true;
      this.isInitialized = true;
      
      return Promise.resolve({
        success: true,
        message: '蓝牙环境初始化成功(模拟模式-错误恢复)',
        simulated: true
      });
    }
    
    return blueToothManager.init()
      .then(res => {
        console.log('锁服务 - 蓝牙环境初始化成功', res);
        this.isInitialized = true;
        
        // 如果是模拟模式，设置模拟状态
        if (res.simulated) {
          this.isSimulatedMode = true;
          console.log('锁服务 - 使用模拟模式');
        }
        
        return res;
      })
      .catch(err => {
        console.log('锁服务 - 蓝牙环境初始化失败:', err);
        
        // 非Mac平台开发环境下，不显示错误
        try {
          // 使用新的API获取平台信息
          const systemInfo = wx.getAppBaseInfo ? wx.getAppBaseInfo() : uni.getSystemInfoSync();
          if (systemInfo.platform === 'devtools' && systemInfo.platform !== 'mac') {
            console.log('锁服务 - 当前为非Mac开发环境，蓝牙功能受限');
            this.isSimulatedMode = true;
            
            // 显示提示
            uni.showToast({
              title: '当前平台不支持蓝牙调试，已切换到模拟模式',
              icon: 'none',
              duration: 3000
            });
            
            return Promise.resolve({
              success: true,
              message: '蓝牙环境初始化成功(模拟模式)',
              simulated: true
            });
          }
        } catch (e) {
          console.error('获取系统信息失败:', e);
        }
        
        throw err;
      });
  }
  
  /**
   * 关闭蓝牙环境
   * @returns {Promise} 关闭结果
   */
  close() {
    console.log('锁服务 - 关闭蓝牙环境');

    return blueToothManager.close()
      .then((res) => {
        console.log('锁服务 - 蓝牙环境关闭成功');

        // 重置初始化状态，确保下次进入时能重新初始化
        this.isInitialized = false;
        this.isSimulatedMode = false;

        // 重置设备连接状态
        this.deviceInfo.connected = false;
        this.deviceInfo.deviceId = null;
        this.deviceInfo.batteryLevel = 0;
        this.deviceInfo.lockStatus = {
          isOpen: false
        };

        console.log('锁服务 - 状态已重置，下次进入将重新初始化');

        return {
          success: true,
          message: '蓝牙环境关闭成功'
        };
      })
      .catch((err) => {
        console.error('锁服务 - 蓝牙环境关闭失败:', err);

        // 即使关闭失败，也要重置状态
        this.isInitialized = false;
        this.isSimulatedMode = false;
        this.deviceInfo.connected = false;

        return Promise.reject({
          success: false,
          message: '蓝牙环境关闭失败',
          error: err
        });
      });
  }
  
  /**
   * 设置设备MAC地址
   * @param {string} mac 设备MAC地址
   */
  setDeviceMac(mac) {
    this.deviceInfo.mac = this._formatMacAddress(mac);
    console.log('设置设备MAC地址:', this.deviceInfo.mac);
    return this;
  }
  
  /**
   * 设置订单ID
   * @param {string} orderId 订单ID
   */
  setOrderId(orderId) {
    this.orderInfo.orderId = orderId;
    console.log('设置订单ID:', orderId);
    return this;
  }
  
  /**
   * 获取设备信息
   * @returns {Object} 设备信息
   */
  getDeviceInfo() {
    return this.deviceInfo;
  }
  
  /**
   * 获取订单信息
   * @returns {Object} 订单信息
   */
  getOrderInfo() {
    return this.orderInfo;
  }
  
  /**
   * 设置预期设备名称
   * @param {string} deviceName 设备名称
   */
  setExpectedDeviceName(deviceName) {
    console.log('设置预期设备名称:', deviceName);
    blueToothManager.setExpectedDeviceName(deviceName);
    this.deviceInfo.name = deviceName;
    return this;
  }
  
  /**
   * 直接连接设备（通过设备名称）
   * @param {string} deviceName 设备名称
   * @returns {Promise} 连接结果
   */
  directConnect(deviceName) {
    console.log('直接连接设备:', deviceName);
    
    // 设置预期设备名称
    this.setExpectedDeviceName(deviceName);
    
    // 如果是模拟模式，直接返回成功
    if (this.isSimulatedMode) {
      console.log('模拟模式下直接返回连接成功');
      this.deviceInfo.connected = true;
      this.deviceInfo.deviceId = 'simulated-device-id';
      
      // 延迟执行，模拟连接过程
      return new Promise(resolve => {
        setTimeout(() => {
          // 调用连接成功回调
          if (typeof this.callbacks.onConnected === 'function') {
            this.callbacks.onConnected(this.deviceInfo);
          }
          
          resolve({
            success: true,
            message: '连接成功(模拟模式)',
            deviceId: 'simulated-device-id',
            simulated: true
          });
        }, 1000);
      });
    }
    
    // 通过名称连接设备
    return blueToothManager.connectByName(deviceName)
      .then(res => {
        console.log('连接成功:', res);
        
        // 连接成功后自动认证
        if (!res.simulated) {
          console.log('自动执行认证流程');
          return this._autoAuthenticate();
        }
        
        return res;
      })
      .catch(err => {
        console.error('连接失败:', err);
        throw err;
      });
  }
  
  /**
   * 连接设备
   * @param {string} deviceMac 设备MAC地址或设备编码
   * @returns {Promise} 连接结果
   */
  connectDevice(deviceMac) {
    console.log('连接设备:', deviceMac);
    
    // 保存设备MAC地址
    this.setDeviceMac(deviceMac);
    
    // 如果是模拟模式，直接返回成功
    if (this.isSimulatedMode) {
      console.log('模拟模式下直接返回连接成功');
      this.deviceInfo.connected = true;
      this.deviceInfo.deviceId = 'simulated-device-id';
      
      // 延迟执行，模拟连接过程
      return new Promise(resolve => {
        setTimeout(() => {
          // 调用连接成功回调
          if (typeof this.callbacks.onConnected === 'function') {
            this.callbacks.onConnected(this.deviceInfo);
          }
          
          resolve({
            success: true,
            message: '连接成功(模拟模式)',
            deviceId: 'simulated-device-id',
            simulated: true
          });
        }, 1000);
      });
    }
    
    return this._doConnectDevice(deviceMac)
      .then(res => {
        console.log('连接成功:', res);
        
        // 连接成功后自动认证
        if (!res.simulated) {
          console.log('自动执行认证流程');
          return this._autoAuthenticate();
        }
        
        return res;
      })
      .catch(err => {
        console.error('连接失败:', err);
        throw err;
      });
  }
  
  /**
   * 自动执行认证流程
   * @returns {Promise} 认证结果
   * @private
   */
  _autoAuthenticate() {
    return blueToothManager.authenticate()
      .then(() => {
        console.log('认证成功');
        return {
          success: true,
          message: '连接并认证成功',
          deviceId: this.deviceInfo.deviceId
        };
      })
      .catch(err => {
        console.error('认证失败:', err);
        // 认证失败不影响连接状态，仍然返回连接成功
        return {
          success: true,
          message: '连接成功但认证失败',
          deviceId: this.deviceInfo.deviceId,
          authError: err
        };
      });
  }
  
  /**
   * 执行设备连接
   * @param {string} deviceMac 设备MAC地址或设备编码
   * @returns {Promise} 连接结果
   * @private
   */
  _doConnectDevice(deviceMac) {
    // 确保蓝牙已初始化
    if (!this.isInitialized) {
      return this.init().then(() => this._doConnectDevice(deviceMac));
    }
    
    return new Promise((resolve, reject) => {
      // 先获取已发现的设备
      uni.getBluetoothDevices({
        success: (res) => {
          console.log('获取已发现的设备:', res);
          
          // 查找匹配的设备
          const targetDevice = this._findTargetDevice(res.devices || []);
          
          if (targetDevice) {
            console.log('在已发现设备中找到目标设备:', targetDevice);
            
            // 连接找到的设备
            this._connectFoundDevice(targetDevice, resolve, reject);
          } else {
            console.log('未找到目标设备，开始扫描');
            
            // 开始扫描并连接
            this._scanAndConnect(deviceMac, resolve, reject);
          }
        },
        fail: (err) => {
          console.error('获取已发现的设备失败:', err);
          
          // 开始扫描并连接
          this._scanAndConnect(deviceMac, resolve, reject);
        }
      });
    });
  }
  
  /**
   * 扫描并连接设备
   * @param {string} deviceMac 设备MAC地址
   * @param {Function} resolve Promise解决函数
   * @param {Function} reject Promise拒绝函数
   * @private
   */
  _scanAndConnect(deviceMac, resolve, reject) {
    // 设置设备发现回调
    const originalDeviceFoundCallback = blueToothManager.onDeviceFoundCallback;
    
    // 临时设置设备发现回调，用于实时连接
    blueToothManager.onDeviceFoundCallback = (device) => {
      // 保留原始回调
      if (originalDeviceFoundCallback) {
        originalDeviceFoundCallback(device);
      }
      
      // 检查是否为目标设备
      if (this._isTargetDevice(device, deviceMac)) {
        console.log('扫描中发现目标设备:', device);
        
        // 停止扫描
        blueToothManager.stopScan().catch(() => {});
        
        // 恢复原始回调
        blueToothManager.onDeviceFoundCallback = originalDeviceFoundCallback;
        
        // 连接设备
        this._connectFoundDevice(device, resolve, reject);
      }
    };
    
    // 开始扫描
    blueToothManager._startScan(15000)
      .then(() => {
        // 恢复原始回调
        blueToothManager.onDeviceFoundCallback = originalDeviceFoundCallback;
        
        // 如果设备已连接，不再处理
        if (this.deviceInfo.connected) return;
        
        console.log('扫描完成，未找到目标设备');
        reject(new Error(`未找到MAC地址为 ${deviceMac} 的设备`));
      })
      .catch(err => {
        // 恢复原始回调
        blueToothManager.onDeviceFoundCallback = originalDeviceFoundCallback;
        
        // 如果设备已连接，不再处理
        if (this.deviceInfo.connected) return;
        
        console.error('扫描设备失败:', err);
        reject(err);
      });
  }
  
  /**
   * 连接发现的设备
   * @param {Object} device 设备对象
   * @param {Function} resolve Promise解析函数
   * @param {Function} reject Promise拒绝函数
   * @private
   */
  _connectFoundDevice(device, resolve, reject) {
    // 防止重复连接
    if (this.deviceInfo.connected) return;
    
    console.log('连接发现的设备:', device);
    
    // 连接设备
    blueToothManager.connect(device.deviceId)
      .then(connectRes => {
        console.log('连接设备成功:', connectRes);
        
        this.deviceInfo.connected = true;
        this.deviceInfo = {
          deviceId: device.deviceId,
          name: device.name || device.localName || 'D30智能锁',
          mac: device.deviceMac || this.deviceInfo.mac,
          isConnected: true,
          isAuthenticated: false,
          batteryLevel: 0,
          batteryPowered: false,
          version: 0,
          lockStatus: {
            isOpen: false
          }
        };
        
        // 认证设备
        return blueToothManager.authenticate();
      })
      .then(() => {
        console.log('设备认证成功');
        this.deviceInfo.isAuthenticated = true;
        
        // 查询设备信息
        return this._queryDeviceInfo();
      })
      .then(() => {
        // 调用连接成功回调
        if (this.callbacks.onConnected) {
          this.callbacks.onConnected(this.deviceInfo);
        }
        
        resolve({
          success: true,
          message: '连接成功'
        });
      })
      .catch(err => {
        console.error('连接或认证设备失败:', err);
        
        // 断开连接
        blueToothManager.disconnect().catch(() => {});
        
        // 如果连接失败，尝试使用模拟模式
        console.log('连接失败，切换到模拟模式');
        this.isSimulatedMode = true;
        this.deviceInfo = {
          deviceId: 'simulated-device',
          name: 'D30智能锁(模拟)',
          mac: this.deviceInfo.mac,
          isConnected: true,
          isAuthenticated: true,
          batteryLevel: 85,
          batteryPowered: true,
          version: 1,
          simulated: true,
          lockStatus: {
            isOpen: false
          }
        };
        
        // 调用连接成功回调
        if (this.callbacks.onConnected) {
          this.callbacks.onConnected(this.deviceInfo);
        }
        
        resolve({
          success: true,
          message: '连接成功(模拟模式)',
          simulated: true
        });
      });
  }
  
  /**
   * 查找目标设备
   * @param {Array} devices 设备列表
   * @returns {Object|null} 目标设备
   * @private
   */
  _findTargetDevice(devices) {
    if (!devices || devices.length === 0) return null;
    
    // 按优先级查找设备
    // 1. 设备名称精确匹配
    if (this.expectedDeviceName) {
      const nameMatch = devices.find(device => 
        (device.name === this.expectedDeviceName || device.localName === this.expectedDeviceName)
      );
      if (nameMatch) {
        console.log('找到设备名称精确匹配的设备:', this.expectedDeviceName);
        return nameMatch;
      }
    }
    
    // 2. MAC地址精确匹配
    if (this.deviceInfo.mac) {
      const macMatch = devices.find(device => 
        device.deviceMac && 
        this._formatMacAddress(device.deviceMac).toLowerCase() === this._formatMacAddress(this.deviceInfo.mac).toLowerCase()
      );
      if (macMatch) {
        console.log('找到MAC地址精确匹配的设备:', this.deviceInfo.mac);
        return macMatch;
      }
    }
    
    // 3. 服务UUID匹配
    const lockServiceUUID = "0000FFC0-0000-1000-8000-00805F9B34FB";
    const serviceMatch = devices.find(device => {
      if (device.advertisServiceUUIDs && Array.isArray(device.advertisServiceUUIDs)) {
        return device.advertisServiceUUIDs.some(uuid => uuid.includes(lockServiceUUID));
      }
      return false;
    });
    if (serviceMatch) {
      console.log('找到服务UUID匹配的设备');
      return serviceMatch;
    }
    
    // 4. 设备名称包含关键词
    const keywordMatch = devices.find(device => {
      const name = (device.name || device.localName || '').toUpperCase();
      return name && (name.includes('D30') || name.includes('智能锁') || name.includes('LOCK') || name.includes('KSJ'));
    });
    if (keywordMatch) {
      console.log('找到名称包含关键词的设备');
      return keywordMatch;
    }
    
    // 5. MAC地址最后4位匹配
    if (this.deviceInfo.mac) {
      const lastFourChars = this._formatMacAddress(this.deviceInfo.mac).replace(/:/g, '').slice(-4).toLowerCase();
      const lastFourMatch = devices.find(device => {
        if (!device.deviceMac) return false;
        return device.deviceMac.toLowerCase().includes(lastFourChars);
      });
      if (lastFourMatch) {
        console.log('找到MAC地址最后4位匹配的设备');
        return lastFourMatch;
      }
    }
    
    // 没有找到匹配的设备
    return null;
  }
  
  /**
   * 检查设备是否为目标设备
   * @param {Object} device 设备对象
   * @param {string} targetMac 目标MAC地址
   * @param {string} expectedName 预期设备名称
   * @returns {boolean} 是否为目标设备
   * @private
   */
  _isTargetDevice(device, targetMac, expectedName) {
    if (!device) return false;
    
    const deviceName = device.name || device.localName || '';
    const deviceMac = device.deviceMac || '';
    
    // 优先级1: 设备名称精确匹配
    if (expectedName && deviceName === expectedName) {
      console.log('设备名称精确匹配成功:', deviceName);
      return true;
    }
    
    // 优先级2: MAC地址匹配
    if (targetMac && deviceMac && 
        this._formatMacAddress(deviceMac).toLowerCase() === this._formatMacAddress(targetMac).toLowerCase()) {
      console.log('MAC地址匹配成功:', deviceMac);
      return true;
    }
    
    // 优先级3: 模糊匹配
    // 检查设备名称是否包含特定关键词
    if (deviceName) {
      const keywords = ['D30', '智能锁', 'LOCK', 'KSJ'];
      const upperName = deviceName.toUpperCase();
      for (const keyword of keywords) {
        if (upperName.includes(keyword)) {
          console.log('设备名称包含关键词匹配成功:', keyword);
          return true;
        }
      }
    }
    
    // 检查MAC地址最后4位
    if (targetMac && deviceMac) {
      const lastFourChars = this._formatMacAddress(targetMac).replace(/:/g, '').slice(-4).toLowerCase();
      if (lastFourChars && deviceMac.toLowerCase().includes(lastFourChars)) {
        console.log('MAC地址最后4位匹配成功:', lastFourChars);
        return true;
      }
    }
    
    return false;
  }
  
  /**
   * 从设备对象中解析MAC地址
   * @param {Object} device 设备对象
   * @returns {string|null} 解析出的MAC地址，如果无法解析则返回null
   * @private
   */
  _parseDeviceMac(device) {
    if (!device) {
      console.log('设备对象为空，无法解析MAC地址');
      return null;
    }
    
    console.log('尝试解析设备MAC地址:', device.name || device.localName || device.deviceId || '未知设备');
    
    // 如果设备对象已经包含MAC地址，直接返回
    if (device.deviceMac) {
      console.log('设备已包含MAC地址:', device.deviceMac);
      return this._formatMacAddress(device.deviceMac);
    }
    
    // 尝试从广播数据中提取MAC地址
    if (device.advertisData) {
      try {
        const hexData = this._arrayBufferToHex(device.advertisData);
        console.log('设备广播数据(HEX):', hexData);
        
        // 一些设备会在广播数据中包含MAC地址，通常在特定位置
        // 这里需要根据具体设备的广播数据格式进行解析
        if (hexData && hexData.length >= 12) {
          // 假设MAC地址在广播数据的前12个字符
          const possibleMac = hexData.substring(0, 12);
          console.log('从广播数据提取的可能MAC地址:', possibleMac);
          return this._formatMacAddress(possibleMac);
        }
      } catch (e) {
        console.error('解析广播数据中的MAC地址失败:', e);
      }
    } else {
      console.log('设备无广播数据');
    }
    
    // 尝试从设备名称中提取MAC地址
    const name = device.name || device.localName || '';
    if (name) {
      console.log('尝试从设备名称中提取MAC地址:', name);
      const macRegex = /([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})/;
      const match = name.match(macRegex);
      if (match) {
        console.log('从设备名称中提取到MAC地址:', match[0]);
        return this._formatMacAddress(match[0]);
      }
    } else {
      console.log('设备无名称信息');
    }
    
    // 如果设备ID看起来像MAC地址，使用它
    if (device.deviceId) {
      console.log('检查设备ID是否为MAC地址:', device.deviceId);
      if (device.deviceId.includes(':') || device.deviceId.length === 12) {
        console.log('设备ID可能是MAC地址');
        return this._formatMacAddress(device.deviceId);
      }
    } else {
      console.log('设备无deviceId');
    }
    
    // 如果无法解析MAC地址，但我们知道设备MAC地址，使用它
    if (this.deviceInfo.mac) {
      console.log('使用预设的MAC地址:', this.deviceInfo.mac);
      return this.deviceInfo.mac;
    }
    
    console.log('无法解析设备MAC地址');
    return null;
  }
  
  /**
   * 将ArrayBuffer转换为十六进制字符串
   * @param {ArrayBuffer} buffer ArrayBuffer数据
   * @returns {string} 十六进制字符串
   * @private
   */
  _arrayBufferToHex(buffer) {
    return Array.prototype.map.call(
      new Uint8Array(buffer),
      x => ('00' + x.toString(16)).slice(-2)
    ).join('');
  }
  
  /**
   * 断开连接
   * @returns {Promise} 断开结果
   */
  disconnect() {
    console.log('锁服务 - 断开连接');
    
    return blueToothManager.disconnect()
      .then(() => {
        this.deviceInfo.connected = false;
        
        return {
          success: true,
          message: '设备已断开连接'
        };
      })
      .catch((err) => {
        console.error('锁服务 - 断开连接失败:', err);
        
        // 即使断开失败，也认为已断开
        this.deviceInfo.connected = false;
        
        return Promise.reject({
          success: false,
          message: '断开连接失败',
          error: err
        });
      });
  }
  
  /**
   * 查询设备信息
   * @returns {Promise} 查询结果
   */
  queryDeviceInfo() {
    console.log('查询设备信息');
    
    // 如果是模拟模式，返回模拟数据
    if (this.isSimulatedMode) {
      // 模拟电池电量
      const batteryLevel = Math.floor(Math.random() * 30) + 70; // 70-99之间的随机数
      this.deviceInfo.batteryLevel = batteryLevel;
      
      // 调用电池更新回调
      if (typeof this.callbacks.onBatteryUpdate === 'function') {
        this.callbacks.onBatteryUpdate({
          batteryLevel: batteryLevel
        });
      }
      
      return Promise.resolve({
        success: true,
        message: '查询设备信息成功(模拟模式)',
        batteryLevel: batteryLevel,
        simulated: true
      });
    }
    
    // 使用固定的查询设备信息命令 - 参考Python脚本中的命令
    const QUERY_DEVICE_INFO_CMD = "66f00077";
    
    // 发送查询命令
    return blueToothManager.writeCharacteristic(QUERY_DEVICE_INFO_CMD)
      .then(res => {
        console.log('查询设备信息命令发送成功:', res);
        
        // 实际的设备信息会通过特征值变化回调返回
        // 在_handleCharacteristicValueChange中处理
        
        return {
          success: true,
          message: '查询设备信息命令发送成功'
        };
      })
      .catch(err => {
        console.error('查询设备信息命令发送失败:', err);
        return Promise.reject(err);
      });
  }
  
  /**
   * 内部查询设备信息方法
   * @private
   * @returns {Promise} 查询结果
   */
  _queryDeviceInfo() {
    console.log('锁服务 - 内部查询设备信息');
    
    // 如果是模拟模式，返回模拟数据
    if (this.isSimulatedMode) {
      // 模拟电池电量
      const batteryLevel = Math.floor(Math.random() * 30) + 70; // 70-99之间的随机数
      this.deviceInfo.batteryLevel = batteryLevel;
      
      // 调用电池更新回调
      if (typeof this.callbacks.onBatteryUpdate === 'function') {
        this.callbacks.onBatteryUpdate({
          batteryLevel: batteryLevel
        });
      }
      
      return Promise.resolve({
        success: true,
        message: '查询设备信息成功(模拟模式)',
        batteryLevel: batteryLevel,
        simulated: true
      });
    }
    
    return this.queryDeviceInfo();
  }
  
  /**
   * 查询锁状态
   * @returns {Promise} 查询结果
   */
  queryLockStatus() {
    console.log('查询锁状态');
    
    // 如果是模拟模式，返回模拟数据
    if (this.isSimulatedMode) {
      // 模拟锁状态
      const isOpen = Math.random() > 0.5;
      
      // 调用锁状态变化回调
      if (typeof this.callbacks.onLockStatusChange === 'function') {
        this.callbacks.onLockStatusChange({
          isOpen: isOpen
        });
      }
      
      return Promise.resolve({
        success: true,
        message: '查询锁状态成功(模拟模式)',
        isOpen: isOpen,
        simulated: true
      });
    }
    
    return blueToothManager.queryLockStatus();
  }
  
  /**
   * 开锁
   * @param {Object} options 开锁选项
   * @returns {Promise} 开锁结果
   */
  openLock(options = {}) {
    console.log('锁服务 - 开锁:', options);
    
    // 如果设置了忽略订单状态，直接执行开锁
    if (options.ignoreOrderStatus || options.force) {
      console.log('锁服务 - 忽略订单状态检查，直接开锁');
      return this._executeOpenLock(options);
    }
    
    // 检查是否设置了订单ID
    if (!this.orderInfo.orderId) {
      console.error('锁服务 - 未设置订单ID，无法开锁');
      return Promise.reject(new Error('未设置订单ID，无法开锁'));
    }
    
    // 获取本地订单状态
    const localOrderStatus = this._getLocalOrderStatus(this.orderInfo.orderId);
    console.log('锁服务 - 本地订单状态:', localOrderStatus);
    
    // 如果本地缓存有支付成功的记录且不强制检查，直接开锁
    if (localOrderStatus && localOrderStatus.status === 1 && !options.forceCheck) {
      console.log('锁服务 - 本地订单状态已支付，直接开锁');
      // 更新订单信息
      this.orderInfo.status = 1;
      this.orderInfo.isPaid = true;
      return this._executeOpenLock({...options, force: true});
    }
    
    // 否则，从服务器检查订单状态
    console.log('锁服务 - 检查订单状态');
    return this._checkOrderStatus(this.orderInfo.orderId)
      .then(orderInfo => {
        console.log('锁服务 - 订单状态检查结果:', orderInfo);
        
        // 更新订单信息
        this.orderInfo = {
          ...this.orderInfo,
          status: orderInfo.status,
          isPaid: orderInfo.status === 1,
          amount: orderInfo.amount || this.orderInfo.amount,
          duration: orderInfo.duration || this.orderInfo.duration
        };
        
        // 检查订单状态是否已支付
        if (orderInfo.status !== 1) {
          console.error('锁服务 - 订单未支付，无法开锁');
          return Promise.reject(new Error('订单未支付，无法开锁'));
        }
        
        // 订单已支付，执行开锁
        console.log('锁服务 - 订单已支付，执行开锁');
        return this._executeOpenLock({...options, force: true});
      })
      .catch(err => {
        // 如果网络请求失败，但本地订单状态为已支付，尝试开锁
        if (localOrderStatus && localOrderStatus.status === 1) {
          console.warn('锁服务 - 订单状态检查失败，但本地订单状态为已支付，尝试开锁:', err);
          // 更新订单信息
          this.orderInfo.status = 1;
          this.orderInfo.isPaid = true;
          return this._executeOpenLock({...options, force: true});
        }
        
        // 如果强制开锁选项设置为true，即使订单状态未知也尝试开锁
        if (options.force) {
          console.warn('锁服务 - 订单状态检查失败，但设置了强制开锁，尝试开锁:', err);
          return this._executeOpenLock({...options, force: true});
        }
        
        console.error('锁服务 - 开锁前检查订单状态失败:', err);
        return Promise.reject(err);
      });
  }
  
  /**
   * 执行开锁操作
   * @param {Object} options 开锁选项
   * @returns {Promise} 开锁结果
   * @private
   */
  _executeOpenLock(options = {}) {
    console.log('锁服务 - 执行开锁操作:', options);
    
    // 获取锁编号
    const lockNumber = options.lockNumber || 1;
    
    // 如果模拟模式，直接返回成功
    if (this.isSimulatedMode) {
      console.log('锁服务 - 模拟模式，模拟开锁成功');
      
      // 模拟开锁成功
      setTimeout(() => {
        // 更新锁状态为开启
        this._updateLockStatus(true);
        
        // 如果有设置回调函数，调用锁状态变化回调
        if (typeof this.callbacks.onLockStatusChange === 'function') {
          this.callbacks.onLockStatusChange({ isOpen: true });
        }
        
        // 延迟关闭锁
        setTimeout(() => {
          // 更新锁状态为关闭
          this._updateLockStatus(false);
          
          // 如果有设置回调函数，调用锁状态变化回调
          if (typeof this.callbacks.onLockStatusChange === 'function') {
            this.callbacks.onLockStatusChange({ isOpen: false });
          }
        }, 5000);
      }, 500);
      
      // 如果有订单ID，更新订单使用状态
      if (this.orderInfo.orderId) {
        this._updateOrderUseStatus(this.orderInfo.orderId, 1).catch(err => {
          console.warn('锁服务 - 更新订单使用状态失败:', err);
        });
      }
      
      return Promise.resolve({
        success: true,
        message: '开锁成功(模拟模式)',
        simulated: true
      });
    }
    
    // 检查设备是否已连接
    if (!this.deviceInfo.connected) {
      return Promise.reject(new Error('设备未连接，无法开锁'));
    }
    
    // 检查是否已认证
    if (!blueToothManager.isAuthenticated) {
      console.log('锁服务 - 设备未认证，先进行认证');
      
      return blueToothManager.authenticate()
        .then(() => {
          console.log('锁服务 - 认证成功，继续开锁');
          // 根据options.singleCommand参数决定是否只发送一次开锁命令
          const stopAfterFirst = options.singleCommand === true;
          return blueToothManager.openLock(lockNumber, stopAfterFirst);
        })
        .then(res => {
          console.log('锁服务 - 开锁成功:', res);
          
          // 更新锁状态为开启
          this._updateLockStatus(true);
          
          // 如果有订单ID，更新订单使用状态
          if (this.orderInfo.orderId) {
            this._updateOrderUseStatus(this.orderInfo.orderId, 1).catch(err => {
              console.warn('锁服务 - 更新订单使用状态失败:', err);
            });
          }
          
          // 移除延迟再次发送开锁命令的代码
          
          return {
            success: true,
            message: '开锁成功'
          };
        })
        .catch(err => {
          console.error('锁服务 - 开锁失败:', err);
          
          // 如果设置了重试选项，再尝试一次
          if (options.retry || options.force) {
            console.log('锁服务 - 开锁失败，尝试重新认证后再次开锁');
            return blueToothManager.authenticate()
              .then(() => {
                console.log('锁服务 - 重新认证成功，再次尝试开锁');
                // 根据options.singleCommand参数决定是否只发送一次开锁命令
                const stopAfterFirst = options.singleCommand === true;
                return blueToothManager.openLock(lockNumber, stopAfterFirst);
              })
              .then(res => {
                console.log('锁服务 - 重试开锁成功:', res);
                
                // 更新锁状态为开启
                this._updateLockStatus(true);
                
                // 如果有订单ID，更新订单使用状态
                if (this.orderInfo.orderId) {
                  this._updateOrderUseStatus(this.orderInfo.orderId, 1).catch(err => {
                    console.warn('锁服务 - 更新订单使用状态失败:', err);
                  });
                }
                
                return {
                  success: true,
                  message: '重试开锁成功'
                };
              })
              .catch(retryErr => {
                console.error('锁服务 - 重试开锁也失败:', retryErr);
                return Promise.reject(retryErr);
              });
          }
          
          return Promise.reject(err);
        });
    }
    
    // 已认证，直接开锁
    console.log('锁服务 - 设备已认证，直接开锁');
    
    // 根据options.singleCommand参数决定是否只发送一次开锁命令
    const stopAfterFirst = options.singleCommand === true;
    
    return blueToothManager.openLock(lockNumber, stopAfterFirst)
      .then(res => {
        console.log('锁服务 - 开锁成功:', res);
        
        // 更新锁状态为开启
        this._updateLockStatus(true);
        
        // 如果有订单ID，更新订单使用状态
        if (this.orderInfo.orderId) {
          this._updateOrderUseStatus(this.orderInfo.orderId, 1).catch(err => {
            console.warn('锁服务 - 更新订单使用状态失败:', err);
          });
        }
        
        // 移除延迟再次发送开锁命令的代码块
        
        return {
          success: true,
          message: '开锁成功'
        };
      })
      .catch(err => {
        console.error('锁服务 - 开锁失败:', err);
        
        // 如果设置了重试选项，再尝试一次
        if (options.retry || options.force) {
          console.log('锁服务 - 开锁失败，尝试重新认证后再次开锁');
          return blueToothManager.authenticate()
            .then(() => {
              console.log('锁服务 - 重新认证成功，再次尝试开锁');
              // 根据options.singleCommand参数决定是否只发送一次开锁命令
              const stopAfterFirst = options.singleCommand === true;
              return blueToothManager.openLock(lockNumber, stopAfterFirst);
            })
            .then(res => {
              console.log('锁服务 - 重试开锁成功:', res);
              
              // 更新锁状态为开启
              this._updateLockStatus(true);
              
              // 如果有订单ID，更新订单使用状态
              if (this.orderInfo.orderId) {
                this._updateOrderUseStatus(this.orderInfo.orderId, 1).catch(err => {
                  console.warn('锁服务 - 更新订单使用状态失败:', err);
                });
              }
              
              return {
                success: true,
                message: '重试开锁成功'
              };
            })
            .catch(retryErr => {
              console.error('锁服务 - 重试开锁也失败:', retryErr);
              return Promise.reject(retryErr);
            });
        }
        
        return Promise.reject(err);
      });
  }
  
  /**
   * 创建订单
   * @param {Object} orderData 订单数据
   * @returns {Promise} 创建结果
   */
  createOrder(orderData) {
    console.log('锁服务 - 创建订单:', orderData);
    return this._createNewOrder(orderData);
  }

  /**
   * 创建新订单
   * @param {Object} orderData 订单数据
   * @returns {Promise} 创建结果
   * @private
   */
  _createNewOrder(orderData) {
    return new Promise((resolve, reject) => {
      // 确保API基础URL存在
      let baseUrl = 'https://api.jycb888.com';
      if (typeof API !== 'undefined' && API.baseUrl) {
        baseUrl = API.baseUrl;
      }

      // 确保baseUrl不以斜杠结尾
      if (baseUrl.endsWith('/')) {
        baseUrl = baseUrl.slice(0, -1);
      }

      // 构建完整URL，确保没有双斜杠
      const url = `${baseUrl}/api/wx/miniapp/order/create`;

      console.log('创建订单请求URL:', url);

      // 发送请求
      post(url, orderData)
        .then(res => {
          console.log('锁服务 - 创建订单成功:', res);
          resolve({
            orderId: res.data.id || res.data.orderId
          });
        })
        .catch(err => {
          console.error('锁服务 - 创建订单失败:', err);
          reject({
            success: false,
            message: '创建订单失败',
            error: err
          });
        });
    });
  }
  
  /**
   * 支付订单
   * @param {string|number} orderId 订单ID
   * @returns {Promise} 支付结果
   */
  payOrder(orderId) {
    console.log('锁服务 - 支付订单:', orderId);
    
    return new Promise((resolve, reject) => {
      if (!orderId) {
        reject(new Error('订单ID不能为空'));
        return;
      }
      
      // 构建API URL - 使用正确的支付接口路径
      const url = buildAPIUrl(`/api/wx/miniapp/order/pay/${orderId}`);

      console.log('支付订单请求URL:', url);
      
      // 请求支付接口
      uni.request({
        url: url,
        method: 'POST',
        header: {
          'Authorization': uni.getStorageSync('token'),
          'Content-Type': 'application/json'
        },
        success: (res) => {
          console.log('锁服务 - 支付接口响应:', res);
          if (res.statusCode === 200 && res.data && (res.data.code === 0 || res.data.code === 200)) {
            const payParams = res.data.data;
            console.log('锁服务 - 支付参数:', payParams);
            
            // 根据新的API格式处理支付参数
            const nonceStr = payParams.nonceStr;
            const timeStamp = payParams.timeStamp;
            const packageValue = payParams.packageValue;
            const appId = payParams.appId;
            const signType = payParams.signType;
            const paySign = payParams.paySign;

            console.log('支付参数详情:', {
              appId,
              timeStamp,
              nonceStr,
              packageValue,
              signType,
              paySign
            });

            // 验证支付参数
            if (!timeStamp || !nonceStr || !packageValue || !signType || !paySign) {
              console.error('支付参数不完整:', {
                timeStamp: !!timeStamp,
                nonceStr: !!nonceStr,
                packageValue: !!packageValue,
                signType: !!signType,
                paySign: !!paySign
              });
              reject(new Error('支付参数不完整'));
              return;
            }

            // 调用微信支付
            uni.requestPayment({
              provider: 'wxpay',
              timeStamp: timeStamp,
              nonceStr: nonceStr,
              package: packageValue,
              signType: signType,
              paySign: paySign,
              success: (payRes) => {
                console.log('锁服务 - 支付成功:', payRes);
                
                // 支付成功后，立即保存本地订单状态（为防止网络问题）
                this._updateLocalOrderStatus(orderId, {
                  status: 1, // 已支付状态
                  isPaid: true,
                  orderId: orderId
                });
                
                // 支付成功后，刷新订单状态以确保状态同步
                this._refreshOrderStatus(orderId)
                  .then(() => {
                    resolve({
                      success: true,
                      message: '支付成功',
                      payResult: payRes
                    });
                  })
                  .catch(err => {
                    // 即使刷新失败，仍然返回支付成功
                    console.warn('锁服务 - 刷新订单状态失败，但支付已成功:', err);
                    resolve({
                      success: true,
                      message: '支付成功，但订单状态未更新',
                      payResult: payRes
                    });
                  });
              },
              fail: (err) => {
                console.error('锁服务 - 支付失败:', err);
                reject(err);
              }
            });
          } else {
            console.error('锁服务 - 获取支付参数失败:', res);
            reject(new Error(res.data && res.data.message ? res.data.message : '获取支付参数失败'));
          }
        },
        fail: (err) => {
          console.error('锁服务 - 支付接口请求失败:', err);
          reject(err);
        }
      });
    });
  }
  
  /**
   * 刷新订单状态
   * @param {string|number} orderId 订单ID
   * @returns {Promise} 更新结果
   * @private
   */
  _refreshOrderStatus(orderId) {
    console.log('锁服务 - 刷新订单状态:', orderId);
    
    return new Promise((resolve, reject) => {
      // 获取API基础URL
      const baseUrl = getAPIBaseUrl();
      const url = `${baseUrl}/api/wx/miniapp/order/${orderId}`;
      
      console.log('刷新订单状态请求URL:', url);
      
      // 请求订单状态
      uni.request({
        url: url,
        method: 'GET',
        header: {
          'Authorization': uni.getStorageSync('token')
        },
        success: (res) => {
          if (res.statusCode === 200 && res.data && res.data.code === 200) {
            const orderInfo = res.data.data;
            console.log('锁服务 - 订单状态更新成功:', orderInfo);
            
            // 更新本地存储的订单状态
            this._updateLocalOrderStatus(orderId, orderInfo);
            
            resolve(orderInfo);
          } else {
            console.error('锁服务 - 获取订单状态失败:', res);
            reject(new Error(res.data && res.data.message ? res.data.message : '获取订单状态失败'));
          }
        },
        fail: (err) => {
          console.error('锁服务 - 订单状态接口请求失败:', err);
          reject(err);
        }
      });
    });
  }
  
  /**
   * 更新本地存储的订单状态
   * @param {string|number} orderId 订单ID
   * @param {Object} orderInfo 订单信息
   * @private
   */
  _updateLocalOrderStatus(orderId, orderInfo) {
    console.log('锁服务 - 更新本地订单状态:', orderId, orderInfo);
    
    // 保存到本地存储
    try {
      const key = `order_${orderId}`;
      uni.setStorageSync(key, JSON.stringify(orderInfo));
    } catch (e) {
      console.error('锁服务 - 更新本地订单状态失败:', e);
    }
  }
  
  /**
   * 调用微信支付
   * @param {Object} payData 支付参数
   * @returns {Promise} 支付结果
   * @private
   */
  _callWxPay(payData) {
    console.log('锁服务 - 支付参数:', payData);
    
    // 特殊处理package字段，可能是packageValue
    const payParams = { ...payData };
    
    // 如果没有package字段但有packageValue字段，使用packageValue
    if (!payParams.package && payParams.packageValue) {
      payParams.package = payParams.packageValue;
      console.log('使用packageValue字段作为package:', payParams.package);
    }
    
    // 检查支付参数是否完整
    const requiredFields = ['timeStamp', 'nonceStr', 'package', 'signType', 'paySign'];
    const missingFields = [];
    
    for (const field of requiredFields) {
      if (!payParams[field]) {
        missingFields.push(field);
      }
    }
    
    if (missingFields.length > 0) {
      console.error('锁服务 - 支付参数不完整:', payParams);
      console.error('锁服务 - 缺失的支付参数字段:', missingFields.join(', '));
      return Promise.reject(new Error(`支付参数不完整，缺少字段: ${missingFields.join(', ')}`));
    }
    
    return new Promise((resolve, reject) => {
      uni.requestPayment({
        provider: 'wxpay',
        timeStamp: payParams.timeStamp,
        nonceStr: payParams.nonceStr,
        package: payParams.package,
        signType: payParams.signType,
        paySign: payParams.paySign,
        success: (res) => {
          console.log('锁服务 - 支付成功:', res);
          resolve(res);
        },
        fail: (err) => {
          console.error('锁服务 - 支付失败:', err);
          reject(err);
        }
      });
    });
  }
  
  /**
   * 结束使用
   * @param {string} orderId 订单ID
   * @returns {Promise} 结束结果
   */
  endUse(orderId) {
    console.log('锁服务 - 结束使用:', orderId);
    
    // 使用指定的订单ID或当前订单ID
    const targetOrderId = orderId || this.orderInfo.orderId;
    
    if (!targetOrderId) {
      return Promise.reject({
        success: false,
        message: '订单ID不能为空'
      });
    }
    
    // 上报设备状态
    return this._reportDeviceStatus({
      useStatus: 2 // 2表示已结束
    })
      .then(() => {
        // 确保API基础URL存在
        let baseUrl = 'https://api.jycb888.com';
        if (typeof API !== 'undefined' && API.baseUrl) {
          baseUrl = API.baseUrl;
        }

        // 确保baseUrl不以斜杠结尾
        if (baseUrl.endsWith('/')) {
          baseUrl = baseUrl.slice(0, -1);
        }

        // 构建完整URL
        const url = `${baseUrl}/api/wx/miniapp/device/end-use/${targetOrderId}`;
        
        console.log('结束使用请求URL:', url);
        
        // 结束使用
        return post(url);
      })
      .then((res) => {
        if (res.code === 200) {
          // 更新订单状态
          this.orderInfo.status = 2; // 已完成
          
          // 断开设备连接
          return this.disconnect();
        } else {
          return Promise.reject(new Error('结束使用失败'));
        }
      })
      .then(() => {
        return {
          success: true,
          message: '结束使用成功'
        };
      })
      .catch((err) => {
        console.error('锁服务 - 结束使用失败:', err);
        return Promise.reject({
          success: false,
          message: '结束使用失败',
          error: err
        });
      });
  }
  
  /**
   * 格式化MAC地址
   * @param {string} mac MAC地址
   * @returns {string} 格式化后的MAC地址
   * @private
   */
  _formatMacAddress(mac) {
    if (!mac) return '';
    
    // 移除所有非字母数字字符
    let cleanMac = mac.replace(/[^A-Fa-f0-9]/g, '');
    
    // 确保是12位
    if (cleanMac.length !== 12) {
      console.warn('锁服务 - 无效的MAC地址:', mac);
      return mac;
    }
    
    // 转换为标准格式 (XX:XX:XX:XX:XX:XX)
    return cleanMac.match(/.{2}/g).join(':').toUpperCase();
  }

  /**
   * 检查订单状态
   * @param {string} orderId 订单ID
   * @returns {Promise} 订单状态
   * @private
   */
  _checkOrderStatus(orderId) {
    console.log('锁服务 - 检查订单状态:', orderId);
    
    // 确保API基础URL存在
    let baseUrl = 'https://api.jycb888.com';
    if (typeof API !== 'undefined' && API.baseUrl) {
      baseUrl = API.baseUrl;
    }

    // 确保baseUrl不以斜杠结尾
    if (baseUrl.endsWith('/')) {
      baseUrl = baseUrl.slice(0, -1);
    }

    // 构建完整URL
    const url = `${baseUrl}/api/wx/miniapp/order/${orderId}`;
    
    console.log('检查订单状态请求URL:', url);
    
    return get(url)
      .then((res) => {
        if (res.code === 200 && res.data) {
          const orderData = res.data;
          
          // 更新订单信息
          this.orderInfo.status = orderData.status;
          this.orderInfo.isPaid = orderData.status === 1;
          this.orderInfo.amount = orderData.amount;
          
          return {
            isPaid: this.orderInfo.isPaid,
            status: this.orderInfo.status
          };
        } else {
          return Promise.reject(new Error('获取订单信息失败'));
        }
      });
  }

  /**
   * 获取位置信息
   * @returns {Promise} 位置信息
   * @private
   */
  _getLocation() {
    return new Promise((resolve) => {
      uni.getLocation({
        type: 'gcj02', // 国测局坐标
        success: (res) => {
          resolve({
            latitude: res.latitude,
            longitude: res.longitude,
            accuracy: res.accuracy
          });
        },
        fail: (err) => {
          console.error('获取位置信息失败:', err);
          resolve({
            latitude: 0,
            longitude: 0,
            accuracy: 0
          });
        }
      });
    });
  }

  /**
   * 上报设备状态
   * @param {Object} extraData 额外数据
   * @returns {Promise} 上报结果
   * @private
   */
  _reportDeviceStatus(extraData = {}) {
    // 如果没有订单ID，不上报
    if (!this.orderInfo.orderId) {
      return Promise.resolve();
    }
    
    console.log('锁服务 - 上报设备状态:', extraData);
    
    // 获取位置信息
    return this._getLocation()
      .then((location) => {
        // 构建上报数据 - 根据API文档格式
        const reportData = {
          orderId: this.orderInfo.orderId,
          deviceId: parseInt(this.deviceInfo.deviceId) || 0,
          deviceCode: this.deviceInfo.mac || '',
          operationType: extraData.operationType || 1, // 1-开锁，2-关锁
          latitude: location.latitude,
          longitude: location.longitude,
          address: location.address || '',
          deviceStatus: extraData.deviceStatus || 'normal',
          battery: this.deviceInfo.batteryLevel || 0
        };
        
        console.log('上报设备状态数据:', reportData);
        
        // 确保API基础URL存在
        let baseUrl = 'https://api.jycb888.com';
        if (typeof API !== 'undefined' && API.baseUrl) {
          baseUrl = API.baseUrl;
        }

        // 确保baseUrl不以斜杠结尾
        if (baseUrl.endsWith('/')) {
          baseUrl = baseUrl.slice(0, -1);
        }
        
        // 构建完整URL - 使用正确的设备状态上报API路径
        const url = `${baseUrl}/api/wx/miniapp/device/unlock`;
        
        console.log('上报设备状态请求URL:', url);
        
        // 上报设备状态
        return post(url, reportData);
      })
      .then((res) => {
        if (res.code === 200) {
          console.log('锁服务 - 设备状态上报成功');
          return {
            success: true,
            message: '设备状态上报成功'
          };
        } else {
          return Promise.reject(new Error('设备状态上报失败'));
        }
      })
      .catch((err) => {
        console.error('锁服务 - 设备状态上报失败:', err);
        return Promise.reject({
          success: false,
          message: '设备状态上报失败',
          error: err
        });
      });
  }

  /**
   * 从本地获取订单状态
   * @param {string|number} orderId 订单ID
   * @returns {Object|null} 订单信息
   * @private
   */
  _getLocalOrderStatus(orderId) {
    if (!orderId) return null;
    
    try {
      // 从本地存储获取订单信息
      const key = `order_${orderId}`;
      const orderInfoStr = uni.getStorageSync(key);
      
      if (orderInfoStr) {
        try {
          const orderInfo = JSON.parse(orderInfoStr);
          console.log('锁服务 - 从本地缓存获取订单状态:', orderInfo);
          return orderInfo;
        } catch (e) {
          console.error('锁服务 - 解析本地订单信息失败:', e);
          return null;
        }
      }
    } catch (e) {
      console.error('锁服务 - 获取本地订单信息失败:', e);
    }
    
    return null;
  }

  /**
   * 更新订单使用状态
   * @param {string|number} orderId 订单ID
   * @param {number} useStatus 使用状态 0-未使用 1-使用中 2-已完成
   * @returns {Promise} 更新结果
   * @private
   */
  _updateOrderUseStatus(orderId, useStatus) {
    console.log('锁服务 - 更新订单使用状态:', orderId, useStatus);
    
    // 返回空的成功Promise，不再发送请求
    return Promise.resolve(null);
  }

  /**
   * 更新锁状态
   * @param {boolean} isOpen 是否开锁
   * @private
   */
  _updateLockStatus(isOpen) {
    console.log('锁服务 - 更新锁状态:', isOpen);

    // 更新设备信息中的锁状态
    if (!this.deviceInfo.lockStatus) {
      this.deviceInfo.lockStatus = {};
    }
    this.deviceInfo.lockStatus.isOpen = isOpen;

    // 调用旧的回调（保持兼容性）
    if (this.onLockStatusChanged) {
      this.onLockStatusChanged({ isOpen: isOpen });
    }

    // 调用新的回调系统
    if (typeof this.callbacks.onLockStatusChange === 'function') {
      this.callbacks.onLockStatusChange({ isOpen: isOpen });
    }
  }

  /**
   * 认证设备
   * @returns {Promise} 认证结果
   */
  authenticate() {
    console.log('锁服务 - 认证设备');
    
    // 如果模拟模式，直接返回成功
    if (this.isSimulatedMode) {
      console.log('锁服务 - 模拟模式，模拟认证成功');
      return Promise.resolve({
        success: true,
        message: '认证成功(模拟模式)',
        simulated: true
      });
    }
    
    // 检查设备是否已连接
    if (!this.deviceInfo.connected) {
      return Promise.reject(new Error('设备未连接，无法认证'));
    }
    
    // 调用蓝牙管理器的认证方法
    return blueToothManager.authenticate()
      .then(res => {
        console.log('锁服务 - 认证成功:', res);
        return {
          success: true,
          message: '认证成功'
        };
      })
      .catch(err => {
        console.error('锁服务 - 认证失败:', err);
        return Promise.reject(err);
      });
  }
}

// 导出单例
export default new LockService(); 