{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/report/report.vue?26d8", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/report/report.vue?4d5b", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/report/report.vue?f870", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/report/report.vue?55fd", "uni-app:///pages/report/report.vue", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/report/report.vue?c850", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/report/report.vue?a0be"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "loading", "submitting", "systemInfo", "navbarStyle", "isIOS", "platform", "orderIndex", "orderList", "orderDisplays", "issueTypes", "label", "value", "icon", "type", "selectedIssueTypes", "description", "uploadedImages", "uploadedImageUrls", "orderId", "deviceId", "onLoad", "console", "onReady", "computed", "isFormValid", "orderSelected", "issueTypeSelected", "selectedTypes", "descriptionValid", "<PERSON><PERSON><PERSON><PERSON>", "selectedOrderId", "getSelectedIssueType", "methods", "calculateIOSAdaptation", "finalTopPosition", "model", "marginTop", "position", "top", "goBack", "uni", "getOrderList", "title", "then", "order", "catch", "getActiveOrders", "content", "showCancel", "success", "formatDateString", "generateOrderDisplays", "getStoreName", "onOrderChange", "toggleIssueType", "chooseImage", "count", "sizeType", "sourceType", "removeImage", "uploadImages", "resolve", "Promise", "reject", "uploadImageFallback", "uploadImageDirect", "url", "filePath", "name", "formData", "fail", "checkNetworkStatus", "duration", "submitReport", "mask", "issueType", "currentOrder", "orderStatus", "images", "contactInfo", "confirmText", "errorMsg", "finally", "handleSubmit", "e", "setTimeout", "submitReportForIOS", "handleSubmitError", "detectPlatform"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACa;;;AAGlE;AACgM;AAChM,gBAAgB,uMAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5BA;AAAA;AAAA;AAAA;AAAovB,CAAgB,kvBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCyHxwB;EACAC;IACA;MACAC;MACAC;MAEA;MACAC;MACAC;MACAC;MAEA;MACAC;MAEA;MACAC;MACAC;MACAC;MAEA;MACAC,aACA;QAAAC;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,EACA;MACAC;MAEA;MACAC;MAEA;MACAC;MACAC;MAEA;MACAC;MACAC;IACA;EACA;EACAC;IACAC;;IAEA;IACA;;IAEA;IACA;MACA;MACAA;IACA;IAEA;MACA;MACAA;IACA;;IAEA;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACAC;MACA;MACA;MACA;MACA;MACA;MAEAH;QACAI;QACAnB;QACAY;QACAQ;QACAC;QACAC;QACAC;MACA;MAEA;IACA;IACAC;MACA;MACA;QACA;MACA;;MAEA;MACA;QACA;QACA;UACA;QACA;MACA;MACA;IACA;IACA;IACAC;MACA;QACA;MACA;;MAEA;MACA;QACA;MACA;;MAEA;MACA;IACA;EACA;EACAC;IACA;IACAC;MACA;QACA;QACA;QAEAZ;QACAA;QAEA;UACA;UACA;UACA;UAEAA;UACAA;UACAA;UAEA;;UAEA;UACA;YACA;YACAa;YACAb;;YAEA;YACA;cACAa;cACAb;YACA;;YAEA;YACA;cACAa;cACAb;YACA;UACA;YACAa;UACA,wEACAC;YACAD;UACA;YACAA;UACA;UAEA;YACAE;YACAC;YACAC;UACA;UAEAjB;UACAA;QACA;UACA;QACA;MACA;QACAA;QACA;MACA;IACA;IACAkB;MACAC;IACA;IACA;IACAC;MAAA;MACA;QACA;;QAEA;QACAD;UACAE;QACA;;QAEA;QACA;;QAEA;QACA;UACA;UACA;YACArB;YACA;UACA;UAEA,wCACAsB;YACA;cACAtB;;cAEA;cACA;gBACA;cACA;;cAEA;cACA;gBAAA,OACAuB;cAAA,EACA;cAEA;gBACA;gBACA;gBACAvB;;gBAEA;gBACA;;gBAEA;gBACA;cACA;gBACA;gBACA;gBACAA;cACA;YACA;UACA,GACAwB;YACAxB;YACAmB;cACAE;cACA9B;YACA;UACA;QACA;MACA;QACAS;QACAmB;QACA;QAEAA;UACAE;UACA9B;QACA;MACA;IACA;IAEA;IACAkC;MAAA;MACA;QACA;UACAzB;UACAmB;UACA;;UAEA;UACAA;YACAE;YACA9B;UACA;UACA;QACA;;QAEA;QACA;QAAA,CACA+B;UACAH;UACA;;UAEA;UACA;UAEA;YACA;YACA;cACA;gBACA;gBACAjC;cACA;gBACA;gBACAA;cACA;gBACA;gBACAA;cACA;gBACA;gBACAA;cACA;YACA;UACA;YACAc;UACA;UAEAA;UAEA;YACA;YACA;;YAEA;YACA;;YAEA;YACA;UACA;YACAA;YACA;YACA;cACAmB;gBACAE;gBACAK;gBACAC;gBACAC;kBACAT;gBACA;cACA;YACA;UACA;QACA,GACAK;UACAxB;UACAmB;UACA;UAEAA;YACAE;YACA9B;UACA;;UAEA;UACA;YACA4B;cACAE;cACAK;cACAC;cACAC;gBACAT;cACA;YACA;UACA;QACA;MACA;QACAnB;QACAmB;QACA;;QAEA;QACAA;UACAE;UACA9B;QACA;MACA;IACA;IAEA;IACAsC;MACA;MAEA;QACA;QACA;QACA;QACA;UACA;QACA;;QAEA;QACA;UACA;UACA;UACA;QACA;;QAEA;QACA;QACA;QACA;UACA;UACA;UACA;;UAEA;UACA;UACA;YACA;YACA;YACA;YACA;UACA;YACA;UACA;QACA;;QAEA;QACA7B;QACA;MACA;QACAA;QACA;MACA;IACA;IAEA;IACA8B;MAAA;MACA;QACA;QACA;UACA;UACA;UACA;UACA;QACA;;QAEA;QACA;UACA9B;UACA;UACA;QACA;QAEA;UACA;UAEA;YACA;YACA;YACA;YACA;;YAEA;YACA;;YAEA;YACA;;YAEA;YACA;;YAEA;YACA;UACA;YACAA;YACA;UACA;QACA;MACA;QACAA;QACA;MACA;IACA;IAEA;IACA+B;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;QACA;MACA;IACA;IACAC;MACA;MACA;QACA;MACA;QACA;MACA;IACA;IACAC;MAAA;MACAf;QACAgB;QACAC;QACAC;QACAT;UACA;UACA;QACA;MACA;IACA;IACAU;MACA;IACA;IACA;IACAC;MAAA;MACA;QACA;UACA;UACA;YACAC;YACA;UACA;;UAEA;UACA;YACAxC;YACA;YACA;cAAA;YAAA;YAEAyC,8BACAnB;cACAtB;cACA;cACAwC;YACA,GACAhB;cACAxB;cACA0C;YACA;YACA;UACA;UAEA;YACA;YACA,iDACApB;cACAtB;cACA;cACA;gBACA;cACA;;cAEA;cACA;cACA;gBACA;cACA;gBACA;cACA;gBACA;gBACA;cACA;gBACAA;gBACA;cACA;YACA,GACAwB;cACAxB;cACA;cACA;YACA;UACA;UAEAyC,4BACAnB;YACAtB;YACA;YACAwC;UACA,GACAhB;YACAxB;YACA0C;UACA;QACA;UACA1C;UACA0C;QACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;QACA;UACA;YACA3C;YACA0C;YACA;UACA;;UAEA;UACA;YACA1C;YACA,+CACAsB;cACAtB;cACA;gBACA;cACA;cAEA;gBACA;cACA;gBACA;cACA;gBACA;cACA;gBACA;cACA;YACA,GACAwB;cACAxB;cACA;cACA,oCACAsB,cACAE;YACA;UACA;YACA;YACAxB;YACA,oCACAsB,cACAE;UACA;QACA;UACAxB;UACA;UACA;YACA,oCACAsB,cACAE;UACA;YACAxB;YACA0C;UACA;QACA;MACA;IACA;IAEA;IACAE;MACA;QACA;UACA;YACA5C;YACA0C;YACA;UACA;UAEA1C;;UAEA;UACA;YACAA;YACA0C;YACA;UACA;UAEAvB;YACA0B;YACAC;YACAC;YACAC;cACAxD;YACA;YACAoC;cACA5B;cACA;gBACA;kBACA;gBACA;gBAEA;gBACA;kBACA;oBACAwC;kBACA;oBACAA;kBACA;oBACAA;kBACA;oBACAE;kBACA;gBACA;kBACAA;gBACA;cACA;gBACA1C;gBACA0C;cACA;YACA;YACAO;cACAjD;cACA0C;YACA;UACA;QACA;UACA1C;UACA0C;QACA;MACA;IACA;IACA;IACAQ;MACA;QACA/B;UACAS;YACA5B;YACA;cACAmB;gBACAE;gBACA9B;gBACA4D;cACA;cACAT;YACA;cACAF;YACA;UACA;UACAS;YACAjD;YACA;YACAwC;UACA;QACA;MACA;IACA;IACA;IACAY;MAAA;MACA;QACApD;QAEA;UACAA;UACAmB;YACAE;YACA9B;UACA;UACA;QACA;;QAEA;QACA;UACAS;UACAmB;YACAE;YACA9B;UACA;UACA;QACA;;QAEA;QACA;;QAEA;QACA4B;UACAE;UACAgC;QACA;;QAEA;QACA,0BACA/B;UACAtB;UACA;UACA;QACA,GACAsB;UACAtB;UACA;UACA;;UAEA;UACA;YACAsD;UACA;;UAEA;UACA;UACA;YACAC;YACA;cACAvD;YACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;YACA;cACA;cACAF;YACA;cACAA;YACA;UACA;;UAEA;UACA;UACA;YACA;cACA;cACA;cACA;cACA;YACA;YACA0D;UACA;UAEAxD;UACAA;UACAA;;UAEA;UACA;YACAH;YACAC;YACAwD;YACA5B;cACA;gBAAA;cAAA;cACA;YACA;YACA+B;YACAC;UACA;UAEA1D;;UAEA;UACA;QACA,GACAsB;UACAH;UAEAnB;;UAEA;UACA;YACA;UACA;;UAEA;UACAmB;YACAE;YACAK;YACAC;YACAgC;YACA/B;cACA;gBACA;gBACAT;cACA;YACA;UACA;QACA,GACAK;UACAxB;UACAmB;;UAEA;UACA;UACA;YACA;cACAyC;YACA;cACAA;YACA;cACAA;YACA;UACA;;UAEA;UACA;YACAA;UACA;;UAEA;UACA;YACA;cACAA;cACA;cACAzC;gBACA0B;cACA;YACA;cACAe;YACA;UACA;UAEAzC;YACAE;YACA9B;YACA4D;UACA;QACA,GACAU;UACA;QACA;MACA;QACA7D;QACAmB;QACA;QACAA;UACAE;UACA9B;UACA4D;QACA;MACA;IACA;IACA;IACAW;MAAA;MACA;MACA;QACAC;MACA;MAEA/D;MACAA;MACAA;MACAA;MACAA;MACAA;MACAA;;MAEA;MACA;QACAA;QACAmB;UACAE;UACA9B;QACA;QACA;MACA;;MAEA;MACA;QACA;QAEA;UACAqE;QACA;UACAA;QACA;UACAA;QACA;QAEA5D;QAEAmB;UACAE;UACA9B;UACA4D;QACA;QACA;MACA;;MAEA;MACAnD;MACA;QACA;QACA;MACA;QACA;QACAgE;UACA;QACA;MACA;IACA;IACA;IACAC;MAAA;MACA;QACAjE;;QAEA;QACA;UACAA;UACAmB;YACAE;YACA9B;UACA;UACA;QACA;;QAEA;QACA;;QAEA;QACA4B;UACAE;UACAgC;QACA;;QAEA;QACAlC;UACAS;YACA;cACAT;cACA;cACAA;gBACAE;gBACA9B;gBACA4D;cACA;cACA;YACA;;YAEA;YACA,sBACA7B;cACAtB;;cAEA;cACA;cACA;gBACAsD;cACA;;cAEA;cACA;cACA;gBACAC;gBACA;kBACAvD;gBACA;cACA;;cAEA;cACA;;cAEA;cACA;cACA;gBACA;kBACAF;gBACA;kBACAA;gBACA;cACA;;cAEA;cACA;cACA;gBACA;kBACA;kBACA;kBACA;kBACA;gBACA;gBACA0D;cACA;;cAEA;cACA;gBACA3D;gBACAC;gBACAwD;gBACA5B;kBACA;oBAAA;kBAAA;kBACA;gBACA;gBACA+B;gBACAC;cACA;cAEA1D;;cAEA;cACA,sCACAsB;gBACAH;gBACAnB;;gBAEA;gBACA;kBACA;gBACA;;gBAEA;gBACAmB;kBACAE;kBACAK;kBACAC;kBACAgC;kBACA/B;oBACA;sBACA;sBACAT;oBACA;kBACA;gBACA;cACA,GACAK;gBACA;cACA,GACAqC;gBACA;cACA;YACA,GACArC;cACA;cACA;YACA;UACA;UACAyB;YACA9B;YACA;YACAA;cACAE;cACA9B;cACA4D;YACA;UACA;QACA;MACA;QACAnD;QACAmB;QACA;QACAA;UACAE;UACA9B;UACA4D;QACA;MACA;IACA;IACA;IACAe;MACA;QACAlE;;QAEA;QACAmB;;QAEA;QACA;;QAEA;QACA;UACA;UACA;YACA;cACAyC;YACA;cACAA;YACA;cACAA;YACA;cACA;cACAA;YACA;UACA;;UAEA;UACA;YACA;cACAA;YACA;cACAA;YACA;UACA;;UAEA;UACA;YACA;cACAA;cACA;cACAI;gBACA7C;kBACA0B;gBACA;cACA;YACA;cACAe;YACA;cACAA;YACA;cACAA;YACA;UACA;;UAEA;UACA;YACA;cACAA;YACA;cACAA;YACA;cACAA;YACA;UACA;QACA;;QAEA;QACAzC;UACAE;UACA9B;UACA4D;QACA;MACA;QACAnD;QACAmB;UACAE;UACA9B;UACA4D;QACA;MACA;IACA;IACA;IACAgB;MAAA;MACA;QACA;QACA;UACA;UACA;YACA;YACA;YACAnE;YACA;UACA;YACAA;UACA;;UAEA;UACA;YACA;cACA;cACA;cACAA;cACA;YACA;UACA;YACAA;UACA;QACA;;QAEA;QACAmB;UACAS;YACA;YACA5B;UACA;UACAiD;YACAjD;YACA;YACAA;UACA;QACA;MACA;QACAA;QACA;QACAA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACv1CA;AAAA;AAAA;AAAA;AAAikC,CAAgB,2hCAAG,EAAC,C;;;;;;;;;;;ACArlC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/report/report.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/report/report.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./report.vue?vue&type=template&id=e403a974&\"\nvar renderjs\nimport script from \"./report.vue?vue&type=script&lang=js&\"\nexport * from \"./report.vue?vue&type=script&lang=js&\"\nimport style0 from \"./report.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/report/report.vue\"\nexport default component.exports", "export * from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./report.vue?vue&type=template&id=e403a974&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.issueTypes, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var g0 = _vm.selectedIssueTypes.includes(item.value)\n    return {\n      $orig: $orig,\n      g0: g0,\n    }\n  })\n  var g1 = _vm.uploadedImages.length\n  var g2 = _vm.description.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        g1: g1,\n        g2: g2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./report.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./report.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"container page-report\">\n\t\t<!-- 页面背景 -->\n\t\t<view class=\"page-background\">\n\t\t\t<!-- 背景图片 -->\n\t\t\t<image class=\"background-image\" src=\"https://wuji1112-1312674791.cos.ap-guangzhou.myqcloud.com/static/shouye3.png\" mode=\"aspectFill\"></image>\n\t\t\t\n\t\t\t<!-- 深磨砂效果叠加层 -->\n\t\t\t<view class=\"frosted-overlay\"></view>\n\t\t</view>\n\t\t\n\t\t<!-- 导航栏 -->\n\t\t<view class=\"navbar\" :style=\"navbarStyle\">\n\t\t\t<view class=\"navbar-left\" @click=\"goBack\">\n\t\t\t\t<text class=\"material-icons md-24 text-primary\">arrow_back</text>\n\t\t\t</view>\n\t\t\t<view class=\"navbar-title\">设备异常反馈</view>\n\t\t\t<view class=\"navbar-right\"></view>\n\t\t</view>\n\t\t\n\t\t<!-- 页面内容 -->\n\t\t<view class=\"content\">\n\t\t\t\t<!-- 表单内容 -->\n\t\t\t\t<view class=\"form-body\">\n\t\t\t\t\t<!-- 订单选择 -->\n\t\t\t\t\t<view class=\"form-group\" style=\"margin-top: -20rpx;\">\n\t\t\t\t\t\t<view class=\"form-label\">选择订单</view>\n\t\t\t\t\t\t<picker @change=\"onOrderChange\" :value=\"orderIndex\" :range=\"orderDisplays\">\n\t\t\t\t\t\t\t<view class=\"form-picker\">\n\t\t\t\t\t\t\t\t<text>{{orderDisplays[orderIndex] || '请选择订单'}}</text>\n\t\t\t\t\t\t\t\t<text class=\"material-icons md-24 text-tertiary\">arrow_drop_down</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</picker>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 异常类型 -->\n\t\t\t\t\t<view class=\"form-group mt-lg\">\n\t\t\t\t\t<view class=\"form-label\">\n\t\t\t\t\t\t异常类型 <text class=\"text-tertiary type-hint\">(可多选)</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"issue-types\">\n\t\t\t\t\t\t\t<view \n\t\t\t\t\t\t\t\tv-for=\"(item, index) in issueTypes\" \n\t\t\t\t\t\t\t\t:key=\"index\" \n\t\t\t\t\t\t\t\tclass=\"issue-type-item\" \n\t\t\t\t\t\t\t\t:class=\"{'active': selectedIssueTypes.includes(item.value)}\"\n\t\t\t\t\t\t\t\t@click=\"toggleIssueType(item.value)\"\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<text class=\"material-icons issue-icon\">{{item.icon}}</text>\n\t\t\t\t\t\t\t\t<text>{{item.label}}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 图片上传 -->\n\t\t\t\t<view class=\"form-group mt-md\">\n\t\t\t\t\t\t<view class=\"form-label\">上传图片 <text class=\"text-tertiary\">(最多3张)</text></view>\n\t\t\t\t\t\t<view class=\"upload-container\">\n\t\t\t\t\t\t\t<view class=\"upload-items\">\n\t\t\t\t\t\t\t\t<view \n\t\t\t\t\t\t\t\t\tv-for=\"(item, index) in uploadedImages\" \n\t\t\t\t\t\t\t\t\t:key=\"index\" \n\t\t\t\t\t\t\t\t\tclass=\"upload-item\"\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t<image :src=\"item\" mode=\"aspectFill\"></image>\n\t\t\t\t\t\t\t\t\t<view class=\"delete-btn\" @click=\"removeImage(index)\">\n\t\t\t\t\t\t\t\t\t\t<text class=\"material-icons md-18\">close</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t<!-- 上传按钮 -->\n\t\t\t\t\t\t\t\t<view class=\"upload-btn\" v-if=\"uploadedImages.length < 3\" @click=\"chooseImage\">\n\t\t\t\t\t\t\t\t\t<text class=\"material-icons md-36 text-tertiary\">add_photo_alternate</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 问题描述 -->\n\t\t\t\t<view class=\"form-group mt-md\">\n\t\t\t\t\t<view class=\"form-label\">问题描述 <text class=\"text-tertiary\">(最少10个字符)</text></view>\n\t\t\t\t\t<textarea \n\t\t\t\t\t\tclass=\"form-textarea\" \n\t\t\t\t\t\tv-model=\"description\" \n\t\t\t\t\t\tplaceholder=\"请详细描述您遇到的问题，以便我们更好地解决...\" \n\t\t\t\t\t\tmaxlength=\"200\"\n\t\t\t\t\t></textarea>\n\t\t\t\t\t<view class=\"text-count text-tertiary\">{{description.length}}/200</view>\n\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 提交按钮 -->\n\t\t\t\t\t<view class=\"form-group\" style=\"margin-top: 10rpx;\">\n\t\t\t\t\t<view class=\"btn-wrapper\" @tap.stop=\"handleSubmit\">\n\t\t\t\t\t\t<button \n\t\t\t\t\t\t\tclass=\"btn btn-submit\" \n\t\t\t\t\t\t\t:class=\"{'btn-active': isFormValid && !submitting, 'btn-disabled': !isFormValid || submitting}\"\n\t\t\t\t\t\t\t:disabled=\"!isFormValid || submitting\"\n\t\t\t\t\t\t\t:style=\"{ opacity: isFormValid ? 1 : 0.5 }\">\n\t\t\t\t\t\t\t<text class=\"btn-text\">{{ submitting ? '提交中...' : '提交' }}</text>\n\t\t\t\t\t\t</button>\n\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 隐私提示 -->\n\t\t\t\t\t<view class=\"privacy-tip text-tertiary mt-md\">\n\t\t\t\t\t\t<text>提交即表示您同意我们收集此信息以解决您的问题。</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 底部空白区域，确保内容不被遮挡 -->\n\t\t\t<view class=\"bottom-space\"></view>\n\t\t</view>\n\n\t\t<!-- 加载状态 -->\n\t\t<view class=\"loading-container\" v-if=\"loading\">\n\t\t\t<view class=\"loading-spinner\"></view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tloading: true,\n\t\t\t\tsubmitting: false,\n\n\t\t\t\t// iOS适配相关\n\t\t\t\tsystemInfo: {},\n\t\t\t\tnavbarStyle: {},\n\t\t\t\tisIOS: false,\n\n\t\t\t\t// 设备平台\n\t\t\t\tplatform: '',\n\t\t\t\t\n\t\t\t\t// 订单相关\n\t\t\t\torderIndex: 0,\n\t\t\t\torderList: [],\n\t\t\t\torderDisplays: ['请选择订单'],\n\t\t\t\t\n\t\t\t\t// 问题类型\n\t\t\t\tissueTypes: [\n\t\t\t\t\t{ label: '机柜损坏', value: 'cabinet_damaged', icon: 'door_front', type: 1 },\n\t\t\t\t\t{ label: '门锁损坏', value: 'lock_damaged', icon: 'lock_open', type: 1 },\n\t\t\t\t\t{ label: '娃娃损坏', value: 'doll_damaged', icon: 'girl', type: 1 },\n\t\t\t\t\t{ label: '无法对话', value: 'no_dialogue', icon: 'mic_off', type: 1 },\n\t\t\t\t\t{ label: '清洁问题', value: 'cleaning_issue', icon: 'cleaning_services', type: 1 },\n\t\t\t\t\t{ label: '其他问题', value: 'other', icon: 'help_outline', type: 3 }\n\t\t\t\t],\n\t\t\t\tselectedIssueTypes: [],\n\t\t\t\t\n\t\t\t\t// 问题描述\n\t\t\t\tdescription: '',\n\t\t\t\t\n\t\t\t\t// 上传图片\n\t\t\t\tuploadedImages: [],\n\t\t\t\tuploadedImageUrls: [],\n\t\t\t\t\n\t\t\t\t// 从订单详情页传入的参数\n\t\t\t\torderId: null,\n\t\t\t\tdeviceId: null\n\t\t\t}\n\t\t},\n\t\tonLoad(options) {\n\t\t\tconsole.log('report.vue onLoad options:', options);\n\n\t\t\t// 获取设备平台信息\n\t\t\tthis.detectPlatform();\n\n\t\t\t// 接收从订单详情页传入的参数\n\t\t\tif (options.orderId) {\n\t\t\t\tthis.orderId = options.orderId;\n\t\t\t\tconsole.log('接收到订单ID:', this.orderId);\n\t\t\t}\n\t\t\t\n\t\t\tif (options.deviceId) {\n\t\t\t\tthis.deviceId = options.deviceId;\n\t\t\t\tconsole.log('接收到设备ID:', this.deviceId);\n\t\t\t}\n\t\t\t\n\t\t\t// 获取订单列表\n\t\t\tthis.getOrderList();\n\t\t},\n\t\tonReady() {\n\t\t\t// 获取系统信息并计算iOS适配参数\n\t\t\tthis.calculateIOSAdaptation();\n\t\t},\n\t\tcomputed: {\n\t\t\tisFormValid() {\n\t\t\t\t// 表单验证\n\t\t\t\t// 修改为必须选择订单（orderIndex > 0），除非有传入的orderId\n\t\t\t\tconst orderSelected = (this.orderIndex > 0) || this.orderId; \n\t\t\t\tconst issueTypeSelected = this.selectedIssueTypes.length > 0;\n\t\t\t\tconst descriptionValid = this.description && this.description.trim().length >= 10;\n\t\t\t\t\n\t\t\t\tconsole.log('表单验证详情:', {\n\t\t\t\t\torderSelected,\n\t\t\t\t\torderIndex: this.orderIndex,\n\t\t\t\t\torderId: this.orderId,\n\t\t\t\t\tissueTypeSelected,\n\t\t\t\t\tselectedTypes: this.selectedIssueTypes,\n\t\t\t\t\tdescriptionValid,\n\t\t\t\t\tdescriptionLength: this.description ? this.description.trim().length : 0\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\treturn orderSelected && issueTypeSelected && descriptionValid;\n\t\t\t},\n\t\t\tselectedOrderId() {\n\t\t\t\t// 如果已有orderId，直接返回\n\t\t\t\tif (this.orderId) {\n\t\t\t\t\treturn this.orderId;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 否则从选择的订单中获取\n\t\t\t\tif (this.orderIndex > 0 && this.orderList && this.orderList.length > 0 && this.orderIndex <= this.orderList.length) {\n\t\t\t\t\tconst selectedOrder = this.orderList[this.orderIndex - 1];\n\t\t\t\t\tif (selectedOrder) {\n\t\t\t\t\t\treturn selectedOrder.orderId || selectedOrder.id || null;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn null;\n\t\t\t},\n\t\t\t// 获取选中的问题类型\n\t\t\tgetSelectedIssueType() {\n\t\t\t\tif (this.selectedIssueTypes.length === 0) {\n\t\t\t\t\treturn 1; // 默认为设备问题\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 如果选中了多个问题类型，优先级：其他问题 > 订单问题 > 设备问题\n\t\t\t\tif (this.selectedIssueTypes.includes('other')) {\n\t\t\t\t\treturn 3; // 其他问题\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 默认为设备问题\n\t\t\t\treturn 1;\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\t// 计算iOS适配参数\n\t\t\tcalculateIOSAdaptation() {\n\t\t\t\ttry {\n\t\t\t\t\tthis.systemInfo = uni.getSystemInfoSync();\n\t\t\t\t\tthis.isIOS = this.systemInfo.platform === 'ios';\n\n\t\t\t\t\tconsole.log('举报页面 - 系统信息:', this.systemInfo);\n\t\t\t\t\tconsole.log('举报页面 - 是否iOS:', this.isIOS);\n\n\t\t\t\t\tif (this.isIOS) {\n\t\t\t\t\t\tconst statusBarHeight = this.systemInfo.statusBarHeight || 44;\n\t\t\t\t\t\tconst model = this.systemInfo.model || '';\n\t\t\t\t\t\tconst safeAreaTop = this.systemInfo.safeArea ? this.systemInfo.safeArea.top : statusBarHeight;\n\n\t\t\t\t\t\tconsole.log('举报页面 - 状态栏高度:', statusBarHeight);\n\t\t\t\t\t\tconsole.log('举报页面 - 设备型号:', model);\n\t\t\t\t\t\tconsole.log('举报页面 - 安全区域顶部:', safeAreaTop);\n\n\t\t\t\t\t\tlet finalTopPosition;\n\n\t\t\t\t\t\t// 使用与订单页面相同的超激进适配策略\n\t\t\t\t\t\tif (model.includes('iPhone 16 Pro')) {\n\t\t\t\t\t\t\t// 方案1：直接使用状态栏高度，无额外间距\n\t\t\t\t\t\t\tfinalTopPosition = statusBarHeight;\n\t\t\t\t\t\t\tconsole.log('举报页面 - iPhone 16 Pro - 方案1（状态栏高度）:', finalTopPosition);\n\n\t\t\t\t\t\t\t// 方案2：如果还是太靠下，尝试更小的值\n\t\t\t\t\t\t\tif (finalTopPosition > 50) {\n\t\t\t\t\t\t\t\tfinalTopPosition = 44; // 使用标准状态栏高度\n\t\t\t\t\t\t\t\tconsole.log('举报页面 - iPhone 16 Pro - 方案2（标准高度）:', finalTopPosition);\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t// 方案3：如果还是太靠下，尝试负值\n\t\t\t\t\t\t\tif (finalTopPosition > 45) {\n\t\t\t\t\t\t\t\tfinalTopPosition = statusBarHeight - 10; // 负偏移\n\t\t\t\t\t\t\t\tconsole.log('举报页面 - iPhone 16 Pro - 方案3（负偏移）:', finalTopPosition);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else if (model.includes('iPhone 15 Pro') || model.includes('iPhone 14 Pro')) {\n\t\t\t\t\t\t\tfinalTopPosition = statusBarHeight;\n\t\t\t\t\t\t} else if (model.includes('iPhone X') || model.includes('iPhone 11') ||\n\t\t\t\t\t\t\t\t  model.includes('iPhone 12') || model.includes('iPhone 13')) {\n\t\t\t\t\t\t\tfinalTopPosition = statusBarHeight;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tfinalTopPosition = statusBarHeight;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tthis.navbarStyle = {\n\t\t\t\t\t\t\tmarginTop: finalTopPosition + 'px',\n\t\t\t\t\t\t\tposition: 'relative',\n\t\t\t\t\t\t\ttop: '0px'\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\tconsole.log('举报页面 - 超激进适配 - 最终顶部位置:', finalTopPosition + 'px');\n\t\t\t\t\t\tconsole.log('举报页面 - 超激进适配 - 导航栏样式:', this.navbarStyle);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.navbarStyle = {};\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('举报页面 - 计算iOS适配参数失败:', error);\n\t\t\t\t\tthis.navbarStyle = {};\n\t\t\t\t}\n\t\t\t},\n\t\t\tgoBack() {\n\t\t\t\tuni.navigateBack();\n\t\t\t},\n\t\t\t// 获取订单列表\n\t\t\tgetOrderList() {\n\t\t\t\ttry {\n\t\t\t\t\tthis.loading = true;\n\t\t\t\t\t\n\t\t\t\t\t// 显示加载中\n\t\t\t\t\tuni.showLoading({\n\t\t\t\t\t\ttitle: '加载中...'\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\t// 直接获取所有设备订单列表\n\t\t\t\t\tthis.getActiveOrders();\n\t\t\t\t\t\n\t\t\t\t\t// 如果已经有传入的orderId，则尝试获取该订单的详情\n\t\t\t\t\tif (this.orderId) {\n\t\t\t\t\t\t// 检查API是否可用\n\t\t\t\t\t\tif (!this.$api || !this.$api.order || typeof this.$api.order.getDetail !== 'function') {\n\t\t\t\t\t\t\tconsole.error('API对象不存在或order.getDetail方法不可用');\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\tthis.$api.order.getDetail(this.orderId)\n\t\t\t\t\t\t\t.then(res => {\n\t\t\t\t\t\t\t\tif (res && res.data) {\n\t\t\t\t\t\t\t\t\tconsole.log('获取到指定订单详情:', res.data);\n\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t// 确保orderList已初始化\n\t\t\t\t\t\t\t\t\tif (!this.orderList) {\n\t\t\t\t\t\t\t\t\t\tthis.orderList = [];\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t// 检查当前订单列表是否已包含该订单\n\t\t\t\t\t\t\t\t\tconst existingOrderIndex = this.orderList.findIndex(order => \n\t\t\t\t\t\t\t\t\t\torder && (order.orderId === this.orderId || order.id === this.orderId)\n\t\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\tif (existingOrderIndex === -1) {\n\t\t\t\t\t\t\t\t\t\t// 如果订单列表中不包含指定的订单，则添加到列表中\n\t\t\t\t\t\t\t\t\t\tthis.orderList.unshift(res.data);\n\t\t\t\t\t\t\t\t\t\tconsole.log('将指定订单添加到列表首位');\n\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t// 重新生成用于显示的订单列表\n\t\t\t\t\t\t\t\t\t\tthis.generateOrderDisplays();\n\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t// 默认选中该订单\n\t\t\t\t\t\t\t\t\t\tthis.orderIndex = 1;\n\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\t// 如果已包含，则选中该订单\n\t\t\t\t\t\t\t\t\t\tthis.orderIndex = existingOrderIndex + 1; // +1 是因为第一项是\"请选择订单\"\n\t\t\t\t\t\t\t\t\t\tconsole.log('订单列表中已包含指定订单，选中索引:', this.orderIndex);\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t.catch(err => {\n\t\t\t\t\t\t\t\tconsole.error('获取指定订单详情失败:', err);\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ttitle: '获取订单详情失败',\n\t\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('getOrderList执行出错:', error);\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\tthis.loading = false;\n\t\t\t\t\t\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '获取订单列表出错',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 获取所有设备订单列表\n\t\t\tgetActiveOrders() {\n\t\t\t\ttry {\n\t\t\t\t\tif (!this.$api || !this.$api.order || typeof this.$api.order.getList !== 'function') {\n\t\t\t\t\t\tconsole.error('API对象不存在或order.getList方法不可用');\n\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\tthis.loading = false;\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 显示错误提示\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '获取订单列表失败，API不可用',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 直接获取所有设备订单列表，不限制状态\n\t\t\t\t\tthis.$api.order.getList(0, 1, 20) // 获取所有订单，第一页，每页20条\n\t\t\t\t\t\t.then(res => {\n\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\tthis.loading = false;\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 处理返回数据，增强兼容性\n\t\t\t\t\t\t\tlet orderList = [];\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t\t// 兼容不同的返回格式\n\t\t\t\t\t\t\t\tif (res && res.data) {\n\t\t\t\t\t\t\t\t\tif (Array.isArray(res.data)) {\n\t\t\t\t\t\t\t\t\t\t// 直接返回数组的情况\n\t\t\t\t\t\t\t\t\t\torderList = res.data;\n\t\t\t\t\t\t\t\t\t} else if (res.data && Array.isArray(res.data.list)) {\n\t\t\t\t\t\t\t\t\t\t// 包含list数组的情况\n\t\t\t\t\t\t\t\t\t\torderList = res.data.list;\n\t\t\t\t\t\t\t\t\t} else if (res.data && Array.isArray(res.data.records)) {\n\t\t\t\t\t\t\t\t\t\t// 包含records数组的情况\n\t\t\t\t\t\t\t\t\t\torderList = res.data.records;\n\t\t\t\t\t\t\t\t\t} else if (res.data && typeof res.data === 'object') {\n\t\t\t\t\t\t\t\t\t\t// 其他情况，尝试将对象转为数组\n\t\t\t\t\t\t\t\t\t\torderList = [res.data];\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\t\t\tconsole.error('处理订单数据出错:', error);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\tconsole.log('获取到订单列表:', orderList);\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\tif (orderList && orderList.length > 0) {\n\t\t\t\t\t\t\t\t// 使用获取到的订单列表\n\t\t\t\t\t\t\t\tthis.orderList = orderList;\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t// 生成用于显示的订单列表\n\t\t\t\t\t\t\t\tthis.generateOrderDisplays();\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t// 不再默认选择第一个订单，保持orderIndex为0，即\"请选择订单\"\n\t\t\t\t\t\t\t\t// 只有在有传入orderId的情况下，才会在getOrderList方法中设置选中项\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tconsole.log('没有获取到订单列表或列表为空');\n\t\t\t\t\t\t\t\t// 如果没有订单\n\t\t\t\t\t\t\t\tif (!this.orderId) {\n\t\t\t\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\t\t\t\ttitle: '提示',\n\t\t\t\t\t\t\t\t\t\tcontent: '您当前没有可用订单，无法上报设备异常',\n\t\t\t\t\t\t\t\t\t\tshowCancel: false,\n\t\t\t\t\t\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\t\t\t\t\t\tuni.navigateBack();\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t})\n\t\t\t\t\t\t.catch(err => {\n\t\t\t\t\t\t\tconsole.error('获取订单列表失败:', err);\n\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\tthis.loading = false;\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '获取订单列表失败',\n\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 如果获取列表失败且没有传入的orderId，则提示用户\n\t\t\t\t\t\t\tif (!this.orderId) {\n\t\t\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\t\t\ttitle: '提示',\n\t\t\t\t\t\t\t\t\tcontent: '获取订单列表失败，请稍后重试',\n\t\t\t\t\t\t\t\t\tshowCancel: false,\n\t\t\t\t\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\t\t\t\t\tuni.navigateBack();\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('getActiveOrders执行出错:', error);\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\tthis.loading = false;\n\t\t\t\t\t\n\t\t\t\t\t// 显示错误提示\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '获取订单列表出错',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 格式化日期字符串，兼容iOS\n\t\t\tformatDateString(dateStr) {\n\t\t\t\tif (!dateStr) return null;\n\t\t\t\t\n\t\t\t\ttry {\n\t\t\t\t\t// 尝试直接解析（兼容标准格式）\n\t\t\t\t\tconst directDate = new Date(dateStr);\n\t\t\t\t\t// 检查是否为有效日期\n\t\t\t\t\tif (!isNaN(directDate.getTime())) {\n\t\t\t\t\t\treturn directDate;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 处理 \"yyyy-MM-dd HH:mm:ss\" 格式\n\t\t\t\t\tif (dateStr.includes('-') && dateStr.includes(':')) {\n\t\t\t\t\t\t// 将 \"yyyy-MM-dd HH:mm:ss\" 转换为 \"yyyy/MM/dd HH:mm:ss\"\n\t\t\t\t\t\tconst formattedStr = dateStr.replace(/-/g, '/');\n\t\t\t\t\t\treturn new Date(formattedStr);\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 处理其他可能的格式\n\t\t\t\t\t// 提取日期部分\n\t\t\t\t\tconst dateParts = dateStr.match(/(\\d{4})[-\\/](\\d{1,2})[-\\/](\\d{1,2})/);\n\t\t\t\t\tif (dateParts) {\n\t\t\t\t\t\tconst year = parseInt(dateParts[1]);\n\t\t\t\t\t\tconst month = parseInt(dateParts[2]) - 1; // 月份从0开始\n\t\t\t\t\t\tconst day = parseInt(dateParts[3]);\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 提取时间部分\n\t\t\t\t\t\tconst timeParts = dateStr.match(/(\\d{1,2}):(\\d{1,2}):(\\d{1,2})/);\n\t\t\t\t\t\tif (timeParts) {\n\t\t\t\t\t\t\tconst hour = parseInt(timeParts[1]);\n\t\t\t\t\t\t\tconst minute = parseInt(timeParts[2]);\n\t\t\t\t\t\t\tconst second = parseInt(timeParts[3]);\n\t\t\t\t\t\t\treturn new Date(year, month, day, hour, minute, second);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\treturn new Date(year, month, day);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 如果无法解析，返回当前日期\n\t\t\t\t\tconsole.error('无法解析日期:', dateStr);\n\t\t\t\t\treturn new Date();\n\t\t\t\t} catch (e) {\n\t\t\t\t\tconsole.error('日期解析错误:', e, dateStr);\n\t\t\t\t\treturn new Date(); // 出错时返回当前日期\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 生成订单显示列表\n\t\t\tgenerateOrderDisplays() {\n\t\t\t\ttry {\n\t\t\t\t\t// 订单状态映射\n\t\t\t\t\tconst statusMap = {\n\t\t\t\t\t\t0: '未支付',\n\t\t\t\t\t\t1: '进行中',\n\t\t\t\t\t\t2: '已完成',\n\t\t\t\t\t\t3: '已取消'\n\t\t\t\t\t};\n\t\t\t\t\t\n\t\t\t\t\t// 确保orderList存在且为数组\n\t\t\t\t\tif (!this.orderList || !Array.isArray(this.orderList)) {\n\t\t\t\t\t\tconsole.error('订单列表不是有效数组:', this.orderList);\n\t\t\t\t\t\tthis.orderDisplays = ['请选择订单'];\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tthis.orderDisplays = ['请选择订单', ...this.orderList.map(order => {\n\t\t\t\t\t\tif (!order) return '未知订单';\n\t\t\t\t\t\t\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t// 获取日期（使用兼容iOS的方法）\n\t\t\t\t\t\t\tconst dateTimeStr = order.createTime || order.startTime || '';\n\t\t\t\t\t\t\tconst date = this.formatDateString(dateTimeStr);\n\t\t\t\t\t\t\tconst dateStr = date ? `${date.getMonth() + 1}月${date.getDate()}日` : '未知日期';\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 获取房间号/设备号\n\t\t\t\t\t\t\tconst roomNumber = order.roomNumber || order.deviceNo || order.deviceId || '';\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 获取状态\n\t\t\t\t\t\t\tconst status = order.payStatus === 1 ? '已完成' : (statusMap[order.status || 0] || '未知状态');\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 获取门店名称\n\t\t\t\t\t\t\tconst storeName = this.getStoreName(order.storeName || order.shopName || '未知门店');\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 返回格式化的订单显示文本\n\t\t\t\t\t\t\treturn `${dateStr} - ${storeName} (${roomNumber}) [${status}]`;\n\t\t\t\t\t\t} catch (innerError) {\n\t\t\t\t\t\t\tconsole.error('处理单个订单数据出错:', innerError, order);\n\t\t\t\t\t\t\treturn '订单数据处理错误';\n\t\t\t\t\t\t}\n\t\t\t\t\t})];\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('生成订单显示列表出错:', error);\n\t\t\t\t\tthis.orderDisplays = ['请选择订单'];\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 处理门店名称，移除\"今夜城堡 - \"前缀\n\t\t\tgetStoreName(fullName) {\n\t\t\t\tif (!fullName) return '未知门店';\n\t\t\t\treturn String(fullName).replace('今夜城堡 - ', '');\n\t\t\t},\n\t\t\tonOrderChange(e) {\n\t\t\t\tthis.orderIndex = e.detail.value;\n\t\t\t\t// 如果选择了新订单，清除之前传入的orderId\n\t\t\t\tif (this.orderIndex > 0) {\n\t\t\t\t\tthis.orderId = null;\n\t\t\t\t}\n\t\t\t},\n\t\t\ttoggleIssueType(value) {\n\t\t\t\tconst index = this.selectedIssueTypes.indexOf(value);\n\t\t\t\tif (index !== -1) {\n\t\t\t\t\tthis.selectedIssueTypes.splice(index, 1);\n\t\t\t\t} else {\n\t\t\t\t\tthis.selectedIssueTypes.push(value);\n\t\t\t\t}\n\t\t\t},\n\t\t\tchooseImage() {\n\t\t\t\tuni.chooseImage({\n\t\t\t\t\tcount: 3 - this.uploadedImages.length,\n\t\t\t\t\tsizeType: ['compressed'],\n\t\t\t\t\tsourceType: ['album', 'camera'],\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t// 添加选择的图片\n\t\t\t\t\t\tthis.uploadedImages = [...this.uploadedImages, ...res.tempFilePaths];\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\tremoveImage(index) {\n\t\t\t\tthis.uploadedImages.splice(index, 1);\n\t\t\t},\n\t\t\t// 上传图片（带备用方案）\n\t\t\tuploadImages() {\n\t\t\t\treturn new Promise((resolve, reject) => {\n\t\t\t\t\ttry {\n\t\t\t\t\t\t// 如果没有图片需要上传，直接返回空数组\n\t\t\t\t\t\tif (this.uploadedImages.length === 0) {\n\t\t\t\t\t\t\tresolve([]);\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 检查API是否可用\n\t\t\t\t\t\tif (!this.$api || !this.$api.report || typeof this.$api.report.uploadImage !== 'function') {\n\t\t\t\t\t\t\tconsole.error('API对象不存在或report.uploadImage方法不可用，尝试备用上传方法');\n\t\t\t\t\t\t\t// 如果主API不可用，尝试使用备用方法上传所有图片\n\t\t\t\t\t\t\tconst fallbackPromises = this.uploadedImages.map(imagePath => this.uploadImageFallback(imagePath));\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\tPromise.all(fallbackPromises)\n\t\t\t\t\t\t\t\t.then(imageUrls => {\n\t\t\t\t\t\t\t\t\tconsole.log('所有图片通过备用方法上传成功:', imageUrls);\n\t\t\t\t\t\t\t\t\tthis.uploadedImageUrls = imageUrls;\n\t\t\t\t\t\t\t\t\tresolve(imageUrls);\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\t.catch(err => {\n\t\t\t\t\t\t\t\t\tconsole.error('备用上传方法失败:', err);\n\t\t\t\t\t\t\t\t\treject(err);\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\tconst uploadPromises = this.uploadedImages.map(imagePath => {\n\t\t\t\t\t\t\t// 先尝试使用专用的反馈图片上传API\n\t\t\t\t\t\t\treturn this.$api.report.uploadImage(imagePath)\n\t\t\t\t\t\t\t\t.then(res => {\n\t\t\t\t\t\t\t\t\tconsole.log('图片上传响应:', res);\n\t\t\t\t\t\t\t\t\t// 检查响应是否有效\n\t\t\t\t\t\t\t\t\tif (!res || !res.data) {\n\t\t\t\t\t\t\t\t\t\tthrow new Error('上传图片响应无效');\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t// 从响应中获取上传后的图片URL\n\t\t\t\t\t\t\t\t\t// 适配不同的返回格式\n\t\t\t\t\t\t\t\t\tif (res.data && res.data.url) {\n\t\t\t\t\t\t\t\t\t\treturn res.data.url;\n\t\t\t\t\t\t\t\t\t} else if (res.data && res.data.imageUrl) {\n\t\t\t\t\t\t\t\t\t\treturn res.data.imageUrl;\n\t\t\t\t\t\t\t\t\t} else if (typeof res.data === 'string') {\n\t\t\t\t\t\t\t\t\t\t// 如果直接返回了URL字符串\n\t\t\t\t\t\t\t\t\t\treturn res.data;\n\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\tconsole.error('上传图片返回格式异常:', res);\n\t\t\t\t\t\t\t\t\t\tthrow new Error('上传图片失败，返回数据格式不正确');\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\t.catch(err => {\n\t\t\t\t\t\t\t\t\tconsole.error('专用API上传失败，尝试通用上传API:', err);\n\t\t\t\t\t\t\t\t\t// 如果专用API失败，尝试使用通用上传API\n\t\t\t\t\t\t\t\t\treturn this.uploadImageFallback(imagePath);\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t});\n\t\t\t\t\t\t\n\t\t\t\t\t\tPromise.all(uploadPromises)\n\t\t\t\t\t\t\t.then(imageUrls => {\n\t\t\t\t\t\t\t\tconsole.log('所有图片上传成功:', imageUrls);\n\t\t\t\t\t\t\t\tthis.uploadedImageUrls = imageUrls;\n\t\t\t\t\t\t\t\tresolve(imageUrls);\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t.catch(err => {\n\t\t\t\t\t\t\t\tconsole.error('上传图片失败:', err);\n\t\t\t\t\t\t\t\treject(err);\n\t\t\t\t\t\t\t});\n\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\tconsole.error('上传图片过程中发生未捕获的错误:', error);\n\t\t\t\t\t\treject(error);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 备用上传方法\n\t\t\tuploadImageFallback(imagePath) {\n\t\t\t\treturn new Promise((resolve, reject) => {\n\t\t\t\t\ttry {\n\t\t\t\t\t\tif (!imagePath) {\n\t\t\t\t\t\t\tconsole.error('备用上传方法：图片路径无效');\n\t\t\t\t\t\t\treject(new Error('图片路径无效'));\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 尝试使用通用上传API\n\t\t\t\t\t\tif (this.$api && this.$api.upload && typeof this.$api.upload.file === 'function') {\n\t\t\t\t\t\t\tconsole.log('尝试使用通用上传API');\n\t\t\t\t\t\t\tthis.$api.upload.file(imagePath, 'feedback')\n\t\t\t\t\t\t\t\t.then(res => {\n\t\t\t\t\t\t\t\t\tconsole.log('通用API上传响应:', res);\n\t\t\t\t\t\t\t\t\tif (!res || !res.data) {\n\t\t\t\t\t\t\t\t\t\tthrow new Error('通用上传API响应无效');\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\tif (res.data && res.data.url) {\n\t\t\t\t\t\t\t\t\t\treturn resolve(res.data.url);\n\t\t\t\t\t\t\t\t\t} else if (res.data && res.data.imageUrl) {\n\t\t\t\t\t\t\t\t\t\treturn resolve(res.data.imageUrl);\n\t\t\t\t\t\t\t\t\t} else if (typeof res.data === 'string') {\n\t\t\t\t\t\t\t\t\t\treturn resolve(res.data);\n\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\tthrow new Error('通用上传API返回格式不正确');\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\t.catch(err => {\n\t\t\t\t\t\t\t\t\tconsole.error('通用上传API也失败:', err);\n\t\t\t\t\t\t\t\t\t// 最后尝试使用uni.uploadFile直接上传\n\t\t\t\t\t\t\t\t\tthis.uploadImageDirect(imagePath)\n\t\t\t\t\t\t\t\t\t\t.then(resolve)\n\t\t\t\t\t\t\t\t\t\t.catch(reject);\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// 如果没有通用上传API，直接使用uni.uploadFile\n\t\t\t\t\t\t\tconsole.log('没有通用上传API，直接使用uni.uploadFile');\n\t\t\t\t\t\t\tthis.uploadImageDirect(imagePath)\n\t\t\t\t\t\t\t\t.then(resolve)\n\t\t\t\t\t\t\t\t.catch(reject);\n\t\t\t\t\t\t}\n\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\tconsole.error('备用上传方法中发生未捕获的错误:', error);\n\t\t\t\t\t\t// 尝试最后的直接上传方法\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\tthis.uploadImageDirect(imagePath)\n\t\t\t\t\t\t\t\t.then(resolve)\n\t\t\t\t\t\t\t\t.catch(reject);\n\t\t\t\t\t\t} catch (finalError) {\n\t\t\t\t\t\t\tconsole.error('所有上传方法都失败:', finalError);\n\t\t\t\t\t\t\treject(finalError);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 直接使用uni.uploadFile上传\n\t\t\tuploadImageDirect(imagePath) {\n\t\t\t\treturn new Promise((resolve, reject) => {\n\t\t\t\t\ttry {\n\t\t\t\t\t\tif (!imagePath) {\n\t\t\t\t\t\t\tconsole.error('直接上传方法：图片路径无效');\n\t\t\t\t\t\t\treject(new Error('图片路径无效'));\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\tconsole.log('尝试直接使用uni.uploadFile上传');\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 检查uni对象是否可用\n\t\t\t\t\t\tif (typeof uni === 'undefined' || typeof uni.uploadFile !== 'function') {\n\t\t\t\t\t\t\tconsole.error('uni对象不存在或uploadFile方法不可用');\n\t\t\t\t\t\t\treject(new Error('上传功能不可用'));\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\tuni.uploadFile({\n\t\t\t\t\t\t\turl: '/api/upload/image',\n\t\t\t\t\t\t\tfilePath: imagePath,\n\t\t\t\t\t\t\tname: 'file',\n\t\t\t\t\t\t\tformData: {\n\t\t\t\t\t\t\t\ttype: 'feedback'\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\t\tconsole.log('直接上传响应:', res);\n\t\t\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t\t\tif (!res || !res.data) {\n\t\t\t\t\t\t\t\t\t\tthrow new Error('上传响应无效');\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\tconst data = JSON.parse(res.data);\n\t\t\t\t\t\t\t\t\tif (data.code === 200 && data.data) {\n\t\t\t\t\t\t\t\t\t\tif (data.data.url) {\n\t\t\t\t\t\t\t\t\t\t\tresolve(data.data.url);\n\t\t\t\t\t\t\t\t\t\t} else if (data.data.imageUrl) {\n\t\t\t\t\t\t\t\t\t\t\tresolve(data.data.imageUrl);\n\t\t\t\t\t\t\t\t\t\t} else if (typeof data.data === 'string') {\n\t\t\t\t\t\t\t\t\t\t\tresolve(data.data);\n\t\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\t\treject(new Error('直接上传返回格式不正确'));\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\treject(new Error(data.message || '直接上传失败'));\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\t\t\t\tconsole.error('解析上传响应失败:', e);\n\t\t\t\t\t\t\t\t\treject(new Error('解析上传响应失败'));\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\t\tconsole.error('直接上传失败:', err);\n\t\t\t\t\t\t\t\treject(err);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\tconsole.error('直接上传方法中发生未捕获的错误:', error);\n\t\t\t\t\t\treject(error);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t// 检查网络状态\n\t\t\tcheckNetworkStatus() {\n\t\t\t\treturn new Promise((resolve, reject) => {\n\t\t\t\t\tuni.getNetworkType({\n\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\tconsole.log('当前网络状态:', res);\n\t\t\t\t\t\t\tif (res.networkType === 'none') {\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ttitle: '网络连接不可用，请检查网络设置',\n\t\t\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\t\t\tduration: 3000\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\treject(new Error('网络连接不可用'));\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tresolve(res.networkType);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\tconsole.error('获取网络状态失败:', err);\n\t\t\t\t\t\t\t// 即使获取网络状态失败，也继续尝试提交\n\t\t\t\t\t\t\tresolve('unknown');\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t\t},\n\t\t\t// 提交异常报告\n\t\t\tsubmitReport() {\n\t\t\t\ttry {\n\t\t\t\t\tconsole.log('开始提交异常报告');\n\t\t\t\t\t\n\t\t\t\t\tif (!this.isFormValid) {\n\t\t\t\t\t\tconsole.log('表单验证失败，无法提交');\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '请完善表单信息',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 检查API是否可用\n\t\t\t\t\tif (!this.$api || !this.$api.report || typeof this.$api.report.submit !== 'function') {\n\t\t\t\t\t\tconsole.error('API对象不存在或report.submit方法不可用');\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: 'API不可用，无法提交',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 设置提交状态\n\t\t\t\t\tthis.submitting = true;\n\t\t\t\t\t\n\t\t\t\t\t// 显示加载中\n\t\t\t\t\tuni.showLoading({\n\t\t\t\t\t\ttitle: '提交中...',\n\t\t\t\t\t\tmask: true // 添加遮罩防止重复点击\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\t// 先检查网络状态\n\t\t\t\t\tthis.checkNetworkStatus()\n\t\t\t\t\t\t.then(() => {\n\t\t\t\t\t\t\tconsole.log('网络状态检查通过，开始上传图片');\n\t\t\t\t\t\t\t// 网络正常，上传图片\n\t\t\t\t\t\t\treturn this.uploadImages();\n\t\t\t\t\t\t})\n\t\t\t\t\t\t.then(imageUrls => {\n\t\t\t\t\t\t\tconsole.log('图片上传成功，准备提交数据', imageUrls);\n\t\t\t\t\t\t\t// 获取选中的问题类型\n\t\t\t\t\t\t\tlet issueType = 1; // 默认为设备问题\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 如果选中了多个问题类型，优先级：其他问题 > 订单问题 > 设备问题\n\t\t\t\t\t\t\tif (this.selectedIssueTypes.includes('other')) {\n\t\t\t\t\t\t\t\tissueType = 3; // 其他问题\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 获取当前选中的订单信息\n\t\t\t\t\t\t\tlet currentOrder = null;\n\t\t\t\t\t\t\tif (this.orderIndex > 0 && this.orderList && this.orderList.length > 0 && this.orderIndex <= this.orderList.length) {\n\t\t\t\t\t\t\t\tcurrentOrder = this.orderList[this.orderIndex - 1];\n\t\t\t\t\t\t\t\tif (!currentOrder) {\n\t\t\t\t\t\t\t\t\tconsole.warn('选中的订单索引有效，但订单对象为空');\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 获取订单ID，优先使用传入的orderId，其次使用选中的订单ID\n\t\t\t\t\t\t\tconst orderId = this.orderId || (currentOrder ? (currentOrder.orderId || currentOrder.id || '') : '');\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 获取设备ID，优先使用传入的deviceId，其次使用订单中的deviceId或deviceNo\n\t\t\t\t\t\t\tlet deviceId = this.deviceId;\n\t\t\t\t\t\t\tif (!deviceId && currentOrder) {\n\t\t\t\t\t\t\t\tif (currentOrder.deviceId) {\n\t\t\t\t\t\t\t\t\t// 如果是数字字符串，转换为数字\n\t\t\t\t\t\t\t\t\tdeviceId = /^\\d+$/.test(currentOrder.deviceId) ? parseInt(currentOrder.deviceId) : currentOrder.deviceId;\n\t\t\t\t\t\t\t\t} else if (currentOrder.deviceNo) {\n\t\t\t\t\t\t\t\t\tdeviceId = /^\\d+$/.test(currentOrder.deviceNo) ? parseInt(currentOrder.deviceNo) : currentOrder.deviceNo;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 获取订单状态\n\t\t\t\t\t\t\tlet orderStatus = '';\n\t\t\t\t\t\t\tif (currentOrder) {\n\t\t\t\t\t\t\t\tconst statusMap = {\n\t\t\t\t\t\t\t\t\t0: '未支付',\n\t\t\t\t\t\t\t\t\t1: '进行中',\n\t\t\t\t\t\t\t\t\t2: '已完成',\n\t\t\t\t\t\t\t\t\t3: '已取消'\n\t\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\t\torderStatus = currentOrder.payStatus === 1 ? '已完成' : (statusMap[currentOrder.status || 0] || '未知状态');\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\tconsole.log('提交异常报告 - 当前订单:', currentOrder);\n\t\t\t\t\t\t\tconsole.log('提交异常报告 - 使用订单ID:', orderId);\n\t\t\t\t\t\t\tconsole.log('提交异常报告 - 使用设备ID:', deviceId);\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 准备异常报告数据\n\t\t\t\t\t\t\tconst reportData = {\n\t\t\t\t\t\t\t\torderId: orderId,\n\t\t\t\t\t\t\t\tdeviceId: deviceId,\n\t\t\t\t\t\t\t\tissueType: issueType,\n\t\t\t\t\t\t\t\tcontent: `问题类型：${this.selectedIssueTypes.map(type => {\n\t\t\t\t\t\t\t\t\tconst issueType = this.issueTypes.find(item => item.value === type);\n\t\t\t\t\t\t\t\t\treturn issueType ? issueType.label : '';\n\t\t\t\t\t\t\t\t}).join('、')}\\n\\n${orderStatus ? `订单状态：${orderStatus}\\n\\n` : ''}${this.description}`,\n\t\t\t\t\t\t\t\timages: imageUrls,\n\t\t\t\t\t\t\t\tcontactInfo: uni.getStorageSync('userInfo') ? uni.getStorageSync('userInfo').phone || '' : ''\n\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\tconsole.log('提交异常报告数据:', reportData);\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 提交异常报告\n\t\t\t\t\t\t\treturn this.$api.report.submit(reportData);\n\t\t\t\t\t\t})\n\t\t\t\t\t\t.then(res => {\n\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\tconsole.log('异常报告提交响应:', res);\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 检查返回结果\n\t\t\t\t\t\t\tif (res.code && res.code !== 200) {\n\t\t\t\t\t\t\t\tthrow new Error(res.message || '提交失败');\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 显示成功提示\n\t\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\t\ttitle: '提交成功',\n\t\t\t\t\t\t\t\tcontent: '感谢您的反馈，我们会尽快处理您的问题。客服将在15分钟内与您联系，请保持电话畅通。',\n\t\t\t\t\t\t\t\tshowCancel: false,\n\t\t\t\t\t\t\t\tconfirmText: '确定',\n\t\t\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\t\t\t\t// 返回上一页\n\t\t\t\t\t\t\t\t\t\tuni.navigateBack();\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t})\n\t\t\t\t\t\t.catch(err => {\n\t\t\t\t\t\t\tconsole.error('提交异常报告失败:', err);\n\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 显示错误信息\n\t\t\t\t\t\t\tlet errorMsg = '提交失败，请稍后重试';\n\t\t\t\t\t\t\tif (err && err.message) {\n\t\t\t\t\t\t\t\tif (err.message.includes('上传图片失败')) {\n\t\t\t\t\t\t\t\t\terrorMsg = '图片上传失败，请重新选择图片';\n\t\t\t\t\t\t\t\t} else if (err.message.includes('网络连接不可用')) {\n\t\t\t\t\t\t\t\t\terrorMsg = '网络连接不可用，请检查网络设置后重试';\n\t\t\t\t\t\t\t\t} else if (typeof err.message === 'string') {\n\t\t\t\t\t\t\t\t\terrorMsg = err.message;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 检查网络连接问题\n\t\t\t\t\t\t\tif (err && err.errMsg && err.errMsg.includes('request:fail')) {\n\t\t\t\t\t\t\t\terrorMsg = '网络连接失败，请检查网络设置后重试';\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 检查服务器错误\n\t\t\t\t\t\t\tif (err && err.statusCode) {\n\t\t\t\t\t\t\t\tif (err.statusCode === 401) {\n\t\t\t\t\t\t\t\t\terrorMsg = '登录已过期，请重新登录';\n\t\t\t\t\t\t\t\t\t// 跳转到登录页\n\t\t\t\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\t\t\t\turl: '/pages/login/login'\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t} else if (err.statusCode >= 500) {\n\t\t\t\t\t\t\t\t\terrorMsg = '服务器错误，请稍后重试';\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: errorMsg,\n\t\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\t\tduration: 3000\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t})\n\t\t\t\t\t\t.finally(() => {\n\t\t\t\t\t\t\tthis.submitting = false;\n\t\t\t\t\t\t});\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('提交过程中发生未捕获的错误:', error);\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\tthis.submitting = false;\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '提交过程发生错误，请稍后重试',\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 3000\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 处理提交按钮点击事件\n\t\t\thandleSubmit(e) {\n\t\t\t\t// 阻止事件冒泡\n\t\t\t\tif (e && typeof e.stopPropagation === 'function') {\n\t\t\t\t\te.stopPropagation();\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tconsole.log('提交按钮被点击', new Date().toISOString());\n\t\t\t\tconsole.log('表单验证状态:', this.isFormValid);\n\t\t\t\tconsole.log('提交中状态:', this.submitting);\n\t\t\t\tconsole.log('当前平台:', this.platform);\n\t\t\t\tconsole.log('订单选择:', this.orderIndex > 0 ? `已选择第${this.orderIndex}个订单` : '未选择订单');\n\t\t\t\tconsole.log('问题类型:', this.selectedIssueTypes);\n\t\t\t\tconsole.log('描述长度:', this.description ? this.description.trim().length : 0);\n\t\t\t\t\n\t\t\t\t// 如果正在提交中，阻止重复提交\n\t\t\t\tif (this.submitting) {\n\t\t\t\t\tconsole.log('正在提交中，阻止重复提交');\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '正在提交中，请稍候...',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 表单验证\n\t\t\t\tif (!this.isFormValid) {\n\t\t\t\t\tlet errorMsg = '请完善表单信息';\n\t\t\t\t\t\n\t\t\t\t\tif (this.orderIndex <= 0 && !this.orderId) {\n\t\t\t\t\t\terrorMsg = '请先选择一个订单';\n\t\t\t\t\t} else if (this.selectedIssueTypes.length === 0) {\n\t\t\t\t\t\terrorMsg = '请选择至少一种异常类型';\n\t\t\t\t\t} else if (!this.description || this.description.trim().length < 10) {\n\t\t\t\t\t\terrorMsg = '问题描述至少需要10个字符';\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tconsole.log('表单验证失败，原因:', errorMsg);\n\t\t\t\t\t\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: errorMsg,\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 根据平台选择不同的提交方式\n\t\t\t\tconsole.log('开始调用提交方法');\n\t\t\t\tif (this.platform === 'ios') {\n\t\t\t\t\t// iOS平台使用特殊处理\n\t\t\t\t\tthis.submitReportForIOS();\n\t\t\t\t} else {\n\t\t\t\t\t// 其他平台使用标准方法\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\tthis.submitReport();\n\t\t\t\t\t}, 100); // 延迟100ms调用，避免可能的事件冲突\n\t\t\t\t}\n\t\t\t},\n\t\t\t// iOS平台特定的提交方法\n\t\t\tsubmitReportForIOS() {\n\t\t\t\ttry {\n\t\t\t\t\tconsole.log('iOS平台特定提交方法');\n\t\t\t\t\t\n\t\t\t\t\t// 检查API是否可用\n\t\t\t\t\tif (!this.$api || !this.$api.report || typeof this.$api.report.submit !== 'function') {\n\t\t\t\t\t\tconsole.error('API对象不存在或report.submit方法不可用');\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: 'API不可用，无法提交',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 设置提交状态\n\t\t\t\t\tthis.submitting = true;\n\t\t\t\t\t\n\t\t\t\t\t// 显示加载中\n\t\t\t\t\tuni.showLoading({\n\t\t\t\t\t\ttitle: '提交中...',\n\t\t\t\t\t\tmask: true // 添加遮罩防止重复点击\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\t// 先检查网络状态\n\t\t\t\t\tuni.getNetworkType({\n\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\tif (res.networkType === 'none') {\n\t\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\t\tthis.submitting = false;\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ttitle: '网络连接不可用，请检查网络设置',\n\t\t\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\t\t\tduration: 3000\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 网络正常，上传图片\n\t\t\t\t\t\t\tthis.uploadImages()\n\t\t\t\t\t\t\t\t.then(imageUrls => {\n\t\t\t\t\t\t\t\t\tconsole.log('图片上传成功，准备提交数据', imageUrls);\n\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t// 获取选中的问题类型\n\t\t\t\t\t\t\t\t\tlet issueType = 1; // 默认为设备问题\n\t\t\t\t\t\t\t\t\tif (this.selectedIssueTypes.includes('other')) {\n\t\t\t\t\t\t\t\t\t\tissueType = 3; // 其他问题\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t// 获取当前选中的订单信息\n\t\t\t\t\t\t\t\t\tlet currentOrder = null;\n\t\t\t\t\t\t\t\t\tif (this.orderIndex > 0 && this.orderList && this.orderList.length > 0 && this.orderIndex <= this.orderList.length) {\n\t\t\t\t\t\t\t\t\t\tcurrentOrder = this.orderList[this.orderIndex - 1];\n\t\t\t\t\t\t\t\t\t\tif (!currentOrder) {\n\t\t\t\t\t\t\t\t\t\t\tconsole.warn('iOS方法中：选中的订单索引有效，但订单对象为空');\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t// 获取订单ID\n\t\t\t\t\t\t\t\t\tconst orderId = this.orderId || (currentOrder ? (currentOrder.orderId || currentOrder.id || '') : '');\n\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t// 获取设备ID\n\t\t\t\t\t\t\t\t\tlet deviceId = this.deviceId;\n\t\t\t\t\t\t\t\t\tif (!deviceId && currentOrder) {\n\t\t\t\t\t\t\t\t\t\tif (currentOrder.deviceId) {\n\t\t\t\t\t\t\t\t\t\t\tdeviceId = /^\\d+$/.test(currentOrder.deviceId) ? parseInt(currentOrder.deviceId) : currentOrder.deviceId;\n\t\t\t\t\t\t\t\t\t\t} else if (currentOrder.deviceNo) {\n\t\t\t\t\t\t\t\t\t\t\tdeviceId = /^\\d+$/.test(currentOrder.deviceNo) ? parseInt(currentOrder.deviceNo) : currentOrder.deviceNo;\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t// 获取订单状态\n\t\t\t\t\t\t\t\t\tlet orderStatus = '';\n\t\t\t\t\t\t\t\t\tif (currentOrder) {\n\t\t\t\t\t\t\t\t\t\tconst statusMap = {\n\t\t\t\t\t\t\t\t\t\t\t0: '未支付',\n\t\t\t\t\t\t\t\t\t\t\t1: '进行中',\n\t\t\t\t\t\t\t\t\t\t\t2: '已完成',\n\t\t\t\t\t\t\t\t\t\t\t3: '已取消'\n\t\t\t\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\t\t\t\torderStatus = currentOrder.payStatus === 1 ? '已完成' : (statusMap[currentOrder.status || 0] || '未知状态');\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t// 准备异常报告数据\n\t\t\t\t\t\t\t\t\tconst reportData = {\n\t\t\t\t\t\t\t\t\t\torderId: orderId,\n\t\t\t\t\t\t\t\t\t\tdeviceId: deviceId,\n\t\t\t\t\t\t\t\t\t\tissueType: issueType,\n\t\t\t\t\t\t\t\t\t\tcontent: `问题类型：${this.selectedIssueTypes.map(type => {\n\t\t\t\t\t\t\t\t\t\t\tconst issueType = this.issueTypes.find(item => item.value === type);\n\t\t\t\t\t\t\t\t\t\t\treturn issueType ? issueType.label : '';\n\t\t\t\t\t\t\t\t\t\t}).join('、')}\\n\\n${orderStatus ? `订单状态：${orderStatus}\\n\\n` : ''}${this.description}`,\n\t\t\t\t\t\t\t\t\t\timages: imageUrls,\n\t\t\t\t\t\t\t\t\t\tcontactInfo: uni.getStorageSync('userInfo') ? uni.getStorageSync('userInfo').phone || '' : ''\n\t\t\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\tconsole.log('提交异常报告数据:', reportData);\n\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t// 提交异常报告\n\t\t\t\t\t\t\t\t\tthis.$api.report.submit(reportData)\n\t\t\t\t\t\t\t\t\t\t.then(res => {\n\t\t\t\t\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\t\t\t\t\tconsole.log('异常报告提交响应:', res);\n\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t// 检查返回结果\n\t\t\t\t\t\t\t\t\t\t\tif (res.code && res.code !== 200) {\n\t\t\t\t\t\t\t\t\t\t\t\tthrow new Error(res.message || '提交失败');\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t// 显示成功提示\n\t\t\t\t\t\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\t\t\t\t\t\ttitle: '提交成功',\n\t\t\t\t\t\t\t\t\t\t\t\tcontent: '感谢您的反馈，我们会尽快处理您的问题。客服将在15分钟内与您联系，请保持电话畅通。',\n\t\t\t\t\t\t\t\t\t\t\t\tshowCancel: false,\n\t\t\t\t\t\t\t\t\t\t\t\tconfirmText: '确定',\n\t\t\t\t\t\t\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\t\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t// 返回上一页\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tuni.navigateBack();\n\t\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\t\t\t.catch(err => {\n\t\t\t\t\t\t\t\t\t\t\tthis.handleSubmitError(err);\n\t\t\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\t\t\t.finally(() => {\n\t\t\t\t\t\t\t\t\t\t\tthis.submitting = false;\n\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\t.catch(err => {\n\t\t\t\t\t\t\t\t\tthis.handleSubmitError(err);\n\t\t\t\t\t\t\t\t\tthis.submitting = false;\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfail: () => {\n\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\tthis.submitting = false;\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '网络状态检查失败，请稍后重试',\n\t\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\t\tduration: 3000\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('iOS提交过程中发生未捕获的错误:', error);\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\tthis.submitting = false;\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '提交过程发生错误，请稍后重试',\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 3000\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 处理提交错误\n\t\t\thandleSubmitError(err) {\n\t\t\t\ttry {\n\t\t\t\t\tconsole.error('提交异常报告失败:', err);\n\t\t\t\t\t\n\t\t\t\t\t// 确保加载状态被清除\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\n\t\t\t\t\t// 显示错误信息\n\t\t\t\t\tlet errorMsg = '提交失败，请稍后重试';\n\t\t\t\t\t\n\t\t\t\t\t// 处理不同类型的错误\n\t\t\t\t\tif (err) {\n\t\t\t\t\t\t// 处理Error对象或带message属性的对象\n\t\t\t\t\t\tif (err.message) {\n\t\t\t\t\t\t\tif (err.message.includes('上传图片失败')) {\n\t\t\t\t\t\t\t\terrorMsg = '图片上传失败，请重新选择图片';\n\t\t\t\t\t\t\t} else if (err.message.includes('网络连接不可用')) {\n\t\t\t\t\t\t\t\terrorMsg = '网络连接不可用，请检查网络设置后重试';\n\t\t\t\t\t\t\t} else if (err.message.includes('API不可用')) {\n\t\t\t\t\t\t\t\terrorMsg = '服务暂时不可用，请稍后重试';\n\t\t\t\t\t\t\t} else if (typeof err.message === 'string') {\n\t\t\t\t\t\t\t\t// 限制错误消息长度，避免过长的错误信息\n\t\t\t\t\t\t\t\terrorMsg = err.message.length > 50 ? err.message.substring(0, 50) + '...' : err.message;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 处理网络请求错误\n\t\t\t\t\t\tif (err.errMsg) {\n\t\t\t\t\t\t\tif (err.errMsg.includes('request:fail')) {\n\t\t\t\t\t\t\t\terrorMsg = '网络连接失败，请检查网络设置后重试';\n\t\t\t\t\t\t\t} else if (err.errMsg.includes('timeout')) {\n\t\t\t\t\t\t\t\terrorMsg = '请求超时，请稍后重试';\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 处理HTTP状态码错误\n\t\t\t\t\t\tif (err.statusCode) {\n\t\t\t\t\t\t\tif (err.statusCode === 401) {\n\t\t\t\t\t\t\t\terrorMsg = '登录已过期，请重新登录';\n\t\t\t\t\t\t\t\t// 跳转到登录页\n\t\t\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\t\t\t\turl: '/pages/login/login'\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t}, 1500);\n\t\t\t\t\t\t\t} else if (err.statusCode === 403) {\n\t\t\t\t\t\t\t\terrorMsg = '您没有权限执行此操作';\n\t\t\t\t\t\t\t} else if (err.statusCode === 404) {\n\t\t\t\t\t\t\t\terrorMsg = '请求的资源不存在';\n\t\t\t\t\t\t\t} else if (err.statusCode >= 500) {\n\t\t\t\t\t\t\t\terrorMsg = '服务器错误，请稍后重试';\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 处理响应数据中的错误信息\n\t\t\t\t\t\tif (err.data) {\n\t\t\t\t\t\t\tif (err.data.message) {\n\t\t\t\t\t\t\t\terrorMsg = err.data.message;\n\t\t\t\t\t\t\t} else if (err.data.msg) {\n\t\t\t\t\t\t\t\terrorMsg = err.data.msg;\n\t\t\t\t\t\t\t} else if (typeof err.data === 'string' && err.data.length < 50) {\n\t\t\t\t\t\t\t\terrorMsg = err.data;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 显示错误提示\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: errorMsg,\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 3000\n\t\t\t\t\t});\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('处理错误信息时发生异常:', error);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '提交失败，请稍后重试',\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 3000\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 检测设备平台\n\t\t\tdetectPlatform() {\n\t\t\t\ttry {\n\t\t\t\t\t// 在微信小程序环境中\n\t\t\t\t\tif (typeof wx !== 'undefined') {\n\t\t\t\t\t\t// 优先使用getSystemInfoSync\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\tconst sysInfo = wx.getSystemInfoSync();\n\t\t\t\t\t\t\tthis.platform = sysInfo.platform;\n\t\t\t\t\t\t\tconsole.log('当前设备平台(getSystemInfoSync):', this.platform);\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\t\tconsole.error('getSystemInfoSync失败:', e);\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 尝试使用getAppBaseInfo\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\tif (wx.getAppBaseInfo) {\n\t\t\t\t\t\t\t\tconst info = wx.getAppBaseInfo();\n\t\t\t\t\t\t\t\tthis.platform = info.platform;\n\t\t\t\t\t\t\t\tconsole.log('当前设备平台(getAppBaseInfo):', this.platform);\n\t\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\t\tconsole.error('getAppBaseInfo失败:', e);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 使用uni-app的API作为备选\n\t\t\t\t\tuni.getSystemInfo({\n\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\tthis.platform = res.platform || 'unknown';\n\t\t\t\t\t\t\tconsole.log('当前设备平台(uni.getSystemInfo):', this.platform);\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\tconsole.error('获取系统信息失败:', err);\n\t\t\t\t\t\t\tthis.platform = 'unknown';\n\t\t\t\t\t\t\tconsole.log('设置默认平台: unknown');\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t} catch (err) {\n\t\t\t\t\tconsole.error('检测平台出错:', err);\n\t\t\t\t\tthis.platform = 'unknown';\n\t\t\t\t\tconsole.log('设置默认平台: unknown');\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style>\n\t/* CSS变量定义 */\n\tpage {\n\t\t--primary-light: #A875FF;\n\t\t--primary-dark: #8C29FF;\n\t\t--neon-pink: #ff36f9;\n\t\t--neon-blue: #00BFFF;\n\t\t--neon-yellow: #FFD700;\n\t\t--neon-red: #FF454A;\n\t\toverflow: hidden; /* 禁用页面滚动 */\n\t}\n\t\n\t/* iOS适配 */\n\t.page-report {\n\t\t/* 适配iOS设备的安全区域和灵动岛 */\n\t\tpadding-top: env(safe-area-inset-top);\n\t}\n\n\t/* 页面基础样式 */\n\t.page-report {\n\t\tcolor: #ffffff;\n\t\theight: 100vh;\n\t\tmin-height: 100vh;\n\t\tbox-sizing: border-box;\n\t\tposition: relative;\n\t\toverflow: hidden; /* 禁用容器滚动 */\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t}\n\t\n\t/* 页面背景样式 */\n\t.page-background {\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tz-index: 0;\n\t}\n\t\n\t/* 背景图片样式 */\n\t.background-image {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tz-index: 0;\n\t\tobject-fit: cover;\n\t}\n\t\n\t/* 深磨砂效果叠加层 */\n\t.frosted-overlay {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tbackground-color: rgba(18, 18, 18, 0.7); /* 深色半透明背景 */\n\t\tbackdrop-filter: blur(8px); /* 较强模糊效果 */\n\t\t-webkit-backdrop-filter: blur(8px);\n\t\tz-index: 1;\n\t}\n\t\n\t/* 顶部导航样式 */\n\t.navbar {\n\t\theight: 90rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\tpadding: 0 30rpx;\n\t\t/* 完全移除CSS的margin-top，只使用JavaScript动态设置 */\n\t\tmargin-top: 0;\n\t\tposition: relative;\n\t\tz-index: 100;\n\t}\n\t\n\t.navbar-title {\n\t\tfont-size: 40rpx;\n\t\tfont-weight: 600;\n\t}\n\t\n\t.navbar-left, .navbar-right {\n\t\twidth: 60rpx;\n\t\theight: 60rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t}\n\t\n\t/* 内容区域 */\n\t.content {\n\t\tflex: 1;\n\t\tpadding: 30rpx 30rpx;\n\t\tposition: relative;\n\t\tz-index: 5;\n\t\twidth: 100%;\n\t\tbox-sizing: border-box;\n\t\toverflow: hidden; /* 修改为hidden，禁用滚动 */\n\t}\n\t\n\t/* 表单容器 */\n\t.form-body {\n\t\tpadding: 30rpx;\n\t}\n\t\n\t.form-group {\n\t\tmargin-bottom: 30rpx;\n\t\twidth: 100%;\n\t}\n\t\n\t.form-group.mt-lg {\n\t\tmargin-top: 50rpx;\n\t}\n\t\n\t.form-group.mt-md {\n\t\tmargin-top: 40rpx;\n\t}\n\t\n\t.form-label {\n\t\tfont-size: 36rpx;\n\t\tfont-weight: 500;\n\t\tmargin-bottom: 16rpx;\n\t\tcolor: #c18fff;\n\t\ttext-shadow: 0 0 10rpx rgba(193, 143, 255, 0.3);\n\t}\n\t\n\t.type-hint {\n\t\tmargin-left: 15rpx;\n\t}\n\t\n\t.form-picker {\n\t\tbackground-color: rgba(255, 255, 255, 0.08);\n\t\tborder: 1px solid rgba(168, 117, 255, 0.2);\n\t\tborder-radius: 15rpx;\n\t\tpadding: 20rpx;\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tcolor: var(--text-primary, #FFFFFF);\n\t\ttransition: all 0.3s ease;\n\t\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\n\t}\n\t\n\t.form-picker:active {\n\t\tbackground-color: rgba(255, 255, 255, 0.12);\n\t\ttransform: scale(0.99);\n\t}\n\t\n\t.issue-types {\n\t\tdisplay: grid;\n\t\tgrid-template-columns: repeat(3, 1fr);\n\t\tgap: 10rpx;\n\t\tmargin-top: 10rpx;\n\t\twidth: 100%;\n\t}\n\t\n\t.issue-type-item {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tpadding: 8rpx 12rpx;\n\t\tbackground-color: rgba(255, 255, 255, 0.08);\n\t\tborder: 1px solid rgba(168, 117, 255, 0.2);\n\t\tborder-radius: 16rpx;\n\t\tfont-size: 26rpx;\n\t\ttransition: all 0.3s ease;\n\t\theight: 60rpx;\n\t}\n\t\n\t.issue-type-item .issue-icon {\n\t\tfont-size: 34rpx;\n\t\tmargin-right: 6rpx;\n\t\tposition: relative;\n\t\ttop: 1rpx;\n\t}\n\t\n\t.issue-type-item.active {\n\t\tbackground-color: rgba(168, 117, 255, 0.15);\n\t\tborder-color: var(--primary-light, #A875FF);\n\t\tcolor: var(--primary-light, #A875FF);\n\t\tbox-shadow: 0 0 10rpx rgba(168, 117, 255, 0.3);\n\t}\n\t\n\t.issue-type-item.active .issue-icon {\n\t\tcolor: var(--primary-light, #A875FF);\n\t}\n\t\n\t.issue-type-item:active {\n\t\ttransform: scale(0.95);\n\t}\n\t\n\t.form-textarea {\n\t\tbackground-color: rgba(255, 255, 255, 0.08);\n\t\tborder: 1px solid rgba(168, 117, 255, 0.2);\n\t\tborder-radius: 15rpx;\n\t\tpadding: 20rpx;\n\t\twidth: 100%;\n\t\theight: 150rpx;\n\t\tbox-sizing: border-box;\n\t\tcolor: var(--text-primary, #FFFFFF);\n\t\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\n\t\ttransition: border-color 0.3s ease, box-shadow 0.3s ease;\n\t\tfont-size: 28rpx;\n\t}\n\t\n\t.form-textarea:focus {\n\t\tborder-color: rgba(168, 117, 255, 0.5);\n\t\tbox-shadow: 0 0 12rpx rgba(168, 117, 255, 0.2);\n\t}\n\t\n\t.form-input {\n\t\tbackground-color: rgba(255, 255, 255, 0.08);\n\t\tborder: 1px solid rgba(168, 117, 255, 0.2);\n\t\tborder-radius: 15rpx;\n\t\tpadding: 28rpx 20rpx;\n\t\twidth: 100%;\n\t\tbox-sizing: border-box;\n\t\tcolor: var(--text-primary, #FFFFFF);\n\t\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\n\t\ttransition: border-color 0.3s ease, box-shadow 0.3s ease;\n\t\tfont-size: 30rpx;\n\t\theight: 90rpx;\n\t\tline-height: 1.5;\n\t}\n\t\n\t.form-input:focus {\n\t\tborder-color: rgba(168, 117, 255, 0.5);\n\t\tbox-shadow: 0 0 12rpx rgba(168, 117, 255, 0.2);\n\t}\n\t\n\t.text-count {\n\t\ttext-align: right;\n\t\tfont-size: 24rpx;\n\t\tmargin-top: 10rpx;\n\t\tcolor: rgba(255, 255, 255, 0.4);\n\t}\n\t\n\t.upload-container {\n\t\tmargin-top: 10rpx;\n\t\twidth: 100%;\n\t}\n\t\n\t.upload-items {\n\t\tdisplay: flex;\n\t\tflex-wrap: wrap;\n\t\tgap: 20rpx;\n\t\twidth: 100%;\n\t}\n\t\n\t.upload-item {\n\t\twidth: 180rpx;\n\t\theight: 180rpx;\n\t\tposition: relative;\n\t\tborder-radius: 15rpx;\n\t\toverflow: hidden;\n\t\tbox-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.2);\n\t\tborder: 1px solid rgba(168, 117, 255, 0.2);\n\t\ttransition: transform 0.3s ease, box-shadow 0.3s ease;\n\t}\n\t\n\t.upload-item:active {\n\t\ttransform: scale(0.95);\n\t\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);\n\t}\n\t\n\t.upload-item image {\n\t\twidth: 100%;\n\t\theight: 100%;\n\t}\n\t\n\t.delete-btn {\n\t\tposition: absolute;\n\t\ttop: 8rpx;\n\t\tright: 8rpx;\n\t\twidth: 36rpx;\n\t\theight: 36rpx;\n\t\tbackground-color: rgba(0, 0, 0, 0.6);\n\t\tborder-radius: 18rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tcolor: #fff;\n\t\ttransition: all 0.3s ease;\n\t}\n\t\n\t.delete-btn:active {\n\t\ttransform: scale(0.9);\n\t\tbackground-color: rgba(255, 69, 58, 0.8);\n\t}\n\t\n\t.upload-btn {\n\t\twidth: 180rpx;\n\t\theight: 180rpx;\n\t\tborder: 1px dashed rgba(168, 117, 255, 0.4);\n\t\tborder-radius: 15rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tbackground-color: rgba(168, 117, 255, 0.05);\n\t\ttransition: all 0.3s ease;\n\t}\n\t\n\t.upload-btn:active {\n\t\tbackground-color: rgba(168, 117, 255, 0.15);\n\t\ttransform: scale(0.95);\n\t}\n\t\n\t.privacy-tip {\n\t\tfont-size: 24rpx;\n\t\ttext-align: center;\n\t\tcolor: rgba(255, 255, 255, 0.4);\n\t}\n\t\n\t/* 按钮包装器，增加点击区域 */\n\t.btn-wrapper {\n\t\twidth: 100%;\n\t\tpadding: 10rpx 0;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t}\n\t\n\t/* 按钮样式 */\n\t.btn {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tborder-radius: 15rpx;\n\t\tpadding: 25rpx;\n\t\tfont-size: 30rpx;\n\t\tfont-weight: 600;\n\t\ttransition: all 0.3s ease;\n\t}\n\t\n\t.btn-submit {\n\t\tbackground: linear-gradient(135deg, #c18fff, #a875ff);\n\t\tcolor: #FFFFFF;\n\t\tbox-shadow: 0 4rpx 20rpx rgba(193, 143, 255, 0.6);\n\t\tborder: none;\n\t\twidth: 60%;\n\t\tmargin: 0 auto;\n\t\tpadding: 16rpx;\n\t\tborder-radius: 50rpx;\n\t\tposition: relative;\n\t\toverflow: hidden;\n\t}\n\t\n\t.btn-active:active {\n\t\ttransform: scale(0.95);\n\t\tbackground: linear-gradient(135deg, #b06aff, #9c63ff);\n\t\tbox-shadow: 0 2rpx 10rpx rgba(193, 143, 255, 0.5);\n\t}\n\t\n\t.btn-disabled {\n\t\tbackground: linear-gradient(135deg, #c18fff99, #a875ff99);\n\t\tbox-shadow: none;\n\t}\n\t\n\t.btn-text {\n\t\tfont-size: 28rpx;\n\t\tfont-weight: 700;\n\t\tletter-spacing: 3rpx;\n\t\ttext-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);\n\t\tcolor: #FFFFFF;\n\t\tz-index: 1;\n\t}\n\t\n\t/* 底部空间 */\n\t.bottom-space {\n\t\theight: 60rpx;\n\t}\n\t\n\t/* Material Icons 字体 */\n\t@font-face {\n\t\tfont-family: 'Material Icons';\n\t\tfont-style: normal;\n\t\tfont-weight: 400;\n\t\tsrc: url(https://fonts.gstatic.com/s/materialicons/v139/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format('woff2');\n\t}\n\n\t.material-icons {\n\t\tfont-family: 'Material Icons';\n\t\tfont-weight: normal;\n\t\tfont-style: normal;\n\t\tfont-size: 24rpx;\n\t\tline-height: 1;\n\t\tletter-spacing: normal;\n\t\ttext-transform: none;\n\t\tdisplay: inline-block;\n\t\twhite-space: nowrap;\n\t\tword-wrap: normal;\n\t\tdirection: ltr;\n\t\t-webkit-font-smoothing: antialiased;\n\t\t-moz-osx-font-smoothing: grayscale;\n\t}\n\t\n\t.material-icons.md-18 {\n\t\tfont-size: 36rpx;\n\t}\n\t\n\t.material-icons.md-24 {\n\t\tfont-size: 48rpx;\n\t}\n\t\n\t.material-icons.md-36 {\n\t\tfont-size: 72rpx;\n\t}\n\t\n\t.material-icons.text-primary {\n\t\tcolor: var(--text-primary, #FFFFFF);\n\t}\n\t\n\t.material-icons.text-tertiary {\n\t\tcolor: var(--text-tertiary, rgba(255, 255, 255, 0.5));\n\t}\n\t\n\t.mr-sm {\n\t\tmargin-right: 10rpx;\n\t}\n\t\n\t.mt-sm {\n\t\tmargin-top: 16rpx;\n\t}\n\t\n\t.mt-md {\n\t\tmargin-top: 30rpx;\n\t}\n\t\n\t.mt-lg {\n\t\tmargin-top: 60rpx;\n\t}\n\t\n\t/* 文本样式 */\n\t.title-sm {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: 600;\n\t\tcolor: #ffffff;\n\t\tmargin-bottom: 6rpx;\n\t}\n\t\n\t.text-secondary {\n\t\tcolor: rgba(255, 255, 255, 0.7);\n\t\tfont-size: 28rpx;\n\t}\n\t\n\t.text-tertiary {\n\t\tcolor: rgba(255, 255, 255, 0.5);\n\t\tfont-size: 26rpx;\n\t}\n\t\n\t.text-primary {\n\t\tcolor: #FFFFFF;\n\t\tfont-size: 28rpx;\n\t}\n\n\t/* 移除所有CSS适配，完全依赖JavaScript动态设置 */\n</style>", "import mod from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./report.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./report.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754165306713\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}