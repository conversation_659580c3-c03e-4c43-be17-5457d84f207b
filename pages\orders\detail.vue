<template>
	<view class="container page-order-detail">
		<!-- 页面背景 -->
		<view class="page-background">
			<!-- 背景图片 -->
			<image class="background-image" src="https://wuji1112-1312674791.cos.ap-guangzhou.myqcloud.com/static/shouye3.png" mode="aspectFill"></image>
			
			<!-- 深磨砂效果叠加层 -->
			<view class="frosted-overlay"></view>
		</view>
		
		<!-- 导航栏 -->
		<view class="navbar" :style="navbarStyle">
			<view class="navbar-left" @click="goBack">
				<text class="material-icons md-24 text-primary">arrow_back</text>
			</view>
			<view class="navbar-title">订单详情</view>
			<view class="navbar-right"></view>
		</view>
		
		<!-- 页面内容区域 -->
		<view class="page-content">
			<view class="content">
				<!-- 订单状态卡片 -->
				<view class="order-status-card" :class="{
					'active': orderDetail.rawStatus === 1,
					'unpaid': orderDetail.rawStatus === 0,
					'completed': orderDetail.rawStatus === 2,
					'cancelled': orderDetail.rawStatus === 3,
					'inactive': orderDetail.rawStatus !== 0 && orderDetail.rawStatus !== 1 && orderDetail.rawStatus !== 2 && orderDetail.rawStatus !== 3
				}">
					<view class="status-icon">
						<text v-if="orderDetail.rawStatus === 0" class="material-icons md-24">schedule</text>
						<text v-else-if="orderDetail.rawStatus === 1" class="material-icons md-24">check_circle</text>
						<text v-else-if="orderDetail.rawStatus === 2" class="material-icons md-24">task_alt</text>
						<text v-else-if="orderDetail.rawStatus === 3" class="material-icons md-24">cancel</text>
						<text v-else class="material-icons md-24">help_outline</text>
					</view>
					<view class="status-text">
						<text class="status-title">{{orderDetail.status}}</text>
						<text class="status-desc" v-if="orderDetail.rawStatus === 0">请尽快完成支付</text>
						<text class="status-desc" v-else-if="orderDetail.rawStatus === 1">订单使用中</text>
						<text class="status-desc" v-else-if="orderDetail.rawStatus === 2">感谢您的使用</text>
						<text class="status-desc" v-else-if="orderDetail.rawStatus === 3">订单已取消</text>
						<text class="status-desc" v-else>状态未知，请联系客服</text>
					</view>
				</view>
				
				<!-- 订单信息卡片 -->
				<view class="card mt-lg">
					<view class="card-header">
						<text class="card-icon">📃</text>
						<text>订单信息</text>
					</view>
					<view class="card-content">
						<view class="detail-item flex justify-between">
							<text class="detail-label">订单编号</text>
							<text class="detail-value">{{orderDetail.orderNo}}</text>
						</view>
						<view class="detail-item flex justify-between mt-sm">
							<text class="detail-label">下单时间</text>
							<text class="detail-value">{{orderDetail.startTime}}</text>
						</view>
						<view class="detail-item flex justify-between mt-sm" v-if="orderDetail.endTime">
							<text class="detail-label">结束时间</text>
							<text class="detail-value">{{orderDetail.endTime}}</text>
						</view>
						<view class="detail-item flex justify-between mt-sm" v-if="orderDetail.duration">
							<text class="detail-label">使用时长</text>
							<text class="detail-value">{{orderDetail.duration}}</text>
						</view>
						<view class="detail-item flex justify-between mt-sm">
							<text class="detail-label">支付方式</text>
							<text class="detail-value">{{orderDetail.payType || '微信支付'}}</text>
						</view>
					</view>
				</view>
				
				<!-- 门店信息卡片 -->
				<view class="card mt-md">
					<view class="card-header">
						<text class="card-icon">🏪</text>
						<text>门店信息</text>
					</view>
					<view class="card-content">
						<view class="detail-item flex justify-between">
							<text class="detail-label">门店名称</text>
							<text class="detail-value">{{orderDetail.storeName}}</text>
						</view>
						<view class="detail-item flex justify-between mt-sm" v-if="orderDetail.deviceName">
							<text class="detail-label">设备名称</text>
							<text class="detail-value">{{orderDetail.deviceName}}</text>
						</view>
						<view class="detail-item flex justify-between mt-sm" v-if="orderDetail.deviceNo">
							<text class="detail-label">设备编号</text>
							<text class="detail-value">{{orderDetail.deviceNo}}</text>
						</view>
					</view>
				</view>
				
				<!-- 支付信息卡片 -->
				<view class="card mt-md">
					<view class="card-header">
						<text class="card-icon">💰</text>
						<text>支付信息</text>
					</view>
					<view class="card-content">
						<view class="detail-item flex justify-between">
							<text class="detail-label">消费金额</text>
							<text class="detail-value primary-light">{{orderDetail.amount}}</text>
						</view>
						<view class="detail-item flex justify-between mt-sm" v-if="orderDetail.rawStatus === 0">
							<text class="detail-label">支付状态</text>
							<text class="detail-value error-light">未支付</text>
						</view>
						<view class="detail-item flex justify-between mt-sm" v-else-if="orderDetail.rawStatus === 1 || orderDetail.rawStatus === 2">
							<text class="detail-label">支付状态</text>
							<text class="detail-value success-light">已支付</text>
						</view>
						<view class="detail-item flex justify-between mt-sm" v-if="orderDetail.payTime">
							<text class="detail-label">支付时间</text>
							<text class="detail-value">{{orderDetail.payTime}}</text>
						</view>
					</view>
				</view>
				
				<!-- 操作按钮 -->
				<view class="actions mt-lg">
					<button v-if="orderDetail.rawStatus === 0" class="btn btn-primary btn-block" @click="payOrder">
						<text class="btn-icon">💳</text> 去支付
					</button>
				
					<button class="btn btn-outline btn-block mt-md" @click="reportProblem">
						<text class="btn-icon">⚠️</text> 设备异常报告
					</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import API from '@/static/js/api.js';

export default {
	data() {
		return {
			orderId: null,
			// iOS适配相关
			systemInfo: {},
			navbarStyle: {},
			isIOS: false,
			orderDetail: {
				id: '',
				orderNo: '',
				storeName: '',
				startTime: '',
				endTime: '',
				duration: '',
				amount: '¥0.00',
				status: '未知状态',
				rawStatus: 0,
				deviceName: '',
				deviceNo: '',
				deviceId: '',
				payTime: '',
				payType: '微信支付'
			}
		}
	},
	onLoad(options) {
		if (options.id) {
			this.orderId = options.id;
			this.loadOrderDetail();
		} else {
			uni.showToast({
				title: '订单ID不存在',
				icon: 'none'
			});
			setTimeout(() => {
				uni.navigateBack();
			}, 1500);
		}
	},
	onReady() {
		// 获取系统信息并计算iOS适配参数
		this.calculateIOSAdaptation();
	},
	methods: {
		// 计算iOS适配参数
		calculateIOSAdaptation() {
			try {
				this.systemInfo = uni.getSystemInfoSync();
				this.isIOS = this.systemInfo.platform === 'ios';

				console.log('订单详情页 - 系统信息:', this.systemInfo);
				console.log('订单详情页 - 是否iOS:', this.isIOS);

				if (this.isIOS) {
					const statusBarHeight = this.systemInfo.statusBarHeight || 44;
					const model = this.systemInfo.model || '';
					const safeAreaTop = this.systemInfo.safeArea ? this.systemInfo.safeArea.top : statusBarHeight;

					console.log('订单详情页 - 状态栏高度:', statusBarHeight);
					console.log('订单详情页 - 设备型号:', model);
					console.log('订单详情页 - 安全区域顶部:', safeAreaTop);

					let finalTopPosition;

					// 使用与订单列表页面相同的超激进适配策略
					if (model.includes('iPhone 16 Pro')) {
						// 方案1：直接使用状态栏高度，无额外间距
						finalTopPosition = statusBarHeight;
						console.log('订单详情页 - iPhone 16 Pro - 方案1（状态栏高度）:', finalTopPosition);

						// 方案2：如果还是太靠下，尝试更小的值
						if (finalTopPosition > 50) {
							finalTopPosition = 44; // 使用标准状态栏高度
							console.log('订单详情页 - iPhone 16 Pro - 方案2（标准高度）:', finalTopPosition);
						}

						// 方案3：如果还是太靠下，尝试负值
						if (finalTopPosition > 45) {
							finalTopPosition = statusBarHeight - 10; // 负偏移
							console.log('订单详情页 - iPhone 16 Pro - 方案3（负偏移）:', finalTopPosition);
						}
					} else if (model.includes('iPhone 15 Pro') || model.includes('iPhone 14 Pro')) {
						finalTopPosition = statusBarHeight;
					} else if (model.includes('iPhone X') || model.includes('iPhone 11') ||
							  model.includes('iPhone 12') || model.includes('iPhone 13')) {
						finalTopPosition = statusBarHeight;
					} else {
						finalTopPosition = statusBarHeight;
					}

					this.navbarStyle = {
						marginTop: finalTopPosition + 'px',
						position: 'relative',
						top: '0px'
					};

					console.log('订单详情页 - 超激进适配 - 最终顶部位置:', finalTopPosition + 'px');
					console.log('订单详情页 - 超激进适配 - 导航栏样式:', this.navbarStyle);
				} else {
					this.navbarStyle = {};
				}
			} catch (error) {
				console.error('订单详情页 - 计算iOS适配参数失败:', error);
				this.navbarStyle = {};
			}
		},
		goBack() {
			uni.navigateBack();
		},
		
		// 加载订单详情
		loadOrderDetail() {
			uni.showLoading({
				title: '加载中...'
			});
			
			// 调用API获取订单详情
			API.order.getDetail(this.orderId)
				.then(res => {
					uni.hideLoading();
					
					// 处理返回数据
					const detail = res.data;
					if (detail) {
						console.log('订单详情数据:', detail);
						
						// 确定订单状态
						let orderStatus = '未知状态';
						let rawStatus = 0;
						
						// 根据payStatus和status确定状态
						if (detail.payStatus === 1) {
							// 已支付
							if (detail.endTime) {
								// 已结束
								orderStatus = '已完成';
								rawStatus = 2;
							} else {
								// 进行中
								orderStatus = '进行中';
								rawStatus = 1;
							}
						} else {
							// 未支付或已取消
							if (detail.remark && detail.remark.includes('取消')) {
								orderStatus = '已取消';
								rawStatus = 3;
							} else {
								orderStatus = '未支付';
								rawStatus = 0;
							}
						}
						
						this.orderDetail = {
							id: detail.id || detail.orderId,
							orderNo: detail.orderNo || '',
							storeName: detail.storeName || detail.shopName || '未知门店',
							startTime: detail.startTime || detail.createTime || '',
							endTime: detail.endTime || '',
							duration: detail.duration ? `${detail.duration}分钟` : '',
							amount: detail.amount ? `¥${parseFloat(detail.amount).toFixed(2)}` : '¥0.00',
							status: orderStatus,
							rawStatus: rawStatus,
							deviceName: detail.deviceName || '',
							deviceNo: detail.deviceNo || detail.deviceId || '',
							deviceId: detail.deviceId || detail.deviceNo || '',
							payTime: detail.payTime || '',
							payType: '微信支付'
						};
						
						console.log('处理后的订单详情:', this.orderDetail);
					}
				})
				.catch(err => {
					uni.hideLoading();
					
					// 显示错误提示
					uni.showToast({
						title: err.message || '获取订单详情失败',
						icon: 'none'
					});
					
					// 加载失败时显示一些默认数据
					this.orderDetail = {
						id: this.orderId,
						orderNo: 'JYC202407100001',
						storeName: '今夜城堡 - 西湖店',
						startTime: '2024-07-10 14:00',
						endTime: '2024-07-10 15:00',
						duration: '60分钟',
						amount: '¥29.00',
						status: '已完成',
						rawStatus: 2,
						deviceName: '按摩椅A',
						deviceNo: 'JY10010001',
						deviceId: 'JY10010001',
						payTime: '2024-07-10 14:01',
						payType: '微信支付'
					};
				});
		},
		
		// 获取状态文本
		getStatusText(status) {
			const statusMap = {
				0: '未支付',
				1: '进行中',
				2: '已完成',
				3: '已取消'
			};
			return statusMap[status] || '未知状态';
		},
		
		// 支付订单
		payOrder() {
			uni.showLoading({
				title: '处理中...'
			});
			
			// 调用支付接口
			API.order.payment(this.orderId)
				.then(res => {
					uni.hideLoading();
					
					const payInfo = res.data;
					
					// 调用微信支付
					uni.requestPayment({
						timeStamp: payInfo.timeStamp,
						nonceStr: payInfo.nonceStr,
						package: payInfo.packageValue,
						signType: payInfo.signType,
						paySign: payInfo.paySign,
						success: () => {
							// 支付成功
							uni.showToast({
								title: '支付成功',
								icon: 'success'
							});
							
							// 刷新订单详情
							this.loadOrderDetail();
						},
						fail: (err) => {
							console.error('支付失败:', err);
							
							// 显示错误提示
							uni.showModal({
								title: '支付失败',
								content: '支付未完成，请重试或选择其他支付方式',
								showCancel: false
							});
						}
					});
				})
				.catch(err => {
					uni.hideLoading();
					
					// 显示错误提示
					uni.showModal({
						title: '支付失败',
						content: err.message || '创建支付订单失败，请重试',
						showCancel: false
					});
				});
		},
		
		// 设备异常报告
		reportProblem() {
			console.log('设备异常报告，订单ID:', this.orderId, '设备ID:', this.orderDetail.deviceId, '设备编号:', this.orderDetail.deviceNo);
			uni.navigateTo({
				url: `/pages/report/report?orderId=${this.orderId}&deviceId=${this.orderDetail.deviceId || this.orderDetail.deviceNo || ''}`
			});
		},
		
		// 联系客服
		contactService() {
			uni.navigateTo({
				url: '/pages/contact/contact'
			});
		}
	}
}
</script>

<style>
/* iOS适配 */
.page-order-detail {
	/* 适配iOS设备的安全区域和灵动岛 */
	padding-top: env(safe-area-inset-top);
}

/* 页面基础样式 */
.page-order-detail {
	color: #ffffff;
	height: 100vh;
	min-height: 100vh;
	box-sizing: border-box;
	position: relative;
	overflow: hidden;
	display: flex;
	flex-direction: column;
}

/* 页面背景样式 */
.page-background {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 0;
}

/* 背景图片样式 */
.background-image {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 0;
	object-fit: cover;
}

/* 深磨砂效果叠加层 */
.frosted-overlay {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(18, 18, 18, 0.7); /* 深色半透明背景 */
	backdrop-filter: blur(8px); /* 较强模糊效果 */
	-webkit-backdrop-filter: blur(8px);
	z-index: 1;
}

/* 顶部导航样式 */
.navbar {
	height: 90rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 30rpx;
	/* 完全移除CSS的margin-top，只使用JavaScript动态设置 */
	margin-top: 0;
	position: relative;
	z-index: 100;
}

.navbar-title {
	font-size: 40rpx;
	font-weight: 600;
}

.navbar-left, .navbar-right {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.icon-back {
	font-size: 40rpx;
	color: #A875FF;
}

/* 页面内容区域 */
.page-content {
	flex: 1;
	display: flex;
	flex-direction: column;
	position: relative;
	z-index: 5;
	padding: 0 30rpx;
}

.content {
	flex: 1;
	padding: 20rpx 0;
	position: relative;
	width: 100%;
	box-sizing: border-box;
}

/* 订单状态卡片 */
.order-status-card {
	display: flex;
	align-items: center;
	padding: 30rpx;
	border-radius: 16rpx;
	background: rgba(30, 30, 40, 0.5);
	backdrop-filter: blur(10px);
	-webkit-backdrop-filter: blur(10px);
	border: 1px solid rgba(168, 117, 255, 0.3);
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
	margin-top: 20rpx;
}

.order-status-card.active {
	background: rgba(168, 117, 255, 0.15);
}

.order-status-card.active .status-icon {
	color: #A875FF;
}

.order-status-card.active .status-title {
	color: #A875FF;
}

.order-status-card.unpaid {
	background: rgba(255, 152, 0, 0.15);
}

.order-status-card.unpaid .status-icon {
	color: #FF9800;
}

.order-status-card.unpaid .status-title {
	color: #FF9800;
}

.order-status-card.completed {
	background: rgba(76, 175, 80, 0.15);
}

.order-status-card.completed .status-icon {
	color: #4CAF50;
}

.order-status-card.completed .status-title {
	color: #4CAF50;
}

.order-status-card.cancelled {
	background: rgba(244, 67, 54, 0.15);
}

.order-status-card.cancelled .status-icon {
	color: #F44336;
}

.order-status-card.cancelled .status-title {
	color: #F44336;
}

.order-status-card.inactive {
	background: rgba(255, 255, 255, 0.07);
}

.order-status-card.inactive .status-icon {
	color: rgba(255, 255, 255, 0.5);
}

.order-status-card.inactive .status-title {
	color: rgba(255, 255, 255, 0.5);
}

.status-icon {
	margin-right: 20rpx;
	font-size: 40rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.status-text {
	display: flex;
	flex-direction: column;
}

.status-title {
	font-size: 32rpx;
	font-weight: 500;
}

.status-desc {
	font-size: 24rpx;
	color: rgba(255, 255, 255, 0.6);
	margin-top: 4rpx;
}

/* 卡片样式 */
.card {
	background: rgba(30, 30, 40, 0.5);
	border-radius: 16rpx;
	overflow: hidden;
	backdrop-filter: blur(10px);
	-webkit-backdrop-filter: blur(10px);
	border: 1px solid rgba(255, 255, 255, 0.1);
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
}

.card-header {
	padding: 20rpx 30rpx;
	border-bottom: 1px solid rgba(255, 255, 255, 0.1);
	font-size: 28rpx;
	font-weight: 500;
	display: flex;
	align-items: center;
}

.card-icon {
	margin-right: 10rpx;
}

.card-content {
	padding: 20rpx 30rpx;
}

/* 详情项样式 */
.detail-item {
	display: flex;
	justify-content: space-between;
	margin-bottom: 10rpx;
}

.detail-label {
	color: rgba(255, 255, 255, 0.7);
	font-size: 28rpx;
}

.detail-value {
	color: #FFFFFF;
	font-size: 28rpx;
}

/* 按钮样式 */
.btn {
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 40rpx;
	padding: 20rpx;
	font-size: 28rpx;
	font-weight: 600;
	transition: all 0.3s ease;
}

.btn-primary {
	background: linear-gradient(135deg, rgba(168, 117, 255, 0.8), rgba(139, 92, 246, 0.9));
	color: #FFFFFF;
	box-shadow: 0 4rpx 20rpx rgba(168, 117, 255, 0.4);
	border: none;
}

.btn-outline {
	background-color: transparent;
	border: 1px solid rgba(168, 117, 255, 0.4);
	color: #A875FF;
}

.btn-block {
	width: 100%;
}

.btn-icon {
	margin-right: 8rpx;
}

/* 辅助类 */
.primary-light {
	color: #A875FF;
	font-weight: 600;
}

.success-light {
	color: #4CAF50;
}

.error-light {
	color: #F44336;
}

.actions {
	padding: 20rpx 0 40rpx;
}

.flex {
	display: flex;
}

.justify-between {
	justify-content: space-between;
}

.mt-sm {
	margin-top: 10rpx;
}

.mt-md {
	margin-top: 20rpx;
}

.mt-lg {
	margin-top: 30rpx;
}

/* Material Icons 字体 */
@font-face {
	font-family: 'Material Icons';
	font-style: normal;
	font-weight: 400;
	src: url(https://fonts.gstatic.com/s/materialicons/v139/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format('woff2');
}

.material-icons {
	font-family: 'Material Icons';
	font-weight: normal;
	font-style: normal;
	font-size: 24rpx;
	line-height: 1;
	letter-spacing: normal;
	text-transform: none;
	display: inline-block;
	white-space: nowrap;
	word-wrap: normal;
	direction: ltr;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.material-icons.md-18 {
	font-size: 36rpx;
}

.material-icons.md-24 {
	font-size: 48rpx;
}

.material-icons.md-48 {
	font-size: 96rpx;
}

.material-icons.text-primary {
	color: var(--text-primary, #FFFFFF);
}

.material-icons.text-tertiary {
	color: var(--text-tertiary, rgba(255, 255, 255, 0.5));
}

.icon-inline {
	margin-right: 8rpx;
	vertical-align: middle;
}

/* 移除所有CSS适配，完全依赖JavaScript动态设置 */
</style>