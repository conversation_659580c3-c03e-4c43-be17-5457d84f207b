









































































































































































































































































/* Material Icons 字体 */
@font-face {
	font-family: 'Material Icons';
	font-style: normal;
	font-weight: 400;
	src: url(https://fonts.gstatic.com/s/materialicons/v139/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format('woff2'),
		 url(https://fonts.googleapis.com/icon?family=Material+Icons) format('truetype');
	font-display: block;
}
.data-v-47b4a9d0:root {
	--primary: #8B5CF6;
	--primary-dark: #8B5CF6;
	--primary-light: #A875FF;
}
.tab-bar.data-v-47b4a9d0 {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	height: 120rpx; /* 增加基础高度，适配安卓设备 */
	background: #FFFFFF;
	display: flex;
	flex-wrap: nowrap; /* 不换行 */
	z-index: 999;
	backdrop-filter: blur(10px);
	-webkit-backdrop-filter: blur(10px);
	box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
	border-top: 1px solid rgba(200, 200, 200, 0.3);
	transition: all 0.3s ease, opacity 0.3s ease;
	width: 100%; /* 确保宽度为100% */
	justify-content: space-around; /* 使用space-around而不是默认的stretch，减少中间空白 */
	padding-bottom: constant(safe-area-inset-bottom);
	padding-bottom: env(safe-area-inset-bottom, 20rpx); /* 安卓下默认20rpx */
	min-height: 120rpx; /* 确保最小高度 */
}
/* 安卓设备专属样式优化 */
.android-tab-bar.data-v-47b4a9d0 {
	height: 160rpx !important; /* 安卓设备增加高度 */
	padding-bottom: 40rpx !important; /* 安卓设备增加底部内边距 */
	min-height: 160rpx !important; /* 确保安卓设备最小高度 */
}
/* 微信小程序环境下的安卓设备优化 */
.android-tab-bar.data-v-47b4a9d0 {
	height: 170rpx !important; /* 微信小程序中安卓设备需要更大高度 */
	padding-bottom: 45rpx !important; /* 微信小程序中安卓设备需要更大底部内边距 */
	min-height: 170rpx !important; /* 确保微信小程序中安卓设备最小高度 */
}
.android-tab-item.data-v-47b4a9d0 {
	height: 170rpx !important; /* 微信小程序中安卓设备项目高度 */
	margin-top: 25rpx !important; /* 微信小程序中安卓设备上边距 */
	padding-bottom: 20rpx !important; /* 微信小程序中安卓设备底部内边距 */
}
.android-icon.data-v-47b4a9d0 {
	font-size: 60rpx !important; /* 微信小程序中安卓设备图标尺寸 */
	margin-top: 20rpx !important; /* 微信小程序中安卓设备图标上边距 */
}
.android-text.data-v-47b4a9d0 {
	font-size: 32rpx !important; /* 微信小程序中安卓设备文字尺寸 */
	margin-top: 10rpx !important; /* 微信小程序中安卓设备文字上边距 */
	font-weight: 600 !important; /* 微信小程序中安卓设备文字加粗 */
}
.tab-bar.safe-area-inset-bottom.data-v-47b4a9d0 {
	padding-bottom: env(safe-area-inset-bottom); /* 添加安全区域内边距 */
}
.safe-area-placeholder.data-v-47b4a9d0 {
	width: 100%;
	height: env(safe-area-inset-bottom);
	background: #FFFFFF !important;
}
.tab-bar-border.data-v-47b4a9d0 {
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 1px;
	background: rgba(200, 200, 200, 0.3);
	-webkit-transform: scaleY(0.5);
	        transform: scaleY(0.5);
}
.tab-bar-item.data-v-47b4a9d0 {
	flex: 1 1 0; /* 平均分配宽度 */
	min-width: 120rpx; /* 设置最小宽度 */
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	color: rgba(100, 100, 100, 0.8);
	transition: all 0.3s cubic-bezier(0.19, 1, 0.22, 1);
	position: relative;
	padding-top: 0;
	padding-bottom: 0;
	margin-top: 15rpx; /* 增加上边距，适配更高的tab-bar */
	height: 120rpx; /* 增加项目高度与tab-bar一致 */
	min-height: 120rpx; /* 确保最小高度 */
}
/* 安卓设备TabBar项目样式优化 */
.android-tab-item.data-v-47b4a9d0 {
	height: 140rpx !important; /* 安卓设备增加项目高度 */
	margin-top: 20rpx !important; /* 安卓设备增加上边距 */
	padding-bottom: 12rpx !important; /* 安卓设备增加底部内边距 */
}
.material-icons.data-v-47b4a9d0 {
	font-family: 'Material Icons';
	font-weight: normal;
	font-style: normal;
	font-size: 46rpx;
	line-height: 1;
	letter-spacing: normal;
	text-transform: none;
	display: block;
	white-space: nowrap;
	word-wrap: normal;
	direction: ltr;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	transition: all 0.3s ease;
	margin: 0 auto;
	text-align: center;
	text-shadow: 0 0 5rpx rgba(255, 255, 255, 0.3);
	margin-top: 8rpx; /* 调整图标上边距 */
}
/* 安卓设备图标样式优化 */
.android-icon.data-v-47b4a9d0 {
	font-size: 56rpx !important; /* 安卓设备增大图标尺寸 */
	margin-top: 16rpx !important; /* 安卓设备增加图标上边距 */
}
.tab-text.data-v-47b4a9d0 {
	font-size: 24rpx; /* 减小字体大小 */
	margin-top: 0; /* 减少与图标的间距 */
	transition: all 0.3s ease;
	text-align: center;
	width: 100%;
	font-weight: 500;
	text-shadow: 0 0 5rpx rgba(255, 255, 255, 0.3);
}
/* 安卓设备文字样式优化 */
.android-text.data-v-47b4a9d0 {
	font-size: 28rpx !important; /* 安卓设备增大文字尺寸 */
	margin-top: 6rpx !important; /* 安卓设备增加与图标的间距 */
	font-weight: 600 !important; /* 安卓设备增加字重 */
}
.tab-bar-item.active.data-v-47b4a9d0 {
	color: var(--primary-light, #A875FF);
	-webkit-transform: translateY(0);
	        transform: translateY(0);
}
.tab-bar-item.active .material-icons.data-v-47b4a9d0 {
	text-shadow: 0 0 10rpx rgba(168, 117, 255, 0.4);
	-webkit-transform: scale(1.1);
	        transform: scale(1.1);
}
.tab-bar-item.active .tab-text.data-v-47b4a9d0 {
	font-weight: 600;
}
/* 添加按下效果 */
.tab-bar-item.data-v-47b4a9d0:active {
	opacity: 0.8;
}

