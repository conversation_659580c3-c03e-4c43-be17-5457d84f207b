{"pages": [{"path": "pages/index/index", "style": {"navigationBarTitleText": "今夜城堡", "navigationStyle": "custom", "app-plus": {"titleNView": false, "bounce": "none", "animationType": "none", "animationDuration": 0, "popGesture": "none"}}}, {"path": "pages/profile/profile", "style": {"navigationBarTitleText": "我的", "navigationStyle": "custom", "app-plus": {"titleNView": false, "bounce": "none", "animationType": "none", "animationDuration": 0}}}, {"path": "pages/orders/orders", "style": {"navigationBarTitleText": "我的订单", "navigationStyle": "custom", "app-plus": {"titleNView": false, "bounce": "none", "animationType": "none", "animationDuration": 0}}}, {"path": "pages/orders/detail", "style": {"navigationBarTitleText": "订单详情", "navigationStyle": "custom", "app-plus": {"titleNView": false, "bounce": "none", "animationType": "none", "animationDuration": 0}}}, {"path": "pages/contact/contact", "style": {"navigationBarTitleText": "联系客服", "navigationStyle": "custom", "app-plus": {"titleNView": false, "bounce": "none", "animationType": "none", "animationDuration": 0}}}, {"path": "pages/report/report", "style": {"navigationBarTitleText": "设备异常上报", "navigationStyle": "custom", "app-plus": {"titleNView": false, "bounce": "none", "animationType": "none", "animationDuration": 0}}}, {"path": "pages/scan/scan", "style": {"navigationBarTitleText": "扫码连接", "navigationStyle": "custom", "app-plus": {"titleNView": false, "bounce": "none", "animationType": "none", "animationDuration": 0}}}, {"path": "pages/scan/device", "style": {"navigationBarTitleText": "房间连接", "navigationStyle": "custom", "app-plus": {"titleNView": false, "bounce": "none", "animationType": "none", "animationDuration": 0}}}, {"path": "pages/scan/payment-result", "style": {"navigationBarTitleText": "支付结果", "navigationStyle": "custom", "app-plus": {"titleNView": false, "bounce": "none", "animationType": "none", "animationDuration": 0}}}], "subPackages": [{"root": "packageA", "pages": [{"path": "pages/zhaoshang/index", "style": {"navigationBarTitleText": "招商合作", "navigationStyle": "custom", "app-plus": {"titleNView": false, "bounce": "none", "animationType": "none", "animationDuration": 0}}}]}], "tabBar": {"custom": true, "color": "rgba(255, 255, 255, 0.5)", "selectedColor": "#A875FF", "backgroundColor": "rgba(30, 30, 30, 0.95)", "borderStyle": "black", "list": [{"pagePath": "pages/index/index", "text": "首页"}, {"pagePath": "pages/profile/profile", "text": "我的"}]}, "globalStyle": {"navigationBarTextStyle": "white", "navigationBarTitleText": "今夜城堡", "navigationBarBackgroundColor": "#121212", "backgroundColor": "#121212", "app-plus": {"background": "#121212", "animationType": "none", "animationDuration": 0}}, "uniIdRouter": {}}