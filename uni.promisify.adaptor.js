uni.addInterceptor({
  returnValue (res) {
    if (!(!!res && (typeof res === "object" || typeof res === "function") && typeof res.then === "function")) {
      return res;
    }
    return new Promise((resolve, reject) => {
      res.then((res) => {
        if (!res) return resolve(res) 
        
        // 处理hideLoading失败的情况
        if (res[0] && res[0].errMsg) {
          // 处理hideLoading失败
          if (res[0].errMsg.indexOf('hideLoading:fail') !== -1) {
            console.warn('hideLoading 调用失败，可能是因为没有对应的 toast 实例：', res[0].errMsg);
            return resolve({});
          }
          
          // 处理showLoading失败
          if (res[0].errMsg.indexOf('showLoading:fail') !== -1) {
            console.warn('showLoading 调用失败：', res[0].errMsg);
            return resolve({});
          }
          
          // 处理蓝牙相关错误
          if (res[0].errMsg.indexOf('bluetooth') !== -1 || 
              res[0].errMsg.indexOf('BLE') !== -1) {
            console.warn('蓝牙操作失败：', res[0].errMsg);
            return reject(res[0]);
          }
        }
        
        return res[0] ? reject(res[0]) : resolve(res[1])
      }).catch(error => {
        console.error('Promise处理过程中发生错误:', error);
        reject(error);
      });
    });
  },
});