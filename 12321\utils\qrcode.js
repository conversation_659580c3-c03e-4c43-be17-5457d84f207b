/**
 * 二维码生成工具
 * 基于weapp-qrcode库
 */

// 引入weapp-qrcode库（使用本地路径）
const drawQrcode = require('./weapp-qrcode/weapp.qrcode.js');

/**
 * 生成二维码
 * @param {Object} options - 配置项
 * @param {String} options.canvasId - canvas元素ID
 * @param {String} options.text - 要生成二维码的文本
 * @param {Number} options.width - 二维码宽度，默认200
 * @param {Number} options.height - 二维码高度，默认200
 * @param {String} options.colorDark - 前景色，默认黑色
 * @param {String} options.colorLight - 背景色，默认白色
 * @param {Function} options.callback - 生成完成回调函数
 */
function createQRCode(options) {
  const {
    canvasId,
    text,
    width = 200,
    height = 200,
    colorDark = '#000000',
    colorLight = '#ffffff',
    callback
  } = options;

  try {
    // 使用drawQrcode函数直接绘制二维码
    drawQrcode({
      canvasId: canvasId,
      text: text,
      width: width,
      height: height,
      colorDark: colorDark,
      colorLight: colorLight,
      // 使用最高级别的纠错能力
      correctLevel: 3, // QR.CorrectLevel.H 的值是3
      callback: (res) => {
        if (callback && typeof callback === 'function') {
          callback(res);
        }
      }
    });
    return true;
  } catch (e) {
    console.error('生成二维码失败:', e);
    return false;
  }
}

/**
 * 保存二维码到相册
 * @param {String} canvasId - canvas元素ID
 * @param {Function} success - 成功回调
 * @param {Function} fail - 失败回调
 */
function saveQRCodeToAlbum(canvasId, success, fail) {
  // 获取临时路径
  wx.canvasToTempFilePath({
    canvasId: canvasId,
    success: function(res) {
      // 保存图片
      wx.saveImageToPhotosAlbum({
        filePath: res.tempFilePath,
        success: function(res) {
          if (success && typeof success === 'function') {
            success(res);
          }
        },
        fail: function(res) {
          if (fail && typeof fail === 'function') {
            fail(res);
          }
        }
      });
    },
    fail: function(res) {
      if (fail && typeof fail === 'function') {
        fail(res);
      }
    }
  });
}

// 使用CommonJS模块导出
module.exports = {
  createQRCode,
  saveQRCodeToAlbum
}; 