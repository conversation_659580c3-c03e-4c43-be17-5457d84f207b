﻿ZX-D30
双模蓝牙模块技术手册

版本：V1.0
日期：2022/04/06



ZX-D30技术手册

目录
1 模块介绍...............................................................................................2

1.1 概述........................................................................................... 2
1.2 特性........................................................................................... 3
1.3 应用........................................................................................... 3
1.4 基础参数表............................................................................. 4
1.5 工作电流参数表.................................................................... 4
1.6 出厂默认配置参数............................................................... 4

2 应用接口...............................................................................................6
2.1 模块引脚定义.........................................................................6
2.2 引脚功能表............................................................................. 6
2.3 特殊引脚 IO功能表............................................................. 7
2.4 电源设计..................................................................................7
2.5 串口电平转换参考电路...................................................... 8
2.6 应用原理图............................................................................. 9

3 回流焊曲线图...................................................................................10
4 Layout 注意事项..............................................................................10
5 AT指令集.......................................................................................... 12
6 更新记录............................................................................................ 18
7 联系我们............................................................................................ 19
8 免责申明和版权公告..................................................................... 19

深圳市智兴微科技有限公司 1 www.wlsiot.com



ZX-D30技术手册

1 模块介绍
1.1 概述

ZX-D30 是深圳市智兴微科技有限公司专

为蓝牙无线数据传输打造的一款小尺寸蓝牙双

模模块，该模块支持蓝牙 BLE5.0 协议以及集

成了高性能的 2.4GHz RF 收发器、丰富特性

的基带处理器、ARM968E-MCU 和多种外设

接口，支持可编程协议。本模组具有极好的稳

定性、超低成本以及超低的功耗和接收灵敏度

高等优点，并且支持苹果、安卓 APP 及微信

小程序连接，可适配客户各种开发项目。

深圳市智兴微科技有限公司 2 www.wlsiot.com



ZX-D30技术手册

1.2 特性

 CPU：ARM968E-S

 蓝牙BLE4.2 + SPP3.0

 双模透传

 工作频率：2.4GHZ

 可视距离：80M

 传输速率：250Kbps/1Mbps/2/Mbps

 发射功率：-20 dBm~4dBm

 接收灵敏度：-97dBm

 支持UART，IIC，SPI，GPIO硬件接口

 工作温度：-40℃~+125℃

 天线采用板载天线

1.3 应用

 智能家居

 定位追踪

 智能教育设备

 测量与监控系统

 工业传感器与控制

 医疗设备监测与无线控制

深圳市智兴微科技有限公司 3 www.wlsiot.com



ZX-D30技术手册

1.4 基础参数表

参数名 描述 参数名 描述
型号 ZX-D30 模块尺寸 13×17x1.5 mm

蓝牙版本 BLE 4.2 + SPP3.0 通信距离 80M
工作频段 2.402GHz-2.480GHz ISM band 串口透传速率 BLE 4KB/S SPP 16KB/S
工作电压 1.8V~3.6V 功能 BLE及 SPP 透传
外设接口 UART/SPI/I2C/ADC/GPIO 天线 板载天线
调制方式 GFSK 工作温度 -40℃~+125℃

1.5 工作电流参数表

工作模式 状态 平均电流

深度睡眠(无广播) 无广播 0.5uA
广播状态 100ms 广播间隔 5.3mA

BLE 连接 4.6mA
连接状态

SPP协议连接 5.6mA

1.6 出厂默认配置参数

功能 出厂默认参数 指令

串口波特率 9600 AT+BAUD=3

蓝牙名称 D30SP_XXXXXX AT+NAME=D30SP_XXX XXX

BLE 服务 UUID FFE0 AT+SUUID=FFE0

BLE 读写特征值 UUID FFE1 AT+CUUID=FFE1

常见问题
1、双模的最大传输单元(MTU) 是多少

即 APP 发送接收的单个数据包大小：

深圳市智兴微科技有限公司 4 www.wlsiot.com



ZX-D30技术手册

BLE : 20 字节
SPP: 100 字节

2、串口写入蓝牙模块的数据长度是否有限制
波特率 38400 以下，数据写入无长度限制，与 APP 相互透

传不丢包。
3、双模使用场景

双模指同时支持蓝牙 BLE 协议和 SPP 协议。一般 SPP 协议
应用于安卓系统、PC 电脑蓝牙透传；BLE 协议应用于苹果系统。
如若安卓苹果都走 BLE 协议或需小程序连接建议使用我们的”
单 BLE”固件。

深圳市智兴微科技有限公司 5 www.wlsiot.com



ZX-D30技术手册

2 应用接口
2.1 模块引脚定义

2.2 引脚功能表

管脚 名称 类型 功能

1 P00 I/O TXD / 可编程输入输出引脚

2 P01 I/O RXD / 可编程输入输出引脚

3 RST I/O 复位引脚 (低电平有效)

4 P17 I/O 可编程输入输出引脚

5 P16 I/O 可编程输入输出引脚

6 VCC POWER 电源 (1.8 - 3.6V)

7 GND GND 地

深圳市智兴微科技有限公司 6 www.wlsiot.com



ZX-D30技术手册
8 NC NC 悬空

9 P33 I/O 定制输入输出引脚

10 P13 I/O 定制输入输出引脚

11 P12 I/O LED 灯状态脚

12 P11 I/O 按键脚

13 P10 I/O 蓝牙状态输出引脚

14 P07 I/O 定制输入输出引脚

15 P06 I/O 定制输入输出引脚

16 P05 I/O 定制输入输出引脚

17 P04 I/O 定制输入输出引脚

18 P03 I/O 定制输入输出引脚

19 P02 I/O 定制输入输出引脚

20 NC NC 悬空

21 NC NC 悬空

2.3 特殊引脚 IO功能表

IO 脚 功能描述

P10 蓝牙连接状态输出引脚：已连接（高电平）未连接（低电平）

P11 输入按键引脚：短按(断开蓝牙连接) 长按 3S(恢复出厂设置)

LED状态指示灯（引脚P12高电平点亮）
LED 显示 连接状态

匀速慢(500ms/on, 500ms/off) 未连接

长亮 已连接

2.4 电源设计

ZX-D30 的供电范围是 1.8V~3.6V，推荐 3.3V 的工作电压
最佳。建议使用 LDO 供电，如使用 DC-DC 建议纹波控制在 30mV
以内。

深圳市智兴微科技有限公司 7 www.wlsiot.com



ZX-D30技术手册

2.5 串口电平转换参考电路

注：如果单片机为 5V串口连接模块时可参考上图电平转换电路,

网络M_TX/RX为模块串口，网络TX/RX为单片机串口

深圳市智兴微科技有限公司 8 www.wlsiot.com



ZX-D30技术手册

2.6 应用原理图

深圳市智兴微科技有限公司 9 www.wlsiot.com



ZX-D30技术手册

3 回流焊曲线图

4 Layout 注意事项
蓝牙模块工作在 2.4G 无线频段，应尽量避免各种因素对无

线收发的影响，注意以下几点：
1、包围蓝牙模块的产品外壳避免使用金属，当使用部分金

属外壳时，应尽量让模块天线部分远离金属部分。
2、产品内部金属连接线或者金属螺钉，应尽量远离模块天

线部分。
3、PCB 布板：蓝牙模块的天线部分的是 PCB 天线，由于金

属会削弱天线的功能，在给模块布板的时候，模块天线下面严

深圳市智兴微科技有限公司 10 www.wlsiot.com



ZX-D30技术手册

禁铺地和走线，若能挖空更好。
4、模块布局参考方案如下图所示：
方案 1（推荐）：将模组沿 PCB 板放置，且天线在板框外；
方案 2:将模组沿 PCB 板边放置，天线沿板边放置且下方挖

空；
方案 3:将模组沿 PCB 板边放置，天线沿板边放置下方均不

铺铜；

深圳市智兴微科技有限公司 11 www.wlsiot.com



ZX-D30技术手册

5 AT指令集
1、<AT>测试指令..........................................................................................................14

2、<AT+VERS>获取软件版本号................................................................................ 14

3、< AT+ADDR >设置/查询模块蓝牙地址..............................................................14

4、<AT+NAME>设置/查询设备名称（自动重启生效）...................................... 14

5、<AT+BAUD>设置/查询波特率（自动重启生效）........................................... 15

6、<AT+SUUID>设置/查询 Service UUID(手动重启生效)...................................15

7、<AT+CUUID>设置/查询Chara UUID(手动重启生效）.................................. 15

8、<AT+WUUID>设置/查询WriteUUID（手动重启生效）............................... 16

9、<AT+RESET> 软件重启 (500ms 后重启)............................................................16

10、<AT+DEFAULT> 软件重置 (500ms 后恢复默认设置)...................................16

11、<AT+TYPE> 设置/查询配对模式（手动重启生效）......................................16

12、<AT+PIN>设置/查询 SPP配对密码（手动重启生效）................................ 17

13、<AT+SLEEP>进入休眠关机模式........................................................................ 17

深圳市智兴微科技有限公司 12 www.wlsiot.com



ZX-D30技术手册

AT 指令的配置与收发注意要点：

模块串口为 3.3V TTL 电平，使用串口调试助手，按照 9600,

N, 8, 1 进行配置，修改 AT 指令时，推荐使用上图智兴微公司

自主开发的串口调试助手，右边集合各类 AT 指令，直接发送即

可。

使用其他串口工具发送 AT 指令时，务必在指令后面加入一

个回车，且只能有一个回车。

单片机发送 AT 指令时，需在指令结尾加入\r\n 或 0x0D

0x0A, 回车换行符。

注： AT 指令只有在蓝牙未连接的状态下有效，蓝牙连接成

功后自动转为透传模式，数据将不做解析完全透传给手机 APP。
深圳市智兴微科技有限公司 13 www.wlsiot.com



ZX-D30技术手册

1、<AT>测试指令

指令 响应 参数

AT OK 无

2、<AT+VERS>获取软件版本号

指令 响应 参数

AT+VERS OK+G_VERS=<Param> 版本号

3、< AT+ADDR >设置/查询模块蓝牙地址

指令 响应 参数

AT+ADDR? OK+G_ADDR=<Param>
Param： 模块蓝牙地址

AT+ADDR=<Param> OK+S_ADDR=<Param>

例：设置蓝牙 MAC 地址
发送：AT+ADDR=F1:F2:F3:F4:F5:F6
返回：OK+S_ADDR= F1:F2:F3:F4:F5:F6

4、<AT+NAME>设置/查询设备名称（自动重启生效）

指令 响应 参数

AT+NAME? OK+G_NAME=<Param> Param：
蓝牙设备名称默认名

称：
AT+NAME=<Param> OK+S_NAME=<Param> “D30SP_XXXXXX”

最长： 20 字节

例：修改蓝牙名：发送：AT+NAME=BLE_DEV——设置模块设
备名为：“BLE_DEV”返回：OK+S_NAME=BLE_DEV——设置模块设备
名为：“BLE_DEV”成功。

深圳市智兴微科技有限公司 14 www.wlsiot.com



ZX-D30技术手册

5、<AT+BAUD>设置/查询波特率（自动重启生效）

指令 响应 参数

AT+BAUD? OK+G_BAUD=<Param> Param ： 波 特 率
（ bits/s ） 取 值 如 下
（1~8）：

AT+BAUD=<Param> OK+S_BAUD=<Param>
1——2400
2——4800
3——9600
4——19200
5——38400
6——57600
7——115200
8——128000
默认设置： 3 (9600)

例：设置串口波特率：115200
发送：AT+BAUD=7
返回：OK+S_BAUD=7

6、<AT+SUUID>设置/查询 Service UUID(手动重启生效)

指令 响应 参数
AT+SUUID? OK+G_SUUID=<Param> Param： 0001~FFFF

AT+SUUID=<Param> OK+S_SUUID=<Param> 默认值： FFE0

7、<AT+CUUID>设置/查询 Chara UUID(手动重启生效）

指令 响应 参数

AT+CUUID? OK+G_CUUID=<Param> Param： 0001~FFFF 默
AT+CUUID=<Param> OK+S_CUUID=<Param> 认值：FFE1

注：该 UUID 属性 <Write / Notify>

深圳市智兴微科技有限公司 15 www.wlsiot.com



ZX-D30技术手册

8、<AT+WUUID>设置/查询WriteUUID（手动重启生效）

指令 响应 参数

AT+WUUID? OK+G_WUUID=<Param> Param ： 0001~FFFF

AT+WUUID=<Param> OK+S_WUUID=<Param> 默认值：FFE2

注：该 UUID 属性 <Write>

9、<AT+RESET> 软件重启 (500ms 后重启)

指令 响应 参数

AT+RESET OK 无

10、<AT+DEFAULT> 软件重置 (500ms 后恢复默认设置)

指令 响应 参数

AT+DEFAULT OK 无

恢复模块默认出厂设置值，模块的所有设置均会被重置，
恢复到出厂时状态，恢复出厂设置后，模块延时 500ms 后重启，
如无必要，请慎用。

11、<AT+TYPE> 设置/查询配对模式（手动重启生效）

指令 响应 参数

AT+TYPE? OK+G_TYPE=<Param> Param：
0: 关闭密码配对

AT+ TYPE =<Param> OK+S_TYPE=<Param> 1: 开启密码配对
默认设置： 0

深圳市智兴微科技有限公司 16 www.wlsiot.com



ZX-D30技术手册

12、<AT+PIN>设置/查询 SPP配对密码（手动重启生效）

指令 响应 参数

AT+PIN OK+G_PIN=1234 Param:
4 位密码

AT+PIN=<Param> OK+S_PIN=1234 默认值：1234

注意：密码功能仅支持 SPP 协议，适用于安卓手机，BLE
协议目前无法修改密码，即苹果手机走 BLE 协议无密码功能。

13、<AT+SLEEP>进入休眠关机模式

指令 响应 参数

AT+SLEEP OK 无

说明：进入休眠模式后，功耗低至 1uA，此时无蓝牙广播
需要串口发任意数据唤醒或 P11 IO 口低电平脉冲唤醒。

深圳市智兴微科技有限公司 17 www.wlsiot.com



ZX-D30技术手册

6 更新记录
版本 固件版本 时间 描述

V1.3 V1.2.3 2022/04/06 1、 新增 MAC 地址修改功能

深圳市智兴微科技有限公司 18 www.wlsiot.com



ZX-D30技术手册

7 联系我们
深圳市智兴微科技有限公司

官方官网：www.wlsiot.com

样品购买：wlsiot.taobao.com

咨询热线：0755-27087743

公司地址：深圳市宝安区福永街道兴围锦灏大厦1912

8 免责申明和版权公告
本文中的信息，包括供参考的 URL 地址，如有变更，恕不

另行通知。文档“按现状”提供，不负任何担保责任，包括对
适销性、适用于特定用途或非侵权性的任何担保，和任何提案、
规格或样品在他处提到的任何担保。本文档不负任何责任，包
括使用本文档内信息产生的侵犯任何专利权行为的责任。本文
档在此未以禁止反言或其他方式授予任何知识产权使用许可，
不管是明示许可还是暗示许可。文中所得测试数据均为测试所
得，实际结果可能略有差异。蓝牙联盟成员标志归蓝牙联盟所
有。文中提到的所有商标名称、商标和注册商标均属其各自所
有者的财产，特此声明。最终解释权归深圳市智兴微科技有限
公司所有。

深圳市智兴微科技有限公司 19 www.wlsiot.com