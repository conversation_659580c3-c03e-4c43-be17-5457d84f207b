













































































































































































































































































































































































































































































































































































































/* iOS适配 */
.page-orders {
	/* 适配iOS设备的安全区域和灵动岛 */
	padding-top: env(safe-area-inset-top);
}
/* 页面基础样式 */
.page-orders {
	color: #ffffff;
	height: 100vh;
	min-height: 100vh;
	box-sizing: border-box;
	position: relative;
	overflow: hidden; /* 确保页面不会整体滚动 */
	display: flex;
	flex-direction: column;
}
/* 页面背景样式 */
.page-background {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 0;
}
/* 背景图片样式 */
.background-image {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 0;
	object-fit: cover;
}
/* 深磨砂效果叠加层 */
.frosted-overlay {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(18, 18, 18, 0.7); /* 深色半透明背景 */
	backdrop-filter: blur(8px); /* 较强模糊效果 */
	-webkit-backdrop-filter: blur(8px);
	z-index: 1;
}
/* 顶部导航样式 */
.navbar {
	height: 90rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 30rpx;
	/* 完全移除CSS的margin-top，只使用JavaScript动态设置 */
	margin-top: 0;
	position: relative;
	z-index: 100;
}
.navbar-title {
	font-size: 40rpx;
	font-weight: 600;
}
.navbar-left, .navbar-right {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}
/* 页面内容区域 */
.page-content {
	flex: 1;
	display: flex;
	flex-direction: column;
	position: relative;
	z-index: 5;
	overflow: hidden; /* 确保内部content滚动 */
	height: calc(100vh - 25px - 90rpx); /* 计算剩余高度 */
}
/* 标签页样式 - 往下移动 */
.tabs-container {
	position: relative;
	z-index: 10;
	border-bottom: 1px solid rgba(255, 255, 255, 0.1);
	margin-bottom: 20rpx;
	margin-top: 20rpx;
	background-color: rgba(30, 30, 40, 0.5);
	border-radius: 20rpx;
	backdrop-filter: blur(10px);
	-webkit-backdrop-filter: blur(10px);
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
	padding: 0 10rpx;
	flex-shrink: 0; /* 防止标签栏被压缩 */
}
.tabs {
	display: flex;
	height: 90rpx;
	border-bottom: none;
	position: relative;
	overflow: hidden;
}
.tab {
	flex: 1;
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: center;
	position: relative;
	color: var(--text-tertiary, rgba(255, 255, 255, 0.5));
	font-size: 30rpx;
	transition: all 0.3s ease;
	overflow: hidden;
	gap: 8rpx;
}
.tab.active {
	color: var(--primary-light, #A875FF);
	font-weight: 500;
	text-shadow: 0 0 8rpx rgba(168, 117, 255, 0.5);
}
.tab:before {
	content: '';
	position: absolute;
	width: 100%;
	height: 100%;
	top: 0;
	left: 0;
	background: rgba(168, 117, 255, 0.05);
	opacity: 0;
	transition: opacity 0.3s ease;
	z-index: -1;
}
.tab.active:before {
	opacity: 1;
}
.tab-line {
	position: absolute;
	bottom: 0;
	width: 40rpx;
	height: 4rpx;
	background: linear-gradient(to right, rgba(168, 117, 255, 0.8), rgba(139, 92, 246, 0.9));
	border-radius: 2rpx;
	box-shadow: 0 0 10rpx rgba(168, 117, 255, 0.5);
	transition: width 0.3s ease;
}
.tab:active {
	-webkit-transform: scale(0.98);
	        transform: scale(0.98);
}
/* 内容区域 */
.content {
	flex: 1;
	padding: 30rpx 30rpx;
	position: relative;
	width: 100%;
	box-sizing: border-box;
	overflow-y: auto !important; /* 强制确保可以垂直滚动 */
	-webkit-overflow-scrolling: touch; /* 增加滚动惯性 */
	height: 100%; /* 确保高度占满 */
	padding-bottom: 50rpx; /* 添加底部内边距，确保内容不会被遮挡 */
	max-height: calc(100vh - 25px - 90rpx - 130rpx); /* 减去导航栏和标签栏的高度 */
}
/* 订单卡片样式 */
.order-item {
	margin-bottom: 30rpx;
	padding: 30rpx;
	background-color: rgba(30, 30, 40, 0.5);
	border-radius: 20rpx;
	border: 1px solid rgba(168, 117, 255, 0.3);
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
	backdrop-filter: blur(10px);
	-webkit-backdrop-filter: blur(10px);
	transition: all 0.3s ease;
	width: 100%;
	box-sizing: border-box;
	margin-left: auto;
	margin-right: auto;
}
.order-item:active {
	-webkit-transform: scale(0.98);
	        transform: scale(0.98);
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}
.order-header {
	margin-bottom: 10rpx;
}
.order-store {
	display: flex;
	flex-direction: column;
}
.order-store .title-sm {
	margin-bottom: 0;
}
/* 分隔线 */
.divider {
	height: 1px;
	background: linear-gradient(to right, rgba(168, 117, 255, 0.1), rgba(168, 117, 255, 0.3), rgba(168, 117, 255, 0.1));
	margin: 10rpx 0;
}
/* 状态标签 */
.status {
	padding: 6rpx 18rpx;
	border-radius: 20rpx;
	font-size: 26rpx;
	font-weight: 500;
	display: inline-flex;
	align-items: center;
	justify-content: center;
	height: 40rpx;
	min-width: 80rpx;
	text-align: center;
	line-height: 1;
	white-space: nowrap;
	box-sizing: border-box;
}
.status.active {
	background-color: rgba(168, 117, 255, 0.15);
	color: var(--primary-light, #A875FF);
	border: 1px solid rgba(168, 117, 255, 0.25);
	box-shadow: 0 0 8rpx rgba(168, 117, 255, 0.2);
}
.status.unpaid {
	background-color: rgba(255, 152, 0, 0.15);
	color: #FF9800;
	border: 1px solid rgba(255, 152, 0, 0.25);
	box-shadow: 0 0 8rpx rgba(255, 152, 0, 0.2);
}
.status.completed {
	background-color: rgba(76, 175, 80, 0.15);
	color: #4CAF50;
	border: 1px solid rgba(76, 175, 80, 0.25);
	box-shadow: 0 0 8rpx rgba(76, 175, 80, 0.2);
}
.status.cancelled {
	background-color: rgba(244, 67, 54, 0.15);
	color: #F44336;
	border: 1px solid rgba(244, 67, 54, 0.25);
	box-shadow: 0 0 8rpx rgba(244, 67, 54, 0.2);
}
.status.inactive {
	background-color: rgba(255, 255, 255, 0.07);
	color: var(--text-tertiary, rgba(255, 255, 255, 0.5));
	border: 1px solid rgba(255, 255, 255, 0.1);
}
/* 订单详情项 */
.detail-item {
	margin-bottom: 20rpx;
}
/* 详情标签和值 - 增大字体 */
.detail-label {
	color: rgba(255, 255, 255, 0.7);
	font-size: 30rpx;
	display: flex;
	align-items: center;
}
.detail-value {
	color: #FFFFFF;
	font-size: 30rpx;
}
/* 按钮样式 */
.btn {
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 15rpx;
	padding: 20rpx;
	font-size: 28rpx;
	font-weight: 600;
	transition: all 0.3s ease;
}
.btn-primary {
	background: linear-gradient(135deg, rgba(168, 117, 255, 0.8), rgba(139, 92, 246, 0.9));
	color: #FFFFFF;
	box-shadow: 0 4rpx 20rpx rgba(168, 117, 255, 0.4);
	border: none;
}
.btn-primary:active {
	-webkit-transform: scale(0.98);
	        transform: scale(0.98);
	box-shadow: 0 2rpx 10rpx rgba(168, 117, 255, 0.3);
}
.btn-outline {
	background-color: transparent;
	border: 1px solid rgba(168, 117, 255, 0.4);
	color: var(--primary-light, #A875FF);
}
.btn-outline:active {
	background-color: rgba(168, 117, 255, 0.1);
	-webkit-transform: scale(0.98);
	        transform: scale(0.98);
}
.btn-sm {
	font-size: 26rpx;
	padding: 15rpx;
}
/* 空状态样式 */
.empty-state {
	padding: 100rpx 0;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	background-color: rgba(30, 30, 40, 0.5);
	border-radius: 20rpx;
	backdrop-filter: blur(10px);
	-webkit-backdrop-filter: blur(10px);
	border: 1px solid rgba(255, 255, 255, 0.1);
	margin-top: 30rpx; /* 添加顶部间距 */
	min-height: 400rpx; /* 确保空状态有足够高度 */
}
.empty-text {
	margin-top: 30rpx;
	margin-bottom: 30rpx;
	color: var(--text-tertiary, rgba(255, 255, 255, 0.5));
	font-size: 28rpx;
}
/* Material Icons 字体 */
@font-face {
	font-family: 'Material Icons';
	font-style: normal;
	font-weight: 400;
	src: url(https://fonts.gstatic.com/s/materialicons/v139/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format('woff2');
}
.material-icons {
	font-family: 'Material Icons';
	font-weight: normal;
	font-style: normal;
	font-size: 24rpx;
	line-height: 1;
	letter-spacing: normal;
	text-transform: none;
	display: inline-block;
	white-space: nowrap;
	word-wrap: normal;
	direction: ltr;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}
.material-icons.md-18 {
	font-size: 36rpx;
}
.material-icons.md-24 {
	font-size: 48rpx;
}
.material-icons.md-48 {
	font-size: 96rpx;
}
.material-icons.text-primary {
	color: var(--text-primary, #FFFFFF);
}
.material-icons.text-tertiary {
	color: var(--text-tertiary, rgba(255, 255, 255, 0.5));
}
.icon-inline {
	margin-right: 8rpx;
	vertical-align: middle;
}
/* 辅助类 */
.flex {
	display: flex;
}
.flex-col {
	display: flex;
	flex-direction: column;
}
.justify-between {
	justify-content: space-between;
}
.items-center {
	align-items: center;
}
.text-center {
	text-align: center;
}
.mt-sm {
	margin-top: 16rpx;
}
.mt-md {
	margin-top: 30rpx;
}
.mt-lg {
	margin-top: 60rpx;
}
.gap-md {
	gap: 20rpx;
}
.flex-1 {
	flex: 1;
}
/* 文本样式 */
.title-sm {
	font-size: 32rpx;
	font-weight: 500;
	color: #ffffff;
}
.text-secondary {
	color: rgba(255, 255, 255, 0.7);
	font-size: 28rpx;
}
.text-tertiary {
	color: rgba(255, 255, 255, 0.5);
	font-size: 26rpx;
}
.text-primary {
	color: #FFFFFF;
	font-size: 28rpx;
}
.primary-light {
	color: var(--primary-light, #A875FF);
	font-size: 34rpx;
	font-weight: 600;
}
.agreement-tip {
	margin-top: 20rpx;
	text-align: center;
	font-size: 24rpx;
	color: rgba(255, 255, 255, 0.6);
}
/* 调试按钮样式 */
.debug-btn {
	position: absolute;
	right: 20rpx;
	top: 10rpx;
	background-color: rgba(255, 0, 0, 0.7);
	color: white;
	padding: 4rpx 16rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
	z-index: 10;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
	border: 1px solid rgba(255, 0, 0, 0.3);
}
.debug-btn:active {
	-webkit-transform: scale(0.95);
	        transform: scale(0.95);
	background-color: rgba(255, 0, 0, 0.8);
}
/* 移除所有CSS适配，完全依赖JavaScript动态设置 */

