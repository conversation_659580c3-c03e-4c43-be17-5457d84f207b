import Vue from 'vue'
import Vuex from 'vuex'
import LoginManager from '@/static/js/login.js'

Vue.use(Vuex)

export default new Vuex.Store({
  state: {
    isLoggedIn: !!uni.getStorageSync('token'),
    showLoginModal: false,
    userInfo: uni.getStorageSync('userInfo') || { userId: '' }
  },
  mutations: {
    // 设置登录状态
    SET_LOGIN_STATUS(state, status) {
      state.isLoggedIn = status
    },
    // 显示登录弹窗
    SHOW_LOGIN_MODAL(state) {
      state.showLoginModal = true
    },
    // 隐藏登录弹窗
    HIDE_LOGIN_MODAL(state) {
      state.showLoginModal = false
    },
    // 设置用户信息
    SET_USER_INFO(state, userInfo) {
      state.userInfo = userInfo
      // 保存到本地存储
      uni.setStorageSync('userInfo', userInfo)
    }
  },
  actions: {
    // 检查登录状态
    checkLoginStatus({ commit }) {
      const token = uni.getStorageSync('token')
      commit('SET_LOGIN_STATUS', !!token)
      return !!token
    },
    // 显示登录弹窗
    showLoginModal({ commit }) {
      commit('SHOW_LOGIN_MODAL')
    },
    // 隐藏登录弹窗
    hideLoginModal({ commit }) {
      commit('HIDE_LOGIN_MODAL')
    },
    // 登录成功处理
    loginSuccess({ commit }, userData) {
      commit('SET_LOGIN_STATUS', true)
      commit('HIDE_LOGIN_MODAL')
      
      // 设置用户信息
      if (userData && userData.userInfo) {
        commit('SET_USER_INFO', userData.userInfo)
      } else if (userData && userData.userId) {
        commit('SET_USER_INFO', { userId: userData.userId })
      }
    },
    // 退出登录
    logout({ commit }) {
      // 清除token和用户信息
      uni.removeStorageSync('token')
      uni.removeStorageSync('userInfo')
      
      // 更新状态
      commit('SET_LOGIN_STATUS', false)
      commit('SET_USER_INFO', { userId: '' })
      
      // 调用LoginManager的logout方法
      return LoginManager.logout()
    }
  },
  getters: {
    // 是否已登录
    isLoggedIn: state => state.isLoggedIn,
    // 是否显示登录弹窗
    showLoginModal: state => state.showLoginModal,
    // 用户信息
    userInfo: state => state.userInfo
  }
}) 