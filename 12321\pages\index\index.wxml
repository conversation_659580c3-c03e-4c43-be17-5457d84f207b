<!-- 添加标题 -->
<view class="app-container">
  <view class="app-header">
    <view class="app-title">今夜城堡调试器</view>
    <view class="app-subtitle">安全便捷的智能门锁解决方案</view>
    
    <!-- 添加状态指示器 -->
    <view class="status-indicators">
      <view class="status-item {{bluetoothInitialized ? 'status-ok' : 'status-error'}}">
        <text class="status-icon">{{bluetoothInitialized ? '✓' : '✗'}}</text>
        <text>蓝牙</text>
      </view>
      <view class="status-item {{networkType !== 'none' ? 'status-ok' : 'status-error'}}">
        <text class="status-icon">{{networkType !== 'none' ? '✓' : '✗'}}</text>
        <text>网络</text>
      </view>
      
      <!-- 操作状态指示器 -->
      <view class="status-item {{operationLocked ? 'status-busy' : 'status-idle'}}">
        <text class="status-icon">{{operationLocked ? '⋯' : '✓'}}</text>
        <text>{{operationLocked ? '进行中' : '就绪'}}</text>
      </view>
    </view>
  </view>

  <!-- 华为设备特殊提示 -->
  <view class="huawei-tips" wx:if="{{systemInfo.brand && systemInfo.brand.toUpperCase().includes('HUAWEI') && systemInfo.system && systemInfo.system.includes('Android 12') && !bluetoothInitialized}}">
    <view class="huawei-tips-content">
      <view class="huawei-tips-icon">⚠️</view>
      <view class="huawei-tips-text">
        <text>华为安卓12设备蓝牙提示</text>
        <text class="huawei-tips-detail">请确认已在设置中打开蓝牙并授予权限</text>
      </view>
    </view>
    <view class="huawei-tips-actions">
      <button class="huawei-tips-button" bindtap="initBluetoothAdapter" data-force="true">重试连接</button>
    </view>
  </view>

  <!-- 操作锁定提示 -->
  <view class="operation-lock-tips" wx:if="{{operationLocked}}">
    <view class="operation-lock-content">
      <view class="operation-lock-icon">⏱️</view>
      <view class="operation-lock-text">
        <text>蓝牙操作进行中</text>
        <text class="operation-lock-detail">{{bleState === 1 ? '正在初始化蓝牙...' : bleState === 3 ? '正在扫描设备...' : bleState === 4 ? '正在连接设备...' : '请稍候...'}}</text>
      </view>
    </view>
  </view>

  <!-- 搜索区域 -->
  <view class="search-container">
    <view class="search-status">
      <text>{{misScanding ? '正在搜索设备...' : '点击下方按钮搜索设备'}}</text>
      <text class="devices-count">已发现 {{devices.length}} 个设备</text>
    </view>
    <button loading="{{misScanding}}" class="scan-button" hover-class="scan-button-hover" bindtap="startBluetoothDevicesDiscovery" disabled="{{operationLocked && !misScanding}}">
      <text class="scan-icon">{{misScanding ? '⟳' : '🔍'}}</text>
      <text>{{scandbutName}}</text>
    </button>
  </view>

  <!-- 设备列表标题区域 -->
  <view class="device-list-header">
    <view class="device-list-title">设备列表</view>
    <view class="device-list-subtitle">{{devices.length > 0 ? '点击设备名称进行连接' : '正在搜索设备...'}}</view>
    
    <!-- 添加搜索中仍可连接的提示 -->
    <view wx:if="{{misScanding && devices.length > 0}}" class="search-tip">
      搜索中，可直接点击设备连接
    </view>
  </view>

  <!-- 设备列表 -->
  <scroll-view scroll-y="true" class="device-list">
    <block wx:if="{{devices.length === 0}}">
      <view class="no-device">
        <view class="no-device-icon">🔍</view>
        <view class="no-device-text">正在搜索设备...</view>
        <view class="no-device-tip">请确保设备已开启并在附近</view>
      </view>
    </block>
    <block wx:else>
      <view wx:for="{{devices}}" wx:key="deviceId" class="device-item {{item.isLock ? 'lock-device' : ''}} {{item.connecting ? 'connecting-device' : ''}} {{item.connected ? 'connected-device' : ''}}" bindtap="goto_Comm" data-name="{{item.name}}" data-device-id="{{item.deviceId}}" data-is-lock="{{item.isLock}}">
        <view class="device-info">
          <view class="device-name">{{item.name || '未知设备'}}</view>
          <view class="device-id">{{item.deviceId}}</view>
          <view class="device-rssi">信号强度: {{item.RSSI}} dBm</view>
        </view>
        <view class="device-type">
          <text class="{{item.isLock ? 'lock-icon' : 'device-icon'}}">{{item.isLock ? '🔒' : '📱'}}</text>
          <view class="device-status" wx:if="{{item.connecting || item.connected}}">
            <text class="status-dot {{item.connecting ? 'connecting-dot' : 'connected-dot'}}"></text>
            <text class="status-text">{{item.connecting ? '连接中' : item.connected ? '已连接' : ''}}</text>
          </view>
        </view>
      </view>
    </block>
  </scroll-view>

  <!-- 底部提示区域 -->
  <view class="footer-tip">
    <text>{{misScanding ? '搜索中，可直接点击设备连接' : '点击刷新开始搜索设备'}}</text>
  </view>

  <!-- 刷新蓝牙按钮 - 更加突出的样式 -->
  <view class="refresh-button-container">
    <button class="refresh-button" bindtap="refreshBluetooth" disabled="{{operationLocked}}">
      <text class="refresh-icon" style="{{bleState === 1 ? 'animation-play-state: running;' : ''}}">🔄</text>
      <text>重新初始化蓝牙</text>
    </button>
    <view class="refresh-button-hint">
      <text>遇到连接问题？点击上方按钮重置蓝牙</text>
    </view>
  </view>
  
  <!-- 错误提示区域 -->
  <view class="error-container" wx:if="{{errorCount > 0}}">
    <view class="error-message">
      <text class="error-icon">⚠️</text>
      <text>检测到应用异常，如持续出现请点击上方刷新按钮</text>
    </view>
  </view>
</view>