{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/profile/profile.vue?d201", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/profile/profile.vue?4dae", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/profile/profile.vue?e5b9", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/profile/profile.vue?c846", "uni-app:///pages/profile/profile.vue", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/profile/profile.vue?c79a", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/profile/profile.vue?742c"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "customTabBar", "data", "title", "isLoggedIn", "userInfo", "userId", "nickname", "avatar", "orderCount", "registerTime", "loading", "onLoad", "console", "onShow", "onReady", "setTimeout", "onPullDownRefresh", "methods", "updateTabBar", "navigateTo", "uni", "url", "goToReportPage", "checkLoginStatus", "getUserInfo", "API", "then", "catch", "icon", "getOrderCount", "logout", "content", "success"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACa;;;AAGnE;AACgM;AAChM,gBAAgB,uMAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAqvB,CAAgB,mvBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC2FzwB;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAEA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;IACA;EACA;EACAC;IACAC;IACA;IACA;EACA;EACAC;IACA;IACA;;IAEA;IACA;MACA;MACA;MACA;IACA;EACA;EACAC;IAAA;IACA;IACAC;MACA;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;EACA;EACAC;IACA;IACAC;MACA;QACA;QACA;UACA;QACA;;QAEA;QACA;UACA;QACA;;QAEA;QACA;UACA;QACA;MACA;IACA;IACAC;MACAC;QACAC;MACA;IACA;IACA;IACAC;MACAV;MACAQ;QACAC;MACA;IACA;IACAE;MACA;MACA;MACA;MAEA;QACA;QACA;QACA;QACA;MACA;QACA;QACA;UACAlB;UACAC;UACAC;UACAC;UACAC;QACA;MACA;IACA;IAEA;IACAe;MAAA;MACA;MAEA;;MAEA;MACAC,4BACAC;QACA;QACAN;;QAEA;QACA;UACAf;UACAC;UACAC;UACAC;UACAC;QACA;MACA,GACAkB;QACA;QACAP;QAEAR;;QAEA;QACA;UACA;UACA;YACAP;YACAC;YACAC;YACAC;YACAC;UACA;QACA;;QAEA;QACAW;UACAlB;UACA0B;QACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MAEAJ,mCACAC;QACA;UACA;QACA;MACA,GACAC;QACAf;MACA;IACA;IAEA;IACAkB;MAAA;MACAV;QACAlB;QACA6B;QACAC;UACA;YACA;YACAZ;;YAEA;YACA;YACA;cACAf;cACAC;cACAC;cACAC;cACAC;YACA;;YAEA;YACAW;cACAlB;cACA0B;YACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7RA;AAAA;AAAA;AAAA;AAAkkC,CAAgB,4hCAAG,EAAC,C;;;;;;;;;;;ACAtlC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/profile/profile.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/profile/profile.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./profile.vue?vue&type=template&id=60cb334c&\"\nvar renderjs\nimport script from \"./profile.vue?vue&type=script&lang=js&\"\nexport * from \"./profile.vue?vue&type=script&lang=js&\"\nimport style0 from \"./profile.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/profile/profile.vue\"\nexport default component.exports", "export * from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./profile.vue?vue&type=template&id=60cb334c&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./profile.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./profile.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"container page-profile\">\n\t\t<!-- 页面背景 -->\n\t\t<view class=\"page-background\">\n\t\t\t<!-- 背景图片 -->\n\t\t\t<image class=\"background-image\" src=\"https://wuji1112-1312674791.cos.ap-guangzhou.myqcloud.com/static/shouye3.png\" mode=\"aspectFill\"></image>\n\t\t\t\n\t\t\t<!-- 磨砂效果叠加层 -->\n\t\t\t<view class=\"frosted-overlay\"></view>\n\t\t</view>\n\t\t\n\t\t<!-- 顶部状态栏占位 -->\n\t\t<view class=\"status-bar safe-area-inset-top\"></view>\n\t\t\n\t\t<!-- 页面内容 -->\n\t\t<view class=\"content\">\n\t\t\t<!-- 个人信息区域 - 移除背景 -->\n\t\t\t<view class=\"profile-header\">\n\t\t\t\t<view class=\"avatar-container\">\n\t\t\t\t\t<view class=\"avatar-wrapper\">\n\t\t\t\t\t\t<image class=\"avatar\" :src=\"userInfo.avatar || '../../static/images/avatar.svg'\" mode=\"aspectFill\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"user-info\">\n\t\t\t\t\t<view class=\"user-name-row\">\n\t\t\t\t\t\t<text class=\"title-md\">{{userInfo.nickname || '隐私用户'}}</text>\n\t\t\t\t\t\t<view class=\"status\" :class=\"{'active': isLoggedIn}\">{{isLoggedIn ? '已登录' : '未登录'}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"text-tertiary\">用户ID：{{userInfo.userId || '未登录'}}</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 功能菜单 -->\n\t\t\t<view class=\"menu-section mt-md\">\n\t\t\t\t\n\t\t\t\t<!-- 菜单列表 -->\n\t\t\t\t<view class=\"menu-list\">\n\t\t\t\t\t<!-- 我的订单 -->\n\t\t\t\t\t<view class=\"menu-item\" @click=\"navigateTo('/pages/orders/orders')\">\n\t\t\t\t\t\t<view class=\"menu-icon\">\n\t\t\t\t\t\t\t<text class=\"material-icons neon-pink\">receipt_long</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"menu-content\">\n\t\t\t\t\t\t\t<view class=\"menu-title\">我的订单</view>\n\t\t\t\t\t\t\t<view class=\"text-tertiary\">已有{{userInfo.orderCount || 0}}个订单记录</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"menu-arrow\">\n\t\t\t\t\t\t\t<text class=\"material-icons text-tertiary\">arrow_forward_ios</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 招募合作 -->\n\t\t\t\t\t<view class=\"menu-item\" @click=\"navigateTo('/packageA/pages/zhaoshang/index')\">\n\t\t\t\t\t\t<view class=\"menu-icon\">\n\t\t\t\t\t\t\t<text class=\"material-icons neon-blue\">group_add</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"menu-content\">\n\t\t\t\t\t\t\t<view class=\"menu-title\">招募合作</view>\n\t\t\t\t\t\t\t<view class=\"text-tertiary\">0元加盟成为合伙人</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"menu-arrow\">\n\t\t\t\t\t\t\t<text class=\"material-icons text-tertiary\">arrow_forward_ios</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 设备异常上报 -->\n\t\t\t\t\t<view class=\"menu-item\" @click=\"goToReportPage\">\n\t\t\t\t\t\t<view class=\"menu-icon\">\n\t\t\t\t\t\t\t<text class=\"material-icons neon-yellow\">build</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"menu-content\">\n\t\t\t\t\t\t\t<view class=\"menu-title\">设备异常反馈</view>\n\t\t\t\t\t\t\t<view class=\"text-tertiary\">报告设备使用问题</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"menu-arrow\">\n\t\t\t\t\t\t\t<text class=\"material-icons text-tertiary\">arrow_forward_ios</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 自定义TabBar -->\n\t\t<custom-tab-bar ref=\"tabBar\"></custom-tab-bar>\n\t</view>\n</template>\n\n<script>\n\timport customTabBar from \"@/custom-tab-bar/index.vue\"\n\timport API from '@/static/js/api.js';\n\t\n\texport default {\n\t\tcomponents: {\n\t\t\tcustomTabBar\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\ttitle: '我的',\n\t\t\t\tisLoggedIn: false,\n\t\t\t\tuserInfo: {\n\t\t\t\t\tuserId: '',\n\t\t\t\t\tnickname: '',\n\t\t\t\t\tavatar: '',\n\t\t\t\t\torderCount: 0,\n\t\t\t\t\tregisterTime: ''\n\t\t\t\t},\n\t\t\t\tloading: false\n\t\t\t}\n\t\t},\n\t\tonLoad() {\n\t\t\tconsole.log('我的页面加载');\n\t\t\t// 检查登录状态\n\t\t\tthis.checkLoginStatus();\n\t\t},\n\t\tonShow() {\n\t\t\t// 确保TabBar正确显示当前页面\n\t\t\tthis.updateTabBar();\n\t\t\t\n\t\t\t// 每次显示页面时刷新用户信息\n\t\t\tif (this.isLoggedIn) {\n\t\t\t\tthis.getUserInfo();\n\t\t\t\t// 获取订单数量\n\t\t\t\tthis.getOrderCount();\n\t\t\t}\n\t\t},\n\t\tonReady() {\n\t\t\t// 页面渲染完成后，再次更新TabBar\n\t\t\tsetTimeout(() => {\n\t\t\t\tthis.updateTabBar();\n\t\t\t}, 100);\n\t\t},\n\t\tonPullDownRefresh() {\n\t\t\t// 下拉刷新，重新获取用户信息\n\t\t\tthis.getUserInfo();\n\t\t\t// 同时获取订单数量\n\t\t\tthis.getOrderCount();\n\t\t},\n\t\tmethods: {\n\t\t\t// 更新TabBar状态\n\t\t\tupdateTabBar() {\n\t\t\t\tif (this.$refs.tabBar) {\n\t\t\t\t\t// 如果有updateCurrentPath方法，则调用\n\t\t\t\t\tif (typeof this.$refs.tabBar.updateCurrentPath === 'function') {\n\t\t\t\t\t\tthis.$refs.tabBar.updateCurrentPath();\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 如果有startPathCheck方法，则调用\n\t\t\t\t\tif (typeof this.$refs.tabBar.startPathCheck === 'function') {\n\t\t\t\t\t\tthis.$refs.tabBar.startPathCheck();\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 强制重新渲染TabBar\n\t\t\t\t\tif (typeof this.$refs.tabBar.$forceUpdate === 'function') {\n\t\t\t\t\t\tthis.$refs.tabBar.$forceUpdate();\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t\tnavigateTo(url) {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: url\n\t\t\t\t});\n\t\t\t},\n\t\t\t// 前往设备异常反馈页面\n\t\t\tgoToReportPage() {\n\t\t\t\tconsole.log('前往设备异常反馈页面');\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/report/report'\n\t\t\t\t});\n\t\t\t},\n\t\t\tcheckLoginStatus() {\n\t\t\t\t// 从缓存获取token\n\t\t\t\tconst token = uni.getStorageSync('token');\n\t\t\t\tthis.isLoggedIn = !!token;\n\t\t\t\t\n\t\t\t\tif (this.isLoggedIn) {\n\t\t\t\t\t// 已登录，获取用户信息\n\t\t\t\t\tthis.getUserInfo();\n\t\t\t\t\t// 获取订单数量\n\t\t\t\t\tthis.getOrderCount();\n\t\t\t\t} else {\n\t\t\t\t\t// 未登录，使用默认信息\n\t\t\t\t\tthis.userInfo = {\n\t\t\t\t\t\tuserId: '未登录',\n\t\t\t\t\t\tnickname: '未登录用户',\n\t\t\t\t\t\tavatar: '',\n\t\t\t\t\t\torderCount: 0,\n\t\t\t\t\t\tregisterTime: ''\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 获取用户信息\n\t\t\tgetUserInfo() {\n\t\t\t\tif (this.loading) return;\n\t\t\t\t\n\t\t\t\tthis.loading = true;\n\t\t\t\t\n\t\t\t\t// 调用API获取用户信息\n\t\t\t\tAPI.user.getInfo()\n\t\t\t\t\t.then(res => {\n\t\t\t\t\t\tthis.loading = false;\n\t\t\t\t\t\tuni.stopPullDownRefresh();\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 更新用户信息\n\t\t\t\t\t\tthis.userInfo = {\n\t\t\t\t\t\t\tuserId: res.data.userId || '未知ID',\n\t\t\t\t\t\t\tnickname: res.data.nickname || '隐私用户',\n\t\t\t\t\t\t\tavatar: res.data.avatar || '',\n\t\t\t\t\t\t\torderCount: res.data.orderCount || 0,\n\t\t\t\t\t\t\tregisterTime: res.data.registerTime || ''\n\t\t\t\t\t\t};\n\t\t\t\t\t})\n\t\t\t\t\t.catch(err => {\n\t\t\t\t\t\tthis.loading = false;\n\t\t\t\t\t\tuni.stopPullDownRefresh();\n\t\t\t\t\t\t\n\t\t\t\t\t\tconsole.error('获取用户信息失败:', err);\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 如果是未登录错误，更新登录状态\n\t\t\t\t\t\tif (err.code === 401) {\n\t\t\t\t\t\t\tthis.isLoggedIn = false;\n\t\t\t\t\t\t\tthis.userInfo = {\n\t\t\t\t\t\t\t\tuserId: '未登录',\n\t\t\t\t\t\t\t\tnickname: '未登录用户',\n\t\t\t\t\t\t\t\tavatar: '',\n\t\t\t\t\t\t\t\torderCount: 0,\n\t\t\t\t\t\t\t\tregisterTime: ''\n\t\t\t\t\t\t\t};\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 显示错误提示\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: err.message || '获取用户信息失败',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 获取订单数量\n\t\t\tgetOrderCount() {\n\t\t\t\tif (!this.isLoggedIn) return;\n\t\t\t\t\n\t\t\t\tAPI.order.getOrderCount()\n\t\t\t\t\t.then(res => {\n\t\t\t\t\t\tif (res.data !== undefined) {\n\t\t\t\t\t\t\tthis.userInfo.orderCount = res.data;\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t\t.catch(err => {\n\t\t\t\t\t\tconsole.error('获取订单数量失败:', err);\n\t\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 退出登录\n\t\t\tlogout() {\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '退出登录',\n\t\t\t\t\tcontent: '确定要退出当前账号吗？',\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\t// 清除token\n\t\t\t\t\t\t\tuni.removeStorageSync('token');\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 更新登录状态\n\t\t\t\t\t\t\tthis.isLoggedIn = false;\n\t\t\t\t\t\t\tthis.userInfo = {\n\t\t\t\t\t\t\t\tuserId: '未登录',\n\t\t\t\t\t\t\t\tnickname: '未登录用户',\n\t\t\t\t\t\t\t\tavatar: '',\n\t\t\t\t\t\t\t\torderCount: 0,\n\t\t\t\t\t\t\t\tregisterTime: ''\n\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 显示提示\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '已退出登录',\n\t\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style>\n\t/* CSS变量定义 */\n\tpage {\n\t\t--primary-light: #A875FF;\n\t\t--neon-pink: #ff36f9;\n\t\t--neon-blue: #00BFFF;\n\t\t--neon-yellow: #FFD700;\n\t\t--divider-color: rgba(255, 255, 255, 0.08);\n\t}\n\t\n\t/* 页面基础样式 */\n\t.page-profile {\n\t\tpadding-top: 140rpx;\n\t\tpadding-bottom: calc(170rpx + env(safe-area-inset-bottom));\n\t\tcolor: #ffffff;\n\t\theight: 100vh;\n\t\tmin-height: 100vh;\n\t\tbox-sizing: border-box;\n\t\tposition: relative;\n\t\toverflow: hidden;\n\t}\n\t\n\t/* 页面背景样式 */\n\t.page-background {\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tz-index: 0;\n\t}\n\t\n\t/* 背景图片样式 */\n\t.background-image {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tz-index: 0;\n\t\tobject-fit: cover;\n\t}\n\t\n\t/* 磨砂效果叠加层 */\n\t.frosted-overlay {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tbackground-color: rgba(18, 18, 18, 0.4);\n\t\tbackdrop-filter: blur(5px);\n\t\t-webkit-backdrop-filter: blur(5px);\n\t\tz-index: 1;\n\t}\n\t\n\t.status-bar {\n\t\twidth: 100%;\n\t\tbackground: rgba(18, 18, 18, 0.8);\n\t\tbackdrop-filter: blur(10px);\n\t\t-webkit-backdrop-filter: blur(10px);\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tz-index: 100;\n\t}\n\t\n\t.content {\n\t\tpadding: 30rpx;\n\t\tposition: relative;\n\t\tz-index: 2;\n\t\theight: calc(100vh - 140rpx - env(safe-area-inset-bottom) - 170rpx);\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\toverflow-y: auto;\n\t\toverflow-x: hidden;\n\t\t-webkit-overflow-scrolling: touch;\n\t}\n\t\n\t/* 个人信息区域 - 移除背景相关样式 */\n\t.profile-header {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tpadding: 40rpx;\n\t\tmargin-bottom: 30rpx;\n\t}\n\t\n\t.avatar-container {\n\t\tmargin-right: 30rpx;\n\t}\n\t\n\t/* 美化头像样式 */\n\t.avatar-wrapper {\n\t\twidth: 120rpx;\n\t\theight: 120rpx;\n\t\tborder-radius: 60rpx;\n\t\tbackground: linear-gradient(145deg, rgba(168, 117, 255, 0.8), rgba(255, 54, 249, 0.5));\n\t\tpadding: 4rpx;\n\t\tbox-shadow: 0 0 20rpx rgba(168, 117, 255, 0.5);\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t}\n\t\n\t.avatar {\n\t\twidth: 112rpx;\n\t\theight: 112rpx;\n\t\tborder-radius: 56rpx;\n\t\tbackground-color: rgba(120, 50, 200, 0.7);\n\t\tborder: 1px solid rgba(255, 255, 255, 0.3);\n\t\tbox-shadow: inset 0 0 10rpx rgba(255, 255, 255, 0.2);\n\t}\n\t\n\t/* 用户名和状态横向排列 */\n\t.user-name-row {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-bottom: 10rpx;\n\t}\n\t\n\t.user-name-row .title-md {\n\t\tmargin-right: 16rpx;\n\t}\n\t\n\t/* 菜单区域 */\n\t.menu-section {\n\t\tmargin-bottom: 30rpx;\n\t\tflex: 1;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t}\n\t\n\t.menu-list {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tmargin-bottom: 30rpx;\n\t\tbackground-color: rgba(168, 117, 255, 0.05);\n\t\tborder-radius: 20rpx;\n\t\toverflow: hidden;\n\t\tborder: 2rpx solid rgba(168, 117, 255, 0.4);\n\t\tbox-shadow: 0 0 20rpx rgba(168, 117, 255, 0.2);\n\t}\n\t\n\t.menu-item {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tpadding: 36rpx;\n\t\ttransition: background-color 0.2s;\n\t\tposition: relative;\n\t}\n\t\n\t/* 除最后一个菜单项外，其他菜单项底部添加分隔线 */\n\t.menu-item:not(:last-child)::after {\n\t\tcontent: '';\n\t\tposition: absolute;\n\t\tleft: 90rpx;\n\t\tright: 30rpx;\n\t\tbottom: 0;\n\t\theight: 1px;\n\t\tbackground-color: var(--divider-color);\n\t}\n\t\n\t.menu-item:active {\n\t\tbackground-color: rgba(255, 255, 255, 0.05);\n\t}\n\t\n\t.menu-icon {\n\t\tmargin-right: 20rpx;\n\t\twidth: 70rpx;\n\t\theight: 70rpx;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tborder-radius: 50%;\n\t\tbackground-color: transparent;\n\t}\n\t\n\t.menu-icon .material-icons {\n\t\tfont-size: 44rpx;\n\t}\n\t\n\t.menu-content {\n\t\tflex: 1;\n\t}\n\t\n\t.menu-title {\n\t\tfont-size: 36rpx;\n\t\tfont-weight: 500;\n\t\tcolor: #ffffff;\n\t\tmargin-bottom: 8rpx;\n\t}\n\t\n\t.menu-arrow {\n\t\twidth: 40rpx;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t}\n\t\n\t.menu-arrow .material-icons {\n\t\tfont-size: 28rpx;\n\t}\n\t\n\t/* 关于区域 */\n\t.about-section {\n\t\tmargin-top: 40rpx;\n\t\tmargin-bottom: 80rpx;\n\t\tpadding-bottom: 100rpx;\n\t}\n\t\n\t/* Material Icons 字体 */\n\t@font-face {\n\t\tfont-family: 'Material Icons';\n\t\tfont-style: normal;\n\t\tfont-weight: 400;\n\t\tsrc: url(https://fonts.gstatic.com/s/materialicons/v139/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format('woff2');\n\t}\n\n\t.material-icons {\n\t\tfont-family: 'Material Icons';\n\t\tfont-weight: normal;\n\t\tfont-style: normal;\n\t\tfont-size: 24rpx;\n\t\tline-height: 1;\n\t\tletter-spacing: normal;\n\t\ttext-transform: none;\n\t\tdisplay: inline-block;\n\t\twhite-space: nowrap;\n\t\tword-wrap: normal;\n\t\tdirection: ltr;\n\t\t-webkit-font-smoothing: antialiased;\n\t\t-moz-osx-font-smoothing: grayscale;\n\t}\n\t\n\t.material-icons.primary-light {\n\t\tcolor: var(--primary-light, #A875FF);\n\t}\n\t\n\t.material-icons.text-tertiary {\n\t\tcolor: var(--text-tertiary, rgba(255, 255, 255, 0.5));\n\t}\n\t\n\t/* 辅助类 */\n\t.flex {\n\t\tdisplay: flex;\n\t}\n\t\n\t.justify-between {\n\t\tjustify-content: space-between;\n\t}\n\t\n\t.items-center {\n\t\talign-items: center;\n\t}\n\t\n\t.text-center {\n\t\ttext-align: center;\n\t}\n\t\n\t.mt-sm {\n\t\tmargin-top: 10rpx;\n\t}\n\t\n\t.mt-md {\n\t\tmargin-top: 20rpx;\n\t}\n\t\n\t.mt-lg {\n\t\tmargin-top: 40rpx;\n\t}\n\t\n\t.mb-sm {\n\t\tmargin-bottom: 10rpx;\n\t}\n\t\n\t/* 状态标签 */\n\t.status {\n\t\tpadding: 6rpx 16rpx;\n\t\tborder-radius: 30rpx;\n\t\tfont-size: 24rpx;\n\t\tdisplay: inline-block;\n\t}\n\t\n\t.status.active {\n\t\tbackground-color: rgba(168, 117, 255, 0.2);\n\t\tcolor: var(--primary-light, #A875FF);\n\t}\n\t\n\t/* 文本样式 */\n\t.title-md {\n\t\tfont-size: 36rpx;\n\t\tfont-weight: 600;\n\t\tcolor: #ffffff;\n\t}\n\t\n\t.title-sm {\n\t\tfont-size: 30rpx;\n\t\tfont-weight: 500;\n\t\tcolor: #ffffff;\n\t}\n\t\n\t.text-secondary {\n\t\tcolor: rgba(255, 255, 255, 0.7);\n\t}\n\t\n\t.text-tertiary {\n\t\tcolor: rgba(255, 255, 255, 0.5);\n\t\tfont-size: 30rpx;\n\t}\n\t\n\t.text-primary {\n\t\tcolor: #ffffff;\n\t}\n\t\n\t.primary-light {\n\t\tcolor: var(--primary-light, #A875FF);\n\t}\n\t\n\t.neon-pink {\n\t\tcolor: var(--neon-pink, #ff36f9);\n\t}\n\t\n\t.neon-blue {\n\t\tcolor: var(--neon-blue, #00BFFF);\n\t}\n\t\n\t.neon-yellow {\n\t\tcolor: var(--neon-yellow, #FFD700);\n\t}\n\t\n\t/* 按钮样式 */\n\t.btn {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tborder-radius: 8rpx;\n\t\tpadding: 16rpx 24rpx;\n\t\tfont-size: 28rpx;\n\t\tfont-weight: 500;\n\t}\n\t\n\t.btn-outline {\n\t\tbackground-color: transparent;\n\t\tborder: 1px solid rgba(255, 255, 255, 0.2);\n\t\tcolor: #ffffff;\n\t}\n\t\n\t.btn-sm {\n\t\tpadding: 12rpx 20rpx;\n\t\tfont-size: 24rpx;\n\t}\n</style> ", "import mod from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./profile.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./profile.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754165306709\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}