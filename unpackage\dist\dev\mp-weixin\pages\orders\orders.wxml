<view class="container page-orders"><view class="page-background"><image class="background-image" src="https://wuji1112-1312674791.cos.ap-guangzhou.myqcloud.com/static/shouye3.png" mode="aspectFill"></image><view class="frosted-overlay"></view></view><view class="navbar" style="{{(navbarStyle)}}"><view data-event-opts="{{[['tap',[['goBack',['$event']]]]]}}" class="navbar-left" bindtap="__e"><text class="material-icons md-24 text-primary">arrow_back</text></view><view class="navbar-title">我的订单</view><view class="navbar-right"></view></view><view class="page-content"><view class="tabs-container"><view class="tabs"><view data-event-opts="{{[['tap',[['changeTab',[0]]]]]}}" class="{{['tab',(activeTab===0)?'active':'']}}" bindtap="__e"><text class="material-icons md-18 icon-inline">list_alt</text><text>全部</text><block wx:if="{{activeTab===0}}"><view class="tab-line"></view></block></view><view data-event-opts="{{[['tap',[['changeTab',[1]]]]]}}" class="{{['tab',(activeTab===1)?'active':'']}}" bindtap="__e"><text class="material-icons md-18 icon-inline">pending_actions</text><text>进行中</text><block wx:if="{{activeTab===1}}"><view class="tab-line"></view></block></view><view data-event-opts="{{[['tap',[['changeTab',[2]]]]]}}" class="{{['tab',(activeTab===2)?'active':'']}}" bindtap="__e"><text class="material-icons md-18 icon-inline">task_alt</text><text>已完成</text><block wx:if="{{activeTab===2}}"><view class="tab-line"></view></block></view></view><block wx:if="{{isDevMode}}"><view data-event-opts="{{[['tap',[['debugOrderList',['$event']]]]]}}" class="debug-btn" bindtap="__e">调试</view></block></view><view class="content"><block wx:if="{{$root.g0>0}}"><view><block wx:for="{{$root.l0}}" wx:for-item="order" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['viewOrderDetail',['$0'],[[['filteredOrders','',index,'id']]]]]]]}}" class="order-item" bindtap="__e"><view class="order-header flex justify-between items-center"><view class="order-store"><text class="title-sm">{{order.$orig.orderNo}}</text></view><view class="{{['status',(order.$orig.rawStatus===1)?'active':'',(order.$orig.rawStatus===0)?'unpaid':'',(order.$orig.rawStatus===2)?'completed':'',(order.$orig.rawStatus===3)?'cancelled':'',(order.$orig.rawStatus!==0&&order.$orig.rawStatus!==1&&order.$orig.rawStatus!==2&&order.$orig.rawStatus!==3)?'inactive':'']}}">{{''+order.$orig.status+''}}</view></view><view class="divider mt-sm"></view><view class="order-details mt-md"><view class="detail-item flex justify-between"><text class="detail-label"><text class="material-icons md-18 icon-inline">store</text>门店</text><text class="detail-value">{{order.m0}}</text></view><view class="detail-item flex justify-between mt-sm"><text class="detail-label"><text class="material-icons md-18 icon-inline">schedule</text>下单时间</text><text class="detail-value">{{order.$orig.startTime}}</text></view><view class="detail-item flex justify-between mt-sm"><text class="detail-label"><text class="material-icons md-18 icon-inline">payments</text>消费金额</text><text class="primary-light">{{order.$orig.amount}}</text></view></view><block wx:if="{{order.$orig.rawStatus===0}}"><view class="order-actions flex mt-md"><button data-event-opts="{{[['tap',[['payOrder',['$0'],[[['filteredOrders','',index,'id']]]]]]]}}" class="btn btn-primary btn-sm flex-1" catchtap="__e"><text class="material-icons md-18 icon-inline">payment</text>去支付</button></view></block></view></block></view></block><block wx:else><view class="empty-state"><text class="material-icons md-48 text-tertiary">receipt_long</text><text class="empty-text">暂无订单记录</text><button data-event-opts="{{[['tap',[['navigateTo',['/pages/index/index']]]]]}}" class="btn btn-outline mt-md" bindtap="__e"><text class="material-icons md-18 icon-inline">home</text>返回首页</button></view></block></view></view></view>