                                                                          //app.js
// 使用安全的方式处理错误
const originalConsoleError = console.error;
console.error = function() {
  // 过滤掉wxfile相关的错误
  const args = Array.from(arguments);
  const errorString = args.join(' ');
  if (errorString.indexOf('wxfile://') !== -1 || 
      errorString.indexOf('no such file or directory') !== -1 ||
      errorString.indexOf('backgroundfetch privacy fail') !== -1 ||
      errorString.indexOf('private_getBackgroundFetchData') !== -1) {
    // 忽略文件系统错误和后台获取错误
    return;
  }
  // 调用原始的console.error
  return originalConsoleError.apply(console, arguments);
};

App({
  onLaunch: function () {
    // 获取系统信息 - 使用新的分离式API
    try {
      let systemInfo;

      // 尝试使用新的分离式API
      if (wx.getDeviceInfo && wx.getAppBaseInfo && wx.getWindowInfo) {
        const deviceInfo = wx.getDeviceInfo();
        const appBaseInfo = wx.getAppBaseInfo();
        const windowInfo = wx.getWindowInfo();

        systemInfo = {
          ...deviceInfo,
          ...appBaseInfo,
          ...windowInfo
        };
      } else {
        // 回退到旧API
        console.warn('新的系统信息API不可用，回退到 wx.getSystemInfoSync');
        systemInfo = wx.getSystemInfoSync();
      }

      this.globalData.systemInfo = systemInfo;
      
      // 检查是否是华为设备
      if (systemInfo.brand && systemInfo.brand.toUpperCase().includes('HUAWEI')) {
        this.globalData.isHuawei = true;
        
        // 获取蓝牙适配器状态，提前初始化蓝牙模块
        this.checkBluetoothPermission();
      }
      
      // 记录启动时间，用于崩溃检测
      this.globalData.startTime = Date.now();
      this.checkForCrash();
    } catch (e) {
      console.error('获取系统信息失败:', e);
    }

    // 设置全局未捕获异常处理
    wx.onError((res) => {
      // 忽略文件系统错误
      if (res.message.indexOf('wxfile://') !== -1 ||
          res.message.indexOf('no such file or directory') !== -1 ||
          res.message.indexOf('backgroundfetch privacy') !== -1) {
        return;
      }
      
      // 记录非文件系统错误
      console.error('应用错误:', res.message);
      
      // 记录错误，用于崩溃恢复
      this.recordError(res.message);
    });
    
    // 设置应用显示状态监听
    wx.onAppShow(() => {
      // 应用进入前台时检查是否需要恢复状态
      this.globalData.isAppActive = true;
      
      // 如果是华为设备，检查蓝牙权限
      if (this.globalData.isHuawei) {
        setTimeout(() => {
          this.checkBluetoothPermission();
        }, 1000);
      }
    });
    
    wx.onAppHide(() => {
      // 应用进入后台时保存当前状态
      this.globalData.isAppActive = false;
      this.saveAppState();
    });
  },
  
  // 检查蓝牙权限状态（专为华为设备）
  checkBluetoothPermission() {
    // 华为设备上需要显式检查蓝牙权限
    if (!this.globalData.isHuawei) {
      return;
    }
    
    try {
      wx.authorize({
        scope: 'scope.bluetooth',
        success: () => {
          // 权限已授予，可以使用蓝牙
          this.globalData.hasBluetoothPermission = true;
          
          // 如果是安卓12，预先初始化蓝牙适配器
          const isAndroid12 = this.globalData.systemInfo && 
                            this.globalData.systemInfo.system && 
                            this.globalData.systemInfo.system.includes('Android 12');
          
          if (isAndroid12) {
            setTimeout(() => {
              this.preInitBluetooth();
            }, 800);
          }
        },
        fail: () => {
          // 用户拒绝了权限
          this.globalData.hasBluetoothPermission = false;
        }
      });
    } catch (e) {
      console.error('蓝牙权限检查失败:', e);
    }
  },
  
  // 预先初始化蓝牙适配器（为华为安卓12准备）
  preInitBluetooth() {
    // 首次预初始化蓝牙
    if (this.globalData.preInitBluetooth) {
      return; // 已经初始化过，不再重复
    }
    
    this.globalData.preInitBluetooth = true;
    
    try {
      wx.openBluetoothAdapter({
        success: () => {
          console.log('蓝牙预初始化成功');
          // 成功后关闭，由页面自行控制蓝牙
          setTimeout(() => {
            wx.closeBluetoothAdapter();
          }, 500);
        },
        fail: (err) => {
          console.log('蓝牙预初始化失败', err);
          // 设置预初始化失败标志
          this.globalData.preInitBluetooth = false;
        }
      });
    } catch (e) {
      console.error('蓝牙预初始化异常:', e);
      this.globalData.preInitBluetooth = false;
    }
  },
  
  // 检查是否发生过崩溃
  checkForCrash() {
    try {
      const lastSession = wx.getStorageSync('lastSession');
      if (lastSession && !lastSession.normalExit) {
        // 上次会话未正常退出，可能发生了崩溃
        console.warn('检测到应用可能发生过崩溃，正在恢复状态...');
        // 这里可以添加崩溃恢复逻辑
      }
      // 设置本次会话状态
      wx.setStorageSync('lastSession', {
        startTime: Date.now(),
        normalExit: false
      });
    } catch (e) {
      console.error('崩溃检测失败:', e);
    }
  },
  
  // 记录错误
  recordError(errMsg) {
    try {
      const errors = wx.getStorageSync('appErrors') || [];
      errors.push({
        time: new Date().toISOString(),
        message: errMsg
      });
      // 只保留最近的10个错误
      if (errors.length > 10) {
        errors.splice(0, errors.length - 10);
      }
      wx.setStorageSync('appErrors', errors);
    } catch (e) {
      console.error('记录错误失败:', e);
    }
  },
  
  // 保存应用状态，用于恢复
  saveAppState() {
    try {
      // 标记正常退出
      const lastSession = wx.getStorageSync('lastSession');
      if (lastSession) {
        lastSession.normalExit = true;
        wx.setStorageSync('lastSession', lastSession);
      }
    } catch (e) {
      console.error('保存应用状态失败:', e);
    }
  },
  
  saveSetting(time,text){
    wx.setStorageSync('autoSendInv', time)
    wx.setStorageSync('sendText', text)
    console.log("WriteSetting ", time, text)
  },
  
  savelastsel(sel){
    wx.setStorageSync('lastsel', sel)
    console.log("Writelastsel ", sel)
  },
  
  globalData: {
    mserviceuuid: "0000FFC0-0000-1000-8000-00805F9B34FB", // 修改为Python脚本中的服务UUID
    mtxduuid: "0000FFC1-0000-1000-8000-00805F9B34FB", // 修改为写入特征值UUID
    mrxduuid: "0000FFC2-0000-1000-8000-00805F9B34FB", // 修改为通知特征值UUID
    muuidSel: 0,
    mautoSendInv:10,
    msendText:"123",
    ble_device:null,
    userInfo: null,
    systemInfo: null,
    startTime: 0,
    isAppActive: true,
    errorCount: 0,
    isHuawei: false,
    hasBluetoothPermission: false,
    preInitBluetooth: false
  }
})