const formatTime = date => {
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  const hour = date.getHours()
  const minute = date.getMinutes()
  const second = date.getSeconds()

  return [year, month, day].map(formatNumber).join('/') + ' ' + [hour, minute, second].map(formatNumber).join(':')
}

const formatNumber = n => {
  n = n.toString()
  return n[1] ? n : '0' + n
}

/**
 * 获取系统信息
 * @returns {Object} 系统信息对象
 */
const getSystemInfo = () => {
  try {
    // 使用新的分离式API
    const deviceInfo = wx.getDeviceInfo ? wx.getDeviceInfo() : {};
    const appBaseInfo = wx.getAppBaseInfo ? wx.getAppBaseInfo() : {};

    // 如果新API不可用，回退到旧API
    if (!wx.getDeviceInfo || !wx.getAppBaseInfo) {
      console.warn('新的系统信息API不可用，回退到 wx.getSystemInfoSync');
      const res = wx.getSystemInfoSync();
      return {
        platform: res.platform, // ios, android, windows, mac
        system: res.system,     // iOS 10.0.1, Android 10, etc.
        brand: res.brand,       // iPhone, HUAWEI, etc.
        model: res.model,       // iPhone X, HUAWEI P40, etc.
        SDKVersion: res.SDKVersion // 微信基础库版本
      };
    }

    return {
      platform: deviceInfo.platform || 'unknown', // ios, android, windows, mac
      system: deviceInfo.system || 'unknown',     // iOS 10.0.1, Android 10, etc.
      brand: deviceInfo.brand || 'unknown',       // iPhone, HUAWEI, etc.
      model: deviceInfo.model || 'unknown',       // iPhone X, HUAWEI P40, etc.
      SDKVersion: appBaseInfo.SDKVersion || '2.0.0' // 微信基础库版本
    };
  } catch (e) {
    console.error('获取系统信息失败:', e);
    return { platform: 'unknown', system: 'unknown', brand: 'unknown', model: 'unknown', SDKVersion: '2.0.0' };
  }
}

/**
 * 检查蓝牙是否可用
 * @returns {Promise} 返回Promise对象
 */
const checkBluetoothAvailable = () => {
  return new Promise((resolve, reject) => {
    wx.getBluetoothAdapterState({
      success: (res) => {
        if (res.available) {
          resolve(res);
        } else {
          reject({
            errCode: 10000,
            errMsg: '蓝牙适配器不可用'
          });
        }
      },
      fail: (err) => {
        reject(err);
      }
    });
  });
}

/**
 * 初始化蓝牙适配器
 * @returns {Promise} 返回Promise对象
 */
const initBluetoothAdapter = () => {
  return new Promise((resolve, reject) => {
    wx.openBluetoothAdapter({
      success: (res) => {
        resolve(res);
      },
      fail: (err) => {
        reject(err);
      }
    });
  });
}

/**
 * 安全地关闭蓝牙连接
 * @param {String} deviceId 设备ID
 * @returns {Promise} 返回Promise对象
 */
const safeCloseConnection = (deviceId) => {
  return new Promise((resolve) => {
    if (!deviceId) {
      resolve({ errMsg: '无设备ID' });
      return;
    }
    
    wx.closeBLEConnection({
      deviceId: deviceId,
      success: (res) => {
        resolve(res);
      },
      fail: (err) => {
        console.error('断开连接失败:', err);
        resolve(err); // 即使失败也resolve，避免阻塞后续操作
      }
    });
  });
}

/**
 * ArrayBuffer转16进制字符串
 * @param {ArrayBuffer} buffer 需要转换的ArrayBuffer
 * @returns {String} 16进制字符串
 */
const ab2hex = (buffer) => {
  if (!buffer) {
    return '';
  }
  
  const hexArr = Array.prototype.map.call(
    new Uint8Array(buffer),
    function (bit) {
      return ('00' + bit.toString(16)).slice(-2);
    }
  );
  return hexArr.join('');
}

/**
 * 16进制字符串转ArrayBuffer
 * @param {String} hex 16进制字符串
 * @returns {ArrayBuffer} 转换后的ArrayBuffer
 */
const hex2ab = (hex) => {
  if (!hex) {
    return new ArrayBuffer(0);
  }
  
  // 移除所有空格
  hex = hex.replace(/\s/g, '');
  
  // 确保字符串长度为偶数
  if (hex.length % 2 !== 0) {
    hex = '0' + hex;
  }
  
  const buffer = new ArrayBuffer(hex.length / 2);
  const dataView = new DataView(buffer);
  
  for (let i = 0; i < hex.length; i += 2) {
    dataView.setUint8(i / 2, parseInt(hex.substr(i, 2), 16));
  }
  
  return buffer;
}

function getstr(){
  return "lisn3188 is ok"
}

function strToUUID(str){ //修正输入
  var templ="0123456789-abcdefABCDEF"
  var len = str.length
  if(len<=0)return ""
  var i
  var mstr=""
  for(i=0;i<len;i++){
    if (templ.indexOf(str.charAt(i))<0){  //没有这个字符
      console.log("error = " + str.charAt(i))
    } else mstr += str.charAt(i)
  }
  return mstr
}

function isUUID(str){ //确认是不是UUID
  console.log("input  = " + str)
  var div = str.split("-",-1)
  if (div.length!=5)return false
  
  if ((div[0].length != 8) || (div[1].length != 4) || (div[2].length != 4) || (div[3].length != 4) || (div[4].length != 12))return false
return true
}

function utf8ByteToUnicodeStr(utf8Bytes) {
  var unicodeStr = "";
  for (var pos = 0; pos < utf8Bytes.length;) {
    var flag = utf8Bytes[pos];
    var unicode = 0;
    if ((flag >>> 7) === 0) {
      unicodeStr += String.fromCharCode(utf8Bytes[pos]);
      pos += 1;

    } else if ((flag & 0xFC) === 0xFC) {
      unicode = (utf8Bytes[pos] & 0x3) << 30;
      unicode |= (utf8Bytes[pos + 1] & 0x3F) << 24;
      unicode |= (utf8Bytes[pos + 2] & 0x3F) << 18;
      unicode |= (utf8Bytes[pos + 3] & 0x3F) << 12;
      unicode |= (utf8Bytes[pos + 4] & 0x3F) << 6;
      unicode |= (utf8Bytes[pos + 5] & 0x3F);
      unicodeStr += String.fromCharCode(unicode);
      pos += 6;

    } else if ((flag & 0xF8) === 0xF8) {
      unicode = (utf8Bytes[pos] & 0x7) << 24;
      unicode |= (utf8Bytes[pos + 1] & 0x3F) << 18;
      unicode |= (utf8Bytes[pos + 2] & 0x3F) << 12;
      unicode |= (utf8Bytes[pos + 3] & 0x3F) << 6;
      unicode |= (utf8Bytes[pos + 4] & 0x3F);
      unicodeStr += String.fromCharCode(unicode);
      pos += 5;

    } else if ((flag & 0xF0) === 0xF0) {
      unicode = (utf8Bytes[pos] & 0xF) << 18;
      unicode |= (utf8Bytes[pos + 1] & 0x3F) << 12;
      unicode |= (utf8Bytes[pos + 2] & 0x3F) << 6;
      unicode |= (utf8Bytes[pos + 3] & 0x3F);
      unicodeStr += String.fromCharCode(unicode);
      pos += 4;

    } else if ((flag & 0xE0) === 0xE0) {
      unicode = (utf8Bytes[pos] & 0x1F) << 12;;
      unicode |= (utf8Bytes[pos + 1] & 0x3F) << 6;
      unicode |= (utf8Bytes[pos + 2] & 0x3F);
      unicodeStr += String.fromCharCode(unicode);
      pos += 3;

    } else if ((flag & 0xC0) === 0xC0) { //110
      unicode = (utf8Bytes[pos] & 0x3F) << 6;
      unicode |= (utf8Bytes[pos + 1] & 0x3F);
      unicodeStr += String.fromCharCode(unicode);
      pos += 2;

    } else {
      unicodeStr += String.fromCharCode(utf8Bytes[pos]);
      pos += 1;
    }
  }
  return unicodeStr;
}

module.exports = {
  formatTime,
  getSystemInfo,
  checkBluetoothAvailable,
  initBluetoothAdapter,
  safeCloseConnection,
  ab2hex,
  hex2ab,
  getstr: getstr,
  strToUUID: strToUUID,
  isUUID: isUUID
}
