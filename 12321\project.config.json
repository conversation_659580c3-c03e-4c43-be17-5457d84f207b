{"description": "项目配置文件", "packOptions": {"ignore": [], "include": []}, "setting": {"urlCheck": true, "es6": true, "postcss": true, "minified": true, "newFeature": true, "autoAudits": false, "compileWorklet": false, "uglifyFileName": false, "uploadWithSourceMap": true, "enhance": false, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "minifyWXML": true, "localPlugins": false, "disableUseStrict": false, "useCompilerPlugins": false, "condition": false, "swc": false, "disableSWC": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}}, "compileType": "miniprogram", "libVersion": "3.8.8", "appid": "wxe53333ac06e97efd", "projectname": "BLE_com", "isGameTourist": false, "simulatorType": "wechat", "simulatorPluginLibVersion": {}, "condition": {"search": {"current": -1, "list": []}, "conversation": {"current": -1, "list": []}, "game": {"currentL": -1, "list": []}, "miniprogram": {"current": -1, "list": []}}, "editorSetting": {}}