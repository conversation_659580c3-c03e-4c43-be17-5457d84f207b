{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/packageA/pages/zhaoshang/index.vue?a4c6", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/packageA/pages/zhaoshang/index.vue?acc7", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/packageA/pages/zhaoshang/index.vue?2928", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/packageA/pages/zhaoshang/index.vue?d848", "uni-app:///packageA/pages/zhaoshang/index.vue", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/packageA/pages/zhaoshang/index.vue?bbc2", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/packageA/pages/zhaoshang/index.vue?22a1"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "formData", "name", "phone", "isIOS", "onLoad", "uni", "success", "frontColor", "backgroundColor", "methods", "goBack", "delta", "submitForm", "title", "icon", "API", "then", "duration", "catch"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AACmM;AACnM,gBAAgB,uMAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAkwB,CAAgB,ivBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACwDtxB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACAC;QACAC;QACAC;MACA;MACAC;IACA;EACA;EACAC;IAAA;IACA;IACAC;MACAC;QACA;;QAEA;QACAD;UACAE;UACAC;QACA;MACA;IACA;EACA;EACAC;IACA;IACAC;MACAL;QACAM;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;QACAP;UACAQ;UACAC;QACA;QACA;MACA;MAEA;QACAT;UACAQ;UACAC;QACA;QACA;MACA;;MAEA;MACAT;QACAQ;MACA;;MAEA;MACAE,2CACAC;QACAX;;QAEA;QACAA;UACAQ;UACAC;UACAG;UACAX;YACA;YACA;YACA;UACA;QACA;MACA,GACAY;QACAb;;QAEA;QACAA;UACAQ;UACAC;UACAG;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC/IA;AAAA;AAAA;AAAA;AAAqlC,CAAgB,0hCAAG,EAAC,C;;;;;;;;;;;ACAzmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "packageA/pages/zhaoshang/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './packageA/pages/zhaoshang/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=7c8b643b&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"packageA/pages/zhaoshang/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=7c8b643b&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"container page-zhaoshang\" :class=\"{'ios-device': isIOS}\">\n\t\t<!-- 页面背景 -->\n\t\t<view class=\"page-background\">\n\t\t\t<!-- 背景图片 -->\n\t\t\t<image class=\"background-image\" src=\"https://wuji1112-1312674791.cos.ap-guangzhou.myqcloud.com/static/编组 <EMAIL>\" mode=\"aspectFill\"></image>\n\t\t\t\n\t\t\t<!-- 页面渐变背景 - 保留磨砂效果 -->\n\t\t\t<view class=\"gradient-overlay\"></view>\n\t\t</view>\n\t\t\n\t\t<!-- 顶部状态栏占位 -->\n\t\t<view class=\"status-bar\" :style=\"{'background-color': 'rgba(84, 51, 224, 0.8)'}\"></view>\n\t\t\n\t\t<!-- 内容区域 -->\n\t\t<scroll-view class=\"content-wrapper\" scroll-y=\"true\" :style=\"{'padding-top': isIOS ? '0' : 'calc(env(safe-area-inset-top))'}\">\n\t\t\t<!-- 顶部图片，从页面顶部开始 -->\n\t\t\t<image class=\"top-image\" src=\"https://wuji1112-1312674791.cos.ap-guangzhou.myqcloud.com/static/zhaoshang/<EMAIL>\" mode=\"widthFix\"></image>\n\t\t\t\n\t\t\t<!-- 表单区域 -->\n\t\t\t<view class=\"form-container\">\n\t\t\t\t<view class=\"form-title\">填写信息，领取限时优惠政策!</view>\n\t\t\t\t\n\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t<view class=\"label-container\">\n\t\t\t\t\t\t<text class=\"form-label\">联系人</text>\n\t\t\t\t\t\t<text class=\"required\">*</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<input class=\"form-input\" type=\"text\" v-model=\"formData.name\" placeholder=\"请输入您的姓名\" placeholder-class=\"placeholder-style\" />\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t<view class=\"label-container\">\n\t\t\t\t\t\t<text class=\"form-label\">联系方式</text>\n\t\t\t\t\t\t<text class=\"required\">*</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<input class=\"form-input\" type=\"number\" v-model=\"formData.phone\" maxlength=\"11\" placeholder=\"请输入您的手机号\" placeholder-class=\"placeholder-style\" />\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"submit-button\" @click=\"submitForm\">\n\t\t\t\t\t<text class=\"submit-text\">立即提交</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 底部图片 -->\n\t\t\t<image class=\"bottom-image\" src=\"https://wuji1112-1312674791.cos.ap-guangzhou.myqcloud.com/static/zhaoshang/<EMAIL>\" mode=\"widthFix\"></image>\n\t\t</scroll-view>\n\t\t\n\t\t<!-- 返回按钮 - 悬浮在左上角，与安全区域保持距离 -->\n\t\t<view class=\"floating-back\" @click=\"goBack\" :style=\"{'top': isIOS ? '90rpx' : 'calc(env(safe-area-inset-top) + 20rpx)'}\">\n\t\t\t<text class=\"material-icons\">arrow_back</text>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport API from '../../../static/js/api.js';\n\t\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tformData: {\n\t\t\t\t\tname: '',\n\t\t\t\t\tphone: ''\n\t\t\t\t},\n\t\t\t\tisIOS: false\n\t\t\t}\n\t\t},\n\t\tonLoad() {\n\t\t\t// 检测平台\n\t\t\tuni.getSystemInfo({\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tthis.isIOS = res.platform.toLowerCase() === 'ios';\n\t\t\t\t\t\n\t\t\t\t\t// 设置状态栏样式，改为金色系\n\t\t\t\t\tuni.setNavigationBarColor({\n\t\t\t\t\t\tfrontColor: '#ffffff',\n\t\t\t\t\t\tbackgroundColor: '#e09a1b'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\tmethods: {\n\t\t\t// 返回上一页\n\t\t\tgoBack() {\n\t\t\t\tuni.navigateBack({\n\t\t\t\t\tdelta: 1\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 提交表单\n\t\t\tsubmitForm() {\n\t\t\t\t// 表单验证\n\t\t\t\tif (!this.formData.name.trim()) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请输入联系人姓名',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tif (!this.formData.phone || !/^1[3-9]\\d{9}$/.test(this.formData.phone)) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请输入正确的手机号',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 显示加载中\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: '提交中...'\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\t// 调用API提交表单数据\n\t\t\t\tAPI.partner.submit(this.formData)\n\t\t\t\t\t.then(res => {\n\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 显示提交成功\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '提交成功',\n\t\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\t\tduration: 2000,\n\t\t\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\t\t\t// 清空表单\n\t\t\t\t\t\t\t\tthis.formData.name = '';\n\t\t\t\t\t\t\t\tthis.formData.phone = '';\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t})\n\t\t\t\t\t.catch(err => {\n\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 显示错误信息\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: err.message || '提交失败，请稍后重试',\n\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t\t});\n\t\t\t\t\t});\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style>\n\t/* CSS变量定义 */\n\tpage {\n\t\t--primary-light: #A875FF;\n\t\t--neon-pink: #ff36f9;\n\t\t--form-purple: #5433e0;\n\t\t--border-color: rgba(165, 166, 185, 0.51);\n\t\t--text-color: #333333;\n\t\t--placeholder-color: rgba(153, 153, 153, 0.6);\n\t}\n\t\n\t/* 页面基础样式 */\n\t.page-zhaoshang {\n\t\twidth: 100%;\n\t\theight: 100vh; /* 使用固定高度 */\n\t\tbox-sizing: border-box;\n\t\tposition: relative;\n\t\tmargin: 0;\n\t\tpadding: 0;\n\t}\n\t\n\t/* 页面背景样式 */\n\t.page-background {\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tz-index: 0;\n\t}\n\t\n\t/* 背景图片样式 */\n\t.background-image {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tz-index: 0;\n\t\tobject-fit: cover;\n\t}\n\t\n\t.gradient-overlay {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tbackground: linear-gradient(to bottom, \n\t\t\trgba(224, 168, 51, 0.7) 0%, \n\t\t\trgba(230, 149, 15, 0.7) 50%,\n\t\t\trgba(240, 155, 25, 0.7) 100%);\n\t\tbackdrop-filter: blur(5px);\n\t\t-webkit-backdrop-filter: blur(5px);\n\t\tz-index: 1;\n\t\tpointer-events: none;\n\t}\n\t\n\t/* 顶部状态栏占位 */\n\t.status-bar {\n\t\twidth: 100%;\n\t\theight: var(--status-bar-height);\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tz-index: 100;\n\t\tbackground-color: rgba(224, 168, 51, 0.8); /* 金色系，与页面风格一致 */\n\t}\n\t\n\t.ios-device .status-bar {\n\t\theight: 44px; /* 增加iOS设备的状态栏高度，为灵动岛留出空间 */\n\t}\n\t\n\t/* 内容包装器 */\n\t.content-wrapper {\n\t\tposition: relative;\n\t\tz-index: 2;\n\t\twidth: 100%;\n\t\theight: 100vh; /* 设置高度 */\n\t\tbox-sizing: border-box;\n\t}\n\t\n\t/* 顶部图片样式 */\n\t.top-image {\n\t\twidth: 100%;\n\t\tdisplay: block;\n\t\tflex-shrink: 0;\n\t\tmargin-top: -10rpx; /* 添加负边距使图片上移 */\n\t\t/* 移除色相旋转滤镜，使用原始颜色 */\n\t}\n\t\n\t/* iOS设备特定样式 */\n\t.ios-device .top-image {\n\t\tmargin-top: 60rpx; /* 将负边距改为正边距，使图片向下移动避免被灵动岛挡住 */\n\t\tmargin-bottom: 0;\n\t}\n\t\n\t/* 表单容器 */\n\t.form-container {\n\t\twidth: 100%;\n\t\tpadding: 30rpx 40rpx;\n\t\tbackground: rgba(255, 255, 255, 1); /* 纯白色背景 */\n\t\tbox-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);\n\t\tbox-sizing: border-box;\n\t\tmargin-bottom: 0;\n\t}\n\t\n\t/* 表单标题 */\n\t.form-title {\n\t\tfont-size: 40rpx;\n\t\tcolor: #5433e0; /* 从金色系改回紫色系 */\n\t\tfont-weight: bold;\n\t\ttext-align: center;\n\t\tmargin: 30rpx 0 40rpx;\n\t}\n\t\n\t/* 表单项容器 */\n\t.form-item {\n\t\tmargin-bottom: 40rpx;\n\t\twidth: 100%;\n\t}\n\t\n\t/* 标签容器 */\n\t.label-container {\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\talign-items: center;\n\t\tmargin-bottom: 10rpx;\n\t}\n\t\n\t/* 必填星号 */\n\t.required {\n\t\tcolor: #ba4d67;\n\t\tfont-size: 21rpx;\n\t\tmargin-left: 5rpx;\n\t}\n\t\n\t.form-label {\n\t\tfont-size: 24rpx;\n\t\tfont-weight: normal;\n\t\tcolor: #444950;\n\t}\n\t\n\t.form-input {\n\t\twidth: 100%;\n\t\theight: 78rpx;\n\t\tbackground: #ffffff;\n\t\tborder: 1px solid rgba(165, 166, 185, 0.51);\n\t\tborder-radius: 10rpx;\n\t\tpadding: 0 30rpx;\n\t\tfont-size: 28rpx;\n\t\tcolor: #333333;\n\t\tbox-sizing: border-box;\n\t}\n\t\n\t/* iOS设备特定样式 - 输入框 */\n\t.ios-device .form-input {\n\t\theight: 70rpx; /* iOS设备上更小的高度 */\n\t}\n\t\n\t.placeholder-style {\n\t\tcolor: rgba(153, 153, 153, 0.6);\n\t}\n\t\n\t/* 提交按钮 */\n\t.submit-button {\n\t\twidth: 100%;\n\t\theight: 80rpx;\n\t\tbackground: linear-gradient(135deg, #6a4dff, #9b4dff); /* 从金色系改回紫色渐变 */\n\t\tborder-radius: 40rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tmargin-top: 60rpx;\n\t\tmargin-bottom: 30rpx;\n\t\tbox-shadow: 0 8rpx 16rpx rgba(155, 77, 255, 0.3); /* 调整阴影颜色为紫色系 */\n\t}\n\t\n\t.submit-text {\n\t\tfont-size: 32rpx;\n\t\tcolor: #ffffff;\n\t\tfont-weight: 500;\n\t}\n\t\n\t/* 底部图片样式 */\n\t.bottom-image {\n\t\twidth: 100%;\n\t\tdisplay: block;\n\t}\n\t\n\t/* 返回按钮 */\n\t.floating-back {\n\t\tposition: fixed;\n\t\tleft: 20rpx;\n\t\twidth: 70rpx;\n\t\theight: 70rpx;\n\t\tborder-radius: 50%;\n\t\tbackground-color: rgba(255, 197, 84, 0.15); /* 调整为金色系 */\n\t\tborder: 1px solid rgba(255, 197, 84, 0.3); /* 调整为金色系 */\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tz-index: 10;\n\t}\n\t\n\t.floating-back .material-icons {\n\t\tfont-size: 48rpx;\n\t\tcolor: #ffffff;\n\t}\n\t\n\t/* Material Icons 字体 */\n\t@font-face {\n\t\tfont-family: 'Material Icons';\n\t\tfont-style: normal;\n\t\tfont-weight: 400;\n\t\tsrc: url(https://fonts.gstatic.com/s/materialicons/v139/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format('woff2');\n\t}\n\n\t.material-icons {\n\t\tfont-family: 'Material Icons';\n\t\tfont-weight: normal;\n\t\tfont-style: normal;\n\t\tfont-size: 48rpx;\n\t\tline-height: 1;\n\t\tletter-spacing: normal;\n\t\ttext-transform: none;\n\t\tdisplay: inline-block;\n\t\twhite-space: nowrap;\n\t\tword-wrap: normal;\n\t\tdirection: ltr;\n\t\t-webkit-font-smoothing: antialiased;\n\t\t-moz-osx-font-smoothing: grayscale;\n\t}\n</style> ", "import mod from \"-!../../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754165306719\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}