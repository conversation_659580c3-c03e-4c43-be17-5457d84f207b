{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/scan/device.vue?06bf", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/scan/device.vue?76fe", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/scan/device.vue?dc6b", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/scan/device.vue?d1f5", "uni-app:///pages/scan/device.vue", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/scan/device.vue?2ea8", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/scan/device.vue?ac9f"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "console", "components", "LoginModal", "data", "deviceInfo", "deviceId", "deviceMac", "deviceName", "deviceStatus", "scanType", "orderId", "orderStatus", "useStatus", "orderAmount", "orderDuration", "roomId", "isLockOpen", "batteryLevel", "hourlyRate", "priceText", "isConnected", "isConnecting", "lastConnectTime", "connectionDebounceTimer", "autoReconnect", "statusUpdateTimer", "bluetoothInitialized", "isRecentlyDisconnected", "isManualDisconnect", "isReconnecting", "reconnectLock", "lastOpenTime", "openCooldown", "cooldownTimer", "currentTime", "lockStatusCheckTimer", "lockStatusCheckInterval", "lastLockStatusCheckTime", "lockS<PERSON>us<PERSON>heckEnabled", "isPaid", "isPaying", "paymentLoading", "paymentSuccess", "showPaymentModal", "doorOpenInProgress", "doorOpenProcessed", "doorOpenCompleted", "showOpenDoorModal", "isPageActive", "isPageLoaded", "autoConnectTimer", "isLoggedIn", "showLoginModal", "userInfo", "statusTimer", "isSimulatedMode", "uiUpdateTimer", "successAudio", "audioSrc", "orderPollingTimer", "orderPollingCount", "max<PERSON><PERSON>ing<PERSON>ount", "pollingInterval", "isNavigating", "lastNavigateTime", "bannerTouchStarted", "bannerTouchStartTime", "computed", "connectionStatusText", "mainActionText", "mainActionIcon", "mainActionClass", "orderStatusText", "onLoad", "queryString", "key", "value", "queryParams", "params", "allParams", "uni", "title", "content", "showCancel", "confirmText", "success", "lockService", "then", "catch", "options", "originalQuery", "cancelText", "setTimeout", "onShow", "onHide", "clearTimeout", "onUnload", "clearInterval", "methods", "checkLoginStatus", "showLoginPrompt", "handleCloseLoginModal", "handleLoginSuccess", "userId", "icon", "duration", "handleLoginFail", "handleBannerClick", "e", "handleBannerTouch", "handleBannerTouchEnd", "handleBannerTouchCancel", "navigateToZhaoshang", "url", "fail", "complete", "startCooldownTimer", "startLockStatusCheck", "stopLockStatusCheck", "checkLockStatus", "isValidMacAddress", "handleConnectedState", "forceSyncConnectionState", "forceSyncDeviceInfo", "forceSetAllLayersConnected", "blueToothManager", "setupLockServiceCallbacks", "onConnected", "onDisconnected", "onLockStatusChange", "onBatteryUpdate", "onError", "debouncedUpdateUIState", "updateUIState", "forceUpdate", "preinitBluetooth", "debounceConnect", "connectDevice", "disconnectDevice", "ensureDisconnectBluetooth", "queryDeviceStatus", "handleMainAction", "handleOpenDoor", "tryAlternativeOpenDoor", "reconnectAndOpenDoor", "ignoreOrderStatus", "force", "retry", "singleCommand", "operationType", "directBluetoothOpenDoor", "processPayment", "message", "deviceCode", "createOrderIfNeeded", "finally", "closePaymentModal", "closeOpenDoorModal", "getDeviceInfo", "continueGetDeviceInfo", "baseUrl", "method", "header", "sslVerify", "requestComplete", "handleDeviceInfoResponse", "<PERSON><PERSON><PERSON><PERSON>", "mac", "bluetoothMac", "connectDeviceOptimized", "Promise", "ensureBluetoothInitialized", "connectByDeviceName", "showConnectionError", "connectDeviceByName", "getDevicePrice", "getOrderStatus", "resolve", "reject", "startOrderTimer", "saveLockState", "playSuccessSound", "type", "tryAlternativeAudioPlay", "innerAudio", "tryShortAudio", "dataUrl", "playErrorSound", "onBackgroundImageLoaded", "goBack", "setupBluetoothListeners", "getDeviceInfoById", "formatDeviceIdToMac", "showError", "initPayment", "tryConnectDevice", "checkOrderAndOpenDoor", "performOpenDoor", "resetDoorState", "savePaymentState", "timestamp", "restorePaymentState", "forceReauthenticateAndUnlock", "performReconnectAndUnlock", "executeUnlockCommand", "name", "checkBluetoothState", "verifyConnectionState", "reinitializeBluetooth", "stateChanged"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACa;;;AAGlE;AACgM;AAChM,gBAAgB,uMAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAAovB,CAAgB,kvBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACsJxwB;AACA;AACA;AAAA;AAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAGA;AACA;EACA;IACA;EACA;EACA;EACA;AACA;;AAEA;AACAC;AAAA,eAEA;EACAC;IACAC;EACA;EACAC;IACA;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;;MAEA;MACAC;MAEA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;MAEA;MACAC;MACAC;MAEA;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;;MAEA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;;MAEA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;;MAEA;MACAC;MACAC;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;MACAC;MAAA;MACAC;MAEA;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;MACAC;MAEA;MACAC;MAEA;MACAC;MAEA;MACAC;MAEA;MACAC;MACAC;MAEA;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;;MAEA;MACAC;MAAA;MACAC;MAAA;;MAEA;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;QACA;MACA;QACA;MACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;QACA;MACA;MAEA;QACA;UACA;UACA;UACA;UAEA;YACA;YACA;UACA;;UAEA;UACA;YACA;UACA;YACA;UACA;YACA;UACA;QACA;UACA;QACA;MACA;QACA;MACA;QACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;QACA;MACA;MAEA;QACA;UACA;QACA;UACA;QACA;MACA;QACA;MACA;QACA;QACA;MACA;IACA;IACA;IACAC;MACA;QACA;UACA;QACA;UACA;QACA;MACA;QACA;MACA;QACA;QACA;MACA;IACA;IACA;IACAC;MACA;QACA;UACA;QACA;UACA;YACA;UACA;YACA;UACA;YACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;MAAA;IAEA;EACA;EACAC;IAAA;IACA;IACA;;IAEA;IACAzE;IACAA;MACA;MACA;MACA;MACA;IACA;;IAEA;IACA;IAEA;MACAA;MACA;QACA;UACA;UACA;UACA;UACAA;UAEA0E;YACA;cAAA;cAAAC;cAAAC;YACA;cACAC;YACA;UACA;UAEA7E;UACA8E;QACA;UACAA;QACA;MACA;QACA9E;QACA8E;MACA;IACA;IAEA9E;;IAEA;IACA;IACA;IACA;;IAEA;IACA;MACA;IACA;;IAEA;IACA;MACA;MACA;MACAA;IACA;IAEAA;MACAS;MACAC;MACAK;MACA;MACA;MACA;MACAgE;IACA;IAEA/E;;IAEA;IACA;IACAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;MACAA;MACAgF;QACAC;QACAC;QACAC;QACAC;QACAC;UACAL;QACA;MACA;MACA;IACA;;IAEA;IACA;MACAM;MACAtF;MACA,sBACAuF;QACAvF;;QAEA;QACA;;QAEA;QACA;QACA;UACAsF;UACAtF;;UAEA;UACA;UACA;UAEAA;QACA;UACA;UACA;UACA;UACAA;QACA;;QAEA;QACA;MACA,GACAwF;QACAxF;MACA;IACA;IAEAA;IACAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;MACA;MACA;;MAEA;MACA;QACAK;MACA;;MAEA;MACA;QACA;UACA;UACA;UACA;YACAA;YACAL;UACA;QACA;UACAA;QACA;MACA;MAEAA;MACAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MAEA;QACA;QACA;QACAA;QACA;MACA;QACAA;UACAK;UACAyE;UACAW;UACAhF;UACAiF;QACA;;QAEA;QACA;QACA;UACA;UACA;UACA;YACArF;YACAL;YACA;YACA;YACA;UACA;QACA;;QAEA;QACA;UACAA;UACA;UACA;UACA;QACA;;QAEA;QACAgF;UACAC;UACAC;UACAC;UACAQ;UACAP;UACAC;YACA;cACA;cACA;YACA;cACAL;YACA;UACA;QACA;MACA;IACA;MACA;MACAhF;MACAA;MAEA;QACA;QACA;UACAA;UACAsF;UACA;UACA;UACA;QACA;UACA;UACAtF;UACA;UACA;UACA;QACA;MACA;QACAA;UACA;UACA;UACA;UACA;UACA;QACA;;QAEA;QACA;QACA;UACAA;UACA;UACA;UACA;QACA;UACA;UACA;YACAA;YACA;YACA;YACA;YACA;UACA;UAEA;UACA4F;YAAAZ;UAAA;QACA;MACA;IACA;EACA;EACAa;IAAA;IACA7F;IACA;;IAEA;IACA;;IAEA;IACA;;IAEA;IACA4F;MACA;IACA;;IAEA;IACA;;IAEA;IACA;;IAEA;IACA;MACA5F;MACA,sBACAuF;QACAvF;;QAEA;QACA;;QAEA;QACA;UACAA;UACAsF;UACA;UACA;;UAEA;UACA;;UAEA;UACA;YACAtF;YACA;UACA;;UAEA;UACA;YACAA;YACA;UACA;UACA;UAAA,KACA;YACAA;YACA;UACA;QACA;QACA;QAAA,KACA;UACAA;UACA;UACA;UACA;QACA;;QAEA;QACA;MACA,GACAwF;QACAxF;MACA;MACA;IACA;;IAEA;IACA;MACAA;MACA;IACA;;IAEA;IACA;MACAA;MACA;MACA;IACA;;IAEA;IACA;MACA;MACAA;MACA;QACA;MACA;IACA;EACA;EACA8F;IACA9F;IACA;;IAEA;IACA;;IAEA;IACA;MACAA;MACA;MACA;IACA;;IAEA;IACA;MACAA;MACA;IACA;;IAEA;IACA;MACA+F;MACA;IACA;;IAEA;IACA;IACA/F;EACA;EACAgG;IACAhG;IACA;;IAEA;IACA;;IAEA;IACA;MACAiG;MACA;IACA;;IAEA;IACA;;IAEA;IACA;IACA;;IAEA;IACA;;IAEA;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;MAEAnG;MAEA;QACA;QACA;QACAA;MACA;QACA;QACA;QACAA;;QAEA;QACA;MACA;IACA;IAEA;IACAoG;MAAA;MACA;MACAR;QACA;UACA;QACA;MACA;IACA;IAEA;IACAS;MACA;IACA;IAEA;IACAC;MACAtG;MACA;MACA;QAAAuG;MAAA;MACA;MAEAvB;QACAC;QACAuB;QACAC;MACA;;MAEA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA1G;MACA;MAEAgF;QACAC;QACAuB;QACAC;MACA;IACA;IAEA;IACAE;MACA3G;;MAEA;MACA;QACA4G;MACA;;MAEA;MACA;IACA;IAEA;IACAC;MACA7G;MACA;MACA;;MAEA;MACA;QACA4G;MACA;MACA;QACAA;MACA;IACA;IAEA;IACAE;MAAA;MACA9G;;MAEA;MACA;QACA4G;MACA;MACA;QACAA;MACA;;MAEA;MACA;QACA;QACA;UACA5G;UACA;UACA4F;YACA;UACA;QACA;MACA;;MAEA;MACA;MACA;IACA;IAEA;IACAmB;MACA/G;MACA;MACA;IACA;IAEA;IACAgH;MAAA;MACAhH;;MAEA;MACA;QACA4G;MACA;MACA;QACAA;MACA;;MAEA;MACA;QACA5G;QACA;MACA;;MAEA;MACA;MACA;QACAA;QACA;MACA;MAEA;MACA;MAEAA;;MAEA;MACAgF;QACAiC;QACA5B;UACArF;UACA;UACAgF;YACAC;YACAuB;YACAC;UACA;QACA;QACAS;UACAlH;UACA;;UAEA;UACA;YACAA;YACAgF;cACAiC;cACAE;gBACAvB;kBACAZ;oBACAiC;kBACA;gBACA;cACA;YACA;UACA;YACAjC;cACAC;cACAuB;YACA;UACA;QACA;QACAW;UACA;UACAvB;YACA;UACA;QACA;MACA;IACA;IAEA;IACAwB;MAAA;MACA;MACA;QACAnB;MACA;;MAEA;MACA;QACA;QACA;QACA;;QAEA;QACA;UACAA;UACA;UACA;QACA;MACA;IACA;IAEA;IACAoB;MAAA;MACArH;;MAEA;MACA;QACAiG;MACA;;MAEA;MACA;QACA;QACA;UACA;UACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAqB;MACAtH;MACA;QACAiG;QACA;MACA;IACA;IAEA;IACAsB;MACA;QACAvH;;QAEA;QACAsF,qCACAC;UACAvF;UACA;QACA,GACAwF;UACAxF;UACA;UACA;QACA;MACA;QACAA;MACA;IACA;IAEA;IACAwH;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACAzH;;MAEA;MACA;MACA;MAEAA;MAEA;QACAA;QACA;QACA;QACA;MACA;;MAEA;MACA;QACAA;QACA;MACA;IACA;IAEA;IACA0H;MAAA;MACA1H;;MAEA;MACA;QACAsF;MACA;MACA;QACAA;MACA;MACA;QACAA;MACA;;MAEA;MACAM;QACA;QACA;QAEA5F;QAEA;UACAA;UACA;QACA;MACA;IACA;IAEA;IACA2H;MACA3H;MAEA;QACA;QACA;UACA;YACAsF;UACA;UACA;YACAA;UACA;UACA;YACA;YACA;cACAA;YACA;UACA;QACA;QAEA;UACAA;QACA;;QAEA;QACA;QAEAtF;MACA;QACAA;MACA;IACA;IAEA;IACA4H;MACA5H;MAEA;QACA;QACA;UACAsF;UACAA;UACAA;UAEA;YACA;YACA;cACAA;YACA;YACA;cACAA;YACA;YACA;cACAA;YACA;YACA;cACAA;YACA;UACA;UAEAtF;UACAA;QACA;;QAEA;QACA;UACA;UACA;UAEA;YACAA;;YAEA;YACA6H;YACAA;YACA7H;YACAA;;YAEA;YACA;cACA6H;cACAA;cACA7H;YACA;;YAEA;YACA;cACAA;YACA;cACAA;YACA;UACA;YACAA;UACA;QACA;UACAA;QACA;QAEAA;MACA;QACAA;MACA;IACA;IAEA;IACA8H;MAAA;MACA;MACAxC;QACA;QACAyC;UACA/H;;UAEA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;YACAgF;UACA;YACAhF;UACA;;UAEA;UACA;YACA;UACA;;UAEA;UACA;;UAEA;UACA;;UAEA;UACA4F;YACA;UACA;;UAEA;UACA;QACA;QAEA;QACAoC;UACAhI;;UAEA;UACA;YACA;YACA;;YAEA;YACA;;YAEA;YACA;YACA4F;cACA;YACA;;YAEA;YACA;;YAEA;YACA;cACA5F;;cAEA;cACA4F;gBACA;gBACA;cACA;YACA;;YAEA;YACA;UACA;QACA;QAEA;QACAqC;UACAjI;UACA;;UAEA;UACA;YACA;YACA;;YAEA;YACA;UACA;YACA;YACAA;YACA;YACA;UACA;;UAEA;UACA;QACA;QAEA;QACAkI;UACAlI;UACA;;UAEA;UACA;QACA;QAEA;QACAmI;UACAnI;;UAEA;UACAgF;YACAC;YACAuB;YACAC;UACA;QACA;MACA;IACA;IAEA;IACA2B;MAAA;MACA;MACA;QACArC;MACA;;MAEA;MACA;QACA;MACA;IACA;IAEA;IACAsC;MAAA;MACArI;;MAEA;MACA;QACA;UACAA;UACA;UACA;;UAEA;UACA;YACAA;YACA;UACA;QACA;MACA;QACA;QACA;QACA;UACAA;UACA,wBACAuF;YACA;YACA;cACAvF;cACA;cACA;cACA;;cAEA;cACA;;cAEA;cACA;gBACAA;gBACA;cACA;YACA;cACA;cACAA;cACA;cACA;;cAEA;cACA;gBACAA;gBACA;cACA;YACA;UACA,GACAwF;YACAxF;UACA;QACA;UACA;UACA;YACAA;YACA;UACA;QACA;MACA;;MAEA;MACA;QACAA;QACAsF;;QAEA;QACA;UACAtF;UACA,sBACAuF;YACAvF;YACA;YACA;;YAEA;YACA;cACAA;cACA;YACA;UACA,GACAwF;YACAxF;UACA;QACA;MACA;;MAEA;MACA;MACA;QACA;QACA;QACA;UACAA;UACA;;UAEA;UACA;YACA;;YAEA;YACA;cACAA;cACA4F;gBACA;cACA;YACA;UACA;QACA;;QAEA;QACA;UACA5F;UACA;QACA;;QAEA;QACA;UACAA;UACA;QACA;MACA;;MAEA;MACA;QACAA;QACA;;QAEA;QACA4F;UACA;QACA;MACA;;MAEA;MACA;IACA;IAEA;IACA0C;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACAvI;;MAEA;MACA;QACAA;QACA;MACA;;MAEA;MACAsF,0BACAC;QACAvF;;QAEA;QACA;UACAA;UACA;QACA;MACA,GACAwF;QACAxF;;QAEA;QACAA;QACA;MACA;IACA;IAEA;IACAwI;MAAA;MACA;MACA;MACA;QACAxI;QACA;MACA;MAEA;;MAEA;MACA;QACA+F;MACA;;MAEA;MACA;QACA;MACA;IACA;IAEA;IACA0C;MACA;MACA;MACA;MAEA;QACAzI;QACA;UACA;UACA;QACA;QACA;MACA;MAEAA;MACA;MACA;;MAEA;MACA;QACAA;QACAsF;MACA;;MAEA;MACA;IACA;IAEA;IACAoD;MACA;QACA;MACA;MAEA,uCACAnD;QACAvF;MACA,GACAwF;QACAxF;MACA;IACA;IAEA;IACA2I;MACA,wBACApD;QACA;MACA,GACAC;QACAxF;MACA;IACA;IAEA;IACA4I;MACA;QACA5I;QACA;QACAsF,qCACAC;UACAvF;UACA;UACA;QACA,GACAuF;UACAvF;QACA,GACAwF;UACAxF;QACA;MACA;QACAA;MACA;IACA;IAEA;IACA6I;MACA7I;;MAEA;MACA;QACAA;QACA;QACA;MACA;;MAEA;MACA;QACA;QACA;MACA;QACA;QACA;MACA;QACA;QACA;MACA;IACA;IAEA;IACA8I;MAAA;QAAA;MACA9I;;MAEA;MACA;QACAA;QACAgF;UACAC;UACAuB;UACAC;QACA;QACA;MACA;;MAEA;MACA;MACA;MAEA;QACA;QACAzB;UACAC;UACAuB;UACAC;QACA;QACA;MACA;;MAEA;MACA;QACAzG;QACA;QACA;MACA;;MAEA;MACA;MACA;MAEAA;MAEA;QACAA;QACA;QACA;MACA;;MAEA;MACA;QACAA;QACA;QACA;MACA;;MAEA;MACAA;MACA;MACAA;;MAEA;MACA;QACAA;QACAsF;QACAA;QACA;UACAA;QACA;;QAEA;QACA;MACA;;MAEA;MACAtF;MACAA;MACAA;;MAEA;MACA;QACA;QACA;UAAA;UACAA;UACAA;UACAA;UACAA;;UAEA;UACA;YACAA;YAEA;cACA6H;cACAA;YACA;YACAA;YACAA;YACAA;YAEA7H;UACA;QACA;MACA;QACAA;MACA;;MAEA;MACAA;;MAEA;MACA,mDACAuF;QACAvF;;QAEA;QACA;QACA;;QAEA;QACA;;QAEA;QACA;QAEA;QACAgF;UACAC;UACAuB;UACAC;QACA;;QAEA;QACA;;QAEA;QACA;;QAEA;QACA;MACA,GACAjB;QACAxF;;QAEA;QACA;UACAA;UACA;QACA;UACAgF;YACAC;YACAuB;YACAC;UACA;QACA;MACA;IACA;IAEA;IACAsC;MAAA;MACA/I;;MAEA;MACA,4BACAwF;QACAxF;QACA;QACA;MACA;IACA;IAEA;IACAgJ;MAAA;MACAhJ;MAEA;QACA;QACA;UACAsF;QACA;UACAtF;QACA;;QAEA;QACA;QACA;QACA;;QAEA;QACA4F;UACA,iCACAL;YACA;YACAK;cACA;cACAN;gBACA2D;gBACAC;gBACAC;gBACAC;gBACAC;cACA,GACA9D,cACAC;YACA;UACA,GACAA;QACA;MACA;IACA;IAEA;IACA8D;MACAtJ;;MAEA;MACA;MACAgF;QACAC;QACAuB;QACAC;MACA;IACA;IAEA;IACA8C;MAAA;MACAvJ;;MAEA;MACA;QACAA;QACA;QACA;QACA;MACA;;MAEA;MACA;MACA;;MAEA;MACA;QACA;QACA;UACAA;UACA;YACAU;UACA;QACA;UACA;UACAV;;UAEA;UACA;;UAEA;UACA;YACAA;YACA;cACAwJ;YACA;UACA;;UAEA;UACA;UACAxJ;UAEA;YACAyJ;YACAhD;UACA;QACA;MACA;;MAEA;MACAiD,sBACAnE;QACAvF;QACA;QACA;UACA;;UAEA;UACA;;UAEA;UACA;;UAEA;UACAsF;;UAEA;UACA;UACAtF;UACA;;UAEA;UACA;QACA;UACA;QACA;MACA,GACAuF;QACAvF;;QAEA;QACA;QACA;QACA;;QAEA;QACA;;QAEA;QACA;;QAEA;QACA;;QAEA;QACAgF;UACAC;UACAuB;UACAC;QACA;;QAEA;QACA;;QAEA;QACAb;UACA;UACA;YACA5F;YACA;UACA;YACA;YACAA;YACA;UACA;YACAA;UACA;QACA;MACA,GACAwF;QACAxF;;QAEA;QACAgF;UACAC;UACAuB;UACAC;QACA;;QAEA;QACA;MACA,GACAkD;QACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;AACA;AACA;AACA;IACAC;MAAA;MACA9J;;MAEA;MACA,oCACAuF;QACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;YACA;YACA;YACAvF;UACA;;UAEA;UACA;UACA;;UAEA;UACAsF;;UAEA;UACA;YACAN;cACAC;cACAuB;cACAC;YACA;;YAEA;YACA;UACA;YACA;YACAzB;cACAC;cACAuB;cACAC;YACA;UACA;QACA;;QAEA;QACA;MACA,GACAjB;QACAxF;QACA;QACA;MACA;IACA;IAEA;IACA+J;MAAA;MACA;MACA;MACA;QACAC;MACA;QACA;QACAA;MACA;;MAEA;MACA;QACAA;MACA;;MAEA;MACA;MACA;MAEAhK;MACAA;MAEA;;MAEA;MACAgF;QACAiC;QACAgD;QACAC;UACA;QACA;QACAC;QACA9E;UACA;UACA;UAEA;YACArF;YACAoK;YAEA;UACA;YACApK;YACA;UACA;QACA;;QACAkH;UACAlH;UACA;QACA;MACA;;MAEA;MACAgF;QACAiC;QACAgD;QACAC;UACA;QACA;QACAC;QACA9E;UACA;UACA;UAEA;YACArF;YACAoK;YAEA;UACA;YACApK;YACA;YACA;cACA4F;gBACA;kBACA;gBACA;cACA;YACA;UACA;QACA;QACAsB;UACA;UACA;YACAlH;;YAEA;YACA4F;cACA;gBACA;cACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAyE;MAAA;MACArK;MACAA;QACAsK;QACAC;QACAC;QACAlK;QACAC;QACAF;MACA;MACA;;MAEA;MACA;MACA;QACA;QACA;MACA;;MAEA;MACA;QACA;QACA;QACA,sBACAkF;UACA;UACA;YACA;YACA;;YAEA;YACA;cACAvF;cACA;YACA;UACA;QACA,GACAwF;UACAxF;QACA;MACA;;MAEA;MACA;MACA;QACA;QACA;QACA;UACA;UACAA;UACAsF;QACA;UACAtF;QACA;MACA;QACAA;MACA;;MAEA;MACA;QACA;QACAA;;QAEA;QACAsF;QACAA;;QAEA;QACA;MACA;QACA;QACAtF;QACA;MACA;QACAA;MACA;IACA;IAEA;AACA;AACA;IACAyK;MAAA;MACAzK;;MAEA;MACA;MACA;MAEA;QACAA;QACA;QACA;QACA;MACA;MAEA;QACAA;QACA;MACA;MAEA;;MAEA;MACA,8CACA0K,oBACA;MAEA;QACA;QACA;UACA1K;UACA,2DACAuF;YACAvF;YACA;UACA,GACAwF;YACAxF;YACA;YACA;cACA;YACA;cACA;cACA;cACA;YACA;UACA;QACA;UACA;UACA;QACA;UACA;UACA;UACAgF;YACAC;YACAuB;UACA;UACA;QACA;MACA;IACA;IAEA;AACA;AACA;IACAmE;MAAA;MACA;QACA;MACA;MAEA3K;MACA,iCACAuF;QACA;QACAvF;QACA;MACA,GACAwF;QACAxF;QACA;MACA;IACA;IAEA;AACA;AACA;IACA4K;MAAA;MACA5K;MACA,yDACAuF;QACAvF;QACA;MACA,GACAwF;QACAxF;QACA;QACA;QACA;MACA;IACA;IAEA;AACA;AACA;IACA6K;MACA7F;QACAC;QACAuB;QACAC;MACA;IACA;IAEA;AACA;AACA;AACA;IACAqE;MAAA;MACA9K;;MAEA;MACAsF,0BACAC;QACA;QACA;MACA,GACAA;QACAvF;QACA;QACAA;;QAEA;QACAgF;UACAC;UACAuB;QACA;MACA,GACAhB;QACAxF;;QAEA;QACA;;QAEA;QACA;;QAEA;QACAgF;UACAC;UACAuB;QACA;MACA;IACA;IAEA;IACAuE;MACA;MACA;IAAA,CACA;IACA;IACAC;MAAA;MACA;QACAhL;QACA;MACA;MACA;QACA;QACA;QACAgF;UACAiC;UACAgD;UACAC;YACA;UACA;UACA7E;YACA;cACA;cACArF;cACA;cACA;cACA;cACA;cACA;gBACAA;gBACA;cACA;cACA;cACA;cACA;;cAEA;cACA;gBACA;gBACA;gBACAA;cACA;;cAEA;cACA;cACA;cACAsF;cACA;cACA;gBACA;cACA;cACA;cACA;gBACA;gBACAN;gBACAhF;cACA;gBACAA;cACA;cAEAiL;YACA;cACA;cACAjL;cACAkL;YACA;UACA;UACAhE;YACAlH;YACAkL;UACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IAAA,CACA;IAEA;IACAC;MACA;MACA;IAAA,CACA;IAEA;IACAC;MAAA;MACArL;;MAEA;;MAEA;QACA;UACA;UACA;UACA;UACA;;UAEA;UACA;YACAL;cACA2L;YACA;UACA;QACA;UACAtL;UACA;UACA;UACA;UACA4F;YACA;cACA;cACA;YACA;UACA;UACA;UACA;YACAjG;cACA2L;YACA;UACA;QACA;MACA;QACAtL;QACA;QACA;UACAL;YACA2L;UACA;QACA;MACA;;MAGA;IA+BA;IAEA;IACAC;MAAA;MACAvL;;MAEA;;MAEA;QACA;UACA;UACAwL;UACAA;UACAA;YACAxL;YACA;YACA;UACA;QACA;UACAA;UACA;QACA;MACA;QACA;MACA;;MAGA;IAUA;IAEA;IACAyL;MAEA;QACA9L;UACA+L;UACAzG;UACAiC;YACAlH;UACA;QACA;MACA;QACA;QACAL;UACA2L;QACA;QACAtL;MACA;IAEA;IAEA;IACA2L;MACA;MACA;IAAA,CACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA7G;IACA;IAIA;IACA8G;MAAA;MACA;MACA9G;QACAhF;;QAEA;QACA;UACA;UACA;;UAEA;UACAgF;YACAC;YACAuB;UACA;QACA;UACA;UACAZ;YACA;UACA;QACA;MACA;;MAEA;MACAZ;QACAhF;;QAEA;QACA;UACA;;UAEA;UACA;YACA4F;cACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAmG;MAAA;MACA/L;;MAEA;MACA;MACA;;MAEA;MACAgF;QACAiC;QACAgD;QACAC;UACA;QACA;QACA7E;UACA;YACArF;;YAEA;YACA;YACA;;YAEA;YACA;cACA;YACA;cACA;cACA;YACA;;YAEA;YACA;cACA;cACA;cACA;gBACA;gBACAA;gBACAsF;cACA;gBACAtF;gBACA;gBACA;gBACAA;gBACAsF;cACA;YACA;cACA;cACA;cACAtF;cACAsF;YACA;;YAEA;YACAtF;YACAsF;;YAEA;YACA;cACA;cACA;YACA;;YAEA;YACA;;YAEA;YACA;cACAtF;cACA;gBACA;cACA;YACA;UACA;YACAA;YACAgF;cACAC;cACAuB;cACAC;YACA;UACA;QACA;QACAS;UACAlH;UACAgF;YACAC;YACAuB;YACAC;UACA;QACA;MACA;IACA;IAEA;IACAuF;MACA;MACA;QACA;QACA;QACA;QACA;MACA;MACA;MAAA,KACA;QACA;QACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MACAjM;;MAEA;MACAgF;QACAC;QACAuB;QACAC;MACA;IACA;IACA;IACAyF;MAAA;MACAlM;MACA;MACA;MACA;;MAEA;MACA;QACA;QACA;UACAA;UACA;YACAU;UACA;QACA;UACA;UACAV;;UAEA;UACA;YACAA;YACA;cACAwJ;YACA;UACA;;UAEA;UACA;UACAxJ;UAEA;YACAyJ;YACAhD;UACA;QACA;MACA;IACA;IACA;IACA0F;MAAA;MACA;MACA;MACA;MACA;MAEAnM;;MAEA;MACA;QACAA;QACA;QACA;QACA;QACA;MACA;;MAEA;MACA;QACAA;QACA;QACA;QACA;QACA;MACA;;MAEA;MACA;QACAA;QACA;MACA;;MAEA;MACA;QACAA;QACA;MACA;;MAEA;MACA;;MAEA;MACA;QACA;UACAA;UACA;UACA;UAEAgF;YACAC;YACAuB;YACAC;UACA;QACA;MACA;;MAEA;MACA,IACA,mBACA,oBACA,kBACA,wEACA;QACAzG;QACA;QACAgF;UACAC;UACAuB;UACAC;QACA;QACA;MACA;;MAEA;MACA;QACAnB;MACA;;MAEA;MACA;QACAA;MACA;;MAEA;MACA;QACAtF;;QAEA;QACAsF,kDACAC;UACAvF;UACA;UACA+F;UACA;UACA/F;QACA,GACAwF;UACAxF;UACA;UACA+F;UACA;UAEAf;YACAC;YACAuB;YACAC;UACA;;UAEA;UACA;QACA;MACA;MACA;MAAA,KACA;QACAzG;;QAEA;QACAsF,iDACAC;UACAvF;UACA;UACA+F;UACA;UACA/F;QACA,GACAwF;UACAxF;UACA;UACA+F;UACA;UAEAf;YACAC;YACAuB;YACAC;UACA;;UAEA;UACA;QACA;MACA;IACA;IACA;IACA2F;MAAA;MACA;MACA;MACA;MAEApM;;MAEA;MACA;QACAA;QACA;QACA;UACAA;UACA;QACA;QACA;MACA;;MAEA;MACA;QACAA;QACA;QACA;MACA;;MAEA;MACA;QACAA;QACA;MACA;;MAEA;MACA;QACAA;QACA;MACA;;MAEA;MACA;QACAA;QACA,0BACAuF;UACA;UACA;YACAvF;YACA;YACA;YACA;;YAEA;YACA;cACAA;cACA;cACA4F;gBACA;cACA;YACA;cACA;cACA5F;cACA;YACA;UACA;YACAA;YACAgF;cACAC;cACAuB;cACAC;YACA;UACA;QACA,GACAjB;UACAxF;UACA;UACA,2BACAuF;YACA;YACA;YAEA;cACAvF;cACA;cACA;cACA;;cAEA;cACA;gBACAA;gBACA;gBACA4F;kBACA;gBACA;cACA;gBACA;gBACA5F;gBACA;cACA;YACA;cACAA;cACAgF;gBACAC;gBACAuB;gBACAC;cACA;YACA;UACA,GACAjB;YACAxF;YACA;YACAgF;cACAC;cACAuB;cACAC;YACA;UACA;QACA;QACA;MACA;;MAEA;MACA;QACAzG;QACA;QACA;MACA;;MAEA;MACA;QACAA;QACAsF;;QAEA;QACAM;UACA;QACA;MACA;QACA;QACA;MACA;IACA;IACA;IACAyG;MAAA;MACA;MACA;QACArM;QACA;;QAEA;QACA;UACAA;;UAEA;UACA;QACA;;QAEA;QACAsF;UACA2D;UACAC;UACAC;UACAC;UACAC;QACA,GACA9D;UACAvF;;UAEA;UACA+F;;UAEA;UACA;;UAEA;UACA;;UAEA;UACA;;UAEA;UACA;;UAEA;UACAf;YACAC;YACAuB;YACAC;UACA;;UAEA;UACA;QACA,GACAjB;UACAxF;;UAEA;UACA+F;;UAEA;UACA;YACA/F;YACA;UACA;YACA;YACA;UACA;QACA;MACA;QACAA;QACA;UACAgF;YACAC;YACAuB;YACAC;UACA;QACA;UACAzB;YACAC;YACAuB;YACAC;UACA;UACA;UACA;QACA;;QAEA;QACA;MACA;IACA;IAEA;IACA6F;MACAtM;MACA;MACA;MACA;IACA;IAEA;IACAuM;MACA;QACA;UACA7L;UACA6B;UACAG;UACA/B;UACA6L;QACA;QAEA;QACAxH;QACAhF;MACA;IACA;IAEA;IACAyM;MACA;QACA;QACA;UACA;UACA;YACA;YACA;YACA;;YAEA;cACAzM;;cAEA;cACA;gBACA;gBACA;gBACA;gBAEAA;cACA;YACA;cACA;cACAA;cACAgF;YACA;UACA;QACA;UACAhF;QACA;MACA;IACA;IAEA;IACA0M;MAAA;MACA1M;MAEA;QACA;QACAA;QACA,yCACAuF;UACAvF;UACAiL;QACA,GACAzF;UACAxF;;UAEA;UACAA;UACA,8CACAuF,cACAC;QACA;MACA;IACA;IAEA;IACAmH;MAAA;MACA3M;;MAEA;MACA;QACAA;QACA;MACA;;MAEA;MACA;MACA;MAEA;QACA;QACAA;QACA;QACAsF,gCACAC;UACAvF;UACA;UACA;YAAA;UAAA;QACA,GACAwF;UACAxF;UACA;QACA,GACAuF;UACA;UACAvF;UACA;QACA,GACAuF;UACAvF;UACA;UACA;YAAA;UAAA;QACA,GACAuF;UACA;UACAvF;UACA;UACA;YACA;UACA;UACAA;UACA;QACA,GACAuF;UACA;UACAvF;UACA;QACA,GACAuF;UACAvF;UACAiL;QACA,GACAzF;UACAxF;UACAkL;QACA,GACAvB;UACA;UACA;UACA/D;YACA;UACA;QACA;MACA;IACA;IAEA;IACAgH;MACA5M;;MAEA;MACA;MACA;QACAA;QACA;MACA;;MAEA;MACA;QACA;QACA;UACAA;UACAA;UACAA;UACA;QACA;QACAA;MACA;QACAA;QACA;MACA;MAEAA;MAEA;QACAiJ;QACAC;QACAC;QACAC;QACAC;QAAA;QACAhJ;QAAA;QACAkK;QAAA;QACAsC;MACA;IACA;IAEA;IACAC;MACA9M;MAEA;QACA;QACAgF;UACAK;YACArF;YACA;cACAiL;YACA;cACAC;YACA;UACA;UACAhE;YACAlH;YACAkL;UACA;QACA;MACA;IACA;IAEA;IACA6B;MACA/M;;MAEA;MACA;MACA;MACA;MACA;MAEAA;;MAEA;MACA;QACAA;QAEA;UACA;UACAA;UACA;UACA;UACA;QACA;UACA;UACAA;UACA;YACA;UACA;YACA;YACA;YACA;YACA;UACA;QACA;MACA;;MAEA;MACA;QACAA;QACA;MACA;IACA;IAEA;IACAgN;MAAA;MACAhN;;MAEA;MACA;MACA;MAEAA;;MAEA;MACA,2BACAuF;QACA;QACA;MACA,GACAA;QACAvF;;QAEA;QACA;;QAEA;QACA;UACAA;UACAsF;UACAA;UACA;YACAA;UACA;QACA;;QAEA;QACA;UACAtF;;UAEA;UACA;UACA;UACAA;UAEA;YACAA;YACA4F;cACA;YACA;UACA;YACA5F;YACA;YACA;YACA;UACA;QACA;MACA,GACAwF;QACAxF;;QAEA;QACAgF;UACAC;UACAuB;UACAC;QACA;MACA;IACA;EAAA,iGAGA;IACA;MACAzG;;MAEA;MACAgF;QACAK;UACArF;UAEA;YACAA;YACAiL;UACA;YACAjL;YACAkL;UACA;QACA;QACAhE;UACAlH;UACA;UACAiL;QACA;MACA;IACA;EACA,sFAGA;IAAA;IACAjL;;IAEA;IACAgF;MACAC;MACAuB;MACAC;IACA;;IAEA;IACAb;MACAN;QACA2D;QACAC;QACAC;QACAC;QACAC;MACA,GACA9D;QACAvF;;QAEA;QACA;QAEA;QACA;QACA;QAEAgF;UACAC;UACAuB;UACAC;QACA;;QAEA;QACA;MACA,GACAjB;QACAxF;QACA;QACAgF;UACAC;UACAuB;UACAC;QACA;;QAEA;QACA;MACA;IACA;EACA,gGAEA;IAAA;IACA;IACA;;IAEA;IACA;MACA;MACA;;MAEA;MACA;;MAEA;MACA;MACA;QACAzG;QACA;QACAiN;;QAEA;QACA;UACA;QACA;MACA;;MAEA;MACA;QACA;QACAA;MACA;;MAEA;MACA;QACA;MACA;IACA;;IAEAjN;EACA,gGAGA;IACA;MACAiG;MACA;MACAjG;IACA;;IAEA;IACA;MACA+F;MACA;IACA;EACA,8EAGA;IAAA;IACA;IACA;IACA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAGA;;MAMA;MACA;;MAEA;MACA;;MAGA;;MAKA;;MAEA;QACA/F;QACA;QACA;MACA;MAUAA;IACA;MACAA;IACA;EACA,0GAGA;IAAA;IACAA;;IAEA;;IAEA;MACA;QACA;QACAwL;QACAA;QACAA;UACAxL;UACA;UACA;QACA;MACA;QACAA;QACA;MACA;IACA;MACA;IACA;;IAGA;EAUA,sFAGA;IAEA;MACAL;QACA+L;QACAzG;QACAiC;UACAlH;QACA;MACA;IACA;MACA;MACAL;QACA2L;MACA;MACAtL;IACA;EAEA,gGAGA;IAAA;IACAA;IAEA;MACAA;MACA;IACA;;IAEA;IACA;IACA;MACAgF;QACAiC;QACAgD;QACAC;UACA;QACA;QACA7E;UACA;YACA;YACA;YACArF;cAAAuC;YAAA;;YAEA;YACA;cACAA;YACA;;YAEA;YACA;cACAvC;cACA;cACA;cACA;;cAEA;cACA;;cAEA;cACAgF;gBACAC;gBACAuB;gBACAC;cACA;;cAEA;cACA;gBACAzG;gBACA;cACA;gBACA;gBACAA;gBACA;cACA;YACA;;YAEA;YACA;YAEAiL;UACA;YACA;YACAjL;YACAkL;UACA;QACA;QACAhE;UACAlH;UACAkL;QACA;MACA;IACA;EACA,4FAGA;IAAA;IACAlL;IAEA;MACAA;MACA;IACA;;IAEA;IACA;IACA;MACAgF;QACAiC;QACAgD;QACAC;UACA;QACA;QACA7E;UACA;YACA;YACArF;;YAEA;YACA;YACA;;YAEA;YACA;cACAA;cACA;YACA;YAEA;YACA;YACA;;YAEA;YACA;;YAEA;YACA;cACAA;cACA;cACA;;cAEA;cACA;;cAEA;cACAgF;gBACAC;gBACAuB;gBACAC;cACA;;cAEA;cACA;gBACAzG;gBACA;cACA;gBACA;gBACAA;gBACA;cACA;YACA;;YAEA;YACA;YAEAiL;UACA;YACA;YACAjL;YACAkL;UACA;QACA;QACAhE;UACAlH;UACAkL;QACA;MACA;IACA;EACA,8FAGA;IAAA;IACAlL;;IAEA;IACA;;IAEA;IACA;;IAEA;IACA;MACAA;MACA;IACA;;IAEA;IACA;MACAA;MACA;IACA;;IAEA;IACA;IACA;MACA;MACA;MAEAA;;MAEA;MACA;QACAA;QACA;;QAEA;QACA,6BACAuF;UACAvF;UACA;YACAA;UACA;QACA,GACAwF;UACAxF;QACA;QACA;MACA;;MAEA;MACA,6BACAuF;QACA;QACA;UACAvF;UACA;;UAEA;UACA;YACAA;YACA;UACA;YACA;YACAA;YACA;UACA;YACAA;UACA;QACA;UACA;UACA;YACAA;UACA;QACA;MACA,GACAwF;QACAxF;;QAEA;QACA,2BACAuF;UACA;UACA;;UAEA;UACA;YACAvF;YACA;;YAEA;YACA;YACA;YACA;;YAEA;YACA;cACAA;cACA;YACA;cACA;cACAA;cACA;YACA;UACA;YACA;YACAA;UACA;QACA,GACAwF;UACAxF;UACA;UACA;YACAA;YACA;UACA;QACA;MACA;IACA;EACA,4FAGA;IACA;MACAA;MACAiG;MACA;IACA;EACA,kGAMA5F;IACAL;;IAEA;IACA;IACA;MACAgK;IACA;MACA;MACAA;IACA;;IAEA;IACA;MACAA;IACA;;IAEA;IACA;IACAhK;IAEA;MACAgF;QACAiC;QACAgD;QACAC;UACA;QACA;QACAC;QACA9E;UACA;YACArF;YACAiL;UACA;YACAjL;YACAiL;UACA;QACA;QACA/D;UACAlH;UACAkL;QACA;MACA;IACA;EACA;AAEA;AAAA,2B;;;;;;;;;;;;;AC5mIA;AAAA;AAAA;AAAA;AAAikC,CAAgB,2hCAAG,EAAC,C;;;;;;;;;;;ACArlC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/scan/device.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/scan/device.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./device.vue?vue&type=template&id=2c2b0e3f&\"\nvar renderjs\nimport script from \"./device.vue?vue&type=script&lang=js&\"\nexport * from \"./device.vue?vue&type=script&lang=js&\"\nimport style0 from \"./device.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/scan/device.vue\"\nexport default component.exports", "export * from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./device.vue?vue&type=template&id=2c2b0e3f&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showLoginModal = true\n    }\n    _vm.e1 = function ($event) {\n      _vm.showPaymentModal = true\n      _vm.closeOpenDoorModal()\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./device.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./device.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container page-device\">\r\n\t\t<!-- 添加页面背景 -->\r\n\t\t<view class=\"page-background\">\r\n\t\t\t<!-- 背景图片 -->\r\n\t\t\t<image class=\"background-image\" src=\"https://wuji1112-1312674791.cos.ap-guangzhou.myqcloud.com/static/编组 <EMAIL>\" mode=\"aspectFill\" :style=\"{opacity: isPageLoaded ? 1 : 0}\" @load=\"onBackgroundImageLoaded\"></image>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 顶部状态栏占位 -->\r\n\t\t<view class=\"status-bar safe-area-inset-top\"></view>\r\n\t\t\r\n\t\t<!-- 导航栏 -->\r\n\t\t<view class=\"nav-bar\">\r\n\t\t\t<!-- 移除返回按钮 -->\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 顶部banner (放在页面顶部，而不是content内) -->\r\n\t\t<view class=\"banner-container\"\r\n\t\t\t  @tap=\"handleBannerClick\"\r\n\t\t\t  @click=\"handleBannerClick\"\r\n\t\t\t  @touchstart=\"handleBannerTouch\"\r\n\t\t\t  @touchend=\"handleBannerTouchEnd\"\r\n\t\t\t  @touchcancel=\"handleBannerTouchCancel\">\r\n\t\t\t<image class=\"banner-image\" src=\"https://wuji1112-1312674791.cos.ap-guangzhou.myqcloud.com/static/<EMAIL>\" mode=\"contain\"></image>\r\n\t\t\t<view class=\"banner-glow\"></view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 内容区域 -->\r\n\t\t<view class=\"content\">\r\n\t\t\t<!-- 蓝牙连接状态区域 -->\r\n\t\t\t<view class=\"connect-section\">\r\n\t\t\t\t<view class=\"neon-title status-title\">\r\n\t\t\t\t\t<view>支付完成</view>\r\n\t\t\t\t\t<view>门锁自动打开</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 登录提示 -->\r\n\t\t\t\t<view v-if=\"!isLoggedIn\" class=\"login-prompt\">\r\n\t\t\t\t\t<view class=\"login-prompt-text\">\r\n\t\t\t\t\t\t<text class=\"material-icons\">account_circle</text>\r\n\t\t\t\t\t\t<text>请先登录以使用设备</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"login-prompt-button\" @click=\"showLoginModal = true\">\r\n\t\t\t\t\t\t<text>一键登录</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 操作按钮 -->\r\n\t\t\t\t<view class=\"action-buttons\">\r\n\t\t\t\t\t<!-- 单一多功能按钮 -->\r\n\t\t\t\t\t<view class=\"primary-button\" \r\n\t\t\t\t\t\t:class=\"mainActionClass\"\r\n\t\t\t\t\t\t@click=\"handleMainAction\">\r\n\t\t\t\t\t\t<view class=\"ripple-layer\"></view>\r\n\t\t\t\t\t\t<text class=\"material-icons\">{{mainActionIcon}}</text>\r\n\t\t\t\t\t\t<text>{{mainActionText}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<!-- 连接状态展示 -->\r\n\t\t\t\t<view class=\"connection-status\" :class=\"{'connected': isConnected, 'disconnected': !isConnected && !isConnecting}\">\r\n\t\t\t\t\t<text class=\"material-icons\" v-if=\"isConnected\">bluetooth_connected</text>\r\n\t\t\t\t\t<text class=\"material-icons\" v-else-if=\"isConnecting\">bluetooth_searching</text>\r\n\t\t\t\t\t<text class=\"material-icons\" v-else>bluetooth_disabled</text>\r\n\t\t\t\t\t<view class=\"connection-text\" :class=\"{'text-connected': isConnected, 'text-connecting': isConnecting, 'text-disconnected': !isConnected && !isConnecting}\">\r\n\t\t\t\t\t\t{{connectionStatusText}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<!-- 连接提示 -->\r\n\t\t\t\t<view class=\"connection-tips\" >\r\n\t\t\t\t\t<text class=\"text-tertiary\">请确保手机蓝牙已开启，并靠近设备</text>\r\n\t\t\t\t\t<text class=\"text-agreement\">点击开门即视为同意《用户协议》及《隐私协议》</text>\r\n\t\t\t\t</view>\r\n\t\t\t\r\n\t\t\t\t\r\n\t\t\t\t\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\r\n\t\t\t\r\n\t\t</view>\r\n\t\t\r\n\t\t\r\n\t\t\r\n\t\t<!-- 支付弹窗 -->\r\n\t\t<view class=\"modal-overlay\" v-if=\"showPaymentModal\" @click=\"closePaymentModal\"></view>\r\n\t\t<view class=\"payment-modal\" v-if=\"showPaymentModal\">\r\n\t\t\t<view class=\"modal-header\">\r\n\t\t\t\t<text class=\"modal-title\">支付确认</text>\r\n\t\t\t\t<text class=\"material-icons close-icon\" @click=\"closePaymentModal\">close</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"modal-body\">\r\n\t\t\t\t<view class=\"payment-info\">\r\n\t\t\t\t\t<view class=\"payment-device-name\">{{deviceName || '智能门锁'}}</view>\r\n\t\t\t\t\t<view class=\"payment-shop-name\" v-if=\"deviceInfo && deviceInfo.shopName\">{{deviceInfo.shopName}}</view>\r\n\t\t\t\t\t<view class=\"payment-price\">{{priceText}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"payment-button\" @click=\"processPayment\" :class=\"{'loading': paymentLoading}\">\r\n\t\t\t\t\t<text v-if=\"!paymentLoading\">确认支付</text>\r\n\t\t\t\t\t<view class=\"loading-spinner\" v-else></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"payment-tips\">点击确认支付后将跳转到微信支付</view>\r\n\t\t\t\t<view class=\"payment-tips\" v-if=\"!isConnected\">支付成功后，您需要连接设备才能开门</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 开门弹窗 -->\r\n\t\t<view class=\"modal-overlay\" v-if=\"showOpenDoorModal\" @click=\"closeOpenDoorModal\"></view>\r\n\t\t<view class=\"open-door-modal\" v-if=\"showOpenDoorModal\">\r\n\t\t\t<view class=\"modal-header\">\r\n\t\t\t\t<text class=\"modal-title\">开门确认</text>\r\n\t\t\t\t<text class=\"material-icons close-icon\" @click=\"closeOpenDoorModal\">close</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"modal-body\">\r\n\t\t\t\t<view class=\"door-info\">\r\n\t\t\t\t\t<view class=\"door-icon\">\r\n\t\t\t\t\t\t<text class=\"material-icons\">lock_open</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"door-message\">您需要先支付才能开门</view>\r\n\t\t\t\t\t<view class=\"door-device-name\">{{deviceName || '智能门锁'}}</view>\r\n\t\t\t\t\t<!-- 添加门店信息显示 -->\r\n\t\t\t\t\t<view class=\"door-shop-name\" v-if=\"deviceInfo && deviceInfo.shopName\">{{deviceInfo.shopName}}</view>\r\n\t\t\t\t\t<view class=\"door-price\">{{priceText}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"door-buttons\">\r\n\t\t\t\t\t<view class=\"door-cancel-button\" @click=\"closeOpenDoorModal\">\r\n\t\t\t\t\t\t<text>取消</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"door-pay-button\" @click=\"showPaymentModal = true; closeOpenDoorModal();\">\r\n\t\t\t\t\t\t<text>去支付</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 添加音频元素 -->\r\n\t\t<audio id=\"successAudio\" src=\"https://wuji1112-1312674791.cos.ap-guangzhou.myqcloud.com/static/audio/ttsmaker-file-2025-6-11-20-49-17.mp3\" style=\"display: none;\"></audio>\r\n\r\n\t\t<!-- 登录模态框 -->\r\n\t\t<login-modal\r\n\t\t\t:visible=\"showLoginModal\"\r\n\t\t\t@close=\"handleCloseLoginModal\"\r\n\t\t\t@login-success=\"handleLoginSuccess\"\r\n\t\t\t@login-fail=\"handleLoginFail\">\r\n\t\t</login-modal>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport API from '@/static/js/api.js';\r\nimport { lockService } from '@/utils/index.js';\r\nimport { request, get, post } from '@/utils/request.js'; // 导入请求库\r\nimport LoginModal from '@/components/login-modal/index.vue';\r\n\r\n// 获取API基础URL\r\nconst getAPIBaseUrl = () => {\r\n  if (API && API.baseUrl) {\r\n    return API.baseUrl;\r\n  }\r\n  // 默认的API基础URL\r\n  return 'https://api.jycb888.com/';\r\n};\r\n\r\n// 确保API对象可用\r\nconsole.log('API导入检查:', API);\r\n\r\nexport default {\r\n\tcomponents: {\r\n\t\tLoginModal\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\t// 设备信息\r\n\t\t\tdeviceInfo: {},\r\n\t\t\tdeviceId: '',\r\n\t\t\tdeviceMac: '',\r\n\t\t\tdeviceName: '',\r\n\t\t\tdeviceStatus: 0, // 0-未知 1-空闲 2-使用中\r\n\t\t\t\r\n\t\t\t// 扫码类型 bluetooth-蓝牙扫码 miniapp-小程序扫码\r\n\t\t\tscanType: 'miniapp',\r\n\t\t\t\r\n\t\t\t// 订单信息\r\n\t\t\torderId: '',\r\n\t\t\torderStatus: -1, // -1-未知 0-未支付 1-已支付 2-已完成 3-已取消\r\n\t\t\tuseStatus: 0, // 0-未使用 1-使用中 2-已完成\r\n\t\t\torderAmount: 0.00,\r\n\t\t\torderDuration: 0,\r\n\t\t\troomId: '',\r\n\t\t\t\r\n\t\t\t// 锁状态\r\n\t\t\tisLockOpen: false,\r\n\t\t\tbatteryLevel: 0,\r\n\t\t\t\r\n\t\t\t// 价格信息\r\n\t\t\thourlyRate: 0.00,\r\n\t\t\tpriceText: '￥0.00/小时',\r\n\t\t\t\r\n\t\t\t// 蓝牙连接状态\r\n\t\t\tisConnected: false,\r\n\t\t\tisConnecting: false,\r\n\t\t\tlastConnectTime: 0,\r\n\t\t\tconnectionDebounceTimer: null,\r\n\t\t\tautoReconnect: true, // 是否自动重连\r\n\t\t\tstatusUpdateTimer: null, // 状态更新防抖定时器\r\n\t\t\tbluetoothInitialized: false, // 蓝牙是否已初始化\r\n\t\t\tisRecentlyDisconnected: false, // 是否刚刚断开连接\r\n\t\t\tisManualDisconnect: false, // 是否手动断开连接\r\n\t\t\tisReconnecting: false, // 是否正在重连中\r\n\t\t\treconnectLock: false, // 重连锁，防止重复重连\r\n\r\n\t\t\t// 开门防抖控制\r\n\t\t\tlastOpenTime: 0, // 上次开门时间\r\n\t\t\topenCooldown: 3000, // 开门冷却时间（3秒）\r\n\t\t\tcooldownTimer: null, // 冷却倒计时定时器\r\n\t\t\tcurrentTime: 0, // 当前时间，用于触发computed更新\r\n\r\n\t\t\t// 锁状态检查\r\n\t\t\tlockStatusCheckTimer: null, // 锁状态检查定时器\r\n\t\t\tlockStatusCheckInterval: 5000, // 检查间隔（5秒，避免过于频繁）\r\n\t\t\tlastLockStatusCheckTime: 0, // 上次检查时间\r\n\t\t\tlockStatusCheckEnabled: true, // 是否启用锁状态检查\r\n\t\t\t\r\n\t\t\t// 支付状态\r\n\t\t\tisPaid: false,\r\n\t\t\tisPaying: false,\r\n\t\t\tpaymentLoading: false,\r\n\t\t\tpaymentSuccess: false,\r\n\t\t\tshowPaymentModal: false,\r\n\t\t\t\r\n\t\t\t// 开门状态\r\n\t\t\tdoorOpenInProgress: false,\r\n\t\t\tdoorOpenProcessed: false,\r\n\t\t\tdoorOpenCompleted: false, // 新增：标记是否已完成开门操作\r\n\t\t\tshowOpenDoorModal: false,\r\n\t\t\t\r\n\t\t\t// 页面状态\r\n\t\t\tisPageActive: true,\r\n\t\t\tisPageLoaded: false,\r\n\t\t\tautoConnectTimer: null,\r\n\r\n\t\t\t// 登录状态\r\n\t\t\tisLoggedIn: false,\r\n\t\t\tshowLoginModal: false,\r\n\t\t\tuserInfo: null,\r\n\t\t\t\r\n\t\t\t// 定时器\r\n\t\t\tstatusTimer: null,\r\n\t\t\t\r\n\t\t\t// 模拟模式\r\n\t\t\tisSimulatedMode: false,\r\n\t\t\t\r\n\t\t\t// 添加UI状态定时更新机制\r\n\t\t\tuiUpdateTimer: null,\r\n\t\t\t\r\n\t\t\t// 添加音频相关变量\r\n\t\t\tsuccessAudio: null,\r\n\t\t\taudioSrc: null,\r\n\t\t\t\r\n\t\t\t// 添加订单状态轮询相关变量\r\n\t\t\torderPollingTimer: null,\r\n\t\t\torderPollingCount: 0,\r\n\t\t\tmaxPollingCount: 30, // 最大轮询次数，防止无限轮询\r\n\t\t\tpollingInterval: 2000, // 轮询间隔，单位毫秒\r\n\r\n\t\t\t// 导航状态\r\n\t\t\tisNavigating: false, // 防止重复导航\r\n\t\t\tlastNavigateTime: null, // 最后导航时间（防抖）\r\n\r\n\t\t\t// Banner触摸状态（iOS兼容性）\r\n\t\t\tbannerTouchStarted: false,\r\n\t\t\tbannerTouchStartTime: null,\r\n\t\t}\r\n\t},\r\n\tcomputed: {\r\n\t\t// 连接状态文本\r\n\t\tconnectionStatusText() {\r\n\t\t\tif (this.isConnected) {\r\n\t\t\t\treturn '蓝牙已连接';\r\n\t\t\t} else if (this.isConnecting) {\r\n\t\t\t\treturn '蓝牙连接中...';\r\n\t\t\t} else {\r\n\t\t\t\treturn '未连接';\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 主操作按钮文本\r\n\t\tmainActionText() {\r\n\t\t\t// 如果用户未登录，显示登录\r\n\t\t\tif (!this.isLoggedIn) {\r\n\t\t\t\treturn '登录';\r\n\t\t\t}\r\n\r\n\t\t\tif (this.isConnected) {\r\n\t\t\t\tif (this.isPaid) {\r\n\t\t\t\t\t// 检查是否在冷却期间（使用currentTime触发响应式更新）\r\n\t\t\t\t\tconst now = this.currentTime || Date.now();\r\n\t\t\t\t\tconst timeSinceLastOpen = now - this.lastOpenTime;\r\n\r\n\t\t\t\t\tif (timeSinceLastOpen < this.openCooldown && this.lastOpenTime > 0) {\r\n\t\t\t\t\t\tconst remainingTime = Math.ceil((this.openCooldown - timeSinceLastOpen) / 1000);\r\n\t\t\t\t\t\treturn `等待${remainingTime}秒`;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// 已支付状态，根据锁状态和开门完成状态显示不同文本\r\n\t\t\t\t\tif (this.doorOpenCompleted && this.isLockOpen) {\r\n\t\t\t\t\t\treturn '已开门';\r\n\t\t\t\t\t} else if (this.doorOpenCompleted && !this.isLockOpen) {\r\n\t\t\t\t\t\treturn '重新开门';\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\treturn '开门';\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\treturn '支付';\r\n\t\t\t\t}\r\n\t\t\t} else if (this.isConnecting) {\r\n\t\t\t\treturn '连接中';\r\n\t\t\t} else {\r\n\t\t\t\t// 未连接状态，如果已获取设备信息，显示支付\r\n\t\t\t\treturn this.deviceId ? '支付' : '连接设备';\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 主操作按钮图标\r\n\t\tmainActionIcon() {\r\n\t\t\t// 如果用户未登录，显示登录图标\r\n\t\t\tif (!this.isLoggedIn) {\r\n\t\t\t\treturn 'account_circle';\r\n\t\t\t}\r\n\r\n\t\t\tif (this.isConnected) {\r\n\t\t\t\tif (this.isPaid) {\r\n\t\t\t\t\treturn 'lock_open';\r\n\t\t\t\t} else {\r\n\t\t\t\t\treturn 'payments';\r\n\t\t\t\t}\r\n\t\t\t} else if (this.isConnecting) {\r\n\t\t\t\treturn 'bluetooth_searching';\r\n\t\t\t} else {\r\n\t\t\t\t// 未连接状态，如果已获取设备信息，显示支付图标\r\n\t\t\t\treturn this.deviceId ? 'payments' : 'bluetooth_connected';\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 主操作按钮类名\r\n\t\tmainActionClass() {\r\n\t\t\tif (this.isConnected) {\r\n\t\t\t\tif (this.isPaid) {\r\n\t\t\t\t\treturn 'action-open';\r\n\t\t\t\t} else {\r\n\t\t\t\t\treturn 'action-pay';\r\n\t\t\t\t}\r\n\t\t\t} else if (this.isConnecting) {\r\n\t\t\t\treturn 'action-connecting';\r\n\t\t\t} else {\r\n\t\t\t\t// 未连接状态，如果已获取设备信息，使用支付按钮样式\r\n\t\t\t\treturn this.deviceId ? 'action-pay' : 'action-connect';\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 订单状态文本\r\n\t\torderStatusText() {\r\n\t\t\tswitch (this.orderStatus) {\r\n\t\t\t\tcase 0:\r\n\t\t\t\t\treturn '待支付';\r\n\t\t\t\tcase 1:\r\n\t\t\t\t\tif (this.useStatus === 0) {\r\n\t\t\t\t\t\treturn '待使用';\r\n\t\t\t\t\t} else if (this.useStatus === 1) {\r\n\t\t\t\t\t\treturn '使用中';\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\treturn '已完成';\r\n\t\t\t\t\t}\r\n\t\t\t\tcase 2:\r\n\t\t\t\t\treturn '已完成';\r\n\t\t\t\tcase 3:\r\n\t\t\t\t\treturn '已取消';\r\n\t\t\t\tdefault:\r\n\t\t\t\t\treturn '';\r\n\t\t\t}\r\n\t\t}\r\n\t},\r\n\tonLoad(options) {\r\n\t\t// 检查登录状态\r\n\t\tthis.checkLoginStatus();\r\n\r\n\t\t// 特殊处理：如果是微信扫一扫，可能需要从URL中直接解析\r\n\t\tconsole.log('页面加载 - 原始参数 options:', options);\r\n\t\tconsole.log('参数类型检查:', {\r\n\t\t\t'typeof options': typeof options,\r\n\t\t\t'options.query类型': typeof options.query,\r\n\t\t\t'options.id类型': typeof options.id,\r\n\t\t\t'JSON.stringify(options)': JSON.stringify(options)\r\n\t\t});\r\n\r\n\t\t// 兼容微信扫一扫 query 参数\r\n\t\tlet params = options;\r\n\r\n\t\tif (options.query) {\r\n\t\t\tconsole.log('检测到 query 参数:', options.query, '类型:', typeof options.query);\r\n\t\t\ttry {\r\n\t\t\t\tif (typeof options.query === 'string') {\r\n\t\t\t\t\t// 解析 query 字符串，例如: \"id=020022&scanType=miniapp\"\r\n\t\t\t\t\tconst queryParams = {};\r\n\t\t\t\t\tconst queryString = decodeURIComponent(options.query);\r\n\t\t\t\t\tconsole.log('解码后的 query 字符串:', queryString);\r\n\r\n\t\t\t\t\tqueryString.split('&').forEach(param => {\r\n\t\t\t\t\t\tconst [key, value] = param.split('=');\r\n\t\t\t\t\t\tif (key && value) {\r\n\t\t\t\t\t\t\tqueryParams[key] = value;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\t\tconsole.log('解析后的 query 参数:', queryParams);\r\n\t\t\t\t\tparams = { ...options, ...queryParams };\r\n\t\t\t\t} else {\r\n\t\t\t\t\tparams = { ...options, ...options.query };\r\n\t\t\t\t}\r\n\t\t\t} catch (e) {\r\n\t\t\t\tconsole.error('解析 query 参数失败:', e);\r\n\t\t\t\tparams = { ...options };\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tconsole.log('设备页面加载，最终参数:', params);\r\n\r\n\t\t// 修复scanType参数处理逻辑\r\n\t\tthis.scanType = params.scanType || 'miniapp'; // 默认miniapp\r\n\t\tthis.orderId = params.orderId || '';\r\n\t\tthis.roomId = params.roomId || '';\r\n\r\n\t\t// 如果有id参数但没有scanType，说明是小程序扫码\r\n\t\tif (params.id && !params.scanType) {\r\n\t\t\tthis.scanType = 'miniapp';\r\n\t\t}\r\n\r\n\t\t// 强制确保scanType为miniapp（针对微信扫一扫）\r\n\t\tif (params.id && (params.id.length <= 10 && /^\\d+$/.test(params.id))) {\r\n\t\t\t// 如果id是纯数字且长度较短，很可能是设备ID，强制设为miniapp模式\r\n\t\t\tthis.scanType = 'miniapp';\r\n\t\t\tconsole.log('检测到数字设备ID，强制设置为miniapp模式:', params.id);\r\n\t\t}\r\n\r\n\t\tconsole.log('参数处理结果:', {\r\n\t\t\tscanType: this.scanType,\r\n\t\t\torderId: this.orderId,\r\n\t\t\troomId: this.roomId,\r\n\t\t\t'params.scanType': params.scanType,\r\n\t\t\t'强制设置前': params.scanType || 'miniapp',\r\n\t\t\t'最终scanType': this.scanType,\r\n\t\t\tallParams: params\r\n\t\t});\r\n\r\n\t\tconsole.log('确定的扫码类型:', this.scanType);\r\n\r\n\t\t// 早期检查：确保有设备标识\r\n\t\tconst hasDeviceId = params.id || params.deviceId || params.deviceCode || params.mac || options.id || options.deviceId;\r\n\t\tconsole.log('早期设备标识检查:', {\r\n\t\t\t'params.id': params.id,\r\n\t\t\t'params.deviceId': params.deviceId,\r\n\t\t\t'params.deviceCode': params.deviceCode,\r\n\t\t\t'params.mac': params.mac,\r\n\t\t\t'options.id': options.id,\r\n\t\t\t'options.deviceId': options.deviceId,\r\n\t\t\t'hasDeviceId': hasDeviceId\r\n\t\t});\r\n\r\n\t\tif (!hasDeviceId) {\r\n\t\t\tconsole.error('所有可能的设备标识都为空');\r\n\t\t\tuni.showModal({\r\n\t\t\t\ttitle: '参数错误',\r\n\t\t\t\tcontent: '无法获取设备标识，请检查二维码是否正确',\r\n\t\t\t\tshowCancel: false,\r\n\t\t\t\tconfirmText: '返回',\r\n\t\t\t\tsuccess: () => {\r\n\t\t\t\t\tuni.navigateBack();\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\t// 如果有订单ID，设置到lockService并检查支付状态\r\n\t\tif (this.orderId) {\r\n\t\t\tlockService.setOrderId(this.orderId);\r\n\t\t\tconsole.log('设置订单ID到lockService:', this.orderId);\r\n\t\t\tthis.getOrderStatus()\r\n\t\t\t\t.then(orderInfo => {\r\n\t\t\t\t\tconsole.log('页面加载时获取订单状态成功:', orderInfo);\r\n\r\n\t\t\t\t\t// 更新订单状态\r\n\t\t\t\t\tthis.orderStatus = orderInfo.status;\r\n\r\n\t\t\t\t\t// 检查支付状态\r\n\t\t\t\t\tconst isPaid = orderInfo.status === 1 || orderInfo.payStatus === 1;\r\n\t\t\t\t\tif (isPaid) {\r\n\t\t\t\t\t\tlockService.setOrderId(this.orderId);\r\n\t\t\t\t\t\tconsole.log('订单已支付，重新确认订单ID设置:', this.orderId);\r\n\r\n\t\t\t\t\t\t// 更新支付状态\r\n\t\t\t\t\t\tthis.isPaid = true;\r\n\t\t\t\t\t\tthis.paymentSuccess = true;\r\n\r\n\t\t\t\t\t\tconsole.log('页面加载时确认订单已支付，更新支付状态');\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// 订单未支付\r\n\t\t\t\t\t\tthis.isPaid = false;\r\n\t\t\t\t\t\tthis.paymentSuccess = false;\r\n\t\t\t\t\t\tconsole.log('页面加载时确认订单未支付');\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// 更新UI状态\r\n\t\t\t\t\tthis.updateUIState();\r\n\t\t\t\t})\r\n\t\t\t\t.catch(err => {\r\n\t\t\t\t\tconsole.error('页面加载时获取订单状态失败:', err);\r\n\t\t\t\t});\r\n\t\t}\r\n\r\n\t\tconsole.log('开始处理设备标识 - 当前scanType:', this.scanType);\r\n\t\tconsole.log('可用的设备标识参数:', {\r\n\t\t\t'params.id': params.id,\r\n\t\t\t'params.deviceId': params.deviceId,\r\n\t\t\t'params.deviceCode': params.deviceCode,\r\n\t\t\t'params.mac': params.mac,\r\n\t\t\t'options.id': options.id,\r\n\t\t\t'options.deviceId': options.deviceId,\r\n\t\t\t'options.query': options.query\r\n\t\t});\r\n\r\n\t\tif (this.scanType === 'miniapp') {\r\n\t\t\t// 尝试多种方式获取设备ID\r\n\t\t\tlet deviceId = params.id || params.deviceId || params.deviceCode || '';\r\n\r\n\t\t\t// 如果还是没有获取到，尝试从原始options中获取\r\n\t\t\tif (!deviceId && options) {\r\n\t\t\t\tdeviceId = options.id || options.deviceId || options.deviceCode || '';\r\n\t\t\t}\r\n\r\n\t\t\t// 如果还是没有，尝试从URL字符串中直接解析\r\n\t\t\tif (!deviceId && options.query && typeof options.query === 'string') {\r\n\t\t\t\ttry {\r\n\t\t\t\t\t// 直接从query字符串中查找id参数\r\n\t\t\t\t\tconst match = options.query.match(/id=([^&]*)/);\r\n\t\t\t\t\tif (match && match[1]) {\r\n\t\t\t\t\t\tdeviceId = decodeURIComponent(match[1]);\r\n\t\t\t\t\t\tconsole.log('从URL字符串中解析到设备ID:', deviceId);\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (e) {\r\n\t\t\t\t\tconsole.log('从URL字符串解析设备ID失败:', e);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tconsole.log('小程序扫码 - 从参数获取设备ID:', deviceId);\r\n\t\t\tconsole.log('参数详情:', {\r\n\t\t\t\t'params.id': params.id,\r\n\t\t\t\t'params.deviceId': params.deviceId,\r\n\t\t\t\t'params.deviceCode': params.deviceCode,\r\n\t\t\t\t'options.id': options.id,\r\n\t\t\t\t'最终deviceId': deviceId,\r\n\t\t\t\t'deviceId类型': typeof deviceId,\r\n\t\t\t\t'deviceId长度': deviceId ? deviceId.length : 0\r\n\t\t\t});\r\n\r\n\t\t\tif (deviceId && deviceId.trim()) {\r\n\t\t\t\t// 设置设备ID\r\n\t\t\t\tthis.deviceId = deviceId.trim();\r\n\t\t\t\tconsole.log('设置设备ID成功:', this.deviceId);\r\n\t\t\t\tthis.getDeviceInfo(this.deviceId);\r\n\t\t\t} else {\r\n\t\t\t\tconsole.error('设备ID为空或无效:', {\r\n\t\t\t\t\tdeviceId,\r\n\t\t\t\t\tparams,\r\n\t\t\t\t\toptions,\r\n\t\t\t\t\tscanType: this.scanType,\r\n\t\t\t\t\toriginalQuery: options.query\r\n\t\t\t\t});\r\n\r\n\t\t\t\t// 最后尝试：如果是从URL https://jycb888.com/pages/scan/device?id=020022&scanType=miniapp 进入\r\n\t\t\t\t// 可能设备ID就是020022这样的格式\r\n\t\t\t\tif (!deviceId && options.query) {\r\n\t\t\t\t\t// 尝试直接使用query中的数字作为设备ID\r\n\t\t\t\t\tconst numberMatch = options.query.match(/\\d+/);\r\n\t\t\t\t\tif (numberMatch && numberMatch[0]) {\r\n\t\t\t\t\t\tdeviceId = numberMatch[0];\r\n\t\t\t\t\t\tconsole.log('从query中提取到数字作为设备ID:', deviceId);\r\n\t\t\t\t\t\tthis.deviceId = deviceId;\r\n\t\t\t\t\t\tthis.getDeviceInfo(deviceId);\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 最终备用方案：如果是从特定URL进入，尝试使用默认设备ID\r\n\t\t\t\tif (options.query && options.query.includes('020022')) {\r\n\t\t\t\t\tconsole.log('检测到URL中包含020022，使用作为设备ID');\r\n\t\t\t\t\tthis.deviceId = '020022';\r\n\t\t\t\t\tthis.getDeviceInfo('020022');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 给用户一个手动输入的机会\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '设备ID获取失败',\r\n\t\t\t\t\tcontent: `无法从扫码中获取设备ID，请检查二维码是否正确。\\n\\n调试信息：\\nquery: ${options.query || '无'}\\nparams: ${JSON.stringify(params)}`,\r\n\t\t\t\t\tshowCancel: true,\r\n\t\t\t\t\tcancelText: '返回',\r\n\t\t\t\t\tconfirmText: '重试',\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t// 重新尝试获取参数\r\n\t\t\t\t\t\t\tthis.onLoad(options);\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tuni.navigateBack();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tconst mac = params.mac || params.id || ''; // 兼容处理，有时id参数可能是MAC地址\r\n\t\t\tconsole.log('蓝牙扫码模式 - 从参数获取MAC地址:', mac);\r\n\t\t\tconsole.log('当前scanType:', this.scanType, '原始params.scanType:', params.scanType);\r\n\r\n\t\t\tif (mac) {\r\n\t\t\t\t// 检查是否是有效的MAC地址格式\r\n\t\t\t\tif (this.isValidMacAddress(mac)) {\r\n\t\t\t\t\tconsole.log('检测到有效的MAC地址格式:', mac);\r\n\t\t\t\t\tlockService.setDeviceMac(mac);\r\n\t\t\t\t\tthis.deviceMac = mac.toLowerCase();\r\n\t\t\t\t\tthis.preinitBluetooth();\r\n\t\t\t\t\tthis.getDeviceInfo(mac);\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 如果不是有效的MAC地址，可能是设备ID，转换为小程序模式\r\n\t\t\t\t\tconsole.log('检测到非MAC地址格式，转换为小程序模式:', mac);\r\n\t\t\t\t\tthis.scanType = 'miniapp';\r\n\t\t\t\t\tthis.deviceId = mac;\r\n\t\t\t\t\tthis.getDeviceInfo(mac);\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\tconsole.error('设备标识为空 - 调试信息:', {\r\n\t\t\t\t\t'params.mac': params.mac,\r\n\t\t\t\t\t'params.id': params.id,\r\n\t\t\t\t\t'params': params,\r\n\t\t\t\t\t'options': options,\r\n\t\t\t\t\t'scanType': this.scanType\r\n\t\t\t\t});\r\n\r\n\t\t\t\t// 最后尝试：检查是否有任何可用的设备标识\r\n\t\t\t\tconst anyId = params.deviceId || params.deviceCode || options.id || options.deviceId;\r\n\t\t\t\tif (anyId) {\r\n\t\t\t\t\tconsole.log('找到备用设备标识:', anyId);\r\n\t\t\t\t\tthis.scanType = 'miniapp';\r\n\t\t\t\t\tthis.deviceId = anyId;\r\n\t\t\t\t\tthis.getDeviceInfo(anyId);\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 最终备用方案：如果是从特定URL进入，尝试使用默认设备ID\r\n\t\t\t\t\tif (options.query && options.query.includes('020022')) {\r\n\t\t\t\t\t\tconsole.log('在else分支检测到URL中包含020022，使用作为设备ID');\r\n\t\t\t\t\t\tthis.scanType = 'miniapp';\r\n\t\t\t\t\t\tthis.deviceId = '020022';\r\n\t\t\t\t\t\tthis.getDeviceInfo('020022');\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tthis.showError('设备标识不能为空');\r\n\t\t\t\t\tsetTimeout(() => { uni.navigateBack(); }, 2000);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t},\r\n\tonShow() {\r\n\t\tconsole.log('设备页面显示');\r\n\t\tthis.isPageActive = true;\r\n\r\n\t\t// 尝试从本地存储恢复支付状态\r\n\t\tthis.restorePaymentState();\r\n\r\n\t\t// 重新初始化蓝牙环境（重要：解决退出后重新进入连接不上的问题）\r\n\t\tthis.reinitializeBluetooth();\r\n\r\n\t\t// 延迟检查连接状态，确保蓝牙初始化完成\r\n\t\tsetTimeout(() => {\r\n\t\t\tthis.verifyConnectionState();\r\n\t\t}, 2000);\r\n\r\n\t\t// 启动UI状态定时更新\r\n\t\tthis.startUIUpdateTimer();\r\n\r\n\t\t// 设置蓝牙状态监听\r\n\t\tthis.setupBluetoothListeners();\r\n\t\t\r\n\t\t// 如果有订单ID，总是检查最新的订单状态（重要：确保重新进入页面时能识别已支付状态）\r\n\t\tif (this.orderId) {\r\n\t\t\tconsole.log('页面显示时检查订单状态，当前订单ID:', this.orderId, '当前支付状态:', this.isPaid);\r\n\t\t\tthis.getOrderStatus()\r\n\t\t\t\t.then(orderInfo => {\r\n\t\t\t\t\tconsole.log('页面显示时获取订单状态成功:', orderInfo);\r\n\r\n\t\t\t\t\t// 更新订单状态\r\n\t\t\t\t\tthis.orderStatus = orderInfo.status;\r\n\r\n\t\t\t\t\t// 如果订单已支付，确保订单ID设置到lockService\r\n\t\t\t\t\tif (orderInfo.status === 1 || orderInfo.payStatus === 1) {\r\n\t\t\t\t\t\tconsole.log('订单已支付，确保订单ID设置到lockService:', this.orderId);\r\n\t\t\t\t\t\tlockService.setOrderId(this.orderId);\r\n\t\t\t\t\t\tthis.isPaid = true;\r\n\t\t\t\t\t\tthis.paymentSuccess = true;\r\n\r\n\t\t\t\t\t\t// 保存支付状态到本地存储\r\n\t\t\t\t\t\tthis.savePaymentState();\r\n\r\n\t\t\t\t\t\t// 停止可能存在的轮询\r\n\t\t\t\t\t\tif (this.orderPollingTimer) {\r\n\t\t\t\t\t\t\tconsole.log('订单已支付，停止轮询');\r\n\t\t\t\t\t\t\tthis.stopOrderPolling();\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t// 如果已支付但未连接设备，尝试连接\r\n\t\t\t\t\t\tif (!this.isConnected && !this.isConnecting) {\r\n\t\t\t\t\t\t\tconsole.log('已支付但未连接设备，尝试连接');\r\n\t\t\t\t\t\t\tthis.tryConnectDevice();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t// 如果已支付且已连接设备，检查是否需要开门\r\n\t\t\t\t\t\telse if (this.isConnected && !this.doorOpenProcessed && !this.doorOpenCompleted) {\r\n\t\t\t\t\t\t\tconsole.log('已支付且已连接设备，检查是否需要开门');\r\n\t\t\t\t\t\t\tthis.checkOrderAndOpenDoor();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// 如果订单未支付，并且没有在轮询，启动轮询\r\n\t\t\t\t\telse if ((orderInfo.status === 0 || orderInfo.status === null) && !this.orderPollingTimer) {\r\n\t\t\t\t\t\tconsole.log('订单未支付，启动订单状态轮询');\r\n\t\t\t\t\t\tthis.isPaid = false;\r\n\t\t\t\t\t\tthis.paymentSuccess = false;\r\n\t\t\t\t\t\tthis.startOrderPolling();\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// 更新UI状态\r\n\t\t\t\t\tthis.updateUIState();\r\n\t\t\t\t})\r\n\t\t\t\t.catch(err => {\r\n\t\t\t\t\tconsole.error('页面显示时获取订单状态失败:', err);\r\n\t\t\t\t});\r\n\t\t\treturn; // 防止执行后续连接逻辑\r\n\t\t}\r\n\t\t\r\n\t\t// 如果有订单ID，未支付，并且没有在轮询，启动轮询\r\n\t\tif (this.orderId && this.orderStatus === 0 && !this.isPaid && !this.orderPollingTimer) {\r\n\t\t\tconsole.log('页面显示时检测到未支付订单，启动订单状态轮询');\r\n\t\t\tthis.startOrderPolling();\r\n\t\t}\r\n\t\t\r\n\t\t// 如果已支付但未连接设备，尝试连接\r\n\t\tif (this.isPaid && !this.isConnected && !this.isConnecting) {\r\n\t\t\tconsole.log('已支付但未连接设备，尝试连接');\r\n\t\t\tthis.tryConnectDevice();\r\n\t\t\treturn;\r\n\t\t}\r\n\t\t\r\n\t\t// 如果有MAC地址，尝试连接设备\r\n\t\tif (this.deviceMac && !this.isConnected && !this.isConnecting) {\r\n\t\t\t// 延迟连接，避免页面刚显示就连接导致的问题\r\n\t\t\tconsole.log('页面显示，准备尝试连接设备');\r\n\t\t\tthis.autoConnectTimer = setTimeout(() => {\r\n\t\t\t\tthis.debounceConnect();\r\n\t\t\t}, 500);\r\n\t\t}\r\n\t},\r\n\tonHide() {\r\n\t\tconsole.log('设备页面隐藏');\r\n\t\tthis.isPageActive = false;\r\n\t\t\r\n\t\t// 停止UI状态定时更新\r\n\t\tthis.clearUIUpdateTimer();\r\n\t\t\r\n\t\t// 如果正在支付或已支付但未连接设备，不清除状态\r\n\t\tif (this.isPaying || this.paymentLoading || (this.isPaid && !this.isConnected)) {\r\n\t\t\tconsole.log('正在支付或已支付但未连接设备，保留状态');\r\n\t\t\t// 不清除自动连接定时器\r\n\t\t\treturn;\r\n\t\t}\r\n\t\t\r\n\t\t// 如果正在轮询订单状态，不清除轮询定时器\r\n\t\tif (this.orderPollingTimer) {\r\n\t\t\tconsole.log('正在轮询订单状态，保留轮询定时器');\r\n\t\t\treturn;\r\n\t\t}\r\n\t\t\r\n\t\t// 清除自动连接定时器\r\n\t\tif (this.autoConnectTimer) {\r\n\t\t\tclearTimeout(this.autoConnectTimer);\r\n\t\t\tthis.autoConnectTimer = null;\r\n\t\t}\r\n\r\n\t\t// 注意：不在onHide时断开蓝牙连接，因为用户可能只是切换到其他页面\r\n\t\t// 蓝牙连接保持，以便用户返回时能快速重连\r\n\t\tconsole.log('页面隐藏，保持蓝牙连接状态');\r\n\t},\r\n\tonUnload() {\r\n\t\tconsole.log('设备页面卸载');\r\n\t\tthis.isPageActive = false;\r\n\r\n\t\t// 停止UI状态定时更新\r\n\t\tthis.clearUIUpdateTimer();\r\n\r\n\t\t// 清除冷却定时器\r\n\t\tif (this.cooldownTimer) {\r\n\t\t\tclearInterval(this.cooldownTimer);\r\n\t\t\tthis.cooldownTimer = null;\r\n\t\t}\r\n\r\n\t\t// 停止锁状态检查\r\n\t\tthis.stopLockStatusCheck();\r\n\r\n\t\t// 重置冷却相关状态\r\n\t\tthis.currentTime = 0;\r\n\t\tthis.lastOpenTime = 0;\r\n\r\n\t\t// 停止订单状态轮询\r\n\t\tthis.stopOrderPolling();\r\n\r\n\t\t// 断开蓝牙连接\r\n\t\tthis.ensureDisconnectBluetooth();\r\n\t},\r\n\tmethods: {\r\n\t\t// 检查登录状态\r\n\t\tcheckLoginStatus() {\r\n\t\t\tconst token = uni.getStorageSync('token');\r\n\t\t\tconst userInfo = uni.getStorageSync('userInfo');\r\n\r\n\t\t\tconsole.log('检查登录状态 - token:', token ? '存在' : '不存在', 'userInfo:', userInfo);\r\n\r\n\t\t\tif (token && userInfo) {\r\n\t\t\t\tthis.isLoggedIn = true;\r\n\t\t\t\tthis.userInfo = userInfo;\r\n\t\t\t\tconsole.log('用户已登录');\r\n\t\t\t} else {\r\n\t\t\t\tthis.isLoggedIn = false;\r\n\t\t\t\tthis.userInfo = null;\r\n\t\t\t\tconsole.log('用户未登录');\r\n\r\n\t\t\t\t// 如果用户未登录，显示登录提示\r\n\t\t\t\tthis.showLoginPrompt();\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 显示登录提示\r\n\t\tshowLoginPrompt() {\r\n\t\t\t// 延迟显示，确保页面加载完成\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tif (!this.isLoggedIn) {\r\n\t\t\t\t\tthis.showLoginModal = true;\r\n\t\t\t\t}\r\n\t\t\t}, 1000);\r\n\t\t},\r\n\r\n\t\t// 关闭登录模态框\r\n\t\thandleCloseLoginModal() {\r\n\t\t\tthis.showLoginModal = false;\r\n\t\t},\r\n\r\n\t\t// 登录成功处理\r\n\t\thandleLoginSuccess(loginData) {\r\n\t\t\tconsole.log('登录成功:', loginData);\r\n\t\t\tthis.isLoggedIn = true;\r\n\t\t\tthis.userInfo = loginData.userInfo || { userId: loginData.userId };\r\n\t\t\tthis.showLoginModal = false;\r\n\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '登录成功',\r\n\t\t\t\ticon: 'success',\r\n\t\t\t\tduration: 2000\r\n\t\t\t});\r\n\r\n\t\t\t// 登录成功后，重新检查订单状态\r\n\t\t\tif (this.orderId) {\r\n\t\t\t\tthis.getOrderStatus();\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 登录失败处理\r\n\t\thandleLoginFail(error) {\r\n\t\t\tconsole.error('登录失败:', error);\r\n\t\t\tthis.showLoginModal = false;\r\n\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '登录失败，请重试',\r\n\t\t\t\ticon: 'none',\r\n\t\t\t\tduration: 2000\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t// Banner点击处理（简化版本）\r\n\t\thandleBannerClick(e) {\r\n\t\t\tconsole.log('Banner点击事件触发', e.type);\r\n\r\n\t\t\t// 防止事件冒泡\r\n\t\t\tif (e && e.stopPropagation) {\r\n\t\t\t\te.stopPropagation();\r\n\t\t\t}\r\n\r\n\t\t\t// 立即执行导航，不依赖复杂的触摸逻辑\r\n\t\t\tthis.navigateToZhaoshang(e);\r\n\t\t},\r\n\r\n\t\t// Banner触摸开始事件（iOS兼容性）\r\n\t\thandleBannerTouch(e) {\r\n\t\t\tconsole.log('Banner触摸开始');\r\n\t\t\tthis.bannerTouchStartTime = Date.now();\r\n\t\t\tthis.bannerTouchStarted = true;\r\n\r\n\t\t\t// 防止事件冒泡\r\n\t\t\tif (e && e.stopPropagation) {\r\n\t\t\t\te.stopPropagation();\r\n\t\t\t}\r\n\t\t\tif (e && e.preventDefault) {\r\n\t\t\t\te.preventDefault();\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// Banner触摸结束事件（iOS兼容性）\r\n\t\thandleBannerTouchEnd(e) {\r\n\t\t\tconsole.log('Banner触摸结束');\r\n\r\n\t\t\t// 防止事件冒泡\r\n\t\t\tif (e && e.stopPropagation) {\r\n\t\t\t\te.stopPropagation();\r\n\t\t\t}\r\n\t\t\tif (e && e.preventDefault) {\r\n\t\t\t\te.preventDefault();\r\n\t\t\t}\r\n\r\n\t\t\t// 检查是否是有效的点击（触摸时间不超过500ms）\r\n\t\t\tif (this.bannerTouchStarted && this.bannerTouchStartTime) {\r\n\t\t\t\tconst touchDuration = Date.now() - this.bannerTouchStartTime;\r\n\t\t\t\tif (touchDuration < 500) {\r\n\t\t\t\t\tconsole.log('检测到有效点击，触摸时长:', touchDuration + 'ms');\r\n\t\t\t\t\t// 延迟一点执行，确保事件处理完成\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tthis.navigateToZhaoshang(e);\r\n\t\t\t\t\t}, 50);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t// 重置状态\r\n\t\t\tthis.bannerTouchStarted = false;\r\n\t\t\tthis.bannerTouchStartTime = null;\r\n\t\t},\r\n\r\n\t\t// Banner触摸取消事件\r\n\t\thandleBannerTouchCancel(e) {\r\n\t\t\tconsole.log('Banner触摸取消');\r\n\t\t\tthis.bannerTouchStarted = false;\r\n\t\t\tthis.bannerTouchStartTime = null;\r\n\t\t},\r\n\r\n\t\t// 跳转到招商页面\r\n\t\tnavigateToZhaoshang(e) {\r\n\t\t\tconsole.log('跳转到招商页面', e);\r\n\r\n\t\t\t// 防止事件冒泡\r\n\t\t\tif (e && e.stopPropagation) {\r\n\t\t\t\te.stopPropagation();\r\n\t\t\t}\r\n\t\t\tif (e && e.preventDefault) {\r\n\t\t\t\te.preventDefault();\r\n\t\t\t}\r\n\r\n\t\t\t// 防止重复导航（更强的防抖机制）\r\n\t\t\tif (this.isNavigating) {\r\n\t\t\t\tconsole.log('正在导航中，忽略重复点击');\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t// 检查最近是否有导航操作（防抖）\r\n\t\t\tconst now = Date.now();\r\n\t\t\tif (this.lastNavigateTime && (now - this.lastNavigateTime) < 1000) {\r\n\t\t\t\tconsole.log('导航操作过于频繁，忽略');\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\tthis.isNavigating = true;\r\n\t\t\tthis.lastNavigateTime = now;\r\n\r\n\t\t\tconsole.log('开始执行导航到招商页面');\r\n\r\n\t\t\t// 立即执行导航，减少延迟\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: '/packageA/pages/zhaoshang/index',\r\n\t\t\t\tsuccess: () => {\r\n\t\t\t\t\tconsole.log('导航到招商页面成功');\r\n\t\t\t\t\t// 给用户反馈\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '正在跳转...',\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\tduration: 1000\r\n\t\t\t\t\t});\r\n\t\t\t\t},\r\n\t\t\t\tfail: (err) => {\r\n\t\t\t\t\tconsole.error('导航到招商页面失败:', err);\r\n\t\t\t\t\tthis.isNavigating = false;\r\n\r\n\t\t\t\t\t// 如果导航失败，尝试使用switchTab\r\n\t\t\t\t\tif (err.errMsg && err.errMsg.includes('routeDone')) {\r\n\t\t\t\t\t\tconsole.log('尝试使用switchTab导航');\r\n\t\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\t\turl: '/pages/index/index',\r\n\t\t\t\t\t\t\tcomplete: () => {\r\n\t\t\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\t\turl: '/packageA/pages/zhaoshang/index'\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t}, 100);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '页面跳转失败',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\tcomplete: () => {\r\n\t\t\t\t\t// 减少重置延迟\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tthis.isNavigating = false;\r\n\t\t\t\t\t}, 500);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t// 启动冷却倒计时定时器\r\n\t\tstartCooldownTimer() {\r\n\t\t\t// 清除之前的定时器\r\n\t\t\tif (this.cooldownTimer) {\r\n\t\t\t\tclearInterval(this.cooldownTimer);\r\n\t\t\t}\r\n\r\n\t\t\t// 启动新的定时器，每秒更新一次\r\n\t\t\tthis.cooldownTimer = setInterval(() => {\r\n\t\t\t\t// 更新当前时间，触发computed属性重新计算\r\n\t\t\t\tthis.currentTime = Date.now();\r\n\t\t\t\tconst timeSinceLastOpen = this.currentTime - this.lastOpenTime;\r\n\r\n\t\t\t\t// 如果冷却时间已过，清除定时器\r\n\t\t\t\tif (timeSinceLastOpen >= this.openCooldown) {\r\n\t\t\t\t\tclearInterval(this.cooldownTimer);\r\n\t\t\t\t\tthis.cooldownTimer = null;\r\n\t\t\t\t\tthis.currentTime = 0; // 重置currentTime\r\n\t\t\t\t}\r\n\t\t\t}, 1000);\r\n\t\t},\r\n\r\n\t\t// 启动锁状态检查定时器\r\n\t\tstartLockStatusCheck() {\r\n\t\t\tconsole.log('启动锁状态检查定时器，检查间隔:', this.lockStatusCheckInterval + 'ms');\r\n\r\n\t\t\t// 清除之前的定时器\r\n\t\t\tif (this.lockStatusCheckTimer) {\r\n\t\t\t\tclearInterval(this.lockStatusCheckTimer);\r\n\t\t\t}\r\n\r\n\t\t\t// 启动定时器，每5秒检查一次锁状态\r\n\t\t\tthis.lockStatusCheckTimer = setInterval(() => {\r\n\t\t\t\t// 只有在已连接、已支付且启用检查的情况下才检查锁状态\r\n\t\t\t\tif (this.isConnected && this.isPaid && this.lockStatusCheckEnabled) {\r\n\t\t\t\t\t// 如果锁当前是开着的，才进行状态检查（检测关闭）\r\n\t\t\t\t\tif (this.isLockOpen) {\r\n\t\t\t\t\t\tthis.checkLockStatus();\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}, this.lockStatusCheckInterval);\r\n\t\t},\r\n\r\n\t\t// 停止锁状态检查定时器\r\n\t\tstopLockStatusCheck() {\r\n\t\t\tconsole.log('停止锁状态检查定时器');\r\n\t\t\tif (this.lockStatusCheckTimer) {\r\n\t\t\t\tclearInterval(this.lockStatusCheckTimer);\r\n\t\t\t\tthis.lockStatusCheckTimer = null;\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 检查锁状态\r\n\t\tcheckLockStatus() {\r\n\t\t\ttry {\r\n\t\t\t\tconsole.log('执行锁状态检查...');\r\n\r\n\t\t\t\t// 查询锁状态\r\n\t\t\t\tlockService.queryLockStatus()\r\n\t\t\t\t\t.then(res => {\r\n\t\t\t\t\t\tconsole.log('锁状态检查命令发送成功:', res);\r\n\t\t\t\t\t\t// 注意：实际的锁状态会通过 onLockStatusChange 回调返回\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch(err => {\r\n\t\t\t\t\t\tconsole.log('锁状态检查失败:', err);\r\n\t\t\t\t\t\t// 检查失败不影响主要功能，只记录日志\r\n\t\t\t\t\t\t// 不进行重试，避免过度查询\r\n\t\t\t\t\t});\r\n\t\t\t} catch (e) {\r\n\t\t\t\tconsole.log('锁状态检查异常:', e);\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 验证MAC地址格式\r\n\t\tisValidMacAddress(mac) {\r\n\t\t\tif (!mac) return false;\r\n\t\t\t// MAC地址格式：XX:XX:XX:XX:XX:XX 或 XXXXXXXXXXXX\r\n\t\t\tconst macRegex = /^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$|^[0-9A-Fa-f]{12}$/;\r\n\t\t\treturn macRegex.test(mac);\r\n\t\t},\r\n\r\n\t\t// 处理连接成功后的状态\r\n\t\thandleConnectedState() {\r\n\t\t\tconsole.log('处理连接成功状态');\r\n\r\n\t\t\t// 验证lockService的连接状态\r\n\t\t\tconst deviceInfo = lockService.getDeviceInfo();\r\n\t\t\tconst lockServiceConnected = deviceInfo && (deviceInfo.connected === true || deviceInfo.isConnected === true);\r\n\r\n\t\t\tconsole.log('验证连接状态 - UI状态:', this.isConnected, 'lockService状态:', lockServiceConnected);\r\n\r\n\t\t\tif (!lockServiceConnected) {\r\n\t\t\t\tconsole.warn('lockService连接状态不一致，尝试强制同步状态');\r\n\t\t\t\t// 强制同步连接状态\r\n\t\t\t\tthis.forceSyncConnectionState();\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t// 如果已支付且已连接，自动开门\r\n\t\t\tif (this.isPaid && !this.doorOpenProcessed && !this.doorOpenCompleted) {\r\n\t\t\t\tconsole.log('连接成功且已支付，自动触发开门');\r\n\t\t\t\tthis.checkOrderAndOpenDoor();\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 强制同步连接状态\r\n\t\tforceSyncConnectionState() {\r\n\t\t\tconsole.log('强制同步连接状态');\r\n\r\n\t\t\t// 尝试重新设置设备信息到lockService\r\n\t\t\tif (this.deviceMac) {\r\n\t\t\t\tlockService.setDeviceMac(this.deviceMac);\r\n\t\t\t}\r\n\t\t\tif (this.deviceName) {\r\n\t\t\t\tlockService.setExpectedDeviceName(this.deviceName);\r\n\t\t\t}\r\n\t\t\tif (this.orderId) {\r\n\t\t\t\tlockService.setOrderId(this.orderId);\r\n\t\t\t}\r\n\r\n\t\t\t// 延迟重新检查状态\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tconst deviceInfo = lockService.getDeviceInfo();\r\n\t\t\t\tconst lockServiceConnected = deviceInfo && (deviceInfo.connected === true || deviceInfo.isConnected === true);\r\n\r\n\t\t\t\tconsole.log('状态同步后检查 - lockService状态:', lockServiceConnected);\r\n\r\n\t\t\t\tif (lockServiceConnected && this.isPaid && !this.doorOpenProcessed && !this.doorOpenCompleted) {\r\n\t\t\t\t\tconsole.log('状态同步成功，触发自动开门');\r\n\t\t\t\t\tthis.checkOrderAndOpenDoor();\r\n\t\t\t\t}\r\n\t\t\t}, 1000);\r\n\t\t},\r\n\r\n\t\t// 强制同步设备信息\r\n\t\tforceSyncDeviceInfo(deviceInfo) {\r\n\t\t\tconsole.log('强制同步设备信息到lockService:', deviceInfo);\r\n\r\n\t\t\ttry {\r\n\t\t\t\t// 确保所有必要的设备信息都设置到lockService\r\n\t\t\t\tif (deviceInfo) {\r\n\t\t\t\t\tif (deviceInfo.mac || this.deviceMac) {\r\n\t\t\t\t\t\tlockService.setDeviceMac(deviceInfo.mac || this.deviceMac);\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (deviceInfo.name || this.deviceName) {\r\n\t\t\t\t\t\tlockService.setExpectedDeviceName(deviceInfo.name || this.deviceName);\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (deviceInfo.deviceId) {\r\n\t\t\t\t\t\t// 如果lockService有设置设备ID的方法，调用它\r\n\t\t\t\t\t\tif (typeof lockService.setDeviceId === 'function') {\r\n\t\t\t\t\t\t\tlockService.setDeviceId(deviceInfo.deviceId);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (this.orderId) {\r\n\t\t\t\t\tlockService.setOrderId(this.orderId);\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 强制设置所有层级的连接状态\r\n\t\t\t\tthis.forceSetAllLayersConnected(deviceInfo);\r\n\r\n\t\t\t\tconsole.log('设备信息同步完成');\r\n\t\t\t} catch (e) {\r\n\t\t\t\tconsole.error('强制同步设备信息失败:', e);\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 强制设置所有层级的连接状态\r\n\t\tforceSetAllLayersConnected(deviceInfo) {\r\n\t\t\tconsole.log('强制设置所有层级连接状态');\r\n\r\n\t\t\ttry {\r\n\t\t\t\t// 1. 设置lockService的连接状态\r\n\t\t\t\tif (lockService.deviceInfo) {\r\n\t\t\t\t\tlockService.deviceInfo.connected = true;\r\n\t\t\t\t\tlockService.deviceInfo.isConnected = true;\r\n\t\t\t\t\tlockService.deviceInfo.isAuthenticated = true;\r\n\r\n\t\t\t\t\tif (deviceInfo) {\r\n\t\t\t\t\t\t// 同步设备信息\r\n\t\t\t\t\t\tif (deviceInfo.deviceId) {\r\n\t\t\t\t\t\t\tlockService.deviceInfo.deviceId = deviceInfo.deviceId;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (deviceInfo.mac || this.deviceMac) {\r\n\t\t\t\t\t\t\tlockService.deviceInfo.mac = deviceInfo.mac || this.deviceMac;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (deviceInfo.name || this.deviceName) {\r\n\t\t\t\t\t\t\tlockService.deviceInfo.name = deviceInfo.name || this.deviceName;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (deviceInfo.batteryLevel) {\r\n\t\t\t\t\t\t\tlockService.deviceInfo.batteryLevel = deviceInfo.batteryLevel;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tconsole.log('lockService内部连接状态已强制设置为true');\r\n\t\t\t\t\tconsole.log('lockService.deviceInfo:', lockService.deviceInfo);\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 2. 尝试获取并设置blueToothManager的连接状态\r\n\t\t\t\ttry {\r\n\t\t\t\t\t// 直接导入blueToothManager\r\n\t\t\t\t\tconst blueToothManager = require('@/utils/blueToothManager.js');\r\n\r\n\t\t\t\t\tif (blueToothManager) {\r\n\t\t\t\t\t\tconsole.log('找到blueToothManager，强制设置连接状态');\r\n\r\n\t\t\t\t\t\t// 强制设置连接状态\r\n\t\t\t\t\t\tblueToothManager.isConnected = true;\r\n\t\t\t\t\t\tblueToothManager.deviceId = this.deviceMac;\r\n\t\t\t\t\t\tconsole.log('blueToothManager.isConnected已设置为true');\r\n\t\t\t\t\t\tconsole.log('blueToothManager.deviceId已设置为:', this.deviceMac);\r\n\r\n\t\t\t\t\t\t// 如果有设备MAC，设置当前连接的设备\r\n\t\t\t\t\t\tif (this.deviceMac) {\r\n\t\t\t\t\t\t\tblueToothManager.currentDeviceId = this.deviceMac;\r\n\t\t\t\t\t\t\tblueToothManager.connectedDeviceId = this.deviceMac;\r\n\t\t\t\t\t\t\tconsole.log('blueToothManager当前设备ID已设置:', this.deviceMac);\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t// 设置服务和特征值ID（如果存在的话）\r\n\t\t\t\t\t\tif (blueToothManager.serviceId && blueToothManager.writeCharacteristicId) {\r\n\t\t\t\t\t\t\tconsole.log('blueToothManager服务和特征值ID已存在');\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tconsole.log('blueToothManager服务和特征值ID不存在，可能需要重新获取');\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tconsole.log('未找到blueToothManager实例，跳过blueToothManager状态设置');\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (btError) {\r\n\t\t\t\t\tconsole.error('设置blueToothManager状态失败:', btError);\r\n\t\t\t\t}\r\n\r\n\t\t\t\tconsole.log('所有层级连接状态强制设置完成');\r\n\t\t\t} catch (e) {\r\n\t\t\t\tconsole.error('强制设置连接状态失败:', e);\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 设置lockService回调\r\n\t\tsetupLockServiceCallbacks() {\r\n\t\t\t// 设置回调函数\r\n\t\t\tlockService.setCallbacks({\r\n\t\t\t\t// 连接成功回调\r\n\t\t\t\tonConnected: (deviceInfo) => {\r\n\t\t\t\t\tconsole.log('lockService连接成功回调:', deviceInfo);\r\n\r\n\t\t\t\t\t// 强制更新连接状态，确保状态同步\r\n\t\t\t\t\tthis.isConnected = true;\r\n\t\t\t\t\tthis.isConnecting = false;\r\n\r\n\t\t\t\t\t// 强制同步设备信息到lockService，确保内部状态一致\r\n\t\t\t\t\tthis.forceSyncDeviceInfo(deviceInfo);\r\n\r\n\t\t\t\t\t// 停止任何正在进行的扫描\r\n\t\t\t\t\ttry {\r\n\t\t\t\t\t\tuni.stopBluetoothDevicesDiscovery();\r\n\t\t\t\t\t} catch (e) {\r\n\t\t\t\t\t\tconsole.log('停止蓝牙扫描失败:', e);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// 更新设备信息\r\n\t\t\t\t\tif (deviceInfo && deviceInfo.batteryLevel) {\r\n\t\t\t\t\t\tthis.batteryLevel = deviceInfo.batteryLevel;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// 使用防抖的状态更新方法\r\n\t\t\t\t\tthis.debouncedUpdateUIState();\r\n\r\n\t\t\t\t\t// 尝试查询设备状态\r\n\t\t\t\t\tthis.queryDeviceStatus();\r\n\r\n\t\t\t\t\t// 验证连接状态并处理自动开门\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tthis.handleConnectedState();\r\n\t\t\t\t\t}, 500);\r\n\r\n\t\t\t\t\t// 启动锁状态检查\r\n\t\t\t\t\tthis.startLockStatusCheck();\r\n\t\t\t\t},\r\n\t\t\t\t\r\n\t\t\t\t// 断开连接回调\r\n\t\t\t\tonDisconnected: (info) => {\r\n\t\t\t\t\tconsole.log('lockService断开连接回调:', info);\r\n\r\n\t\t\t\t\t// 防止重复设置状态\r\n\t\t\t\t\tif (this.isConnected) {\r\n\t\t\t\t\t\tthis.isConnected = false;\r\n\t\t\t\t\t\tthis.isConnecting = false;\r\n\r\n\t\t\t\t\t\t// 停止锁状态检查\r\n\t\t\t\t\t\tthis.stopLockStatusCheck();\r\n\r\n\t\t\t\t\t\t// 设置断开连接标志，防止立即触发自动开门\r\n\t\t\t\t\t\tthis.isRecentlyDisconnected = true;\r\n\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\tthis.isRecentlyDisconnected = false;\r\n\t\t\t\t\t\t}, 5000); // 5秒内不触发自动开门\r\n\r\n\t\t\t\t\t\t// 使用防抖的状态更新方法\r\n\t\t\t\t\t\tthis.debouncedUpdateUIState();\r\n\r\n\t\t\t\t\t\t// 如果页面活跃且需要自动重连，尝试重新连接\r\n\t\t\t\t\t\tif (this.isPageActive && (this.deviceMac || this.deviceName) && this.autoReconnect && !this.isManualDisconnect) {\r\n\t\t\t\t\t\t\tconsole.log('设备断开连接，尝试重新连接');\r\n\r\n\t\t\t\t\t\t\t// 延迟重连，给蓝牙适配器一些时间恢复\r\n\t\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t\t// 重新初始化蓝牙环境后再连接\r\n\t\t\t\t\t\t\t\tthis.reinitializeBluetooth();\r\n\t\t\t\t\t\t\t}, 3000); // 延长重连时间\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t// 重置手动断开标志\r\n\t\t\t\t\t\tthis.isManualDisconnect = false;\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\t\r\n\t\t\t\t// 锁状态变化回调\r\n\t\t\t\tonLockStatusChange: (status) => {\r\n\t\t\t\t\tconsole.log('lockService锁状态变化回调:', status);\r\n\t\t\t\t\tthis.isLockOpen = status.isOpen;\r\n\r\n\t\t\t\t\t// 如果锁打开了，设置开门完成标志\r\n\t\t\t\t\tif (status.isOpen) {\r\n\t\t\t\t\t\tthis.doorOpenCompleted = true;\r\n\t\t\t\t\t\tthis.doorOpenProcessed = true;\r\n\r\n\t\t\t\t\t\t// 播放成功音效\r\n\t\t\t\t\t\tthis.playSuccessSound();\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// 锁关闭时，重置开门完成状态，允许再次开门\r\n\t\t\t\t\t\tconsole.log('锁已关闭，重置开门状态，允许再次开门');\r\n\t\t\t\t\t\tthis.doorOpenCompleted = false;\r\n\t\t\t\t\t\t// 注意：不重置 doorOpenProcessed，避免重复自动开门\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// 更新UI状态\r\n\t\t\t\t\tthis.updateUIState();\r\n\t\t\t\t},\r\n\t\t\t\t\r\n\t\t\t\t// 电池电量更新回调\r\n\t\t\t\tonBatteryUpdate: (info) => {\r\n\t\t\t\t\tconsole.log('lockService电池电量更新回调:', info);\r\n\t\t\t\t\tthis.batteryLevel = info.batteryLevel;\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 更新UI状态\r\n\t\t\t\t\tthis.updateUIState();\r\n\t\t\t\t},\r\n\t\t\t\t\r\n\t\t\t\t// 错误回调\r\n\t\t\t\tonError: (error) => {\r\n\t\t\t\t\tconsole.error('lockService错误回调:', error);\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 显示错误提示\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '设备操作出错',\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 防抖的状态更新方法\r\n\t\tdebouncedUpdateUIState() {\r\n\t\t\t// 清除之前的定时器\r\n\t\t\tif (this.statusUpdateTimer) {\r\n\t\t\t\tclearTimeout(this.statusUpdateTimer);\r\n\t\t\t}\r\n\r\n\t\t\t// 设置新的定时器，延迟更新状态\r\n\t\t\tthis.statusUpdateTimer = setTimeout(() => {\r\n\t\t\t\tthis.updateUIState();\r\n\t\t\t}, 200);\r\n\t\t},\r\n\r\n\t\t// 添加updateUIState方法，确保UI状态正确更新\r\n\t\tupdateUIState() {\r\n\t\t\tconsole.log('更新UI状态 - 蓝牙连接状态:', this.isConnected, '订单状态:', this.orderStatus, '支付状态:', this.isPaid, '锁状态:', this.isLockOpen);\r\n\t\t\t\r\n\t\t\t// 确保订单状态和支付状态一致\r\n\t\t\tif (this.orderStatus === 1) {\r\n\t\t\t\tif (!this.isPaid) {\r\n\t\t\t\t\tconsole.log('订单状态为已支付，但支付状态为未支付，更新为已支付');\r\n\t\t\t\t\tthis.isPaid = true;\r\n\t\t\t\t\tthis.paymentSuccess = true;\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 如果正在轮询，停止轮询\r\n\t\t\t\t\tif (this.orderPollingTimer) {\r\n\t\t\t\t\t\tconsole.log('订单已支付，停止轮询');\r\n\t\t\t\t\t\tthis.stopOrderPolling();\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t} else if (this.orderStatus === 0 || this.orderStatus === null) {\r\n\t\t\t\t// 处理orderStatus为null的情况\r\n\t\t\t\t// 如果支付状态为true，但订单状态不是1，需要重新查询订单状态\r\n\t\t\t\tif (this.isPaid) {\r\n\t\t\t\t\tconsole.log('支付状态为已支付，但订单状态不是已支付，重新查询订单状态');\r\n\t\t\t\t\tthis.queryOrderStatus()\r\n\t\t\t\t\t\t.then(orderInfo => {\r\n\t\t\t\t\t\t\t// 如果通过查询确认已支付，更新订单状态\r\n\t\t\t\t\t\t\tif (orderInfo.status === 1 || orderInfo.payStatus === 1) {\r\n\t\t\t\t\t\t\t\tconsole.log('重新查询确认订单已支付');\r\n\t\t\t\t\t\t\t\tthis.orderStatus = 1;\r\n\t\t\t\t\t\t\t\tthis.isPaid = true;\r\n\t\t\t\t\t\t\t\tthis.paymentSuccess = true;\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t// 停止轮询\r\n\t\t\t\t\t\t\t\tthis.stopOrderPolling();\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t// 如果已连接设备，自动开门\r\n\t\t\t\t\t\t\t\tif (this.isConnected && !this.doorOpenProcessed && !this.doorOpenCompleted) {\r\n\t\t\t\t\t\t\t\t\tconsole.log('确认支付成功，设备已连接，自动开门');\r\n\t\t\t\t\t\t\t\t\tthis.checkOrderAndOpenDoor();\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t// 如果查询结果仍然不是已支付，则更新支付状态为未支付\r\n\t\t\t\t\t\t\t\tconsole.log('重新查询确认订单未支付，更新支付状态');\r\n\t\t\t\t\t\t\t\tthis.isPaid = false;\r\n\t\t\t\t\t\t\t\tthis.paymentSuccess = false;\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t// 如果订单未支付且没有在轮询，启动轮询\r\n\t\t\t\t\t\t\t\tif (!this.orderPollingTimer && this.orderId) {\r\n\t\t\t\t\t\t\t\t\tconsole.log('订单未支付且没有在轮询，启动轮询');\r\n\t\t\t\t\t\t\t\t\tthis.startOrderPolling();\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t.catch(err => {\r\n\t\t\t\t\t\t\tconsole.error('重新查询订单状态失败:', err);\r\n\t\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 如果订单未支付且没有在轮询，启动轮询\r\n\t\t\t\t\tif (!this.orderPollingTimer && this.orderId && this.isPageActive) {\r\n\t\t\t\t\t\tconsole.log('订单未支付且没有在轮询，启动轮询');\r\n\t\t\t\t\t\tthis.startOrderPolling();\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 确保订单ID已同步到lockService\r\n\t\t\tif (this.orderId && lockService.getOrderInfo().orderId !== this.orderId) {\r\n\t\t\t\tconsole.log('UI状态更新 - 同步订单ID到lockService:', this.orderId);\r\n\t\t\t\tlockService.setOrderId(this.orderId);\r\n\t\t\t\t\r\n\t\t\t\t// 获取最新订单状态\r\n\t\t\t\tif (this.orderId && (this.orderStatus === undefined || this.orderStatus === -1 || this.orderStatus === null)) {\r\n\t\t\t\t\tconsole.log('UI状态更新 - 获取最新订单状态');\r\n\t\t\t\t\tthis.getOrderStatus()\r\n\t\t\t\t\t\t.then(orderInfo => {\r\n\t\t\t\t\t\t\tconsole.log('获取订单状态成功:', orderInfo);\r\n\t\t\t\t\t\t\t// 更新支付状态 - 同时检查status和payStatus\r\n\t\t\t\t\t\t\tthis.isPaid = this.orderStatus === 1 || orderInfo.payStatus === 1;\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t// 如果订单已支付且已连接设备，自动开门\r\n\t\t\t\t\t\t\tif (this.isPaid && this.isConnected && !this.doorOpenProcessed && !this.doorOpenCompleted) {\r\n\t\t\t\t\t\t\t\tconsole.log('订单已支付且已连接设备，自动开门');\r\n\t\t\t\t\t\t\t\tthis.checkOrderAndOpenDoor();\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t.catch(err => {\r\n\t\t\t\t\t\t\tconsole.error('获取订单状态失败:', err);\r\n\t\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 从lockService获取最新设备状态\r\n\t\t\tconst deviceInfo = lockService.getDeviceInfo();\r\n\t\t\tif (deviceInfo) {\r\n\t\t\t\t// 更新设备连接状态 - 修复undefined问题\r\n\t\t\t\tconst lockServiceConnected = deviceInfo.connected === true || deviceInfo.isConnected === true;\r\n\t\t\t\tif (this.isConnected !== lockServiceConnected) {\r\n\t\t\t\t\tconsole.log('连接状态不一致，从lockService更新:', deviceInfo.connected, '解析为:', lockServiceConnected);\r\n\t\t\t\t\tthis.isConnected = lockServiceConnected;\r\n\r\n\t\t\t\t\t// 如果连接状态变为已连接，停止连接中状态\r\n\t\t\t\t\tif (lockServiceConnected) {\r\n\t\t\t\t\t\tthis.isConnecting = false;\r\n\r\n\t\t\t\t\t\t// 如果已支付且已连接，自动开门\r\n\t\t\t\t\t\tif (this.isPaid && !this.doorOpenProcessed && !this.doorOpenCompleted) {\r\n\t\t\t\t\t\t\tconsole.log('连接成功且已支付，准备自动开门');\r\n\t\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t\tthis.checkOrderAndOpenDoor();\r\n\t\t\t\t\t\t\t}, 1000);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 更新电池电量\r\n\t\t\t\tif (deviceInfo.batteryLevel && this.batteryLevel !== deviceInfo.batteryLevel) {\r\n\t\t\t\t\tconsole.log('电池电量更新:', deviceInfo.batteryLevel);\r\n\t\t\t\t\tthis.batteryLevel = deviceInfo.batteryLevel;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 更新锁状态（添加null检查）\r\n\t\t\t\tif (deviceInfo.lockStatus && deviceInfo.lockStatus !== undefined && this.isLockOpen !== deviceInfo.lockStatus.isOpen) {\r\n\t\t\t\t\tconsole.log('锁状态更新:', deviceInfo.lockStatus.isOpen ? '已开启' : '已关闭');\r\n\t\t\t\t\tthis.isLockOpen = deviceInfo.lockStatus.isOpen;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 如果已连接蓝牙并且已支付，自动触发开门逻辑\r\n\t\t\tif (this.isConnected && this.isPaid && !this.doorOpenProcessed && !this.doorOpenCompleted && this.isPageActive && !this.isRecentlyDisconnected) {\r\n\t\t\t\tconsole.log('连接成功且已支付，自动触发开门');\r\n\t\t\t\tthis.doorOpenProcessed = true;\r\n\r\n\t\t\t\t// 延迟执行开门操作，确保UI已更新\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tthis.checkOrderAndOpenDoor();\r\n\t\t\t\t}, 1000);\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 使用forceUpdate触发视图更新\r\n\t\t\tthis.forceUpdate();\r\n\t\t},\r\n\t\t\r\n\t\t// 强制刷新组件\r\n\t\tforceUpdate() {\r\n\t\t\t// 通过修改一个不可见属性来触发视图更新\r\n\t\t\tthis.updateKey = Date.now();\r\n\t\t},\r\n\t\t\r\n\t\t// 预初始化蓝牙环境\r\n\t\tpreinitBluetooth() {\r\n\t\t\tconsole.log('预初始化蓝牙环境');\r\n\t\t\t\r\n\t\t\t// 检查是否为模拟模式\r\n\t\t\tif (typeof global !== 'undefined' && global.isSimulatedMode) {\r\n\t\t\t\tconsole.log('检测到全局模拟模式标志');\r\n\t\t\t\tthis.isSimulatedMode = true;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 初始化蓝牙环境\r\n\t\t\tlockService.init()\r\n\t\t\t\t.then(res => {\r\n\t\t\t\t\tconsole.log('蓝牙环境初始化成功:', res);\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 检查是否为模拟模式\r\n\t\t\t\t\tif (res.simulated) {\r\n\t\t\t\t\t\tconsole.log('切换到模拟模式');\r\n\t\t\t\t\t\tthis.isSimulatedMode = true;\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t.catch(err => {\r\n\t\t\t\t\tconsole.error('蓝牙环境初始化失败:', err);\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 即使蓝牙初始化失败，也不显示错误，让用户可以继续使用\r\n\t\t\t\t\tconsole.log('切换到模拟模式');\r\n\t\t\t\t\tthis.isSimulatedMode = true;\r\n\t\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 防抖连接\r\n\t\tdebounceConnect() {\r\n\t\t\t// 防止短时间内多次连接\r\n\t\t\tconst now = Date.now();\r\n\t\t\tif (now - this.lastConnectTime < 2000) {\r\n\t\t\t\tconsole.log('连接操作过于频繁，已忽略');\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tthis.lastConnectTime = now;\r\n\t\t\t\r\n\t\t\t// 清除之前的定时器\r\n\t\t\tif (this.connectionDebounceTimer) {\r\n\t\t\t\tclearTimeout(this.connectionDebounceTimer);\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 设置新的定时器\r\n\t\t\tthis.connectionDebounceTimer = setTimeout(() => {\r\n\t\t\t\tthis.connectDevice();\r\n\t\t\t}, 300);\r\n\t\t},\r\n\t\t\r\n\t\t// 连接设备（优化版本）\r\n\t\tconnectDevice() {\r\n\t\t\t// 检查是否已连接或正在连接\r\n\t\t\tconst deviceInfo = lockService.getDeviceInfo();\r\n\t\t\tconst actualConnected = deviceInfo && (deviceInfo.connected === true || deviceInfo.isConnected === true);\r\n\r\n\t\t\tif (actualConnected || this.isConnected || this.isConnecting) {\r\n\t\t\t\tconsole.log('设备已连接或正在连接中，不重复连接');\r\n\t\t\t\tif (actualConnected && !this.isConnected) {\r\n\t\t\t\t\tthis.isConnected = true;\r\n\t\t\t\t\tthis.isConnecting = false;\r\n\t\t\t\t}\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\tconsole.log('开始连接设备');\r\n\t\t\tthis.isConnecting = true;\r\n\t\t\tthis.debouncedUpdateUIState(); // 使用防抖更新\r\n\r\n\t\t\t// 确保订单ID已设置到锁服务\r\n\t\t\tif (this.orderId) {\r\n\t\t\t\tconsole.log('连接设备前设置订单ID:', this.orderId);\r\n\t\t\t\tlockService.setOrderId(this.orderId);\r\n\t\t\t}\r\n\r\n\t\t\t// 使用优化的连接方法\r\n\t\t\tthis.connectDeviceOptimized();\r\n\t\t},\r\n\t\t\r\n\t\t// 断开连接\r\n\t\tdisconnectDevice() {\r\n\t\t\tif (!this.isConnected) {\r\n\t\t\t\treturn Promise.resolve();\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\treturn lockService.disconnect()\r\n\t\t\t\t.then(() => {\r\n\t\t\t\t\tconsole.log('断开连接成功');\r\n\t\t\t\t})\r\n\t\t\t\t.catch(err => {\r\n\t\t\t\t\tconsole.error('断开连接失败:', err);\r\n\t\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 确保断开蓝牙连接\r\n\t\tensureDisconnectBluetooth() {\r\n\t\t\tthis.disconnectDevice()\r\n\t\t\t\t.then(() => {\r\n\t\t\t\t\treturn lockService.close();\r\n\t\t\t\t})\r\n\t\t\t\t.catch(err => {\r\n\t\t\t\t\tconsole.error('关闭蓝牙失败:', err);\r\n\t\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 查询设备状态\r\n\t\tqueryDeviceStatus() {\r\n\t\t\ttry {\r\n\t\t\t\tconsole.log('尝试查询设备状态');\r\n\t\t\t\t// 先查询锁状态\r\n\t\t\t\tlockService.queryLockStatus()\r\n\t\t\t\t\t.then(() => {\r\n\t\t\t\t\t\tconsole.log('锁状态查询成功');\r\n\t\t\t\t\t\t// 再查询设备信息（包含电量）\r\n\t\t\t\t\t\treturn lockService.queryDeviceInfo();\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.then(() => {\r\n\t\t\t\t\t\tconsole.log('设备信息查询成功');\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch(err => {\r\n\t\t\t\t\t\tconsole.error('设备状态查询失败:', err);\r\n\t\t\t\t\t});\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('查询设备状态出错:', error);\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 处理主要操作按钮点击\r\n\t\thandleMainAction() {\r\n\t\t\tconsole.log('处理主要操作按钮点击');\r\n\r\n\t\t\t// 如果用户未登录，显示登录弹窗\r\n\t\t\tif (!this.isLoggedIn) {\r\n\t\t\t\tconsole.log('用户未登录，显示登录弹窗');\r\n\t\t\t\tthis.showLoginModal = true;\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t// 根据不同状态执行不同操作\r\n\t\t\tif (!this.isConnected) {\r\n\t\t\t\t// 未连接状态 - 连接设备\r\n\t\t\t\tthis.tryConnectDevice();\r\n\t\t\t} else if (this.orderStatus === 0 || !this.isPaid) {\r\n\t\t\t\t// 已连接但未支付 - 显示支付弹窗\r\n\t\t\t\tthis.showPaymentModal = true;\r\n\t\t\t} else {\r\n\t\t\t\t// 已连接且已支付 - 开门\r\n\t\t\t\tthis.handleOpenDoor();\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 处理开门按钮点击\r\n\t\thandleOpenDoor() {\r\n\t\t\tconsole.log('处理开门按钮点击，开门完成状态:', this.doorOpenCompleted, '锁状态:', this.isLockOpen);\r\n\r\n\t\t\t// 如果正在重连中，不允许开门\r\n\t\t\tif (this.isReconnecting) {\r\n\t\t\t\tconsole.log('正在重连中，请稍候');\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '正在重连设备，请稍候',\r\n\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\tduration: 2000\r\n\t\t\t\t});\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t// 检查开门冷却时间，防止频繁开锁\r\n\t\t\tconst currentTime = Date.now();\r\n\t\t\tconst timeSinceLastOpen = currentTime - this.lastOpenTime;\r\n\r\n\t\t\tif (timeSinceLastOpen < this.openCooldown) {\r\n\t\t\t\tconst remainingTime = Math.ceil((this.openCooldown - timeSinceLastOpen) / 1000);\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: `请等待${remainingTime}秒后再试`,\r\n\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\tduration: 2000\r\n\t\t\t\t});\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t// 如果锁已关闭，允许再次开门\r\n\t\t\tif (this.doorOpenCompleted && !this.isLockOpen) {\r\n\t\t\t\tconsole.log('锁已关闭，允许再次开门');\r\n\t\t\t\t// 重置开门完成状态，允许再次开门\r\n\t\t\t\tthis.doorOpenCompleted = false;\r\n\t\t\t}\r\n\r\n\t\t\t// 验证设备连接状态（双重检查）\r\n\t\t\tconst deviceInfo = lockService.getDeviceInfo();\r\n\t\t\tconst lockServiceConnected = deviceInfo && (deviceInfo.connected === true || deviceInfo.isConnected === true);\r\n\r\n\t\t\tconsole.log('开门前连接状态检查 - UI状态:', this.isConnected, 'lockService状态:', lockServiceConnected);\r\n\r\n\t\t\tif (!this.isConnected || !lockServiceConnected) {\r\n\t\t\t\tconsole.log('设备未连接，先连接设备');\r\n\t\t\t\tthis.tryConnectDevice();\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t// 如果未支付，显示支付提示\r\n\t\t\tif (!this.isPaid && this.orderStatus !== 1) {\r\n\t\t\t\tconsole.log('未支付，显示支付提示');\r\n\t\t\t\tthis.showOpenDoorModal = true;\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 开门前最后一次连接状态验证和强制同步\r\n\t\t\tconsole.log('开门前最后验证连接状态');\r\n\t\t\tconst finalDeviceInfo = lockService.getDeviceInfo();\r\n\t\t\tconsole.log('最终设备信息:', finalDeviceInfo);\r\n\r\n\t\t\t// 强制同步设备连接状态到lockService\r\n\t\t\tif (this.deviceMac && this.deviceName) {\r\n\t\t\t\tconsole.log('强制同步设备信息到lockService');\r\n\t\t\t\tlockService.setDeviceMac(this.deviceMac);\r\n\t\t\t\tlockService.setExpectedDeviceName(this.deviceName);\r\n\t\t\t\tif (this.orderId) {\r\n\t\t\t\t\tlockService.setOrderId(this.orderId);\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 强制设置所有层级连接状态\r\n\t\t\t\tthis.forceSetAllLayersConnected(finalDeviceInfo);\r\n\t\t\t}\r\n\r\n\t\t\t// 开门前最后验证所有层级的连接状态\r\n\t\t\tconsole.log('开门前验证所有层级连接状态:');\r\n\t\t\tconsole.log('lockService.deviceInfo.connected:', lockService.deviceInfo?.connected);\r\n\t\t\tconsole.log('lockService.deviceInfo:', lockService.deviceInfo);\r\n\r\n\t\t\t// 特别检查blueToothManager的连接状态\r\n\t\t\ttry {\r\n\t\t\t\tconst blueToothManager = lockService.blueToothManager || lockService.bluetoothManager;\r\n\t\t\t\tif (blueToothManager) {\r\n\t\t\t\t\tconsole.log('blueToothManager连接状态检查:');\r\n\t\t\t\t\tconsole.log('blueToothManager.isConnected:', blueToothManager.isConnected);\r\n\t\t\t\t\tconsole.log('blueToothManager.deviceInfo:', blueToothManager.deviceInfo);\r\n\t\t\t\t\tconsole.log('blueToothManager.currentDeviceId:', blueToothManager.currentDeviceId);\r\n\r\n\t\t\t\t\t// 如果blueToothManager显示未连接，强制重新设置\r\n\t\t\t\t\tif (!blueToothManager.isConnected || !blueToothManager.deviceInfo?.connected) {\r\n\t\t\t\t\t\tconsole.log('检测到blueToothManager未连接，强制设置连接状态');\r\n\r\n\t\t\t\t\t\tif (blueToothManager.deviceInfo) {\r\n\t\t\t\t\t\t\tblueToothManager.deviceInfo.connected = true;\r\n\t\t\t\t\t\t\tblueToothManager.deviceInfo.isConnected = true;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tblueToothManager.isConnected = true;\r\n\t\t\t\t\t\tblueToothManager.currentDeviceId = this.deviceMac;\r\n\t\t\t\t\t\tblueToothManager.connectedDeviceId = this.deviceMac;\r\n\r\n\t\t\t\t\t\tconsole.log('blueToothManager连接状态已强制修复');\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t} catch (btError) {\r\n\t\t\t\tconsole.error('检查blueToothManager状态失败:', btError);\r\n\t\t\t}\r\n\r\n\t\t\t// 开门前强制重新认证设备，确保blueToothManager层面的连接\r\n\t\t\tconsole.log('开门前强制重新认证设备');\r\n\r\n\t\t\t// 先尝试重新认证，然后执行开门\r\n\t\t\tthis.forceReauthenticateAndUnlock(finalDeviceInfo)\r\n\t\t\t\t.then(res => {\r\n\t\t\t\t\tconsole.log('强制重新认证开门成功:', res);\r\n\r\n\t\t\t\t\t// 记录开门时间，用于防频繁开锁\r\n\t\t\t\t\tthis.lastOpenTime = Date.now();\r\n\t\t\t\t\tthis.currentTime = this.lastOpenTime; // 初始化currentTime\r\n\r\n\t\t\t\t\t// 启动冷却倒计时定时器\r\n\t\t\t\t\tthis.startCooldownTimer();\r\n\r\n\t\t\t\t\t// 设置开门完成标志，防止重复开门\r\n\t\t\t\t\tthis.doorOpenCompleted = true;\r\n\r\n\t\t\t\t\tthis.isLockOpen = true;\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '开门成功',\r\n\t\t\t\t\t\ticon: 'success',\r\n\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\t\t// 更新订单使用状态\r\n\t\t\t\t\tthis.useStatus = 1;\r\n\r\n\t\t\t\t\t// 播放成功音效\r\n\t\t\t\t\tthis.playSuccessSound();\r\n\r\n\t\t\t\t\t// 更新UI状态\r\n\t\t\t\t\tthis.updateUIState();\r\n\t\t\t\t})\r\n\t\t\t\t.catch(err => {\r\n\t\t\t\t\tconsole.error('强制重新认证开门失败:', err);\r\n\r\n\t\t\t\t\t// 如果是连接问题，尝试备用开门方法\r\n\t\t\t\t\tif (err.message && err.message.includes('设备未连接')) {\r\n\t\t\t\t\t\tconsole.log('尝试备用开门方法');\r\n\t\t\t\t\t\tthis.tryAlternativeOpenDoor();\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '开门失败，请重试',\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t},\r\n\r\n\t\t// 备用开门方法\r\n\t\ttryAlternativeOpenDoor() {\r\n\t\t\tconsole.log('尝试备用开门方法');\r\n\r\n\t\t\t// 方法1：尝试重新连接后开门\r\n\t\t\tthis.reconnectAndOpenDoor()\r\n\t\t\t\t.catch(err => {\r\n\t\t\t\t\tconsole.error('重连开门失败:', err);\r\n\t\t\t\t\t// 方法2：尝试直接发送蓝牙开门命令\r\n\t\t\t\t\tthis.directBluetoothOpenDoor();\r\n\t\t\t\t});\r\n\t\t},\r\n\r\n\t\t// 重新连接并开门\r\n\t\treconnectAndOpenDoor() {\r\n\t\t\tconsole.log('重新连接设备并开门');\r\n\r\n\t\t\treturn new Promise((resolve, reject) => {\r\n\t\t\t\t// 先断开连接\r\n\t\t\t\ttry {\r\n\t\t\t\t\tlockService.disconnect();\r\n\t\t\t\t} catch (e) {\r\n\t\t\t\t\tconsole.log('断开连接失败:', e);\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 重置连接状态\r\n\t\t\t\tthis.isConnected = false;\r\n\t\t\t\tthis.isConnecting = false;\r\n\t\t\t\tthis.bluetoothInitialized = false;\r\n\r\n\t\t\t\t// 延迟重新连接\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tthis.connectDeviceOptimized()\r\n\t\t\t\t\t\t.then(() => {\r\n\t\t\t\t\t\t\t// 连接成功后等待一段时间确保状态同步\r\n\t\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t\t// 再次尝试开门\r\n\t\t\t\t\t\t\t\tlockService.openLock({\r\n\t\t\t\t\t\t\t\t\tignoreOrderStatus: true,\r\n\t\t\t\t\t\t\t\t\tforce: true,\r\n\t\t\t\t\t\t\t\t\tretry: true,\r\n\t\t\t\t\t\t\t\t\tsingleCommand: true,\r\n\t\t\t\t\t\t\t\t\toperationType: 1\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t.then(resolve)\r\n\t\t\t\t\t\t\t\t.catch(reject);\r\n\t\t\t\t\t\t\t}, 2000);\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t.catch(reject);\r\n\t\t\t\t}, 1000);\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t// 直接蓝牙开门命令\r\n\t\tdirectBluetoothOpenDoor() {\r\n\t\t\tconsole.log('尝试直接蓝牙开门命令');\r\n\r\n\t\t\t// 如果有直接的蓝牙开门命令，可以在这里实现\r\n\t\t\t// 这是最后的备用方案\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '开门失败，请检查设备连接',\r\n\t\t\t\ticon: 'none',\r\n\t\t\t\tduration: 3000\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t// 处理支付\r\n\t\tprocessPayment() {\r\n\t\t\tconsole.log('处理支付');\r\n\r\n\t\t\t// 检查登录状态\r\n\t\t\tif (!this.isLoggedIn) {\r\n\t\t\t\tconsole.log('用户未登录，显示登录弹窗');\r\n\t\t\t\tthis.closePaymentModal();\r\n\t\t\t\tthis.showLoginModal = true;\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t// 设置支付中状态\r\n\t\t\tthis.paymentLoading = true;\r\n\t\t\tthis.paymentSuccess = false;\r\n\t\t\t\r\n\t\t\t// 创建订单并支付\r\n\t\t\tconst createOrderIfNeeded = () => {\r\n\t\t\t\t// 检查是否已有订单ID\r\n\t\t\t\tif (this.orderId) {\r\n\t\t\t\t\tconsole.log('已有订单ID，直接返回:', this.orderId);\r\n\t\t\t\t\treturn Promise.resolve({\r\n\t\t\t\t\t\torderId: this.orderId\r\n\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 创建新订单\r\n\t\t\t\t\tconsole.log('创建新订单');\r\n\r\n\t\t\t\t\t// 重置开门状态，因为这是新订单\r\n\t\t\t\t\tthis.resetDoorState();\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 确保设备编码不为空\r\n\t\t\t\t\tif (!this.deviceInfo || (!this.deviceInfo.deviceCode && !this.deviceInfo.bindCode && !this.deviceInfo.macAddress)) {\r\n\t\t\t\t\t\tconsole.error('设备编码不能为空');\r\n\t\t\t\t\t\treturn Promise.reject({\r\n\t\t\t\t\t\t\tmessage: '设备编码不能为空，请重新扫描设备'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 使用设备信息中的绑定码、MAC地址或设备编号作为deviceCode\r\n\t\t\t\t\tconst deviceCode = this.deviceInfo.bindCode || this.deviceInfo.macAddress || this.deviceInfo.deviceNo || '';\r\n\t\t\t\t\tconsole.log('使用设备编码:', deviceCode);\r\n\t\t\t\t\t\r\n\t\t\t\t\treturn lockService.createOrder({\r\n\t\t\t\t\t\tdeviceCode: deviceCode,\r\n\t\t\t\t\t\tduration: 60 // 默认60分钟\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t};\r\n\t\t\t\r\n\t\t\t// 执行创建订单流程\r\n\t\t\tcreateOrderIfNeeded()\r\n\t\t\t\t.then(res => {\r\n\t\t\t\t\tconsole.log('订单准备完成:', res);\r\n\t\t\t\t\t// 确保订单ID存在\r\n\t\t\t\t\tif (res && res.orderId) {\r\n\t\t\t\t\t\tthis.orderId = res.orderId;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 设置支付中状态\r\n\t\t\t\t\t\tthis.isPaying = true;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 关闭支付弹窗\r\n\t\t\t\t\t\tthis.closePaymentModal();\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 设置订单ID到lockService\r\n\t\t\t\t\t\tlockService.setOrderId(this.orderId);\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 在跳转到微信支付页面前启动订单状态轮询\r\n\t\t\t\t\t\t// 这样即使用户不点击\"完成\"按钮，也能检测到支付状态变化\r\n\t\t\t\t\t\tconsole.log('即将跳转到微信支付页面，启动订单状态轮询');\r\n\t\t\t\t\t\tthis.startOrderPolling();\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 支付订单\r\n\t\t\t\t\t\treturn lockService.payOrder(this.orderId);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthrow new Error('获取订单ID失败');\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t.then(res => {\r\n\t\t\t\t\tconsole.log('支付成功:', res);\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 更新订单状态\r\n\t\t\t\t\tthis.orderStatus = 1;\r\n\t\t\t\t\tthis.isPaid = true;\r\n\t\t\t\t\tthis.paymentSuccess = true;\r\n\r\n\t\t\t\t\t// 保存支付状态到本地存储\r\n\t\t\t\t\tthis.savePaymentState();\r\n\r\n\t\t\t\t\t// 更新UI状态\r\n\t\t\t\t\tthis.updateUIState();\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 播放支付成功音效\r\n\t\t\t\t\tthis.playSuccessSound();\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 显示支付成功提示\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '支付成功',\r\n\t\t\t\t\t\ticon: 'success',\r\n\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t});\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 停止订单状态轮询，因为已经确认支付成功\r\n\t\t\t\t\tthis.stopOrderPolling();\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 延迟执行下一步操作，确保UI状态更新完成\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t// 如果已连接设备且未完成开门，自动开门\r\n\t\t\t\t\t\tif (this.isConnected && !this.doorOpenCompleted) {\r\n\t\t\t\t\t\t\tconsole.log('支付成功，设备已连接，自动开门');\r\n\t\t\t\t\t\t\tthis.checkOrderAndOpenDoor();\r\n\t\t\t\t\t\t} else if (!this.isConnected) {\r\n\t\t\t\t\t\t\t// 未连接设备，尝试连接\r\n\t\t\t\t\t\t\tconsole.log('支付成功，设备未连接，尝试连接');\r\n\t\t\t\t\t\t\tthis.tryConnectDevice();\r\n\t\t\t\t\t\t} else if (this.doorOpenCompleted) {\r\n\t\t\t\t\t\t\tconsole.log('支付成功，但已完成开门，无需重复操作');\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}, 1000); // 延长延迟时间，确保状态完全更新\r\n\t\t\t\t})\r\n\t\t\t\t.catch(err => {\r\n\t\t\t\t\tconsole.error('支付流程失败:', err);\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 显示失败提示\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: err.message || '支付失败，请重试',\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t});\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 支付失败时不停止轮询，因为可能是用户取消了支付页面\r\n\t\t\t\t\t// 但实际已经完成了支付，轮询可以检测到这种情况\r\n\t\t\t\t})\r\n\t\t\t\t.finally(() => {\r\n\t\t\t\t\tthis.paymentLoading = false;\r\n\t\t\t\t\tthis.isPaying = false;\r\n\t\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 关闭支付弹窗\r\n\t\tclosePaymentModal() {\r\n\t\t\tthis.showPaymentModal = false;\r\n\t\t\tthis.paymentLoading = false;\r\n\t\t},\r\n\t\t\r\n\t\t// 关闭开门弹窗\r\n\t\tcloseOpenDoorModal() {\r\n\t\t\tthis.showOpenDoorModal = false;\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 获取设备信息\r\n\t\t * @param {string} deviceId 设备编号或ID\r\n\t\t */\r\n\t\tgetDeviceInfo(deviceId) {\r\n\t\t\tconsole.log('获取设备信息:', deviceId);\r\n\t\t\t\r\n\t\t\t// 先检查用户是否有设备的订单\r\n\t\t\tthis.checkUserDeviceOrder(deviceId)\r\n\t\t\t\t.then(orderInfo => {\r\n\t\t\t\t\tif (orderInfo) {\r\n\t\t\t\t\t\t// 设置订单信息\r\n\t\t\t\t\t\tthis.orderId = orderInfo.orderId;\r\n\t\t\t\t\t\tthis.orderStatus = orderInfo.status || 0;\r\n\t\t\t\t\t\tthis.useStatus = orderInfo.useStatus || 0;\r\n\t\t\t\t\t\tthis.orderAmount = orderInfo.amount || 0;\r\n\t\t\t\t\t\tthis.orderDuration = orderInfo.duration || 0;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 保存门店信息\r\n\t\t\t\t\t\tif (orderInfo.shopName) {\r\n\t\t\t\t\t\t\tif (!this.deviceInfo) this.deviceInfo = {};\r\n\t\t\t\t\t\t\tthis.deviceInfo.shopName = orderInfo.shopName;\r\n\t\t\t\t\t\t\tconsole.log('保存门店信息:', orderInfo.shopName);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 更新支付状态\r\n\t\t\t\t\t\tthis.isPaid = orderInfo.status === 1 || orderInfo.payStatus === 1;\r\n\t\t\t\t\t\tthis.paymentSuccess = this.isPaid;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 设置订单ID到lockService\r\n\t\t\t\t\t\tlockService.setOrderId(this.orderId);\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 如果订单已支付，显示相应提示\r\n\t\t\t\t\t\tif (this.isPaid) {\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: '您有已支付的订单，将自动开门',\r\n\t\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t// 播放支付成功音效\r\n\t\t\t\t\t\t\tthis.playSuccessSound();\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t// 未支付订单\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: '您有未完成的订单',\r\n\t\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 继续获取设备信息\r\n\t\t\t\t\tthis.continueGetDeviceInfo(deviceId);\r\n\t\t\t\t})\r\n\t\t\t\t.catch(err => {\r\n\t\t\t\t\tconsole.error('检查用户订单失败:', err);\r\n\t\t\t\t\t// 继续获取设备信息\r\n\t\t\t\t\tthis.continueGetDeviceInfo(deviceId);\r\n\t\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 继续获取设备信息的方法\r\n\t\tcontinueGetDeviceInfo(deviceId) {\r\n\t\t\t// 构建API请求URL\r\n\t\t\tlet baseUrl = '';\r\n\t\t\tif (API && API.baseUrl) {\r\n\t\t\t\tbaseUrl = API.baseUrl;\r\n\t\t\t} else {\r\n\t\t\t\t// 默认使用HTTPS协议\r\n\t\t\t\tbaseUrl = 'https://api.jycb888.com';\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 使用HTTPS协议\r\n\t\t\tif (baseUrl.startsWith('http://')) {\r\n\t\t\t\tbaseUrl = baseUrl.replace('http://', 'https://');\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 不做判断，同时使用两个API路径\r\n\t\t\tconst scanUrl = `${baseUrl}/api/wx/miniapp/device/scan?deviceCode=${encodeURIComponent(deviceId)}`;\r\n\t\t\tconst statusUrl = `${baseUrl}/api/wx/miniapp/device/status/${deviceId}`;\r\n\t\t\t\r\n\t\t\tconsole.log('尝试请求设备信息URL1:', scanUrl);\r\n\t\t\tconsole.log('尝试请求设备信息URL2:', statusUrl);\r\n\t\t\t\r\n\t\t\tlet requestComplete = false;\r\n\t\t\t\r\n\t\t\t// 请求扫描接口\r\n\t\t\tuni.request({\r\n\t\t\t\turl: scanUrl,\r\n\t\t\t\tmethod: 'GET',\r\n\t\t\t\theader: {\r\n\t\t\t\t\t'Authorization': uni.getStorageSync('token')\r\n\t\t\t\t},\r\n\t\t\t\tsslVerify: false,\r\n\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t// 如果另一个请求已成功处理，不再处理\r\n\t\t\t\t\tif (requestComplete) return;\r\n\r\n\t\t\t\t\tif (res.statusCode === 200 && res.data && res.data.code === 200 && res.data.data) {\r\n\t\t\t\t\t\tconsole.log('扫描接口成功:', res.data.data);\r\n\t\t\t\t\t\trequestComplete = true;\r\n\r\n\t\t\t\t\t\tthis.handleDeviceInfoResponse(res.data.data, deviceId);\r\n\t\t\t\t\t} else if (res.statusCode === 200 && res.data && res.data.code === 500) {\r\n\t\t\t\t\t\tconsole.error('扫描接口返回错误:', res.data.message);\r\n\t\t\t\t\t\t// 如果是设备所属门店不存在的错误，尝试其他接口\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\tfail: (err) => {\r\n\t\t\t\t\tconsole.error('扫描接口请求失败:', err);\r\n\t\t\t\t\t// 不处理错误，因为还有状态接口请求\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\t\r\n\t\t\t// 请求状态接口\r\n\t\t\tuni.request({\r\n\t\t\t\turl: statusUrl,\r\n\t\t\t\tmethod: 'GET',\r\n\t\t\t\theader: {\r\n\t\t\t\t\t'Authorization': uni.getStorageSync('token')\r\n\t\t\t\t},\r\n\t\t\t\tsslVerify: false,\r\n\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t// 如果另一个请求已成功处理，不再处理\r\n\t\t\t\t\tif (requestComplete) return;\r\n\r\n\t\t\t\t\tif (res.statusCode === 200 && res.data && res.data.code === 200 && res.data.data) {\r\n\t\t\t\t\t\tconsole.log('状态接口成功:', res.data.data);\r\n\t\t\t\t\t\trequestComplete = true;\r\n\r\n\t\t\t\t\t\tthis.handleDeviceInfoResponse(res.data.data, deviceId);\r\n\t\t\t\t\t} else if (res.statusCode === 200 && res.data && res.data.code === 500) {\r\n\t\t\t\t\t\tconsole.error('状态接口返回错误:', res.data.message);\r\n\t\t\t\t\t\t// 如果两个接口都失败，显示错误信息\r\n\t\t\t\t\t\tif (!requestComplete) {\r\n\t\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t\tif (!requestComplete) {\r\n\t\t\t\t\t\t\t\t\tthis.showError(`设备信息获取失败: ${res.data.message}`);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}, 1000);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t// 只有在两个接口都失败时才处理错误\r\n\t\t\t\t\tif (!requestComplete) {\r\n\t\t\t\t\t\tconsole.error('状态接口请求失败:', err);\r\n\r\n\t\t\t\t\t\t// 延迟显示错误，给另一个接口一些时间\r\n\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\tif (!requestComplete) {\r\n\t\t\t\t\t\t\t\tthis.showError('获取设备信息失败，请检查网络连接');\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}, 1000);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 处理设备信息响应\r\n\t\thandleDeviceInfoResponse(deviceData, deviceId) {\r\n\t\t\tconsole.log('处理设备信息响应:', deviceData);\r\n\t\t\tconsole.log('设备信息字段检查:', {\r\n\t\t\t\tmacAddress: deviceData.macAddress,\r\n\t\t\t\tmac: deviceData.mac,\r\n\t\t\t\tbluetoothMac: deviceData.bluetoothMac,\r\n\t\t\t\tdeviceMac: deviceData.deviceMac,\r\n\t\t\t\tdeviceName: deviceData.deviceName,\r\n\t\t\t\tdeviceId: deviceData.id || deviceData.deviceId\r\n\t\t\t});\r\n\t\t\tthis.deviceInfo = deviceData;\r\n\t\t\t\r\n\t\t\t// 更新设备ID和价格信息\r\n\t\t\tthis.deviceId = deviceData.id || deviceData.deviceId || deviceId;\r\n\t\t\tif (deviceData.hourlyRate || deviceData.price) {\r\n\t\t\t\tthis.hourlyRate = deviceData.hourlyRate || parseFloat(deviceData.price) || 0;\r\n\t\t\t\tthis.priceText = `￥${this.hourlyRate.toFixed(2)}`;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 检查是否有当前订单\r\n\t\t\tif (deviceData.currentOrderId && !this.orderId) {\r\n\t\t\t\tthis.orderId = deviceData.currentOrderId;\r\n\t\t\t\t// 获取订单状态\r\n\t\t\t\tthis.getOrderStatus()\r\n\t\t\t\t\t.then(orderInfo => {\r\n\t\t\t\t\t\t// 如果订单已支付，设置支付状态\r\n\t\t\t\t\t\tif (orderInfo.status === 1 || orderInfo.payStatus === 1) {\r\n\t\t\t\t\t\t\tthis.isPaid = true;\r\n\t\t\t\t\t\t\tthis.paymentSuccess = true;\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t// 如果已连接设备，自动开门\r\n\t\t\t\t\t\t\tif (this.isConnected && !this.doorOpenProcessed) {\r\n\t\t\t\t\t\t\t\tconsole.log('检测到已支付订单，设备已连接，自动开门');\r\n\t\t\t\t\t\t\t\tthis.checkOrderAndOpenDoor();\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch(err => {\r\n\t\t\t\t\t\tconsole.error('获取订单状态失败:', err);\r\n\t\t\t\t\t});\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 设置设备MAC地址 - 支持多种字段名\r\n\t\t\tconst macAddress = deviceData.macAddress || deviceData.mac || deviceData.bluetoothMac || deviceData.deviceMac;\r\n\t\t\tif (macAddress) {\r\n\t\t\t\t// 格式化MAC地址，添加冒号\r\n\t\t\t\tconst rawMac = macAddress.replace(/:/g, '').replace(/-/g, '').toUpperCase();\r\n\t\t\t\tif (rawMac.length === 12) {\r\n\t\t\t\t\tthis.deviceMac = rawMac.match(/.{1,2}/g).join(':');\r\n\t\t\t\t\tconsole.log('从API获取到设备MAC地址:', this.deviceMac);\r\n\t\t\t\t\tlockService.setDeviceMac(this.deviceMac);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tconsole.warn('MAC地址格式不正确:', macAddress);\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\tconsole.warn('API返回的设备信息中没有MAC地址字段');\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 优化连接流程：避免重复初始化，直接连接\r\n\t\t\tif (deviceData.deviceName && this.deviceMac) {\r\n\t\t\t\tthis.deviceName = deviceData.deviceName;\r\n\t\t\t\tconsole.log('使用设备名称和MAC地址连接:', this.deviceName, this.deviceMac);\r\n\r\n\t\t\t\t// 设置预期设备名称和MAC地址\r\n\t\t\t\tlockService.setExpectedDeviceName(this.deviceName);\r\n\t\t\t\tlockService.setDeviceMac(this.deviceMac);\r\n\r\n\t\t\t\t// 直接连接，不延迟\r\n\t\t\t\tthis.connectDeviceOptimized();\r\n\t\t\t} else if (this.deviceMac) {\r\n\t\t\t\t// 如果没有设备名称，但有MAC地址，尝试使用MAC地址连接\r\n\t\t\t\tconsole.log('没有设备名称，使用MAC地址连接');\r\n\t\t\t\tthis.connectDeviceOptimized();\r\n\t\t\t} else {\r\n\t\t\t\tconsole.warn('设备信息中既没有设备名称也没有MAC地址，无法连接蓝牙设备');\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 优化的设备连接方法\r\n\t\t */\r\n\t\tconnectDeviceOptimized() {\r\n\t\t\tconsole.log('开始优化连接流程');\r\n\r\n\t\t\t// 检查是否已连接\r\n\t\t\tconst deviceInfo = lockService.getDeviceInfo();\r\n\t\t\tconst actualConnected = deviceInfo && (deviceInfo.connected === true || deviceInfo.isConnected === true);\r\n\r\n\t\t\tif (actualConnected) {\r\n\t\t\t\tconsole.log('设备已连接，无需重复连接');\r\n\t\t\t\tthis.isConnected = true;\r\n\t\t\t\tthis.isConnecting = false;\r\n\t\t\t\treturn Promise.resolve();\r\n\t\t\t}\r\n\r\n\t\t\tif (this.isConnecting) {\r\n\t\t\t\tconsole.log('正在连接中，避免重复连接');\r\n\t\t\t\treturn Promise.resolve();\r\n\t\t\t}\r\n\r\n\t\t\tthis.isConnecting = true;\r\n\r\n\t\t\t// 确保蓝牙已初始化（避免重复初始化）\r\n\t\t\tconst initPromise = this.bluetoothInitialized ?\r\n\t\t\t\tPromise.resolve() :\r\n\t\t\t\tthis.ensureBluetoothInitialized();\r\n\r\n\t\t\treturn initPromise.then(() => {\r\n\t\t\t\t// 优先使用MAC地址直接连接（更快）\r\n\t\t\t\tif (this.deviceMac) {\r\n\t\t\t\t\tconsole.log('使用MAC地址快速连接:', this.deviceMac);\r\n\t\t\t\t\treturn lockService.connectDevice(this.deviceMac)\r\n\t\t\t\t\t\t.then(res => {\r\n\t\t\t\t\t\t\tconsole.log('MAC地址连接成功:', res);\r\n\t\t\t\t\t\t\treturn res;\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t.catch(err => {\r\n\t\t\t\t\t\t\tconsole.error('MAC地址连接失败，尝试设备名称连接:', err);\r\n\t\t\t\t\t\t\t// MAC连接失败，尝试设备名称连接\r\n\t\t\t\t\t\t\tif (this.deviceName) {\r\n\t\t\t\t\t\t\t\treturn this.connectByDeviceName();\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tthis.isConnecting = false;\r\n\t\t\t\t\t\t\t\tthis.showConnectionError(err);\r\n\t\t\t\t\t\t\t\tthrow err;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t} else if (this.deviceName) {\r\n\t\t\t\t\t// 没有MAC地址，使用设备名称连接\r\n\t\t\t\t\treturn this.connectByDeviceName();\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.isConnecting = false;\r\n\t\t\t\t\tconst error = new Error('设备信息不完整');\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '设备信息不完整',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t\tthrow error;\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t * 确保蓝牙已初始化\r\n\t\t */\r\n\t\tensureBluetoothInitialized() {\r\n\t\t\tif (this.bluetoothInitialized) {\r\n\t\t\t\treturn Promise.resolve();\r\n\t\t\t}\r\n\r\n\t\t\tconsole.log('初始化蓝牙环境');\r\n\t\t\treturn lockService.init()\r\n\t\t\t\t.then(res => {\r\n\t\t\t\t\tthis.bluetoothInitialized = true;\r\n\t\t\t\t\tconsole.log('蓝牙环境初始化完成');\r\n\t\t\t\t\treturn res;\r\n\t\t\t\t})\r\n\t\t\t\t.catch(err => {\r\n\t\t\t\t\tconsole.error('蓝牙环境初始化失败:', err);\r\n\t\t\t\t\tthrow err;\r\n\t\t\t\t});\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t * 使用设备名称连接设备\r\n\t\t */\r\n\t\tconnectByDeviceName() {\r\n\t\t\tconsole.log('使用设备名称连接:', this.deviceName);\r\n\t\t\treturn lockService.directConnect(this.deviceName)\r\n\t\t\t\t.then(res => {\r\n\t\t\t\t\tconsole.log('设备名称连接成功:', res);\r\n\t\t\t\t\treturn res;\r\n\t\t\t\t})\r\n\t\t\t\t.catch(err => {\r\n\t\t\t\t\tconsole.error('设备名称连接失败:', err);\r\n\t\t\t\t\tthis.isConnecting = false;\r\n\t\t\t\t\tthis.showConnectionError(err);\r\n\t\t\t\t\tthrow err;\r\n\t\t\t\t});\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t * 显示连接错误\r\n\t\t */\r\n\t\tshowConnectionError(err) {\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '连接失败: ' + (err.message || '未知错误'),\r\n\t\t\t\ticon: 'none',\r\n\t\t\t\tduration: 2000\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t * 使用设备名称连接设备（旧方法，保留兼容性）\r\n\t\t * @param {string} deviceName 设备名称\r\n\t\t */\r\n\t\tconnectDeviceByName(deviceName) {\r\n\t\t\tconsole.log('使用设备名称连接设备:', deviceName);\r\n\t\t\t\r\n\t\t\t// 先初始化蓝牙环境\r\n\t\t\tlockService.init()\r\n\t\t\t\t.then(() => {\r\n\t\t\t\t\t// 直接使用设备名称连接\r\n\t\t\t\t\treturn lockService.directConnect(deviceName);\r\n\t\t\t\t})\r\n\t\t\t\t.then(res => {\r\n\t\t\t\t\tconsole.log('连接命令执行成功:', res);\r\n\t\t\t\t\t// 注意：不在这里设置连接状态，让lockService的回调来处理\r\n\t\t\t\t\tconsole.log('等待lockService回调确认连接状态');\r\n\r\n\t\t\t\t\t// 显示连接成功提示\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '连接成功',\r\n\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t});\r\n\t\t\t\t})\r\n\t\t\t\t.catch(err => {\r\n\t\t\t\t\tconsole.error('连接失败:', err);\r\n\r\n\t\t\t\t\t// 更新连接状态\r\n\t\t\t\t\tthis.isConnecting = false;\r\n\r\n\t\t\t\t\t// 使用防抖更新UI状态\r\n\t\t\t\t\tthis.debouncedUpdateUIState();\r\n\r\n\t\t\t\t\t// 显示连接失败提示\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '连接失败，请重试',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 获取设备价格\r\n\t\tgetDevicePrice() {\r\n\t\t\t// 实现获取设备价格的逻辑\r\n\t\t\t// ...\r\n\t\t},\r\n\t\t// 获取订单状态\r\n\t\tgetOrderStatus() {\r\n\t\t\tif (!this.orderId || isNaN(this.orderId)) {\r\n\t\t\t\tconsole.error('无效的订单ID:', this.orderId);\r\n\t\t\t\treturn Promise.reject(new Error('无效的订单ID'));\r\n\t\t\t}\r\n\t\t\treturn new Promise((resolve, reject) => {\r\n\t\t\t\t// 使用getAPIBaseUrl函数获取API基础URL\r\n\t\t\t\tconst baseUrl = getAPIBaseUrl();\r\n\t\t\t\tuni.request({\r\n\t\t\t\t\turl: `${baseUrl}/api/wx/miniapp/order/${this.orderId}`,\r\n\t\t\t\t\tmethod: 'GET',\r\n\t\t\t\t\theader: {\r\n\t\t\t\t\t\t'Authorization': uni.getStorageSync('token')\r\n\t\t\t\t\t},\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tif (res.data && res.data.code === 200) {\r\n\t\t\t\t\t\t\tconst orderInfo = res.data.data;\r\n\t\t\t\t\t\t\tconsole.log('获取订单状态成功:', orderInfo);\r\n\t\t\t\t\t\t\t// 更新订单信息\r\n\t\t\t\t\t\t\t// 注意：status可能为null，需要检查payStatus\r\n\t\t\t\t\t\t\tthis.orderStatus = orderInfo.status || 0; // 如果status为null，默认为0（未支付）\r\n\t\t\t\t\t\t\t// 如果status为null但payStatus为1，说明已支付\r\n\t\t\t\t\t\t\tif (orderInfo.status === null && orderInfo.payStatus === 1) {\r\n\t\t\t\t\t\t\t\tconsole.log('订单status为null但payStatus为1，视为已支付');\r\n\t\t\t\t\t\t\t\tthis.orderStatus = 1;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tthis.useStatus = orderInfo.useStatus || 0;\r\n\t\t\t\t\t\t\tthis.orderAmount = orderInfo.amount || 0;\r\n\t\t\t\t\t\t\tthis.orderDuration = orderInfo.duration || 0;\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t// 保存门店信息\r\n\t\t\t\t\t\t\tif (orderInfo.shopName) {\r\n\t\t\t\t\t\t\t\tif (!this.deviceInfo) this.deviceInfo = {};\r\n\t\t\t\t\t\t\t\tthis.deviceInfo.shopName = orderInfo.shopName;\r\n\t\t\t\t\t\t\t\tconsole.log('保存门店信息:', orderInfo.shopName);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t// 更新支付状态 - 同时检查status和payStatus\r\n\t\t\t\t\t\t\tthis.isPaid = this.orderStatus === 1 || orderInfo.payStatus === 1;\r\n\t\t\t\t\t\t\t// 设置订单ID到lockServic\r\n\t\t\t\t\t\t\tlockService.setOrderId(this.orderId);\r\n\t\t\t\t\t\t\t// 如果订单已支付且使用中，开始计时\r\n\t\t\t\t\t\t\tif (this.isPaid && this.useStatus === 1) {\r\n\t\t\t\t\t\t\t\tthis.startOrderTimer();\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t// 存储订单信息到本地\r\n\t\t\t\t\t\t\ttry {\r\n\t\t\t\t\t\t\t\tconst key = `order_${this.orderId}`;\r\n\t\t\t\t\t\t\t\tuni.setStorageSync(key, JSON.stringify(orderInfo));\r\n\t\t\t\t\t\t\t\tconsole.log('订单信息已存储到本地');\r\n\t\t\t\t\t\t\t} catch (e) {\r\n\t\t\t\t\t\t\t\tconsole.error('存储订单信息到本地失败:', e);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\r\n\t\t\t\t\t\t\tresolve(orderInfo);\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tconst errorMsg = res.data ? res.data.message || '未知错误' : '未知错误';\r\n\t\t\t\t\t\t\tconsole.error('获取订单状态失败:', errorMsg);\r\n\t\t\t\t\t\t\treject(new Error('获取订单状态失败: ' + errorMsg));\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\tconsole.error('获取订单状态请求失败:', err);\r\n\t\t\t\t\t\treject(err);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 开始订单计时\r\n\t\tstartOrderTimer() {\r\n\t\t\t// 实现订单计时逻辑\r\n\t\t\t// ...\r\n\t\t},\r\n\t\t\r\n\t\t// 保存锁状态\r\n\t\tsaveLockState(isOpen) {\r\n\t\t\t// 可以将锁状态保存到本地存储或其他地方\r\n\t\t\t// ...\r\n\t\t},\r\n\t\t\r\n\t\t// 播放成功提示音\r\n\t\tplaySuccessSound() {\r\n\t\t\tconsole.log('播放支付成功音效');\r\n\t\t\t\r\n\t\t\t// 对于微信小程序环境\r\n\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\ttry {\r\n\t\t\t\tif (this.successAudio) {\r\n\t\t\t\t\t// 重新设置src确保能够再次播放\r\n\t\t\t\t\tthis.successAudio.src = \"https://wuji1112-1312674791.cos.ap-guangzhou.myqcloud.com/static/audio/ttsmaker-file-2025-6-11-20-49-17.mp3\";\r\n\t\t\t\t\t// 背景音频管理器需要设置autoplay=true才会播放\r\n\t\t\t\t\tthis.successAudio.autoplay = true;\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 添加振动反馈作为辅助\r\n\t\t\t\t\tif (wx && wx.vibrateShort) {\r\n\t\t\t\t\t\twx.vibrateShort({\r\n\t\t\t\t\t\t\ttype: 'medium'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tconsole.warn('音频对象未初始化');\r\n\t\t\t\t\t// 尝试重新初始化\r\n\t\t\t\t\tthis.initAudio();\r\n\t\t\t\t\t// 延迟设置src并播放\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tif (this.successAudio) {\r\n\t\t\t\t\t\t\tthis.successAudio.autoplay = true;\r\n\t\t\t\t\t\t\tthis.successAudio.src = \"https://wuji1112-1312674791.cos.ap-guangzhou.myqcloud.com/static/audio/ttsmaker-file-2025-6-11-20-49-17.mp3\";\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}, 300);\r\n\t\t\t\t\t// 使用系统振动反馈\r\n\t\t\t\t\tif (wx && wx.vibrateShort) {\r\n\t\t\t\t\t\twx.vibrateShort({\r\n\t\t\t\t\t\t\ttype: 'medium'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('播放音频失败:', error);\r\n\t\t\t\t// 备用：使用振动反馈\r\n\t\t\t\tif (wx && wx.vibrateShort) {\r\n\t\t\t\t\twx.vibrateShort({\r\n\t\t\t\t\t\ttype: 'medium'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t// #endif\r\n\t\t\t\r\n\t\t\t// 非微信小程序环境\r\n\t\t\t// #ifndef MP-WEIXIN\r\n\t\t\tif (this.successAudio) {\r\n\t\t\t\ttry {\r\n\t\t\t\t\t// 重置播放位置并播放\r\n\t\t\t\t\tthis.successAudio.stop();\r\n\t\t\t\t\tthis.successAudio.seek(0);\r\n\t\t\t\t\tthis.successAudio.play();\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('播放音频失败:', error);\r\n\t\t\t\t\t// 尝试替代方案\r\n\t\t\t\t\tthis.tryAlternativeAudioPlay();\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\tconsole.warn('音频对象未初始化');\r\n\t\t\t\t// 尝试重新初始化\r\n\t\t\t\tthis.initAudio();\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tif (this.successAudio) {\r\n\t\t\t\t\t\ttry {\r\n\t\t\t\t\t\t\tthis.successAudio.play();\r\n\t\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\t\tconsole.error('延迟播放音频失败:', error);\r\n\t\t\t\t\t\t\tthis.tryAlternativeAudioPlay();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.tryAlternativeAudioPlay();\r\n\t\t\t\t\t}\r\n\t\t\t\t}, 300);\r\n\t\t\t}\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\t\r\n\t\t// 尝试替代方案播放音频\r\n\t\ttryAlternativeAudioPlay() {\r\n\t\t\tconsole.log('尝试替代方案播放音频');\r\n\t\t\t\r\n\t\t\t// 使用微信小程序原生API播放\r\n\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\tif (wx && wx.createInnerAudioContext) {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst innerAudio = wx.createInnerAudioContext();\r\n\t\t\t\t\tinnerAudio.autoplay = true;\r\n\t\t\t\t\tinnerAudio.src = \"https://wuji1112-1312674791.cos.ap-guangzhou.myqcloud.com/static/audio/ttsmaker-file-2025-6-11-20-49-17.mp3\";\r\n\t\t\t\t\tinnerAudio.onError((err) => {\r\n\t\t\t\t\t\tconsole.error('替代音频播放失败:', err);\r\n\t\t\t\t\t\t// 如果还是失败，尝试使用短音效API\r\n\t\t\t\t\t\tthis.tryShortAudio();\r\n\t\t\t\t\t});\r\n\t\t\t\t} catch (e) {\r\n\t\t\t\t\tconsole.error('替代音频方案失败:', e);\r\n\t\t\t\t\tthis.tryShortAudio();\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\tthis.tryShortAudio();\r\n\t\t\t}\r\n\t\t\t// #endif\r\n\t\t\t\r\n\t\t\t// 非小程序环境\r\n\t\t\t// #ifndef MP-WEIXIN\r\n\t\t\ttry {\r\n\t\t\t\tconst audio = new Audio();\r\n\t\t\t\taudio.src = \"https://wuji1112-1312674791.cos.ap-guangzhou.myqcloud.com/static/audio/ttsmaker-file-2025-6-11-20-49-17.mp3\";\r\n\t\t\t\taudio.play().catch(err => console.error('HTML5音频播放失败:', err));\r\n\t\t\t} catch (e) {\r\n\t\t\t\tconsole.error('替代音频播放失败:', e);\r\n\t\t\t}\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\t\r\n\t\t// 尝试使用短音效API\r\n\t\ttryShortAudio() {\r\n\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\tif (wx && wx.playBackgroundAudio) {\r\n\t\t\t\twx.playBackgroundAudio({\r\n\t\t\t\t\tdataUrl: \"https://wuji1112-1312674791.cos.ap-guangzhou.myqcloud.com/static/audio/ttsmaker-file-2025-6-11-20-49-17.mp3\",\r\n\t\t\t\t\ttitle: '支付成功提示音',\r\n\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\tconsole.error('背景音频播放失败:', err);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t} else if (wx && wx.createAudioContext) {\r\n\t\t\t\t// 尝试使用系统提示音\r\n\t\t\t\twx.vibrateShort({\r\n\t\t\t\t\ttype: 'medium'\r\n\t\t\t\t});\r\n\t\t\t\tconsole.log('使用振动提示代替音频');\r\n\t\t\t}\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\t\r\n\t\t// 播放错误提示音\r\n\t\tplayErrorSound() {\r\n\t\t\t// 实现播放错误提示音的逻辑\r\n\t\t\t// ...\r\n\t\t},\r\n\t\t\r\n\t\t// 页面背景图片加载完成\r\n\t\tonBackgroundImageLoaded() {\r\n\t\t\tthis.isPageLoaded = true;\r\n\t\t},\r\n\t\t\r\n\t\t// 返回上一页\r\n\t\tgoBack() {\r\n\t\t\tuni.navigateBack();\r\n\t\t},\r\n\t\t\r\n\r\n\t\t\r\n\t\t// 设置蓝牙状态监听\r\n\t\tsetupBluetoothListeners() {\r\n\t\t\t// 监听蓝牙适配器状态变化\r\n\t\t\tuni.onBluetoothAdapterStateChange((res) => {\r\n\t\t\t\tconsole.log('蓝牙适配器状态变化:', res);\r\n\t\t\t\t\r\n\t\t\t\t// 如果蓝牙被关闭，更新连接状态\r\n\t\t\t\tif (!res.available) {\r\n\t\t\t\t\tthis.isConnected = false;\r\n\t\t\t\t\tthis.isConnecting = false;\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 显示提示\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '蓝牙已关闭，请开启蓝牙',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t} else if (res.available && this.deviceMac && !this.isConnected && !this.isConnecting) {\r\n\t\t\t\t\t// 蓝牙被重新打开，尝试重新连接\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tthis.debounceConnect();\r\n\t\t\t\t\t}, 1000);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\t\r\n\t\t\t// 监听蓝牙连接状态变化\r\n\t\t\tuni.onBLEConnectionStateChange((res) => {\r\n\t\t\t\tconsole.log('蓝牙连接状态变化:', res);\r\n\t\t\t\t\r\n\t\t\t\t// 如果连接断开，更新状态\r\n\t\t\t\tif (!res.connected && this.isConnected) {\r\n\t\t\t\t\tthis.isConnected = false;\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 如果页面活跃，尝试重新连接\r\n\t\t\t\t\tif (this.isPageActive && this.deviceMac) {\r\n\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\tthis.debounceConnect();\r\n\t\t\t\t\t\t}, 1000);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 通过设备ID获取设备信息\r\n\t\tgetDeviceInfoById(deviceId) {\r\n\t\t\tconsole.log('通过ID获取设备信息:', deviceId);\r\n\t\t\t\r\n\t\t\t// 获取API基础URL\r\n\t\t\tconst baseUrl = getAPIBaseUrl();\r\n\t\t\tconst url = `${baseUrl}/api/wx/miniapp/device/status/${deviceId}`;\r\n\t\t\t\r\n\t\t\t// 请求设备信息\r\n\t\t\tuni.request({\r\n\t\t\t\turl: url,\r\n\t\t\t\tmethod: 'GET',\r\n\t\t\t\theader: {\r\n\t\t\t\t\t'Authorization': uni.getStorageSync('token')\r\n\t\t\t\t},\r\n\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\tif (res.statusCode === 200 && res.data && res.data.code === 200) {\r\n\t\t\t\t\t\tconsole.log('获取设备信息成功:', res.data);\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 保存设备信息\r\n\t\t\t\t\t\tthis.deviceInfo = res.data.data;\r\n\t\t\t\t\t\tthis.deviceId = this.deviceInfo.id || deviceId;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 设置设备名称和MAC地址\r\n\t\t\t\t\t\tif (this.deviceInfo.deviceName) {\r\n\t\t\t\t\t\t\tthis.deviceName = this.deviceInfo.deviceName;\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t// 如果没有设备名称，尝试使用ID生成一个\r\n\t\t\t\t\t\t\tthis.deviceName = 'LOCK' + deviceId;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 设置MAC地址\r\n\t\t\t\t\t\tif (this.deviceInfo.macAddress) {\r\n\t\t\t\t\t\t\t// 格式化MAC地址，添加冒号\r\n\t\t\t\t\t\t\tconst rawMac = this.deviceInfo.macAddress.replace(/:/g, '').replace(/-/g, '').toUpperCase();\r\n\t\t\t\t\t\t\tif (rawMac.length === 12) {\r\n\t\t\t\t\t\t\t\tthis.deviceMac = rawMac.match(/.{1,2}/g).join(':');\r\n\t\t\t\t\t\t\t\tconsole.log('设置设备MAC地址:', this.deviceMac);\r\n\t\t\t\t\t\t\t\tlockService.setDeviceMac(this.deviceMac);\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tconsole.warn('MAC地址格式不正确:', this.deviceInfo.macAddress);\r\n\t\t\t\t\t\t\t\t// 尝试使用设备ID生成MAC地址\r\n\t\t\t\t\t\t\t\tthis.deviceMac = this.formatDeviceIdToMac(deviceId);\r\n\t\t\t\t\t\t\t\tconsole.log('使用设备ID生成MAC地址:', this.deviceMac);\r\n\t\t\t\t\t\t\t\tlockService.setDeviceMac(this.deviceMac);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t// 如果没有MAC地址，尝试使用设备ID生成一个\r\n\t\t\t\t\t\t\tthis.deviceMac = this.formatDeviceIdToMac(deviceId);\r\n\t\t\t\t\t\t\tconsole.log('使用设备ID生成MAC地址:', this.deviceMac);\r\n\t\t\t\t\t\t\tlockService.setDeviceMac(this.deviceMac);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 设置预期设备名称\r\n\t\t\t\t\t\tconsole.log('设置预期设备名称:', this.deviceName);\r\n\t\t\t\t\t\tlockService.setExpectedDeviceName(this.deviceName);\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 获取设备价格\r\n\t\t\t\t\t\tif (this.deviceInfo.price) {\r\n\t\t\t\t\t\t\tthis.hourlyRate = parseFloat(this.deviceInfo.price) || 0;\r\n\t\t\t\t\t\t\tthis.priceText = '￥' + this.hourlyRate.toFixed(2) + '/小时';\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 初始化蓝牙环境\r\n\t\t\t\t\t\tthis.preinitBluetooth();\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 如果在页面活跃状态下，自动尝试连接设备\r\n\t\t\t\t\t\tif (this.isPageActive) {\r\n\t\t\t\t\t\t\tconsole.log('页面活跃状态，准备尝试连接设备');\r\n\t\t\t\t\t\t\tthis.autoConnectTimer = setTimeout(() => {\r\n\t\t\t\t\t\t\t\tthis.tryConnectDevice();\r\n\t\t\t\t\t\t\t}, 1000);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tconsole.error('获取设备信息失败:', res);\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '获取设备信息失败',\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\tfail: (err) => {\r\n\t\t\t\t\tconsole.error('请求设备信息失败:', err);\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '网络错误，请重试',\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 格式化设备ID为MAC地址\r\n\t\tformatDeviceIdToMac(deviceId) {\r\n\t\t\t// 如果设备ID是纯数字格式\r\n\t\t\tif (/^\\d+$/.test(deviceId)) {\r\n\t\t\t\t// 补齐6位数字\r\n\t\t\t\tconst paddedId = deviceId.padStart(6, '0');\r\n\t\t\t\t// 转换为16进制并分段\r\n\t\t\t\treturn 'AB:' + paddedId.match(/.{1,2}/g).join(':').toUpperCase();\r\n\t\t\t} \r\n\t\t\t// 如果设备ID是其他格式\r\n\t\t\telse {\r\n\t\t\t\t// 取前12个字符，如果不足则补0\r\n\t\t\t\tlet macChars = deviceId.replace(/[^0-9a-fA-F]/g, '').substring(0, 12).padEnd(12, '0').toUpperCase();\r\n\t\t\t\t// 分段并添加冒号\r\n\t\t\t\treturn macChars.match(/.{1,2}/g).join(':');\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 显示错误提示\r\n\t\tshowError(message) {\r\n\t\t\tconsole.error('错误:', message);\r\n\t\t\t\r\n\t\t\t// 显示错误提示\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: message,\r\n\t\t\t\ticon: 'none',\r\n\t\t\t\tduration: 3000\r\n\t\t\t});\r\n\t\t},\r\n\t\t// 初始化支付流程\r\n\t\tinitPayment() {\r\n\t\t\tconsole.log('初始化支付流程');\r\n\t\t\tthis.showPaymentModal = true;\r\n\t\t\tthis.paymentLoading = true;\r\n\t\t\tthis.paymentSuccess = false;\r\n\t\t\t\r\n\t\t\t// 创建订单并支付\r\n\t\t\tconst createOrderIfNeeded = () => {\r\n\t\t\t\t// 检查是否已有订单ID\r\n\t\t\t\tif (this.orderId) {\r\n\t\t\t\t\tconsole.log('已有订单ID，直接返回:', this.orderId);\r\n\t\t\t\t\treturn Promise.resolve({\r\n\t\t\t\t\t\torderId: this.orderId\r\n\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 创建新订单\r\n\t\t\t\t\tconsole.log('创建新订单');\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 确保设备编码不为空\r\n\t\t\t\t\tif (!this.deviceInfo || (!this.deviceInfo.deviceCode && !this.deviceInfo.bindCode && !this.deviceInfo.macAddress)) {\r\n\t\t\t\t\t\tconsole.error('设备编码不能为空');\r\n\t\t\t\t\t\treturn Promise.reject({\r\n\t\t\t\t\t\t\tmessage: '设备编码不能为空，请重新扫描设备'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 使用设备信息中的绑定码、MAC地址或设备编号作为deviceCode\r\n\t\t\t\t\tconst deviceCode = this.deviceInfo.bindCode || this.deviceInfo.macAddress || this.deviceInfo.deviceNo || '';\r\n\t\t\t\t\tconsole.log('使用设备编码:', deviceCode);\r\n\t\t\t\t\t\r\n\t\t\t\t\treturn lockService.createOrder({\r\n\t\t\t\t\t\tdeviceCode: deviceCode,\r\n\t\t\t\t\t\tduration: 60 // 默认60分钟\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t};\r\n\t\t},\r\n\t\t// 尝试自动连接设备\r\n\t\ttryConnectDevice() {\r\n\t\t\t// 先检查实际的连接状态\r\n\t\t\tconst deviceInfo = lockService.getDeviceInfo();\r\n\t\t\tconst actualConnected = deviceInfo && (deviceInfo.connected === true || deviceInfo.isConnected === true);\r\n\t\t\tconst lockServiceConnected = deviceInfo && deviceInfo.connected;\r\n\r\n\t\t\tconsole.log('尝试连接设备 - UI连接状态:', this.isConnected, 'lockService连接状态:', lockServiceConnected, '实际连接状态:', actualConnected, '连接中状态:', this.isConnecting);\r\n\r\n\t\t\t// 如果lockService已连接，同步UI状态\r\n\t\t\tif (lockServiceConnected && !this.isConnected) {\r\n\t\t\t\tconsole.log('lockService已连接，同步UI状态');\r\n\t\t\t\tthis.isConnected = true;\r\n\t\t\t\tthis.isConnecting = false;\r\n\t\t\t\tthis.updateUIState();\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t// 如果实际已连接，更新状态并返回\r\n\t\t\tif (actualConnected && !this.isConnected) {\r\n\t\t\t\tconsole.log('设备实际已连接，更新UI状态');\r\n\t\t\t\tthis.isConnected = true;\r\n\t\t\t\tthis.isConnecting = false;\r\n\t\t\t\tthis.updateUIState();\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t// 如果已连接，不重复连接\r\n\t\t\tif (this.isConnected && lockServiceConnected) {\r\n\t\t\t\tconsole.log('设备已连接，不重复连接');\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t// 如果正在连接中，避免重复操作\r\n\t\t\tif (this.isConnecting) {\r\n\t\t\t\tconsole.log('设备正在连接中，请稍候');\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 设置连接中状态\r\n\t\t\tthis.isConnecting = true;\r\n\r\n\t\t\t// 设置连接超时处理\r\n\t\t\tconst connectTimeout = setTimeout(() => {\r\n\t\t\t\tif (this.isConnecting) {\r\n\t\t\t\t\tconsole.log('连接超时，重置连接状态');\r\n\t\t\t\t\tthis.isConnecting = false;\r\n\t\t\t\t\tthis.updateUIState();\r\n\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '连接超时，请重试',\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t}, 15000); // 15秒超时\r\n\r\n\t\t\t// 检查是否有设备MAC地址或名称\r\n\t\t\tif (\r\n\t\t\t\t!this.deviceMac &&\r\n\t\t\t\t!this.deviceName &&\r\n\t\t\t\t!this.deviceId &&\r\n\t\t\t\t!(this.deviceInfo && (this.deviceInfo.deviceNo || this.deviceInfo.id))\r\n\t\t\t) {\r\n\t\t\t\tconsole.error('设备标识信息都为空，无法连接');\r\n\t\t\t\tthis.isConnecting = false;\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '设备信息不完整，无法连接',\r\n\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\tduration: 2000\r\n\t\t\t\t});\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 设置锁服务的设备MAC地址\r\n\t\t\tif (this.deviceMac) {\r\n\t\t\t\tlockService.setDeviceMac(this.deviceMac);\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 设置预期设备名称\r\n\t\t\tif (this.deviceName) {\r\n\t\t\t\tlockService.setExpectedDeviceName(this.deviceName);\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 使用设备名称连接设备\r\n\t\t\tif (this.deviceName) {\r\n\t\t\t\tconsole.log('使用设备名称连接设备:', this.deviceName);\r\n\t\t\t\t\r\n\t\t\t\t// 直接连接设备\r\n\t\t\t\tlockService.directConnect(this.deviceName)\r\n\t\t\t\t\t.then(res => {\r\n\t\t\t\t\t\tconsole.log('连接命令执行成功:', res);\r\n\t\t\t\t\t\t// 清除超时定时器\r\n\t\t\t\t\t\tclearTimeout(connectTimeout);\r\n\t\t\t\t\t\t// 注意：不在这里设置连接状态，让lockService的回调来处理\r\n\t\t\t\t\t\tconsole.log('等待lockService回调确认连接状态');\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch(err => {\r\n\t\t\t\t\t\tconsole.error('连接失败:', err);\r\n\t\t\t\t\t\t// 清除超时定时器\r\n\t\t\t\t\t\tclearTimeout(connectTimeout);\r\n\t\t\t\t\t\tthis.isConnecting = false;\r\n\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '设备连接失败，请重试',\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t});\r\n\r\n\t\t\t\t\t\t// 更新UI状态\r\n\t\t\t\t\t\tthis.debouncedUpdateUIState();\r\n\t\t\t\t\t});\r\n\t\t\t}\r\n\t\t\t// 使用MAC地址连接设备\r\n\t\t\telse if (this.deviceMac) {\r\n\t\t\t\tconsole.log('使用MAC地址连接设备:', this.deviceMac);\r\n\t\t\t\t\r\n\t\t\t\t// 连接设备\r\n\t\t\t\tlockService.connectDevice(this.deviceMac)\r\n\t\t\t\t\t.then(res => {\r\n\t\t\t\t\t\tconsole.log('连接命令执行成功:', res);\r\n\t\t\t\t\t\t// 清除超时定时器\r\n\t\t\t\t\t\tclearTimeout(connectTimeout);\r\n\t\t\t\t\t\t// 注意：不在这里设置连接状态，让lockService的回调来处理\r\n\t\t\t\t\t\tconsole.log('等待lockService回调确认连接状态');\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch(err => {\r\n\t\t\t\t\t\tconsole.error('连接失败:', err);\r\n\t\t\t\t\t\t// 清除超时定时器\r\n\t\t\t\t\t\tclearTimeout(connectTimeout);\r\n\t\t\t\t\t\tthis.isConnecting = false;\r\n\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '设备连接失败，请重试',\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t});\r\n\r\n\t\t\t\t\t\t// 更新UI状态\r\n\t\t\t\t\t\tthis.debouncedUpdateUIState();\r\n\t\t\t\t\t});\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 检查订单状态并决定是否开门\r\n\t\tcheckOrderAndOpenDoor() {\r\n\t\t\t// 先检查实际的连接状态\r\n\t\t\tconst deviceInfo = lockService.getDeviceInfo();\r\n\t\t\tconst actualConnected = deviceInfo && (deviceInfo.connected === true || deviceInfo.isConnected === true);\r\n\r\n\t\t\tconsole.log('检查订单状态并决定是否开门，当前订单ID:', this.orderId, '支付状态:', this.isPaid, '连接状态:', this.isConnected, '实际连接状态:', actualConnected, '开门完成状态:', this.doorOpenCompleted);\r\n\r\n\t\t\t// 如果设备实际未连接，不执行开门\r\n\t\t\tif (!actualConnected) {\r\n\t\t\t\tconsole.log('设备实际未连接，不执行开门操作');\r\n\t\t\t\t// 尝试重新连接\r\n\t\t\t\tif (this.deviceMac && this.isPaid) {\r\n\t\t\t\t\tconsole.log('尝试重新连接设备');\r\n\t\t\t\t\tthis.tryConnectDevice();\r\n\t\t\t\t}\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t// 如果实际已连接但状态不一致，更新状态\r\n\t\t\tif (actualConnected && !this.isConnected) {\r\n\t\t\t\tconsole.log('检测到实际已连接，更新连接状态');\r\n\t\t\t\tthis.isConnected = true;\r\n\t\t\t\tthis.isConnecting = false;\r\n\t\t\t}\r\n\r\n\t\t\t// 如果没有订单ID，不执行开门操作\r\n\t\t\tif (!this.orderId) {\r\n\t\t\t\tconsole.log('没有订单ID，不执行开门操作');\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t// 如果已经完成开门操作，不再重复开门\r\n\t\t\tif (this.doorOpenCompleted) {\r\n\t\t\t\tconsole.log('已完成开门操作，不再重复开门');\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 如果未支付，先查询一次订单支付状态，确认最新支付状态\r\n\t\t\tif (!this.isPaid && this.orderStatus !== 1) {\r\n\t\t\t\tconsole.log('本地状态显示订单未支付，查询最新订单支付状态');\r\n\t\t\t\tthis.queryPaymentStatus()\r\n\t\t\t\t\t.then(paymentInfo => {\r\n\t\t\t\t\t\t// 检查是否已支付\r\n\t\t\t\t\t\tif (paymentInfo.isPaid) {\r\n\t\t\t\t\t\t\tconsole.log('查询到订单已支付，更新支付状态');\r\n\t\t\t\t\t\t\tthis.isPaid = true;\r\n\t\t\t\t\t\t\tthis.orderStatus = 1;\r\n\t\t\t\t\t\t\tthis.paymentSuccess = true;\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t// 如果已连接设备，继续开门流程\r\n\t\t\t\t\t\t\tif (this.isConnected) {\r\n\t\t\t\t\t\t\t\tconsole.log('确认支付成功，设备已连接，继续开门');\r\n\t\t\t\t\t\t\t\t// 延迟执行，确保状态更新完成\r\n\t\t\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t\t\tthis.performOpenDoor();\r\n\t\t\t\t\t\t\t\t}, 500);\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t// 未连接设备，先尝试连接\r\n\t\t\t\t\t\t\t\tconsole.log('确认支付成功，设备未连接，先尝试连接');\r\n\t\t\t\t\t\t\t\tthis.tryConnectDevice();\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tconsole.log('确认订单未支付，不执行开门操作');\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: '请先完成支付',\r\n\t\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch(err => {\r\n\t\t\t\t\t\tconsole.error('查询订单支付状态失败，尝试查询完整订单状态:', err);\r\n\t\t\t\t\t\t// 如果支付状态接口失败，回退到查询完整订单状态\r\n\t\t\t\t\t\tthis.queryOrderStatus()\r\n\t\t\t\t\t\t\t.then(orderInfo => {\r\n\t\t\t\t\t\t\t\t// 检查是否已支付 - 同时检查status和payStatus\r\n\t\t\t\t\t\t\t\tconst isPaid = orderInfo.status === 1 || orderInfo.payStatus === 1;\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\tif (isPaid) {\r\n\t\t\t\t\t\t\t\t\tconsole.log('查询到订单已支付，更新支付状态');\r\n\t\t\t\t\t\t\t\t\tthis.isPaid = true;\r\n\t\t\t\t\t\t\t\t\tthis.orderStatus = 1;\r\n\t\t\t\t\t\t\t\t\tthis.paymentSuccess = true;\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t// 如果已连接设备，继续开门流程\r\n\t\t\t\t\t\t\t\t\tif (this.isConnected) {\r\n\t\t\t\t\t\t\t\t\t\tconsole.log('确认支付成功，设备已连接，继续开门');\r\n\t\t\t\t\t\t\t\t\t\t// 延迟执行，确保状态更新完成\r\n\t\t\t\t\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t\t\t\t\tthis.performOpenDoor();\r\n\t\t\t\t\t\t\t\t\t\t}, 500);\r\n\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t// 未连接设备，先尝试连接\r\n\t\t\t\t\t\t\t\t\t\tconsole.log('确认支付成功，设备未连接，先尝试连接');\r\n\t\t\t\t\t\t\t\t\t\tthis.tryConnectDevice();\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tconsole.log('确认订单未支付，不执行开门操作');\r\n\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\ttitle: '请先完成支付',\r\n\t\t\t\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t.catch(err => {\r\n\t\t\t\t\t\t\t\tconsole.error('查询订单状态失败:', err);\r\n\t\t\t\t\t\t\t\t// 显示错误提示\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ttitle: '查询订单状态失败，请重试',\r\n\t\t\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t});\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 如果未连接设备，先尝试连接设备\r\n\t\t\tif (!this.isConnected && !actualConnected) {\r\n\t\t\t\tconsole.log('设备未连接，先尝试连接设备');\r\n\t\t\t\tthis.tryConnectDevice();\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 确保订单ID已设置到lockService\r\n\t\t\tif (lockService.getOrderInfo().orderId !== this.orderId) {\r\n\t\t\t\tconsole.log('自动开门前重新设置订单ID到lockService:', this.orderId);\r\n\t\t\t\tlockService.setOrderId(this.orderId);\r\n\t\t\t\t\r\n\t\t\t\t// 短暂延迟，确保订单ID设置生效\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tthis.performOpenDoor();\r\n\t\t\t\t}, 500); // 延长延迟时间，确保订单ID设置完全生效\r\n\t\t\t} else {\r\n\t\t\t\t// 直接执行开门\r\n\t\t\t\tthis.performOpenDoor();\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 执行开门操作\r\n\t\tperformOpenDoor() {\r\n\t\t\t// 如果已支付且连接成功，自动开门\r\n\t\t\tif (this.isPaid && this.isConnected) {\r\n\t\t\t\tconsole.log('已支付且已连接，自动触发开门');\r\n\t\t\t\tthis.doorOpenProcessed = true;\r\n\t\t\t\t\r\n\t\t\t\t// 设置开门超时保护\r\n\t\t\t\tconst openDoorTimeout = setTimeout(() => {\r\n\t\t\t\t\tconsole.log('开门操作超时，尝试强制开门');\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 尝试强制开门\r\n\t\t\t\t\tthis.forceOpenDoor();\r\n\t\t\t\t}, 10000); // 10秒超时\r\n\t\t\t\t\r\n\t\t\t\t// 直接使用强制模式开门，忽略订单状态检查，添加singleCommand参数和operationType参数\r\n\t\t\t\tlockService.openLock({ \r\n\t\t\t\t\tignoreOrderStatus: true, \r\n\t\t\t\t\tforce: true, \r\n\t\t\t\t\tretry: true, \r\n\t\t\t\t\tsingleCommand: true,\r\n\t\t\t\t\toperationType: 1 // 1表示开锁操作，用于设备状态上报\r\n\t\t\t\t})\r\n\t\t\t\t\t.then(res => {\r\n\t\t\t\t\t\tconsole.log('自动开门成功:', res);\r\n\r\n\t\t\t\t\t\t// 清除超时保护\r\n\t\t\t\t\t\tclearTimeout(openDoorTimeout);\r\n\r\n\t\t\t\t\t\t// 设置开门完成标志，防止重复开门\r\n\t\t\t\t\t\tthis.doorOpenCompleted = true;\r\n\r\n\t\t\t\t\t\t// 更新锁状态\r\n\t\t\t\t\t\tthis.isLockOpen = true;\r\n\r\n\t\t\t\t\t\t// 更新订单使用状态\r\n\t\t\t\t\t\tthis.useStatus = 1;\r\n\r\n\t\t\t\t\t\t// 更新UI状态\r\n\t\t\t\t\t\tthis.updateUIState();\r\n\r\n\t\t\t\t\t\t// 显示开门成功提示\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '开门成功',\r\n\t\t\t\t\t\t\ticon: 'success',\r\n\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t});\r\n\r\n\t\t\t\t\t\t// 播放成功音效\r\n\t\t\t\t\t\tthis.playSuccessSound();\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch(err => {\r\n\t\t\t\t\t\tconsole.error('自动开门失败:', err);\r\n\r\n\t\t\t\t\t\t// 清除超时保护\r\n\t\t\t\t\t\tclearTimeout(openDoorTimeout);\r\n\r\n\t\t\t\t\t\t// 如果是连接问题，尝试备用开门方法\r\n\t\t\t\t\t\tif (err.message && err.message.includes('设备未连接')) {\r\n\t\t\t\t\t\t\tconsole.log('自动开门连接失败，尝试备用方法');\r\n\t\t\t\t\t\t\tthis.tryAlternativeOpenDoor();\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t// 尝试强制开门\r\n\t\t\t\t\t\t\tthis.forceOpenDoor();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t} else {\r\n\t\t\t\tconsole.log('支付状态或连接状态不满足开门条件');\r\n\t\t\t\tif (!this.isPaid) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请先完成支付',\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t});\r\n\t\t\t\t} else if (!this.isConnected) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请先连接设备',\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t});\r\n\t\t\t\t\t// 尝试连接设备\r\n\t\t\t\t\tthis.tryConnectDevice();\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 重置开门处理标志，允许再次尝试\r\n\t\t\t\tthis.doorOpenProcessed = false;\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 重置开门状态\r\n\t\tresetDoorState() {\r\n\t\t\tconsole.log('重置开门状态');\r\n\t\t\tthis.doorOpenProcessed = false;\r\n\t\t\tthis.doorOpenCompleted = false;\r\n\t\t\tthis.doorOpenInProgress = false;\r\n\t\t},\r\n\r\n\t\t// 保存支付状态到本地存储\r\n\t\tsavePaymentState() {\r\n\t\t\tif (this.orderId) {\r\n\t\t\t\tconst paymentState = {\r\n\t\t\t\t\torderId: this.orderId,\r\n\t\t\t\t\tisPaid: this.isPaid,\r\n\t\t\t\t\tpaymentSuccess: this.paymentSuccess,\r\n\t\t\t\t\torderStatus: this.orderStatus,\r\n\t\t\t\t\ttimestamp: Date.now()\r\n\t\t\t\t};\r\n\r\n\t\t\t\tconst key = `payment_state_${this.orderId}`;\r\n\t\t\t\tuni.setStorageSync(key, paymentState);\r\n\t\t\t\tconsole.log('保存支付状态到本地存储:', paymentState);\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 从本地存储恢复支付状态\r\n\t\trestorePaymentState() {\r\n\t\t\tif (this.orderId) {\r\n\t\t\t\tconst key = `payment_state_${this.orderId}`;\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst paymentState = uni.getStorageSync(key);\r\n\t\t\t\t\tif (paymentState && paymentState.timestamp) {\r\n\t\t\t\t\t\t// 检查数据是否过期（24小时）\r\n\t\t\t\t\t\tconst now = Date.now();\r\n\t\t\t\t\t\tconst maxAge = 24 * 60 * 60 * 1000; // 24小时\r\n\r\n\t\t\t\t\t\tif (now - paymentState.timestamp < maxAge) {\r\n\t\t\t\t\t\t\tconsole.log('从本地存储恢复支付状态:', paymentState);\r\n\r\n\t\t\t\t\t\t\t// 只有当前状态为未支付时才恢复\r\n\t\t\t\t\t\t\tif (!this.isPaid && paymentState.isPaid) {\r\n\t\t\t\t\t\t\t\tthis.isPaid = paymentState.isPaid;\r\n\t\t\t\t\t\t\t\tthis.paymentSuccess = paymentState.paymentSuccess;\r\n\t\t\t\t\t\t\t\tthis.orderStatus = paymentState.orderStatus;\r\n\r\n\t\t\t\t\t\t\t\tconsole.log('成功恢复支付状态 - 已支付');\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t// 数据过期，删除\r\n\t\t\t\t\t\t\tconsole.log('本地支付状态数据过期，删除');\r\n\t\t\t\t\t\t\tuni.removeStorageSync(key);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (err) {\r\n\t\t\t\t\tconsole.error('恢复支付状态失败:', err);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 智能重新认证并开门\r\n\t\tforceReauthenticateAndUnlock(deviceInfo) {\r\n\t\t\tconsole.log('智能重新认证并开门');\r\n\r\n\t\t\treturn new Promise((resolve, reject) => {\r\n\t\t\t\t// 1. 先尝试直接开门，不断开连接\r\n\t\t\t\tconsole.log('步骤1: 尝试直接开门');\r\n\t\t\t\tthis.executeUnlockCommand(deviceInfo)\r\n\t\t\t\t\t.then(result => {\r\n\t\t\t\t\t\tconsole.log('直接开门成功:', result);\r\n\t\t\t\t\t\tresolve(result);\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch(directError => {\r\n\t\t\t\t\t\tconsole.log('直接开门失败，开始重连流程:', directError);\r\n\r\n\t\t\t\t\t\t// 2. 如果直接开门失败，才进行重连\r\n\t\t\t\t\t\tconsole.log('步骤2: 开始重连流程');\r\n\t\t\t\t\t\tthis.performReconnectAndUnlock(deviceInfo)\r\n\t\t\t\t\t\t\t.then(resolve)\r\n\t\t\t\t\t\t\t.catch(reject);\r\n\t\t\t\t\t});\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t// 执行重连并开门\r\n\t\tperformReconnectAndUnlock(deviceInfo) {\r\n\t\t\tconsole.log('执行重连并开门');\r\n\r\n\t\t\t// 检查是否已经在重连中\r\n\t\t\tif (this.isReconnecting || this.reconnectLock) {\r\n\t\t\t\tconsole.log('已经在重连中，跳过重复重连');\r\n\t\t\t\treturn Promise.reject(new Error('重连已在进行中'));\r\n\t\t\t}\r\n\r\n\t\t\t// 设置重连锁\r\n\t\t\tthis.isReconnecting = true;\r\n\t\t\tthis.reconnectLock = true;\r\n\r\n\t\t\treturn new Promise((resolve, reject) => {\r\n\t\t\t\t// 1. 强制断开连接\r\n\t\t\t\tconsole.log('重连步骤1: 强制断开连接');\r\n\t\t\t\tthis.isManualDisconnect = true; // 标记为手动断开\r\n\t\t\t\tlockService.disconnect()\r\n\t\t\t\t\t.then(() => {\r\n\t\t\t\t\t\tconsole.log('断开连接成功');\r\n\t\t\t\t\t\t// 等待断开完成\r\n\t\t\t\t\t\treturn new Promise(resolve => setTimeout(resolve, 1500));\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch(err => {\r\n\t\t\t\t\t\tconsole.log('断开连接失败，继续执行:', err);\r\n\t\t\t\t\t\treturn Promise.resolve();\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.then(() => {\r\n\t\t\t\t\t\t// 2. 重新连接\r\n\t\t\t\t\t\tconsole.log('重连步骤2: 重新连接设备');\r\n\t\t\t\t\t\treturn lockService.connectDevice(this.deviceMac);\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.then(() => {\r\n\t\t\t\t\t\tconsole.log('重新连接成功');\r\n\t\t\t\t\t\t// 等待连接稳定\r\n\t\t\t\t\t\treturn new Promise(resolve => setTimeout(resolve, 2000));\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.then(() => {\r\n\t\t\t\t\t\t// 3. 验证连接状态\r\n\t\t\t\t\t\tconsole.log('重连步骤3: 验证连接状态');\r\n\t\t\t\t\t\tconst currentDeviceInfo = lockService.getDeviceInfo();\r\n\t\t\t\t\t\tif (!currentDeviceInfo || !currentDeviceInfo.connected) {\r\n\t\t\t\t\t\t\tthrow new Error('设备连接验证失败');\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tconsole.log('连接状态验证成功');\r\n\t\t\t\t\t\treturn Promise.resolve();\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.then(() => {\r\n\t\t\t\t\t\t// 4. 执行开门\r\n\t\t\t\t\t\tconsole.log('重连步骤4: 执行开门');\r\n\t\t\t\t\t\treturn this.executeUnlockCommand(deviceInfo);\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.then(result => {\r\n\t\t\t\t\t\tconsole.log('重连开门成功:', result);\r\n\t\t\t\t\t\tresolve(result);\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch(error => {\r\n\t\t\t\t\t\tconsole.error('重连开门失败:', error);\r\n\t\t\t\t\t\treject(error);\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.finally(() => {\r\n\t\t\t\t\t\t// 释放重连锁\r\n\t\t\t\t\t\tthis.isReconnecting = false;\r\n\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\tthis.reconnectLock = false;\r\n\t\t\t\t\t\t}, 3000); // 3秒后释放锁，防止频繁重连\r\n\t\t\t\t\t});\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t// 执行开门命令\r\n\t\texecuteUnlockCommand(deviceInfo) {\r\n\t\t\tconsole.log('执行开门命令');\r\n\r\n\t\t\t// 开门前最后检查 - lockService层面\r\n\t\t\tconst currentDeviceInfo = lockService.getDeviceInfo();\r\n\t\t\tif (!currentDeviceInfo || !currentDeviceInfo.connected) {\r\n\t\t\t\tconsole.error('开门前检查：lockService层面设备未连接');\r\n\t\t\t\treturn Promise.reject(new Error('lockService层面设备未连接，无法开门'));\r\n\t\t\t}\r\n\r\n\t\t\t// 开门前最后检查 - blueToothManager层面\r\n\t\t\ttry {\r\n\t\t\t\tconst blueToothManager = require('@/utils/blueToothManager.js');\r\n\t\t\t\tif (!blueToothManager.isConnected || !blueToothManager.deviceId) {\r\n\t\t\t\t\tconsole.error('开门前检查：blueToothManager层面设备未连接');\r\n\t\t\t\t\tconsole.log('blueToothManager.isConnected:', blueToothManager.isConnected);\r\n\t\t\t\t\tconsole.log('blueToothManager.deviceId:', blueToothManager.deviceId);\r\n\t\t\t\t\treturn Promise.reject(new Error('blueToothManager层面设备未连接，无法开门'));\r\n\t\t\t\t}\r\n\t\t\t\tconsole.log('开门前检查通过，blueToothManager层面设备已连接');\r\n\t\t\t} catch (e) {\r\n\t\t\t\tconsole.error('获取blueToothManager失败:', e);\r\n\t\t\t\treturn Promise.reject(new Error('无法获取blueToothManager，无法开门'));\r\n\t\t\t}\r\n\r\n\t\t\tconsole.log('开门前检查通过，所有层面设备已连接');\r\n\r\n\t\t\treturn lockService.openLock({\r\n\t\t\t\tignoreOrderStatus: true,\r\n\t\t\t\tforce: true,\r\n\t\t\t\tretry: true,\r\n\t\t\t\tsingleCommand: true,\r\n\t\t\t\toperationType: 1, // 1表示开锁操作，用于设备状态上报\r\n\t\t\t\tdeviceId: deviceInfo?.deviceId, // 明确传递设备ID\r\n\t\t\t\tmac: this.deviceMac, // 明确传递MAC地址\r\n\t\t\t\tname: this.deviceName // 明确传递设备名称\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t// 检查蓝牙状态\r\n\t\tcheckBluetoothState() {\r\n\t\t\tconsole.log('检查蓝牙状态');\r\n\r\n\t\t\treturn new Promise((resolve, reject) => {\r\n\t\t\t\t// 检查蓝牙适配器状态\r\n\t\t\t\tuni.getBluetoothAdapterState({\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tconsole.log('蓝牙适配器状态:', res);\r\n\t\t\t\t\t\tif (res.available) {\r\n\t\t\t\t\t\t\tresolve(res);\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\treject(new Error('蓝牙适配器不可用'));\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\tconsole.error('获取蓝牙适配器状态失败:', err);\r\n\t\t\t\t\t\treject(err);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t// 验证连接状态一致性\r\n\t\tverifyConnectionState() {\r\n\t\t\tconsole.log('验证连接状态一致性');\r\n\r\n\t\t\t// 检查UI状态和lockService状态是否一致\r\n\t\t\tconst uiConnected = this.isConnected;\r\n\t\t\tconst deviceInfo = lockService.getDeviceInfo();\r\n\t\t\tconst lockServiceConnected = deviceInfo && deviceInfo.connected;\r\n\t\t\tconst isPaid = this.isPaid;\r\n\r\n\t\t\tconsole.log('状态检查 - UI连接:', uiConnected, 'lockService连接:', lockServiceConnected, '已支付:', isPaid);\r\n\r\n\t\t\t// 如果已支付但连接状态不一致，尝试修复\r\n\t\t\tif (isPaid && uiConnected !== lockServiceConnected) {\r\n\t\t\t\tconsole.log('检测到连接状态不一致，尝试修复');\r\n\r\n\t\t\t\tif (lockServiceConnected && !uiConnected) {\r\n\t\t\t\t\t// lockService已连接但UI显示未连接，同步UI状态\r\n\t\t\t\t\tconsole.log('同步UI状态为已连接');\r\n\t\t\t\t\tthis.isConnected = true;\r\n\t\t\t\t\tthis.isConnecting = false;\r\n\t\t\t\t\tthis.updateUIState();\r\n\t\t\t\t} else if (!lockServiceConnected && uiConnected) {\r\n\t\t\t\t\t// UI显示已连接但lockService未连接，尝试重新连接\r\n\t\t\t\t\tconsole.log('UI显示已连接但lockService未连接，尝试重新连接');\r\n\t\t\t\t\tif (this.deviceMac && this.deviceName) {\r\n\t\t\t\t\t\tthis.tryConnectDevice();\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// 没有设备信息，重置UI状态\r\n\t\t\t\t\t\tthis.isConnected = false;\r\n\t\t\t\t\t\tthis.isConnecting = false;\r\n\t\t\t\t\t\tthis.updateUIState();\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t// 如果已支付但都未连接，且有设备信息，尝试连接\r\n\t\t\tif (isPaid && !uiConnected && !lockServiceConnected && this.deviceMac && this.deviceName) {\r\n\t\t\t\tconsole.log('已支付但未连接，且有设备信息，尝试自动连接');\r\n\t\t\t\tthis.tryConnectDevice();\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 重新初始化蓝牙环境\r\n\t\treinitializeBluetooth() {\r\n\t\t\tconsole.log('重新初始化蓝牙环境');\r\n\r\n\t\t\t// 保存当前连接状态，避免不必要的重置\r\n\t\t\tconst wasConnected = this.isConnected;\r\n\t\t\tconst wasPaid = this.isPaid;\r\n\r\n\t\t\tconsole.log('重新初始化前状态 - 连接:', wasConnected, '支付:', wasPaid);\r\n\r\n\t\t\t// 先检查蓝牙状态\r\n\t\t\tthis.checkBluetoothState()\r\n\t\t\t\t.then(() => {\r\n\t\t\t\t\t// 蓝牙状态正常，重新初始化lockService\r\n\t\t\t\t\treturn lockService.init();\r\n\t\t\t\t})\r\n\t\t\t\t.then(res => {\r\n\t\t\t\t\tconsole.log('蓝牙环境重新初始化成功:', res);\r\n\r\n\t\t\t\t\t// 重新设置回调\r\n\t\t\t\t\tthis.setupLockServiceCallbacks();\r\n\r\n\t\t\t\t\t// 重新设置设备信息到lockService\r\n\t\t\t\t\tif (this.deviceMac && this.deviceName) {\r\n\t\t\t\t\t\tconsole.log('重新设置设备信息到lockService');\r\n\t\t\t\t\t\tlockService.setDeviceMac(this.deviceMac);\r\n\t\t\t\t\t\tlockService.setExpectedDeviceName(this.deviceName);\r\n\t\t\t\t\t\tif (this.orderId) {\r\n\t\t\t\t\t\t\tlockService.setOrderId(this.orderId);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// 如果之前已连接且已支付，尝试重新连接\r\n\t\t\t\t\tif (wasPaid && (this.deviceMac || this.deviceName)) {\r\n\t\t\t\t\t\tconsole.log('检测到之前已支付，尝试重新连接设备');\r\n\r\n\t\t\t\t\t\t// 先检查lockService的连接状态\r\n\t\t\t\t\t\tconst deviceInfo = lockService.getDeviceInfo();\r\n\t\t\t\t\t\tconst lockServiceConnected = deviceInfo && deviceInfo.connected;\r\n\t\t\t\t\t\tconsole.log('lockService连接状态:', lockServiceConnected);\r\n\r\n\t\t\t\t\t\tif (!lockServiceConnected) {\r\n\t\t\t\t\t\t\tconsole.log('lockService未连接，开始重新连接');\r\n\t\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t\tthis.tryConnectDevice();\r\n\t\t\t\t\t\t\t}, 1000);\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tconsole.log('lockService已连接，同步UI状态');\r\n\t\t\t\t\t\t\tthis.isConnected = true;\r\n\t\t\t\t\t\t\tthis.isConnecting = false;\r\n\t\t\t\t\t\t\tthis.updateUIState();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t.catch(err => {\r\n\t\t\t\t\tconsole.error('蓝牙环境重新初始化失败:', err);\r\n\r\n\t\t\t\t\t// 如果初始化失败，显示提示\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '蓝牙初始化失败，请检查蓝牙设置',\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\tduration: 3000\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t},\r\n\r\n\t\t// 检查蓝牙状态\r\n\t\tcheckBluetoothState() {\r\n\t\t\treturn new Promise((resolve, reject) => {\r\n\t\t\t\tconsole.log('检查蓝牙状态');\r\n\r\n\t\t\t\t// 检查蓝牙适配器状态\r\n\t\t\t\tuni.getBluetoothAdapterState({\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tconsole.log('蓝牙适配器状态:', res);\r\n\r\n\t\t\t\t\t\tif (res.available) {\r\n\t\t\t\t\t\t\tconsole.log('蓝牙适配器可用');\r\n\t\t\t\t\t\t\tresolve(res);\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tconsole.log('蓝牙适配器不可用');\r\n\t\t\t\t\t\t\treject(new Error('蓝牙适配器不可用'));\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\tconsole.log('获取蓝牙适配器状态失败，可能需要重新初始化:', err);\r\n\t\t\t\t\t\t// 获取状态失败，可能是适配器未初始化，这是正常的\r\n\t\t\t\t\t\tresolve();\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t// 强制开门方法\r\n\t\tforceOpenDoor() {\r\n\t\t\tconsole.log('尝试强制开门模式');\r\n\t\t\t\r\n\t\t\t// 显示正在重试提示\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '开门中，请稍候...',\r\n\t\t\t\ticon: 'loading',\r\n\t\t\t\tduration: 2000\r\n\t\t\t});\r\n\t\t\t\r\n\t\t\t// 延迟执行，确保提示显示完成\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tlockService.openLock({ \r\n\t\t\t\t\tignoreOrderStatus: true, \r\n\t\t\t\t\tforce: true, \r\n\t\t\t\t\tretry: true, \r\n\t\t\t\t\tsingleCommand: true,\r\n\t\t\t\t\toperationType: 1 // 1表示开锁操作，用于设备状态上报\r\n\t\t\t\t})\r\n\t\t\t\t\t.then(res => {\r\n\t\t\t\t\t\tconsole.log('强制开门成功:', res);\r\n\r\n\t\t\t\t\t\t// 设置开门完成标志，防止重复开门\r\n\t\t\t\t\t\tthis.doorOpenCompleted = true;\r\n\r\n\t\t\t\t\t\tthis.isLockOpen = true;\r\n\t\t\t\t\t\tthis.useStatus = 1;\r\n\t\t\t\t\t\tthis.updateUIState();\r\n\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '开门成功',\r\n\t\t\t\t\t\t\ticon: 'success',\r\n\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t});\r\n\r\n\t\t\t\t\t\t// 播放成功音效\r\n\t\t\t\t\t\tthis.playSuccessSound();\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch(e => {\r\n\t\t\t\t\t\tconsole.error('强制开门也失败:', e);\r\n\t\t\t\t\t\t// 显示错误提示，但不中断用户体验\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '自动开门失败，请手动点击开门',\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\tduration: 3000\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 重置开门处理标志，允许再次尝试\r\n\t\t\t\t\t\tthis.doorOpenProcessed = false;\r\n\t\t\t\t\t});\r\n\t\t\t}, 2000);\r\n\t\t},\r\n\t\t// 添加UI状态定时更新机制\r\n\t\tstartUIUpdateTimer() {\r\n\t\t\t// 清除之前的定时器\r\n\t\t\tthis.clearUIUpdateTimer();\r\n\r\n\t\t\t// 创建新的定时器，每2秒检查一次状态并更新UI（降低频率避免冲突）\r\n\t\t\tthis.uiUpdateTimer = setInterval(() => {\r\n\t\t\t\t// 从lockService获取最新状态\r\n\t\t\t\tconst deviceInfo = lockService.getDeviceInfo();\r\n\r\n\t\t\t\t// 只在状态真正变化时更新，避免频繁更新导致UI闪烁\r\n\t\t\t\tlet stateChanged = false;\r\n\r\n\t\t\t\t// 更新连接状态 - 修复undefined问题\r\n\t\t\t\tconst lockServiceConnected = deviceInfo.connected === true || deviceInfo.isConnected === true;\r\n\t\t\t\tif (this.isConnected !== lockServiceConnected) {\r\n\t\t\t\t\tconsole.log('UI定时更新: 连接状态变化', this.isConnected, '->', lockServiceConnected);\r\n\t\t\t\t\tthis.isConnected = lockServiceConnected;\r\n\t\t\t\t\tstateChanged = true;\r\n\r\n\t\t\t\t\t// 如果已连接，停止连接中状态\r\n\t\t\t\t\tif (lockServiceConnected) {\r\n\t\t\t\t\t\tthis.isConnecting = false;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 更新电池电量\r\n\t\t\t\tif (deviceInfo.batteryLevel && this.batteryLevel !== deviceInfo.batteryLevel) {\r\n\t\t\t\t\tthis.batteryLevel = deviceInfo.batteryLevel;\r\n\t\t\t\t\tstateChanged = true;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 只有在状态真正变化时才触发UI更新\r\n\t\t\t\tif (stateChanged) {\r\n\t\t\t\t\tthis.updateUIState();\r\n\t\t\t\t}\r\n\t\t\t}, 2000); // 改为2秒更新一次\r\n\r\n\t\t\tconsole.log('UI状态定时更新已启动');\r\n\t\t},\r\n\t\t\r\n\t\t// 清除UI更新定时器\r\n\t\tclearUIUpdateTimer() {\r\n\t\t\tif (this.uiUpdateTimer) {\r\n\t\t\t\tclearInterval(this.uiUpdateTimer);\r\n\t\t\t\tthis.uiUpdateTimer = null;\r\n\t\t\t\tconsole.log('UI状态定时更新已停止');\r\n\t\t\t}\r\n\r\n\t\t\t// 清除状态更新防抖定时器\r\n\t\t\tif (this.statusUpdateTimer) {\r\n\t\t\t\tclearTimeout(this.statusUpdateTimer);\r\n\t\t\t\tthis.statusUpdateTimer = null;\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 初始化音频对象\r\n\t\tinitAudio() {\r\n\t\t\t// 使用uni.getBackgroundAudioManager替代createInnerAudioContext\r\n\t\t\t// 因为backgroundAudioManager可以在后台播放且权限更高\r\n\t\t\ttry {\r\n\t\t\t\t// 检查平台，小程序环境使用背景音频管理器\r\n\t\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\t\tthis.successAudio = uni.getBackgroundAudioManager();\r\n\t\t\t\tthis.successAudio.title = '支付成功提示音'; // 必填项\r\n\t\t\t\tthis.successAudio.epname = '今夜城堡'; // 必填项\r\n\t\t\t\tthis.successAudio.singer = '系统提示音'; // 必填项\r\n\t\t\t\t// 重要：设置src但不会自动播放\r\n\t\t\t\tlet audioSrc = \"https://wuji1112-1312674791.cos.ap-guangzhou.myqcloud.com/static/audio/ttsmaker-file-2025-6-11-20-49-17.mp3\";\r\n\t\t\t\t// 先将autoplay设为false，因为设置src可能会自动播放\r\n\t\t\t\tthis.successAudio.autoplay = false;\r\n\t\t\t\t// #endif\r\n\t\t\t\t\r\n\t\t\t\t// 非小程序环境使用普通音频\r\n\t\t\t\t// #ifndef MP-WEIXIN\r\n\t\t\t\tthis.successAudio = uni.createInnerAudioContext();\r\n\t\t\t\tthis.successAudio.autoplay = false; // 确保不自动播放\r\n\t\t\t\t// #endif\r\n\t\t\t\t\r\n\t\t\t\t// 设置音频源，但不会自动播放\r\n\t\t\t\t// 微信小程序环境中，设置src可能会自动播放，所以暂时不设置src\r\n\t\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\t\t// 仅保存URL，但不立即设置\r\n\t\t\t\tthis.audioSrc = audioSrc;\r\n\t\t\t\t// #endif\r\n\t\t\t\t\r\n\t\t\t\t// 非微信小程序环境，直接设置src\r\n\t\t\t\t// #ifndef MP-WEIXIN\r\n\t\t\t\tthis.successAudio.src = \"https://wuji1112-1312674791.cos.ap-guangzhou.myqcloud.com/static/audio/ttsmaker-file-2025-6-11-20-49-17.mp3\";\r\n\t\t\t\t// #endif\r\n\t\t\t\t\r\n\t\t\t\t// 错误处理\r\n\t\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\t\tthis.successAudio.onError((res) => {\r\n\t\t\t\t\tconsole.error('音频播放错误:', res);\r\n\t\t\t\t\t// 尝试使用另一种方式播放\r\n\t\t\t\t\tthis.tryAlternativeAudioPlay();\r\n\t\t\t\t});\r\n\t\t\t\t// #endif\r\n\t\t\t\t\r\n\t\t\t\t// #ifndef MP-WEIXIN\r\n\t\t\t\tthis.successAudio.onError((res) => {\r\n\t\t\t\t\tconsole.error('音频播放错误:', res);\r\n\t\t\t\t\tthis.tryAlternativeAudioPlay();\r\n\t\t\t\t});\r\n\t\t\t\t// #endif\r\n\t\t\t\t\r\n\t\t\t\tconsole.log('初始化音频对象完成，不会自动播放');\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('初始化音频对象失败:', error);\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 尝试替代方案播放音频\r\n\t\ttryAlternativeAudioPlay() {\r\n\t\t\tconsole.log('尝试替代方案播放音频');\r\n\t\t\t\r\n\t\t\t// 使用微信小程序原生API播放\r\n\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\tif (wx && wx.createInnerAudioContext) {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst innerAudio = wx.createInnerAudioContext();\r\n\t\t\t\t\tinnerAudio.autoplay = true;\r\n\t\t\t\t\tinnerAudio.src = \"https://wuji1112-1312674791.cos.ap-guangzhou.myqcloud.com/static/audio/ttsmaker-file-2025-6-11-20-49-17.mp3\";\r\n\t\t\t\t\tinnerAudio.onError((err) => {\r\n\t\t\t\t\t\tconsole.error('替代音频播放失败:', err);\r\n\t\t\t\t\t\t// 如果还是失败，尝试使用短音效API\r\n\t\t\t\t\t\tthis.tryShortAudio();\r\n\t\t\t\t\t});\r\n\t\t\t\t} catch (e) {\r\n\t\t\t\t\tconsole.error('替代音频方案失败:', e);\r\n\t\t\t\t\tthis.tryShortAudio();\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\tthis.tryShortAudio();\r\n\t\t\t}\r\n\t\t\t// #endif\r\n\t\t\t\r\n\t\t\t// 非小程序环境\r\n\t\t\t// #ifndef MP-WEIXIN\r\n\t\t\ttry {\r\n\t\t\t\tconst audio = new Audio();\r\n\t\t\t\taudio.src = \"https://wuji1112-1312674791.cos.ap-guangzhou.myqcloud.com/static/audio/ttsmaker-file-2025-6-11-20-49-17.mp3\";\r\n\t\t\t\taudio.play().catch(err => console.error('HTML5音频播放失败:', err));\r\n\t\t\t} catch (e) {\r\n\t\t\t\tconsole.error('替代音频播放失败:', e);\r\n\t\t\t}\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\t\r\n\t\t// 尝试使用短音效API\r\n\t\ttryShortAudio() {\r\n\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\tif (wx && wx.playBackgroundAudio) {\r\n\t\t\t\twx.playBackgroundAudio({\r\n\t\t\t\t\tdataUrl: \"https://wuji1112-1312674791.cos.ap-guangzhou.myqcloud.com/static/audio/ttsmaker-file-2025-6-11-20-49-17.mp3\",\r\n\t\t\t\t\ttitle: '支付成功提示音',\r\n\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\tconsole.error('背景音频播放失败:', err);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t} else if (wx && wx.createAudioContext) {\r\n\t\t\t\t// 尝试使用系统提示音\r\n\t\t\t\twx.vibrateShort({\r\n\t\t\t\t\ttype: 'medium'\r\n\t\t\t\t});\r\n\t\t\t\tconsole.log('使用振动提示代替音频');\r\n\t\t\t}\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\t\r\n\t\t// 添加查询订单支付状态的方法\r\n\t\tqueryPaymentStatus() {\r\n\t\t\tconsole.log('查询订单支付状态，订单ID:', this.orderId);\r\n\t\t\t\r\n\t\t\tif (!this.orderId || isNaN(this.orderId)) {\r\n\t\t\t\tconsole.error('无效的订单ID:', this.orderId);\r\n\t\t\t\treturn Promise.reject(new Error('无效的订单ID'));\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 使用getAPIBaseUrl函数获取API基础URL\r\n\t\t\tconst baseUrl = getAPIBaseUrl();\r\n\t\t\treturn new Promise((resolve, reject) => {\r\n\t\t\t\tuni.request({\r\n\t\t\t\t\turl: `${baseUrl}/api/wx/miniapp/order/payment-status/${this.orderId}`,\r\n\t\t\t\t\tmethod: 'GET',\r\n\t\t\t\t\theader: {\r\n\t\t\t\t\t\t'Authorization': uni.getStorageSync('token')\r\n\t\t\t\t\t},\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tif (res.data && res.data.code === 200) {\r\n\t\t\t\t\t\t\t// 接口返回的是布尔值，true 表示已支付\r\n\t\t\t\t\t\t\tconst isPaid = res.data.data === true;\r\n\t\t\t\t\t\t\tconsole.log('查询订单支付状态成功:', { isPaid });\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t// 构造统一的返回格式，方便其他地方使用\r\n\t\t\t\t\t\t\tconst paymentInfo = {\r\n\t\t\t\t\t\t\t\tisPaid: isPaid\r\n\t\t\t\t\t\t\t};\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t// 如果支付状态为已支付，更新订单状态\r\n\t\t\t\t\t\t\tif (isPaid) {\r\n\t\t\t\t\t\t\t\tconsole.log('订单已支付，更新支付状态');\r\n\t\t\t\t\t\t\t\tthis.orderStatus = 1;\r\n\t\t\t\t\t\t\t\tthis.isPaid = true;\r\n\t\t\t\t\t\t\t\tthis.paymentSuccess = true;\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t// 播放支付成功音效\r\n\t\t\t\t\t\t\t\tthis.playSuccessSound();\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t// 显示支付成功提示\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ttitle: '支付成功',\r\n\t\t\t\t\t\t\t\t\ticon: 'success',\r\n\t\t\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t// 如果已连接设备，自动开门\r\n\t\t\t\t\t\t\t\tif (this.isConnected) {\r\n\t\t\t\t\t\t\t\t\tconsole.log('支付成功，设备已连接，自动开门');\r\n\t\t\t\t\t\t\t\t\tthis.checkOrderAndOpenDoor();\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t// 未连接设备，尝试连接\r\n\t\t\t\t\t\t\t\t\tconsole.log('支付成功，设备未连接，尝试连接');\r\n\t\t\t\t\t\t\t\t\tthis.tryConnectDevice();\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t// 更新UI状态\r\n\t\t\t\t\t\t\tthis.updateUIState();\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\tresolve(paymentInfo);\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tconst errorMsg = res.data ? res.data.message || '未知错误' : '未知错误';\r\n\t\t\t\t\t\t\tconsole.error('查询订单支付状态失败:', errorMsg);\r\n\t\t\t\t\t\t\treject(new Error('查询订单支付状态失败: ' + errorMsg));\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\tconsole.error('查询订单支付状态请求失败:', err);\r\n\t\t\t\t\t\treject(err);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 添加主动查询订单状态的方法\r\n\t\tqueryOrderStatus() {\r\n\t\t\tconsole.log('主动查询订单状态，订单ID:', this.orderId);\r\n\t\t\t\r\n\t\t\tif (!this.orderId || isNaN(this.orderId)) {\r\n\t\t\t\tconsole.error('无效的订单ID:', this.orderId);\r\n\t\t\t\treturn Promise.reject(new Error('无效的订单ID'));\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 使用getAPIBaseUrl函数获取API基础URL\r\n\t\t\tconst baseUrl = getAPIBaseUrl();\r\n\t\t\treturn new Promise((resolve, reject) => {\r\n\t\t\t\tuni.request({\r\n\t\t\t\t\turl: `${baseUrl}/api/wx/miniapp/order/${this.orderId}`,\r\n\t\t\t\t\tmethod: 'GET',\r\n\t\t\t\t\theader: {\r\n\t\t\t\t\t\t'Authorization': uni.getStorageSync('token')\r\n\t\t\t\t\t},\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tif (res.data && res.data.code === 200) {\r\n\t\t\t\t\t\t\tconst orderInfo = res.data.data;\r\n\t\t\t\t\t\t\tconsole.log('主动查询订单状态成功:', orderInfo);\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t// 更新订单信息\r\n\t\t\t\t\t\t\t// 注意：status可能为null，需要检查payStatus\r\n\t\t\t\t\t\t\tthis.orderStatus = orderInfo.status || 0; // 如果status为null，默认为0（未支付）\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t// 如果status为null但payStatus为1，说明已支付\r\n\t\t\t\t\t\t\tif (orderInfo.status === null && orderInfo.payStatus === 1) {\r\n\t\t\t\t\t\t\t\tconsole.log('订单status为null但payStatus为1，视为已支付');\r\n\t\t\t\t\t\t\t\tthis.orderStatus = 1;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\tthis.useStatus = orderInfo.useStatus || 0;\r\n\t\t\t\t\t\t\tthis.orderAmount = orderInfo.amount || 0;\r\n\t\t\t\t\t\t\tthis.orderDuration = orderInfo.duration || 0;\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t// 检查订单是否已支付 - 同时检查status和payStatus\r\n\t\t\t\t\t\t\tconst isPaid = this.orderStatus === 1 || orderInfo.payStatus === 1;\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t// 如果订单状态变为已支付，且之前未设置为已支付\r\n\t\t\t\t\t\t\tif (isPaid && !this.isPaid) {\r\n\t\t\t\t\t\t\t\tconsole.log('订单状态变为已支付，更新支付状态');\r\n\t\t\t\t\t\t\t\tthis.isPaid = true;\r\n\t\t\t\t\t\t\t\tthis.paymentSuccess = true;\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t// 播放支付成功音效\r\n\t\t\t\t\t\t\t\tthis.playSuccessSound();\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t// 显示支付成功提示\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ttitle: '支付成功',\r\n\t\t\t\t\t\t\t\t\ticon: 'success',\r\n\t\t\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t// 如果已连接设备，自动开门\r\n\t\t\t\t\t\t\t\tif (this.isConnected) {\r\n\t\t\t\t\t\t\t\t\tconsole.log('支付成功，设备已连接，自动开门');\r\n\t\t\t\t\t\t\t\t\tthis.checkOrderAndOpenDoor();\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t// 未连接设备，尝试连接\r\n\t\t\t\t\t\t\t\t\tconsole.log('支付成功，设备未连接，尝试连接');\r\n\t\t\t\t\t\t\t\t\tthis.tryConnectDevice();\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t// 更新UI状态\r\n\t\t\t\t\t\t\tthis.updateUIState();\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\tresolve(orderInfo);\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tconst errorMsg = res.data ? res.data.message || '未知错误' : '未知错误';\r\n\t\t\t\t\t\t\tconsole.error('主动查询订单状态失败:', errorMsg);\r\n\t\t\t\t\t\t\treject(new Error('主动查询订单状态失败: ' + errorMsg));\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\tconsole.error('主动查询订单状态请求失败:', err);\r\n\t\t\t\t\t\treject(err);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 启动订单状态轮询\r\n\t\tstartOrderPolling() {\r\n\t\t\tconsole.log('启动订单状态轮询');\r\n\t\t\t\r\n\t\t\t// 先清除可能存在的轮询定时器\r\n\t\t\tthis.stopOrderPolling();\r\n\t\t\t\r\n\t\t\t// 重置轮询计数\r\n\t\t\tthis.orderPollingCount = 0;\r\n\t\t\t\r\n\t\t\t// 如果没有订单ID，不启动轮询\r\n\t\t\tif (!this.orderId) {\r\n\t\t\t\tconsole.log('没有订单ID，不启动轮询');\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 如果订单已支付，不需要轮询\r\n\t\t\tif (this.isPaid || this.orderStatus === 1) {\r\n\t\t\t\tconsole.log('订单已支付，不需要轮询');\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 创建轮询定时器，缩短轮询间隔为1秒，提高响应速度\r\n\t\t\tthis.pollingInterval = 1000; // 1秒钟轮询一次\r\n\t\t\tthis.orderPollingTimer = setInterval(() => {\r\n\t\t\t\t// 增加轮询计数\r\n\t\t\t\tthis.orderPollingCount++;\r\n\t\t\t\t\r\n\t\t\t\tconsole.log(`订单状态轮询 [${this.orderPollingCount}/${this.maxPollingCount}]`);\r\n\t\t\t\t\r\n\t\t\t\t// 如果超过最大轮询次数，停止轮询\r\n\t\t\t\tif (this.orderPollingCount >= this.maxPollingCount) {\r\n\t\t\t\t\tconsole.log('达到最大轮询次数，停止轮询');\r\n\t\t\t\t\tthis.stopOrderPolling();\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 最后再查询一次，确保不遗漏支付状态变化\r\n\t\t\t\t\tthis.queryPaymentStatus()\r\n\t\t\t\t\t\t.then(paymentInfo => {\r\n\t\t\t\t\t\t\tconsole.log('最终查询订单支付状态:', paymentInfo);\r\n\t\t\t\t\t\t\tif (paymentInfo.isPaid) {\r\n\t\t\t\t\t\t\t\tconsole.log('最终查询确认订单已支付');\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t.catch(err => {\r\n\t\t\t\t\t\t\tconsole.error('最终查询订单支付状态失败:', err);\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 优先使用专门的支付状态接口\r\n\t\t\t\tthis.queryPaymentStatus()\r\n\t\t\t\t\t.then(paymentInfo => {\r\n\t\t\t\t\t\t// 如果订单已支付，停止轮询\r\n\t\t\t\t\t\tif (paymentInfo.isPaid) {\r\n\t\t\t\t\t\t\tconsole.log('订单已支付，停止轮询');\r\n\t\t\t\t\t\t\tthis.stopOrderPolling();\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t// 如果已连接设备且未处理开门，自动开门\r\n\t\t\t\t\t\t\tif (this.isConnected && !this.doorOpenProcessed && !this.doorOpenCompleted) {\r\n\t\t\t\t\t\t\t\tconsole.log('订单已支付且设备已连接，自动开门');\r\n\t\t\t\t\t\t\t\tthis.checkOrderAndOpenDoor();\r\n\t\t\t\t\t\t\t} else if (!this.isConnected) {\r\n\t\t\t\t\t\t\t\t// 未连接设备，尝试连接\r\n\t\t\t\t\t\t\t\tconsole.log('订单已支付但设备未连接，尝试连接设备');\r\n\t\t\t\t\t\t\t\tthis.tryConnectDevice();\r\n\t\t\t\t\t\t\t} else if (this.doorOpenCompleted) {\r\n\t\t\t\t\t\t\t\tconsole.log('订单已支付且已完成开门，无需重复操作');\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t// 未支付，每3次轮询打印一次日志\r\n\t\t\t\t\t\t\tif (this.orderPollingCount % 3 === 0) {\r\n\t\t\t\t\t\t\t\tconsole.log('订单未支付，继续轮询');\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch(err => {\r\n\t\t\t\t\t\tconsole.error('查询支付状态失败，尝试查询完整订单状态:', err);\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 如果专门的支付状态接口失败，回退到查询完整订单状态\r\n\t\t\t\t\t\tthis.queryOrderStatus()\r\n\t\t\t\t\t\t\t.then(orderInfo => {\r\n\t\t\t\t\t\t\t\t// 检查是否已支付 - 同时检查status和payStatus\r\n\t\t\t\t\t\t\t\tconst isPaid = orderInfo.status === 1 || orderInfo.payStatus === 1;\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t// 如果订单已支付，停止轮询\r\n\t\t\t\t\t\t\t\tif (isPaid) {\r\n\t\t\t\t\t\t\t\t\tconsole.log('订单已支付，停止轮询');\r\n\t\t\t\t\t\t\t\t\tthis.stopOrderPolling();\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t// 更新订单状态和支付状态\r\n\t\t\t\t\t\t\t\t\tthis.orderStatus = 1;\r\n\t\t\t\t\t\t\t\t\tthis.isPaid = true;\r\n\t\t\t\t\t\t\t\t\tthis.paymentSuccess = true;\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t// 如果已连接设备，自动开门\r\n\t\t\t\t\t\t\t\t\tif (this.isConnected && !this.doorOpenProcessed) {\r\n\t\t\t\t\t\t\t\t\t\tconsole.log('支付成功，设备已连接，自动开门');\r\n\t\t\t\t\t\t\t\t\t\tthis.checkOrderAndOpenDoor();\r\n\t\t\t\t\t\t\t\t\t} else if (!this.isConnected) {\r\n\t\t\t\t\t\t\t\t\t\t// 未连接设备，尝试连接\r\n\t\t\t\t\t\t\t\t\t\tconsole.log('支付成功，设备未连接，尝试连接');\r\n\t\t\t\t\t\t\t\t\t\tthis.tryConnectDevice();\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t} else if (orderInfo.status === null && this.orderPollingCount % 3 === 0) {\r\n\t\t\t\t\t\t\t\t\t// 如果status为null，每隔3次轮询打印一次日志\r\n\t\t\t\t\t\t\t\t\tconsole.log('订单status为null，继续轮询，当前payStatus:', orderInfo.payStatus);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t.catch(err => {\r\n\t\t\t\t\t\t\t\tconsole.error('轮询查询订单状态失败:', err);\r\n\t\t\t\t\t\t\t\t// 失败次数过多时停止轮询\r\n\t\t\t\t\t\t\t\tif (this.orderPollingCount >= 5) {\r\n\t\t\t\t\t\t\t\t\tconsole.log('轮询失败次数过多，停止轮询');\r\n\t\t\t\t\t\t\t\t\tthis.stopOrderPolling();\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t});\r\n\t\t\t}, this.pollingInterval);\r\n\t\t},\r\n\t\t\r\n\t\t// 停止订单状态轮询\r\n\t\tstopOrderPolling() {\r\n\t\t\tif (this.orderPollingTimer) {\r\n\t\t\t\tconsole.log('停止订单状态轮询');\r\n\t\t\t\tclearInterval(this.orderPollingTimer);\r\n\t\t\t\tthis.orderPollingTimer = null;\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 检查用户是否有设备的订单\r\n\t\t * @param {string} deviceId 设备编号或ID\r\n\t\t */\r\n\t\tcheckUserDeviceOrder(deviceId) {\r\n\t\t\tconsole.log('检查用户是否有设备的订单:', deviceId);\r\n\t\t\t\r\n\t\t\t// 构建API请求URL\r\n\t\t\tlet baseUrl = '';\r\n\t\t\tif (API && API.baseUrl) {\r\n\t\t\t\tbaseUrl = API.baseUrl;\r\n\t\t\t} else {\r\n\t\t\t\t// 默认使用HTTPS协议\r\n\t\t\t\tbaseUrl = 'https://api.jycb888.com/';\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 使用HTTPS协议\r\n\t\t\tif (baseUrl.startsWith('http://')) {\r\n\t\t\t\tbaseUrl = baseUrl.replace('http://', 'https://');\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 检查用户对该设备是否有订单\r\n\t\t\tconst checkOrderUrl = `${baseUrl}/api/wx/miniapp/device/check-user-order/${deviceId}`;\r\n\t\t\tconsole.log('检查用户对设备是否有订单URL:', checkOrderUrl);\r\n\t\t\t\r\n\t\t\treturn new Promise((resolve, reject) => {\r\n\t\t\t\tuni.request({\r\n\t\t\t\t\turl: checkOrderUrl,\r\n\t\t\t\t\tmethod: 'GET',\r\n\t\t\t\t\theader: {\r\n\t\t\t\t\t\t'Authorization': uni.getStorageSync('token')\r\n\t\t\t\t\t},\r\n\t\t\t\t\tsslVerify: false,\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tif (res.statusCode === 200 && res.data && res.data.code === 200 && res.data.data) {\r\n\t\t\t\t\t\t\tconsole.log('用户对设备有订单:', res.data.data);\r\n\t\t\t\t\t\t\tresolve(res.data.data);\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tconsole.log('用户对设备没有订单');\r\n\t\t\t\t\t\t\tresolve(null);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\tconsole.error('检查用户订单失败:', err);\r\n\t\t\t\t\t\treject(err);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t});\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style>\r\n\t/* CSS变量定义 */\r\n\tpage {\r\n\t\t--primary-light: #A875FF;\r\n\t\t--neon-pink: #ff36f9;\r\n\t\t--success-color: #4CAF50;\r\n\t\t--error-color: #F44336;\r\n\t\toverflow: hidden; /* 禁用页面级别的滚动 */\r\n\t\theight: 100vh; /* 固定页面高度 */\r\n\t}\r\n\t\r\n\t/* 页面基础样式 */\r\n\t.page-device {\r\n\t\tpadding-top: 120rpx; /* 减小顶部内边距，从160rpx改为120rpx */\r\n\t\tpadding-bottom: calc(50rpx + env(safe-area-inset-bottom));\r\n\t\tcolor: #ffffff;\r\n\t\theight: 100vh;\r\n\t\tmin-height: 100vh;\r\n\t\tbox-sizing: border-box;\r\n\t\tposition: relative;\r\n\t\toverflow: hidden;\r\n\t}\r\n\t\r\n\t/* 页面背景样式 */\r\n\t.page-background {\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tz-index: 0;\r\n\t\twill-change: transform;\r\n\t\tbackface-visibility: hidden;\r\n\t\t-webkit-backface-visibility: hidden;\r\n\t}\r\n\t\r\n\t.background-image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tobject-fit: cover;\r\n\t\ttransition: opacity 0.3s ease;\r\n\t\ttransform: scale(1.05); /* 稍微放大背景图片，确保覆盖边缘 */\r\n\t}\r\n\t\r\n\t.gradient-overlay {\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tbackground: linear-gradient(to bottom, \r\n\t\t\trgba(18, 18, 18, 0.7) 0%, \r\n\t\t\trgba(18, 18, 18, 0.6) 50%,\r\n\t\t\trgba(18, 18, 18, 0.7) 100%);\r\n\t\tz-index: 1;\r\n\t}\r\n\t\r\n\t/* 顶部状态栏和导航栏 */\r\n\t.status-bar {\r\n\t\twidth: 100%;\r\n\t\tbackground: transparent; /* 移除背景色，改为透明 */\r\n\t\tbackdrop-filter: none; /* 移除模糊效果 */\r\n\t\t-webkit-backdrop-filter: none; /* 移除模糊效果 */\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tz-index: 100;\r\n\t}\r\n\t\r\n\t/* 导航栏 */\r\n\t.nav-bar {\r\n\t\tposition: fixed;\r\n\t\ttop: calc(env(safe-area-inset-top) + 60rpx);\r\n\t\tleft: 15rpx;\r\n\t\tright: 15rpx;\r\n\t\theight: 110rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tbackground: transparent; /* 移除渐变背景，改为透明 */\r\n\t\tbackdrop-filter: none; /* 移除模糊效果 */\r\n\t\t-webkit-backdrop-filter: none; /* 移除模糊效果 */\r\n\t\tz-index: 100;\r\n\t\tpadding: 0 30rpx;\r\n\t\tborder-bottom: none; /* 移除底部边框 */\r\n\t\tbox-shadow: none; /* 移除阴影效果 */\r\n\t\tborder-radius: 0 0 30rpx 30rpx;\r\n\t}\r\n\t\r\n\t.nav-back {\r\n\t\tposition: absolute;\r\n\t\tleft: 30rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\twidth: 70rpx;\r\n\t\theight: 70rpx;\r\n\t\tborder-radius: 50%;\r\n\t\tbackground-color: rgba(168, 117, 255, 0.15);\r\n\t\tborder: 1px solid rgba(168, 117, 255, 0.3);\r\n\t}\r\n\t\r\n\t.nav-back .material-icons {\r\n\t\tfont-size: 44rpx;\r\n\t\tcolor: rgba(255, 255, 255, 0.9);\r\n\t\ttext-shadow: 0 0 8rpx rgba(168, 117, 255, 0.5);\r\n\t}\r\n\t\r\n\t.nav-title {\r\n\t\tfont-size: 38rpx;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #ffffff;\r\n\t\ttext-shadow: 0 0 10rpx rgba(168, 117, 255, 0.5);\r\n\t\tletter-spacing: 2rpx;\r\n\t}\r\n\t\r\n\t/* Banner样式 - iOS真机优化 */\r\n\t.banner-container {\r\n\t\tposition: fixed;\r\n\t\ttop: 140rpx; /* 将位置从170rpx向上移动到140rpx */\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\twidth: 92%; /* 从100%缩小到92% */\r\n\t\tmax-width: 700rpx; /* 从750rpx缩小到700rpx */\r\n\t\tmargin: 0 auto; /* 使用margin auto实现水平居中 */\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tz-index: 10;\r\n\t\tpadding: 20rpx; /* 增加内边距，扩大点击区域 */\r\n\t\t/* iOS兼容性 - 关键优化 */\r\n\t\t-webkit-tap-highlight-color: rgba(0,0,0,0);\r\n\t\t-webkit-touch-callout: none;\r\n\t\t-webkit-user-select: none;\r\n\t\tuser-select: none;\r\n\t\tcursor: pointer;\r\n\t\t/* 确保可点击区域 */\r\n\t\tmin-height: 220rpx; /* 增加最小高度 */\r\n\t\t/* iOS触摸优化 */\r\n\t\ttouch-action: manipulation;\r\n\t\t/* 添加点击反馈 */\r\n\t\ttransition: all 0.1s ease;\r\n\t\t/* 强制硬件加速 */\r\n\t\t-webkit-transform: translateZ(0);\r\n\t\ttransform: translateZ(0);\r\n\t\twill-change: transform, opacity;\r\n\t}\r\n\r\n\t.banner-container:active {\r\n\t\topacity: 0.7;\r\n\t\ttransform: scale(0.98) translateZ(0);\r\n\t}\r\n\r\n\t.banner-image {\r\n\t\twidth: 100%;\r\n\t\theight: 180rpx; /* 调整高度为180rpx */\r\n\t\tobject-fit: contain;\r\n\t\t/* iOS兼容性 - 完全禁用图片交互 */\r\n\t\tpointer-events: none;\r\n\t\t-webkit-tap-highlight-color: transparent;\r\n\t\t-webkit-touch-callout: none;\r\n\t\t-webkit-user-select: none;\r\n\t\tuser-select: none;\r\n\t\t/* 禁用图片拖拽 */\r\n\t\t-webkit-user-drag: none;\r\n\t\t-khtml-user-drag: none;\r\n\t\t-moz-user-drag: none;\r\n\t\t-o-user-drag: none;\r\n\t}\r\n\r\n\t.banner-glow {\r\n\t\t/* 确保glow元素完全不拦截点击事件 */\r\n\t\tpointer-events: none;\r\n\t\t-webkit-tap-highlight-color: transparent;\r\n\t\t-webkit-touch-callout: none;\r\n\t\t-webkit-user-select: none;\r\n\t\tuser-select: none;\r\n\t\ttouch-action: none;\r\n\t}\r\n\t\r\n\t/* 删除Logo区域样式 */\r\n\t\r\n\t/* 内容区域 */\r\n\t.content {\r\n\t\tpadding: 0 30rpx 30rpx; /* 保持左右内边距 */\r\n\t\tposition: relative;\r\n\t\tz-index: 2;\r\n\t\theight: calc(100vh - 120rpx - env(safe-area-inset-bottom) - 50rpx); /* 调整高度，从160rpx改为120rpx */\r\n\t\tmax-height: calc(100vh - 120rpx - env(safe-area-inset-bottom) - 50rpx); /* 限制最大高度 */\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\toverflow: hidden; /* 禁用滚动 */\r\n\t\ttouch-action: none; /* 禁用触摸滚动 */\r\n\t\tmargin-top: 660rpx; /* 调整顶部边距，使内容稍微往下移 */\r\n\t}\r\n\t\r\n\t/* 欢迎标题样式 */\r\n\t.welcome-section {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tmargin-bottom: 30rpx;\r\n\t}\r\n\t\r\n\t.welcome-title {\r\n\t\tfont-size: 60rpx;\r\n\t\tfont-weight: 700;\r\n\t\tcolor: #ffffff;\r\n\t\ttext-align: center;\r\n\t\ttext-shadow: \r\n\t\t\t0 0 10rpx rgba(255, 255, 255, 0.5),\r\n\t\t\t0 0 20rpx rgba(168, 117, 255, 0.5);\r\n\t\tletter-spacing: 4rpx;\r\n\t}\r\n\t\r\n\t.welcome-subtitle {\r\n\t\tfont-size: 80rpx;\r\n\t\tfont-weight: 800;\r\n\t\tcolor: #ffffff;\r\n\t\ttext-align: center;\r\n\t\tbackground: linear-gradient(to right, #A875FF, #ff36f9);\r\n\t\t-webkit-background-clip: text;\r\n\t\t-webkit-text-fill-color: transparent;\r\n\t\ttext-shadow: 0 0 20rpx rgba(168, 117, 255, 0.7);\r\n\t\tletter-spacing: 6rpx;\r\n\t\tmargin-top: 10rpx;\r\n\t\tanimation: glow 2s ease-in-out infinite alternate;\r\n\t}\r\n\t\r\n\t@keyframes glow {\r\n\t\tfrom {\r\n\t\t\ttext-shadow: 0 0 10rpx rgba(168, 117, 255, 0.5), 0 0 20rpx rgba(168, 117, 255, 0.3);\r\n\t\t}\r\n\t\tto {\r\n\t\t\ttext-shadow: 0 0 20rpx rgba(168, 117, 255, 0.7), 0 0 30rpx rgba(168, 117, 255, 0.5), 0 0 40rpx rgba(168, 117, 255, 0.3);\r\n\t\t}\r\n\t}\r\n\t\r\n\t.welcome-device {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tmargin-top: 30rpx;\r\n\t\tpadding: 15rpx 40rpx;\r\n\t\tbackground: rgba(168, 117, 255, 0.2);\r\n\t\tborder: 1px solid rgba(168, 117, 255, 0.4);\r\n\t\tborder-radius: 50rpx;\r\n\t}\r\n\t\r\n\t.welcome-device .material-icons {\r\n\t\tfont-size: 36rpx;\r\n\t\tmargin-right: 10rpx;\r\n\t\tcolor: rgba(255, 255, 255, 0.9);\r\n\t}\r\n\t\r\n\t.welcome-device text {\r\n\t\tfont-size: 30rpx;\r\n\t\tcolor: rgba(255, 255, 255, 0.9);\r\n\t}\r\n\t\r\n\t/* 登录提示样式 */\r\n\t.login-prompt {\r\n\t\tbackground: rgba(255, 193, 7, 0.1);\r\n\t\tborder: 1px solid rgba(255, 193, 7, 0.3);\r\n\t\tborder-radius: 20rpx;\r\n\t\tpadding: 30rpx;\r\n\t\tmargin: 30rpx 0;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tgap: 20rpx;\r\n\t}\r\n\r\n\t.login-prompt-text {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tgap: 15rpx;\r\n\t\tcolor: rgba(255, 255, 255, 0.9);\r\n\t\tfont-size: 28rpx;\r\n\t}\r\n\r\n\t.login-prompt-text .material-icons {\r\n\t\tfont-size: 32rpx;\r\n\t\tcolor: #FFC107;\r\n\t}\r\n\r\n\t.login-prompt-button {\r\n\t\tbackground: linear-gradient(135deg, #FFC107, #FF9800);\r\n\t\tborder-radius: 50rpx;\r\n\t\tpadding: 20rpx 40rpx;\r\n\t\tcolor: #000;\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: 600;\r\n\t\tbox-shadow: 0 8rpx 20rpx rgba(255, 193, 7, 0.3);\r\n\t\ttransition: all 0.3s ease;\r\n\t}\r\n\r\n\t.login-prompt-button:active {\r\n\t\ttransform: scale(0.95);\r\n\t\tbox-shadow: 0 4rpx 10rpx rgba(255, 193, 7, 0.2);\r\n\t}\r\n\r\n\t/* 设备价格与支付区域 */\r\n\t.price-section {\r\n\t\tmargin-top: 30rpx; /* 减小与上方元素的间距，从60rpx改为30rpx */\r\n\t}\r\n\t\r\n\t/* 设备卡片样式 */\r\n\t.price-card {\r\n\t\tpadding: 30rpx; /* 减小内边距，从40rpx改为30rpx */\r\n\t\tbackground-color: rgba(30, 30, 30, 0.5); /* 添加背景色 */\r\n\t\tborder-radius: 20rpx;\r\n\t\tbackdrop-filter: blur(10px); /* 添加模糊效果 */\r\n\t\t-webkit-backdrop-filter: blur(10px); /* 添加模糊效果 */\r\n\t\tbox-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2); /* 添加阴影 */\r\n\t\tborder: 1px solid rgba(168, 117, 255, 0.3); /* 保留紫色边框 */\r\n\t}\r\n\t\r\n\t/* 设备连接区域 */\r\n\t.connect-section {\r\n\t\tflex: 1;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tpadding: 30rpx 0; /* 减小内边距，从40rpx改为30rpx */\r\n\t\tmargin-top: 20rpx; /* 减小与上方元素的间距，因为已经有欢迎标题了 */\r\n\t}\r\n\t\r\n\t/* 霓虹灯标题效果 */\r\n\t.neon-title {\r\n\t\tfont-size: 42rpx;\r\n\t\tfont-weight: 700;\r\n\t\ttext-align: center;\r\n\t\tcolor: #fff;\r\n\t\ttext-shadow: \r\n\t\t\t0 0 5rpx rgba(255, 255, 255, 0.5),\r\n\t\t\t0 0 10rpx rgba(255, 255, 255, 0.3), \r\n\t\t\t0 0 15rpx rgba(168, 117, 255, 0.4),\r\n\t\t\t0 0 25rpx rgba(168, 117, 255, 0.3);\r\n\t\tletter-spacing: 4rpx;\r\n\t\tanimation: soft-flicker 4s infinite alternate;\r\n\t}\r\n\t\r\n\t/* 智能门锁连接状态标题使用圆体 */\r\n\t.status-title {\r\n\t\tfont-family: \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"WenQuanYi Micro Hei\", sans-serif;\r\n\t\tfont-weight: 400;\r\n\t\tborder-radius: 999rpx;\r\n\t}\r\n\t\r\n\t.neon-subtitle {\r\n\t\tfont-size: 36rpx;\r\n\t\tfont-weight: 600;\r\n\t\ttext-align: center;\r\n\t\tmargin-top: 10rpx;\r\n\t\tcolor: rgba(255, 255, 255, 0.9);\r\n\t\ttext-shadow: \r\n\t\t\t0 0 5rpx rgba(255, 255, 255, 0.3),\r\n\t\t\t0 0 10rpx rgba(255, 255, 255, 0.2), \r\n\t\t\t0 0 15rpx rgba(255, 102, 221, 0.3);\r\n\t\tletter-spacing: 2rpx;\r\n\t\tanimation: soft-flicker 5s infinite alternate;\r\n\t\tanimation-delay: 0.5s;\r\n\t}\r\n\t\r\n\t@keyframes soft-flicker {\r\n\t\t0%, 100% {\r\n\t\t\topacity: 1;\r\n\t\t}\r\n\t\t30% {\r\n\t\t\topacity: 0.9;\r\n\t\t}\r\n\t\t60% {\r\n\t\t\topacity: 0.95;\r\n\t\t}\r\n\t}\r\n\t\r\n\t/* 操作按钮 */\r\n\t.action-buttons {\r\n\t\tmargin-top: 40rpx;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t}\r\n\t\r\n\t.primary-button {\r\n\t\tposition: relative;\r\n\t\twidth: 240rpx; /* 增加按钮尺寸 */\r\n\t\theight: 240rpx; /* 增加按钮尺寸 */\r\n\t\tborder-radius: 50%;\r\n\t\tbackground: rgba(139, 92, 246, 0.15);\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tbox-shadow: 0 10rpx 40rpx rgba(0, 0, 0, 0.3); /* 增强阴影效果 */\r\n\t\toverflow: visible;\r\n\t\tborder: 2px solid rgba(168, 117, 255, 0.4); /* 加粗边框 */\r\n\t\ttransition: transform 0.3s, box-shadow 0.3s;\r\n\t\tanimation: pulse 2s infinite ease-in-out;\r\n\t\tbackdrop-filter: blur(20px);\r\n\t\t-webkit-backdrop-filter: blur(20px);\r\n\t\tz-index: 1;\r\n\t}\r\n\t\r\n\t.primary-button:active {\r\n\t\ttransform: scale(0.96);\r\n\t\tbox-shadow: 0 4rpx 10rpx rgba(168, 117, 255, 0.2);\r\n\t}\r\n\t\r\n\t/* 添加内部渐变效果 */\r\n\t.primary-button::after {\r\n\t\tcontent: '';\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tborder-radius: 50%;\r\n\t\tbackground: linear-gradient(145deg, rgba(139, 92, 246, 0.3), rgba(168, 117, 255, 0.1));\r\n\t\tz-index: -1;\r\n\t}\r\n\t\r\n\t/* 添加涟漪动画效果 - 第一层 */\r\n\t.primary-button::before {\r\n\t\tcontent: '';\r\n\t\tposition: absolute;\r\n\t\ttop: 50%;\r\n\t\tleft: 50%;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tborder-radius: 50%;\r\n\t\ttransform: translate(-50%, -50%) scale(0.9);\r\n\t\topacity: 0;\r\n\t\tz-index: -2;\r\n\t\tborder: 2px solid rgba(168, 117, 255, 0.5);\r\n\t\tanimation: ripple 2s infinite linear;\r\n\t}\r\n\t\r\n\t/* 添加第二层涟漪 */\r\n\t.primary-button .ripple-layer {\r\n\t\tcontent: '';\r\n\t\tposition: absolute;\r\n\t\ttop: 50%;\r\n\t\tleft: 50%;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tborder-radius: 50%;\r\n\t\ttransform: translate(-50%, -50%) scale(0.9);\r\n\t\topacity: 0;\r\n\t\tz-index: -2;\r\n\t\tborder: 2px solid rgba(168, 117, 255, 0.5);\r\n\t\tanimation: ripple 2s infinite linear 1s;\r\n\t}\r\n\t\r\n\t@keyframes ripple {\r\n\t\t0% {\r\n\t\t\ttransform: translate(-50%, -50%) scale(0.9);\r\n\t\t\topacity: 0.7;\r\n\t\t}\r\n\t\t100% {\r\n\t\t\ttransform: translate(-50%, -50%) scale(1.5);\r\n\t\t\topacity: 0;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.primary-button .material-icons {\r\n\t\tfont-size: 100rpx; /* 增大图标尺寸 */\r\n\t\tcolor: rgba(255, 255, 255, 0.85);\r\n\t\tmargin-bottom: 10rpx;\r\n\t}\r\n\t\r\n\t.primary-button text:not(.material-icons) {\r\n\t\tfont-size: 34rpx; /* 从36rpx改为34rpx */\r\n\t\tfont-weight: 400; /* 从600改为400，取消加粗 */\r\n\t\tcolor: rgba(255, 255, 255, 0.9);\r\n\t}\r\n\t\r\n\t/* 按钮内部脉冲效果 */\r\n\t@keyframes pulse {\r\n\t\t0%, 100% {\r\n\t\t\tbox-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);\r\n\t\t}\r\n\t\t50% {\r\n\t\t\tbox-shadow: 0 8rpx 32rpx rgba(168, 117, 255, 0.4);\r\n\t\t}\r\n\t}\r\n\t\r\n\t.open-button {\r\n\t\tbackground: rgba(76, 175, 80, 0.2); /* 简化背景，为毛玻璃效果做准备 */\r\n\t\tbox-shadow: 0 8rpx 20rpx rgba(76, 175, 80, 0.2);\r\n\t\tborder: 1px solid rgba(76, 175, 80, 0.5);\r\n\t\tanimation: pulse-green 2s infinite ease-in-out;\r\n\t}\r\n\t\r\n\t/* 添加开门按钮内部渐变效果 */\r\n\t.open-button::after {\r\n\t\tbackground: linear-gradient(145deg, rgba(76, 175, 80, 0.4), rgba(76, 175, 80, 0.1));\r\n\t}\r\n\t\r\n\t/* 修改开门按钮涟漪效果 */\r\n\t.open-button::before,\r\n\t.open-button .ripple-layer {\r\n\t\tborder-color: rgba(76, 175, 80, 0.5);\r\n\t}\r\n\t\r\n\t.open-button:active {\r\n\t\tbox-shadow: 0 4rpx 10rpx rgba(76, 175, 80, 0.2);\r\n\t}\r\n\t\r\n\t@keyframes pulse-green {\r\n\t\t0%, 100% {\r\n\t\t\tbox-shadow: 0 8rpx 20rpx rgba(76, 175, 80, 0.2);\r\n\t\t}\r\n\t\t50% {\r\n\t\t\tbox-shadow: 0 8rpx 32rpx rgba(76, 175, 80, 0.5);\r\n\t\t}\r\n\t}\r\n\t\r\n\t/* 连接状态展示 */\r\n\t.connection-status {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tpadding: 16rpx 40rpx; /* 从20rpx 50rpx缩小到16rpx 40rpx */\r\n\t\tmargin-top: 40rpx;\r\n\t\tbackground: linear-gradient(145deg, rgba(139, 92, 246, 0.3), rgba(168, 117, 255, 0.5));\r\n\t\tborder: 1px solid rgba(168, 117, 255, 0.5);\r\n\t\tborder-radius: 40rpx; /* 从50rpx缩小到40rpx */\r\n\t\tbox-shadow: 0 8rpx 20rpx rgba(168, 117, 255, 0.2);\r\n\t\ttransition: all 0.3s ease;\r\n\t\tposition: relative;\r\n\t\toverflow: hidden;\r\n\t\ttransform: scale(0.95); /* 整体缩小到95% */\r\n\t}\r\n\t\r\n\t/* 添加连接状态的波纹背景效果 */\r\n\t.connection-status::before {\r\n\t\tcontent: '';\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tleft: -100%;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tbackground: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\r\n\t\tanimation: wave 2s infinite linear;\r\n\t}\r\n\t\r\n\t@keyframes wave {\r\n\t\t0% {\r\n\t\t\tleft: -100%;\r\n\t\t}\r\n\t\t50%, 100% {\r\n\t\t\tleft: 100%;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.connection-status.connected {\r\n\t\tbackground: linear-gradient(145deg, rgba(76, 175, 80, 0.3), rgba(76, 175, 80, 0.5));\r\n\t\tbox-shadow: 0 8rpx 20rpx rgba(76, 175, 80, 0.2);\r\n\t\tborder: 1px solid rgba(76, 175, 80, 0.5);\r\n\t\tanimation: glow-green 2s infinite alternate;\r\n\t}\r\n\t\r\n\t@keyframes glow-green {\r\n\t\t0% {\r\n\t\t\tbox-shadow: 0 8rpx 20rpx rgba(76, 175, 80, 0.2);\r\n\t\t}\r\n\t\t100% {\r\n\t\t\tbox-shadow: 0 8rpx 30rpx rgba(76, 175, 80, 0.6);\r\n\t\t}\r\n\t}\r\n\t\r\n\t.connection-status.disconnected {\r\n\t\tbackground: linear-gradient(145deg, rgba(158, 158, 158, 0.2), rgba(158, 158, 158, 0.3));\r\n\t\tbox-shadow: 0 0 20rpx rgba(158, 158, 158, 0.2);\r\n\t}\r\n\t\r\n\t.connection-text {\r\n\t\tfont-size: 28rpx; /* 从32rpx缩小到28rpx */\r\n\t\tfont-weight: 500;\r\n\t\tcolor: #ffffff;\r\n\t\tfont-family: \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"WenQuanYi Micro Hei\", sans-serif;\r\n\t}\r\n\t\r\n\t.connection-status .material-icons {\r\n\t\tmargin-right: 12rpx; /* 从16rpx缩小到12rpx */\r\n\t\tfont-size: 36rpx; /* 从40rpx缩小到36rpx */\r\n\t}\r\n\t\r\n\t/* 连接提示 */\r\n\t.connection-tips {\r\n\t\tmargin-top: 30rpx; /* 从40rpx减小到30rpx，使文字上移 */\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\ttext-align: center;\r\n\t}\r\n\t\r\n\t.connection-tips .text-tertiary {\r\n\t\tline-height: 48rpx;\r\n\t}\r\n\t\r\n\t.connection-tips .text-agreement {\r\n\t\tfont-size: 26rpx; /* 从28rpx缩小到26rpx */\r\n\t\tcolor: rgba(255, 255, 255, 0.4);\r\n\t\tline-height: 40rpx;\r\n\t\tmargin-top: 10rpx;\r\n\t}\r\n\t\r\n\t/* 操作结果提示 */\r\n\t.result-message {\r\n\t\tmargin-top: 40rpx;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tanimation: fadeIn 0.5s;\r\n\t}\r\n\t\r\n\t@keyframes fadeIn {\r\n\t\tfrom {\r\n\t\t\topacity: 0;\r\n\t\t\ttransform: translateY(20rpx);\r\n\t\t}\r\n\t\tto {\r\n\t\t\topacity: 1;\r\n\t\t\ttransform: translateY(0);\r\n\t\t}\r\n\t}\r\n\t\r\n\t.result-icon {\r\n\t\tfont-size: 80rpx;\r\n\t\tmargin-bottom: 16rpx;\r\n\t}\r\n\t\r\n\t.result-icon.success {\r\n\t\tcolor: var(--success-color, #4CAF50);\r\n\t}\r\n\t\r\n\t.result-icon:not(.success) {\r\n\t\tcolor: var(--error-color, #F44336);\r\n\t}\r\n\t\r\n\t.result-text {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: 500;\r\n\t\tcolor: #ffffff;\r\n\t}\r\n\t\r\n\t.text-connected {\r\n\t\tcolor: var(--success-color, #4CAF50);\r\n\t\ttext-shadow: 0 0 10rpx rgba(76, 175, 80, 0.5); /* 增强绿色发光效果 */\r\n\t}\r\n\t\r\n\t.text-connecting {\r\n\t\tcolor: var(--primary-light, #A875FF);\r\n\t\ttext-shadow: 0 0 10rpx rgba(168, 117, 255, 0.5); /* 增强紫色发光效果 */\r\n\t}\r\n\t\r\n\t.text-disconnected {\r\n\t\tcolor: rgba(158, 158, 158, 0.9);\r\n\t\ttext-shadow: 0 0 10rpx rgba(158, 158, 158, 0.4);\r\n\t\tanimation: pulse-text 2s infinite;\r\n\t}\r\n\t\r\n\t@keyframes pulse-text {\r\n\t\t0% {\r\n\t\t\topacity: 0.7;\r\n\t\t}\r\n\t\t50% {\r\n\t\t\topacity: 1;\r\n\t\t}\r\n\t\t100% {\r\n\t\t\topacity: 0.7;\r\n\t\t}\r\n\t}\r\n\t\r\n\t/* Material Icons 字体 */\r\n\t@font-face {\r\n\t\tfont-family: 'Material Icons';\r\n\t\tfont-style: normal;\r\n\t\tfont-weight: 400;\r\n\t\tsrc: url(https://fonts.gstatic.com/s/materialicons/v139/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format('woff2');\r\n\t}\r\n\r\n\t.material-icons {\r\n\t\tfont-family: 'Material Icons';\r\n\t\tfont-weight: normal;\r\n\t\tfont-style: normal;\r\n\t\tfont-size: 48rpx;\r\n\t\tline-height: 1;\r\n\t\tletter-spacing: normal;\r\n\t\ttext-transform: none;\r\n\t\tdisplay: inline-block;\r\n\t\twhite-space: nowrap;\r\n\t\tword-wrap: normal;\r\n\t\tdirection: ltr;\r\n\t\t-webkit-font-smoothing: antialiased;\r\n\t\t-moz-osx-font-smoothing: grayscale;\r\n\t}\r\n\t\r\n\t/* 辅助类 */\r\n\t.flex {\r\n\t\tdisplay: flex;\r\n\t}\r\n\t\r\n\t.justify-between {\r\n\t\tjustify-content: space-between;\r\n\t}\r\n\t\r\n\t.items-center {\r\n\t\talign-items: center;\r\n\t}\r\n\t\r\n\t.mt-sm {\r\n\t\tmargin-top: 16rpx;\r\n\t}\r\n\t\r\n\t.mt-md {\r\n\t\tmargin-top: 30rpx;\r\n\t}\r\n\t\r\n\t.mt-lg {\r\n\t\tmargin-top: 60rpx;\r\n\t}\r\n\t\r\n\t/* 标题和文本样式 */\r\n\t.title-md {\r\n\t\tfont-size: 38rpx;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #ffffff;\r\n\t}\r\n\t\r\n\t.text-secondary {\r\n\t\tcolor: rgba(255, 255, 255, 0.7);\r\n\t}\r\n\t\r\n\t.text-tertiary {\r\n\t\tcolor: rgba(255, 255, 255, 0.5);\r\n\t}\r\n\t\r\n\t/* 卡片样式 */\r\n\t.card {\r\n\t\tborder-radius: 20rpx;\r\n\t\tbackground-color: transparent !important; /* 使用!important确保覆盖其他样式 */\r\n\t\tbox-shadow: none !important; /* 移除阴影 */\r\n\t\tborder: 1px solid rgba(168, 117, 255, 0.3); /* 添加紫色边框 */\r\n\t}\r\n\t\r\n\t.text-paid {\r\n\t\tcolor: var(--success-color, #4CAF50);\r\n\t\ttext-shadow: 0 0 10rpx rgba(76, 175, 80, 0.5);\r\n\t}\r\n\t\r\n\t.text-unpaid {\r\n\t\tcolor: #FF9800;\r\n\t\ttext-shadow: 0 0 10rpx rgba(255, 152, 0, 0.5);\r\n\t}\r\n\t\r\n\t/* 付款模态框样式 */\r\n\t.modal-overlay {\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tbackground-color: rgba(0, 0, 0, 0.7);\r\n\t\tbackdrop-filter: blur(5px);\r\n\t\t-webkit-backdrop-filter: blur(5px);\r\n\t\tz-index: 1000;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tanimation: fadeIn 0.3s ease;\r\n\t}\r\n\t\r\n\t@keyframes fadeIn {\r\n\t\tfrom {\r\n\t\t\topacity: 0;\r\n\t\t}\r\n\t\tto {\r\n\t\t\topacity: 1;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.payment-modal, .open-door-modal {\r\n\t\twidth: 80%;\r\n\t\tmax-width: 600rpx;\r\n\t\tbackground: linear-gradient(145deg, rgba(30, 30, 30, 0.9), rgba(40, 40, 40, 0.9));\r\n\t\tborder-radius: 20rpx;\r\n\t\tbox-shadow: 0 10rpx 40rpx rgba(0, 0, 0, 0.5);\r\n\t\tborder: 1px solid rgba(168, 117, 255, 0.3);\r\n\t\toverflow: hidden;\r\n\t\tanimation: slideUp 0.3s ease;\r\n\t\tz-index: 1001;\r\n\t\tposition: fixed;\r\n\t\ttop: 50%;\r\n\t\tleft: 50%;\r\n\t\ttransform: translate(-50%, -55%);\r\n\t}\r\n\t\r\n\t@keyframes slideUp {\r\n\t\tfrom {\r\n\t\t\ttransform: translate(-50%, -45%);\r\n\t\t\topacity: 0;\r\n\t\t}\r\n\t\tto {\r\n\t\t\ttransform: translate(-50%, -55%);\r\n\t\t\topacity: 1;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.modal-header {\r\n\t\tpadding: 20rpx 30rpx;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tborder-bottom: 1px solid rgba(168, 117, 255, 0.2);\r\n\t}\r\n\t\r\n\t.modal-title {\r\n\t\tfont-size: 34rpx;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #ffffff;\r\n\t\ttext-shadow: 0 0 10rpx rgba(168, 117, 255, 0.5);\r\n\t}\r\n\t\r\n\t.close-icon {\r\n\t\tfont-size: 40rpx;\r\n\t\tcolor: rgba(255, 255, 255, 0.7);\r\n\t}\r\n\t\r\n\t.modal-body {\r\n\t\tpadding: 30rpx;\r\n\t}\r\n\t\r\n\t/* 支付弹窗样式 */\r\n\t.payment-info {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tmargin-bottom: 30rpx;\r\n\t}\r\n\t\r\n\t.payment-device-name {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #ffffff;\r\n\t\tmargin-bottom: 10rpx;\r\n\t}\r\n\t\r\n\t.payment-price {\r\n\t\tfont-size: 48rpx;\r\n\t\tfont-weight: 700;\r\n\t\tcolor: #ffffff;\r\n\t\tbackground: linear-gradient(to right, #A875FF, #ff36f9);\r\n\t\t-webkit-background-clip: text;\r\n\t\t-webkit-text-fill-color: transparent;\r\n\t\ttext-shadow: 0 0 10rpx rgba(168, 117, 255, 0.3);\r\n\t}\r\n\t\r\n\t.payment-button {\r\n\t\twidth: 100%;\r\n\t\theight: 80rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tbackground: linear-gradient(145deg, #A875FF, #ff36f9);\r\n\t\tborder-radius: 40rpx;\r\n\t\tmargin: 20rpx 0;\r\n\t\tposition: relative;\r\n\t\toverflow: hidden;\r\n\t}\r\n\t\r\n\t.payment-button::before {\r\n\t\tcontent: '';\r\n\t\tposition: absolute;\r\n\t\ttop: -50%;\r\n\t\tleft: -50%;\r\n\t\twidth: 200%;\r\n\t\theight: 200%;\r\n\t\tbackground: linear-gradient(transparent, rgba(255, 255, 255, 0.3), transparent);\r\n\t\ttransform: rotate(45deg);\r\n\t\tanimation: shine 2s infinite;\r\n\t}\r\n\t\r\n\t@keyframes shine {\r\n\t\t0% {\r\n\t\t\tleft: -50%;\r\n\t\t}\r\n\t\t100% {\r\n\t\t\tleft: 150%;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.payment-button text {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #ffffff;\r\n\t\tz-index: 1;\r\n\t}\r\n\t\r\n\t.payment-button.loading {\r\n\t\tbackground: linear-gradient(145deg, rgba(168, 117, 255, 0.7), rgba(255, 54, 249, 0.7));\r\n\t}\r\n\t\r\n\t.loading-spinner {\r\n\t\twidth: 40rpx;\r\n\t\theight: 40rpx;\r\n\t\tborder: 4rpx solid rgba(255, 255, 255, 0.3);\r\n\t\tborder-top: 4rpx solid #ffffff;\r\n\t\tborder-radius: 50%;\r\n\t\tanimation: spin 1s linear infinite;\r\n\t}\r\n\t\r\n\t@keyframes spin {\r\n\t\t0% {\r\n\t\t\ttransform: rotate(0deg);\r\n\t\t}\r\n\t\t100% {\r\n\t\t\ttransform: rotate(360deg);\r\n\t\t}\r\n\t}\r\n\t\r\n\t.payment-tips {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: rgba(255, 255, 255, 0.5);\r\n\t\ttext-align: center;\r\n\t\tmargin-top: 10rpx;\r\n\t}\r\n\t\r\n\t/* 开门弹窗样式 */\r\n\t.door-info {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tmargin-bottom: 30rpx;\r\n\t}\r\n\t\r\n\t.door-icon {\r\n\t\twidth: 120rpx;\r\n\t\theight: 120rpx;\r\n\t\tborder-radius: 50%;\r\n\t\tbackground: rgba(76, 175, 80, 0.2);\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\tborder: 1px solid rgba(76, 175, 80, 0.5);\r\n\t\tbox-shadow: 0 0 20rpx rgba(76, 175, 80, 0.3);\r\n\t}\r\n\t\r\n\t.door-icon .material-icons {\r\n\t\tfont-size: 60rpx;\r\n\t\tcolor: rgba(76, 175, 80, 0.9);\r\n\t}\r\n\t\r\n\t.door-message {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #ffffff;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\t\r\n\t.door-device-name {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: rgba(255, 255, 255, 0.8);\r\n\t\tmargin-bottom: 10rpx;\r\n\t}\r\n\t\r\n\t.door-price {\r\n\t\tfont-size: 36rpx;\r\n\t\tfont-weight: 700;\r\n\t\tcolor: #ffffff;\r\n\t\tbackground: linear-gradient(to right, #A875FF, #ff36f9);\r\n\t\t-webkit-background-clip: text;\r\n\t\t-webkit-text-fill-color: transparent;\r\n\t\ttext-shadow: 0 0 10rpx rgba(168, 117, 255, 0.3);\r\n\t}\r\n\t\r\n\t.door-buttons {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\tmargin-top: 30rpx;\r\n\t}\r\n\t\r\n\t.door-cancel-button, .door-pay-button {\r\n\t\twidth: 45%;\r\n\t\theight: 80rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tborder-radius: 40rpx;\r\n\t}\r\n\t\r\n\t.door-cancel-button {\r\n\t\tbackground: rgba(255, 255, 255, 0.1);\r\n\t\tborder: 1px solid rgba(255, 255, 255, 0.3);\r\n\t}\r\n\t\r\n\t.door-pay-button {\r\n\t\tbackground: linear-gradient(145deg, #A875FF, #ff36f9);\r\n\t\tposition: relative;\r\n\t\toverflow: hidden;\r\n\t}\r\n\t\r\n\t.door-pay-button::before {\r\n\t\tcontent: '';\r\n\t\tposition: absolute;\r\n\t\ttop: -50%;\r\n\t\tleft: -50%;\r\n\t\twidth: 200%;\r\n\t\theight: 200%;\r\n\t\tbackground: linear-gradient(transparent, rgba(255, 255, 255, 0.3), transparent);\r\n\t\ttransform: rotate(45deg);\r\n\t\tanimation: shine 2s infinite;\r\n\t}\r\n\t\r\n\t.door-cancel-button text {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: rgba(255, 255, 255, 0.8);\r\n\t}\r\n\t\r\n\t.door-pay-button text {\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #ffffff;\r\n\t\tz-index: 1;\r\n\t}\r\n\t\r\n\t/* 价格信息样式 */\r\n\t.price-info {\r\n\t\tmargin-top: 40rpx;\r\n\t\tpadding: 20rpx;\r\n\t\tbackground: rgba(30, 30, 30, 0.5);\r\n\t\tborder-radius: 20rpx;\r\n\t\tborder: 1px solid rgba(168, 117, 255, 0.3);\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t}\r\n\t\r\n\t.price-title {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: rgba(255, 255, 255, 0.7);\r\n\t\tmargin-bottom: 10rpx;\r\n\t}\r\n\t\r\n\t.price-value {\r\n\t\tfont-size: 40rpx;\r\n\t\tfont-weight: 700;\r\n\t\tcolor: #ffffff;\r\n\t\tbackground: linear-gradient(to right, #A875FF, #ff36f9);\r\n\t\t-webkit-background-clip: text;\r\n\t\t-webkit-text-fill-color: transparent;\r\n\t\ttext-shadow: 0 0 10rpx rgba(168, 117, 255, 0.3);\r\n\t}\r\n\t\r\n\t/* 支付按钮样式 */\r\n\t.pay-button {\r\n\t\tbackground: rgba(255, 152, 0, 0.2);\r\n\t\tbox-shadow: 0 8rpx 20rpx rgba(255, 152, 0, 0.2);\r\n\t\tborder: 1px solid rgba(255, 152, 0, 0.5);\r\n\t\tanimation: pulse-orange 2s infinite ease-in-out;\r\n\t}\r\n\t\r\n\t.pay-button::after {\r\n\t\tbackground: linear-gradient(145deg, rgba(255, 152, 0, 0.4), rgba(255, 152, 0, 0.1));\r\n\t}\r\n\t\r\n\t.pay-button::before,\r\n\t.pay-button .ripple-layer {\r\n\t\tborder-color: rgba(255, 152, 0, 0.5);\r\n\t}\r\n\t\r\n\t.pay-button:active {\r\n\t\tbox-shadow: 0 4rpx 10rpx rgba(255, 152, 0, 0.2);\r\n\t}\r\n\t\r\n\t@keyframes pulse-orange {\r\n\t\t0%, 100% {\r\n\t\t\tbox-shadow: 0 8rpx 20rpx rgba(255, 152, 0, 0.2);\r\n\t\t}\r\n\t\t50% {\r\n\t\t\tbox-shadow: 0 8rpx 32rpx rgba(255, 152, 0, 0.5);\r\n\t\t}\r\n\t}\r\n\t\r\n\t/* 添加锁状态显示区域样式 */\r\n\t.lock-status-section {\r\n\t\tmargin-top: 40rpx;\r\n\t\tpadding: 30rpx;\r\n\t\tbackground: rgba(30, 30, 30, 0.5);\r\n\t\tborder-radius: 20rpx;\r\n\t\tborder: 1px solid rgba(168, 117, 255, 0.3);\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t}\r\n\t\r\n\t.lock-status-title {\r\n\t\tfont-size: 32rpx;\r\n\t\tcolor: rgba(255, 255, 255, 0.8);\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\t\r\n\t.lock-status-display {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tpadding: 15rpx 40rpx;\r\n\t\tborder-radius: 40rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\t\r\n\t.lock-status-display .material-icons {\r\n\t\tfont-size: 44rpx;\r\n\t\tmargin-right: 10rpx;\r\n\t}\r\n\t\r\n\t.lock-status-display text:not(.material-icons) {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: 500;\r\n\t}\r\n\t\r\n\t.lock-open {\r\n\t\tbackground: rgba(76, 175, 80, 0.2);\r\n\t\tborder: 1px solid rgba(76, 175, 80, 0.5);\r\n\t\tcolor: rgba(76, 175, 80, 0.9);\r\n\t}\r\n\t\r\n\t.lock-closed {\r\n\t\tbackground: rgba(158, 158, 158, 0.2);\r\n\t\tborder: 1px solid rgba(158, 158, 158, 0.4);\r\n\t\tcolor: rgba(255, 255, 255, 0.8);\r\n\t}\r\n\t\r\n\t.lock-battery-info {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tcolor: rgba(255, 255, 255, 0.7);\r\n\t\tfont-size: 28rpx;\r\n\t}\r\n\t\r\n\t.lock-battery-info .material-icons {\r\n\t\tfont-size: 36rpx;\r\n\t\tmargin-right: 8rpx;\r\n\t}\r\n\t\r\n\t/* 底部导航栏样式 */\r\n\t.bottom-nav-bar {\r\n\t\tposition: fixed;\r\n\t\tbottom: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\theight: 100rpx;\r\n\t\tbackground: rgba(30, 30, 30, 0.8);\r\n\t\tbackdrop-filter: blur(10px);\r\n\t\t-webkit-backdrop-filter: blur(10px);\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-around;\r\n\t\talign-items: center;\r\n\t\tborder-top: 1px solid rgba(168, 117, 255, 0.3);\r\n\t\tpadding-bottom: env(safe-area-inset-bottom);\r\n\t\tz-index: 100;\r\n\t}\r\n\t\r\n\t.nav-item {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tflex: 1;\r\n\t\theight: 100%;\r\n\t\tcolor: rgba(255, 255, 255, 0.7);\r\n\t}\r\n\t\r\n\t.nav-item .material-icons {\r\n\t\tfont-size: 48rpx;\r\n\t\tmargin-bottom: 4rpx;\r\n\t}\r\n\t\r\n\t.nav-item text:not(.material-icons) {\r\n\t\tfont-size: 24rpx;\r\n\t}\r\n\t\r\n\t.nav-item.active {\r\n\t\tcolor: var(--primary-light, #A875FF);\r\n\t}\r\n\t\r\n\t/* 调整内容区域，确保不被底部导航栏遮挡 */\r\n\t.content {\r\n\t\tpadding-bottom: calc(150rpx + env(safe-area-inset-bottom));\r\n\t}\r\n\t\r\n\t/* 订单状态显示区域样式 */\r\n\t.order-status-section {\r\n\t\tmargin-top: 40rpx;\r\n\t\tpadding: 30rpx;\r\n\t\tbackground: rgba(30, 30, 30, 0.5);\r\n\t\tborder-radius: 20rpx;\r\n\t\tborder: 1px solid rgba(168, 117, 255, 0.3);\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tanimation: fadeIn 0.5s ease;\r\n\t}\r\n\t\r\n\t.order-status-title {\r\n\t\tfont-size: 32rpx;\r\n\t\tcolor: rgba(255, 255, 255, 0.8);\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\t\r\n\t.order-status-display {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tpadding: 15rpx 40rpx;\r\n\t\tborder-radius: 40rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\tmin-width: 200rpx;\r\n\t\ttext-align: center;\r\n\t}\r\n\t\r\n\t.order-status-display .material-icons {\r\n\t\tfont-size: 44rpx;\r\n\t\tmargin-right: 10rpx;\r\n\t}\r\n\t\r\n\t.order-status-display text:not(.material-icons) {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: 500;\r\n\t}\r\n\t\r\n\t.order-paid {\r\n\t\tbackground: rgba(76, 175, 80, 0.2);\r\n\t\tborder: 1px solid rgba(76, 175, 80, 0.5);\r\n\t\tcolor: rgba(76, 175, 80, 0.9);\r\n\t\tanimation: pulse-green-soft 2s infinite alternate;\r\n\t}\r\n\t\r\n\t@keyframes pulse-green-soft {\r\n\t\t0% {\r\n\t\t\tbox-shadow: 0 0 10rpx rgba(76, 175, 80, 0.2);\r\n\t\t}\r\n\t\t100% {\r\n\t\t\tbox-shadow: 0 0 20rpx rgba(76, 175, 80, 0.5);\r\n\t\t}\r\n\t}\r\n\t\r\n\t.order-unpaid {\r\n\t\tbackground: rgba(255, 152, 0, 0.2);\r\n\t\tborder: 1px solid rgba(255, 152, 0, 0.5);\r\n\t\tcolor: rgba(255, 152, 0, 0.9);\r\n\t}\r\n\t\r\n\t.order-finished {\r\n\t\tbackground: rgba(33, 150, 243, 0.2);\r\n\t\tborder: 1px solid rgba(33, 150, 243, 0.5);\r\n\t\tcolor: rgba(33, 150, 243, 0.9);\r\n\t}\r\n\t\r\n\t.order-cancelled {\r\n\t\tbackground: rgba(158, 158, 158, 0.2);\r\n\t\tborder: 1px solid rgba(158, 158, 158, 0.4);\r\n\t\tcolor: rgba(255, 255, 255, 0.8);\r\n\t}\r\n\t\r\n\t.order-info {\r\n\t\twidth: 100%;\r\n\t\tmargin-top: 20rpx;\r\n\t}\r\n\t\r\n\t.order-info-item {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tmargin-bottom: 10rpx;\r\n\t\tcolor: rgba(255, 255, 255, 0.8);\r\n\t}\r\n\t\r\n\t.order-info-item .material-icons {\r\n\t\tfont-size: 36rpx;\r\n\t\tmargin-right: 10rpx;\r\n\t}\r\n\t\r\n\t.order-info-item text:not(.material-icons) {\r\n\t\tfont-size: 28rpx;\r\n\t}\r\n\t\r\n\t.order-unpaid-tip {\r\n\t\tmargin-top: 15rpx;\r\n\t\tcolor: rgba(255, 152, 0, 0.9);\r\n\t\tfont-size: 28rpx;\r\n\t\tanimation: blink 1.5s infinite;\r\n\t}\r\n\t\r\n\t@keyframes blink {\r\n\t\t0%, 100% {\r\n\t\t\topacity: 1;\r\n\t\t}\r\n\t\t50% {\r\n\t\t\topacity: 0.6;\r\n\t\t}\r\n\t}\r\n\t\r\n\t/* 状态标题 */\r\n\t.status-title {\r\n\t\tfont-size: 46rpx; /* 从44rpx增大到46rpx */\r\n\t\tfont-weight: 600; /* 从500增加到600，加粗文字 */\r\n\t\tmargin-bottom: 40rpx;\r\n\t\ttext-align: center;\r\n\t\tcolor: #ffffff;\r\n\t\ttext-shadow: 0 0 10rpx rgba(168, 117, 255, 0.8);\r\n\t\tletter-spacing: 2rpx;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tanimation: breathing 4s ease-in-out infinite; /* 从3秒改为4秒，使动画更慢 */\r\n\t}\r\n\t\r\n\t.status-title view {\r\n\t\tline-height: 70rpx; /* 从68rpx增大到70rpx，适应更大的字体 */\r\n\t}\r\n\t\r\n\t/* 增强的呼吸灯动画 */\r\n\t@keyframes breathing {\r\n\t\t0% {\r\n\t\t\ttext-shadow: 0 0 8rpx rgba(168, 117, 255, 0.6);\r\n\t\t\topacity: 0.85;\r\n\t\t\ttransform: scale(0.96);\r\n\t\t\tfont-size: 44rpx;\r\n\t\t}\r\n\t\t25% {\r\n\t\t\ttext-shadow: 0 0 12rpx rgba(168, 117, 255, 0.75);\r\n\t\t\topacity: 0.92;\r\n\t\t\ttransform: scale(0.98);\r\n\t\t\tfont-size: 45rpx;\r\n\t\t}\r\n\t\t50% {\r\n\t\t\ttext-shadow: \r\n\t\t\t\t0 0 15rpx rgba(168, 117, 255, 0.9),\r\n\t\t\t\t0 0 25rpx rgba(168, 117, 255, 0.6),\r\n\t\t\t\t0 0 35rpx rgba(168, 117, 255, 0.4),\r\n\t\t\t\t0 0 45rpx rgba(168, 117, 255, 0.2);\r\n\t\t\topacity: 1;\r\n\t\t\ttransform: scale(1.04);\r\n\t\t\tfont-size: 48rpx;\r\n\t\t}\r\n\t\t75% {\r\n\t\t\ttext-shadow: 0 0 12rpx rgba(168, 117, 255, 0.75);\r\n\t\t\topacity: 0.92;\r\n\t\t\ttransform: scale(0.98);\r\n\t\t\tfont-size: 45rpx;\r\n\t\t}\r\n\t\t100% {\r\n\t\t\ttext-shadow: 0 0 8rpx rgba(168, 117, 255, 0.6);\r\n\t\t\topacity: 0.85;\r\n\t\t\ttransform: scale(0.96);\r\n\t\t\tfont-size: 44rpx;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.payment-shop-name {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: rgba(255, 255, 255, 0.8);\r\n\t\tmargin: 8rpx 0 12rpx;\r\n\t\tpadding: 6rpx 16rpx;\r\n\t\tbackground-color: rgba(168, 117, 255, 0.2);\r\n\t\tborder-radius: 20rpx;\r\n\t\tborder: 1px solid rgba(168, 117, 255, 0.3);\r\n\t\tdisplay: inline-block;\r\n\t}\r\n\t\r\n\t.door-shop-name {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: rgba(255, 255, 255, 0.8);\r\n\t\tmargin: 8rpx 0 12rpx;\r\n\t\tpadding: 6rpx 16rpx;\r\n\t\tbackground-color: rgba(168, 117, 255, 0.2);\r\n\t\tborder-radius: 20rpx;\r\n\t\tborder: 1px solid rgba(168, 117, 255, 0.3);\r\n\t\tdisplay: inline-block;\r\n\t}\r\n</style> ", "import mod from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./device.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./device.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754165306707\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}