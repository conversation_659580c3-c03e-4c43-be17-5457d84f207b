<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>今夜城堡</title>
    <style>
      /* 全局样式重置 */
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
        -webkit-tap-highlight-color: transparent;
      }
      
      body {
        font-family: "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei UI Light", "Microsoft YaHei", "WenQuanYi Micro Hei", "Yuanti SC", "Source Han Sans CN", sans-serif;
        color: #ffffff;
        height: 100vh;
        width: 100vw;
        overflow: hidden;
        position: fixed;
        background-color: #000;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        touch-action: none; /* 禁止所有触摸行为 */
      }
      
      /* CSS变量定义 */
      :root {
        --primary-light: #A875FF;
        --neon-pink: #ff36f9;
        --safe-area-inset-bottom: env(safe-area-inset-bottom, 0px);
        --safe-area-inset-top: env(safe-area-inset-top, 0px);
      }
      
      /* 页面背景样式 */
      .page-background {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 0;
        background-color: #000;
      }
      
      .background-image {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 0;
        object-fit: cover;
        opacity: 0.95;
      }
      
      /* 顶部状态栏占位 */
      .status-bar {
        width: 100%;
        height: var(--safe-area-inset-top);
        background: transparent;
        position: fixed;
        top: 0;
        left: 0;
        z-index: 100;
      }
      
      /* 页面容器 */
      .container {
        width: 100%;
        height: 100vh;
        overflow: hidden;
        position: relative;
        display: flex;
        flex-direction: column;
      }
      
      /* 内容区域 */
      .content {
        flex: 1;
        padding: 0 15px;
        position: relative;
        z-index: 2;
        display: flex;
        flex-direction: column;
        overflow: hidden; /* 禁止滚动 */
        padding-top: calc(var(--safe-area-inset-top) + 10px); /* 减小顶部内边距，使整体内容上移 */
        padding-bottom: calc(60px + var(--safe-area-inset-bottom)); /* 减小底部内边距 */
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* IE and Edge */
        justify-content: center; /* 添加垂直居中 */
      }
      
      .content::-webkit-scrollbar {
        display: none; /* Chrome, Safari, Opera */
      }
      
      /* Banner样式 */
      .banner-container {
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 0; /* 移除顶部间距，使banner向上移动 */
        margin-bottom: 15px; /* 减小底部间距 */
        max-height: 100px;
      }
      
      .banner-image {
        width: 100%;
        max-height: 100px;
        object-fit: contain;
      }
      
      /* Logo区域 */
      .logo-container {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 180px; /* 稍微减小高度 */
        perspective: 1200px;
        transform-style: preserve-3d;
        margin-bottom: 20px; /* 减小底部间距 */
        margin-top: 0; /* 移除顶部间距，使logo向上移动 */
      }
      
      .logo-background {
        position: absolute;
        width: 90%;
        height: 90%;
        background: rgba(60, 25, 100, 0.05); /* 添加轻微的背景色 */
        border-radius: 15px;
        z-index: 2;
        box-shadow: 0 0 20px rgba(168, 117, 255, 0.3); /* 增强基础阴影 */
        transform: translateZ(-7px);
        overflow: hidden;
        border: 1.5px solid rgba(168, 117, 255, 0.7); /* 增加边框粗细和亮度 */
        animation: breatheBorder 4s ease-in-out infinite;
      }
      
      @keyframes breatheBorder {
        0%, 100% {
          box-shadow: 0 0 15px rgba(168, 117, 255, 0.5), 0 0 8px rgba(168, 117, 255, 0.7);
          border: 1.5px solid rgba(168, 117, 255, 0.6);
        }
        50% {
          box-shadow: 0 0 30px rgba(168, 117, 255, 0.9), 0 0 15px rgba(168, 117, 255, 1), 0 0 40px rgba(168, 117, 255, 0.4);
          border: 1.5px solid rgba(168, 117, 255, 1);
        }
      }
      
      /* 添加额外的光晕效果 */
      .logo-background::before {
        content: '';
        position: absolute;
        top: -10px;
        left: -10px;
        right: -10px;
        bottom: -10px;
        border-radius: 20px;
        background: transparent;
        z-index: -1;
        box-shadow: 0 0 25px rgba(168, 117, 255, 0.3);
        animation: glowPulse 4s ease-in-out infinite alternate;
      }
      
      @keyframes glowPulse {
        0% {
          opacity: 0.3;
          box-shadow: 0 0 25px rgba(168, 117, 255, 0.3);
        }
        100% {
          opacity: 0.7;
          box-shadow: 0 0 40px rgba(168, 117, 255, 0.6), 0 0 60px rgba(168, 117, 255, 0.3);
        }
      }
      
      .logo-image {
        width: 100%;
        max-width: 500px;
        height: auto;
        animation: logoFloat 4s ease-in-out infinite;
        opacity: 0.98;
        position: relative;
        z-index: 3;
        transform-style: preserve-3d;
        transform: translateY(0) translateZ(10px);
        filter: brightness(1.15) contrast(1.05) drop-shadow(0 7px 10px rgba(0, 0, 0, 0.5)) drop-shadow(0 0 1px rgba(51, 51, 51, 0.8));
      }
      
      @keyframes logoFloat {
        0% {
          transform: translateY(0) translateZ(10px) rotateX(0deg);
        }
        25% {
          transform: translateY(-4px) translateZ(12px) rotateX(0.5deg); /* 减小上移范围 */
        }
        75% {
          transform: translateY(4px) translateZ(8px) rotateX(-0.3deg); /* 减小下移范围 */
        }
        100% {
          transform: translateY(0) translateZ(10px) rotateX(0deg);
        }
      }
      
      /* 快捷功能区域 */
      .quick-actions {
        margin-bottom: 10px; /* 减小底部间距 */
        margin-top: 0;
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
      }
      
      /* 霓虹灯标题效果 */
      .neon-title {
        font-size: 28px;
        font-weight: 500;
        text-align: center;
        color: #fff;
        text-shadow: 0 0 3px rgba(255, 255, 255, 0.6), 0 0 7px rgba(255, 255, 255, 0.4), 0 0 9px rgba(168, 117, 255, 0.5), 0 0 15px rgba(168, 117, 255, 0.4);
        letter-spacing: 3px;
        animation: soft-flicker 4s infinite alternate;
        transform: scale(1, 0.95);
        margin-bottom: 2px;
        line-height: 1.2;
        font-family: "Yuanti SC", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei UI Light", sans-serif;
      }
      
      @keyframes soft-flicker {
        0%, 100% {
          opacity: 1;
        }
        30% {
          opacity: 0.9;
        }
        60% {
          opacity: 0.95;
        }
      }
      
      /* 扫码按钮容器 */
      .action-container {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 20px; /* 减小顶部间距，使按钮整体上移 */
        width: 100%;
      }
      
      /* 主扫码按钮样式 */
      .scan-button-wrapper {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 160px;
        height: 160px;
        padding: 10px;
        transition: transform 0.3s, box-shadow 0.3s;
      }
      
      .scan-button-wrapper:active {
        transform: scale(0.96);
      }
      
      /* 扫描框角 */
      .scan-corner {
        position: absolute;
        width: 25px;
        height: 25px;
        border-color: var(--primary-light, #A875FF);
        opacity: 0.8;
        z-index: 4;
        box-shadow: 0 0 5px rgba(168, 117, 255, 0.5);
      }
      
      .scan-corner-top-left {
        top: 0;
        left: 0;
        border-top: 3px solid;
        border-left: 3px solid;
        border-top-left-radius: 4px;
      }
      
      .scan-corner-top-right {
        top: 0;
        right: 0;
        border-top: 3px solid;
        border-right: 3px solid;
        border-top-right-radius: 4px;
      }
      
      .scan-corner-bottom-left {
        bottom: 0;
        left: 0;
        border-bottom: 3px solid;
        border-left: 3px solid;
        border-bottom-left-radius: 4px;
      }
      
      .scan-corner-bottom-right {
        bottom: 0;
        right: 0;
        border-bottom: 3px solid;
        border-right: 3px solid;
        border-bottom-right-radius: 4px;
      }
      
      /* 按钮内层 */
      .scan-button-inner {
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 120px;
        height: 120px;
        border-radius: 50%;
        background: transparent;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
        z-index: 2;
        overflow: visible;
      }
      
      /* 增加圆圈边缘光波扩散效果 */
      .scan-button-inner::before,
      .scan-button-inner::after {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        border-radius: 50%;
        border: 1px solid rgba(168, 117, 255, 0.7);
        z-index: -1;
        box-shadow: 0 0 6px rgba(168, 117, 255, 0.4);
      }
      
      .scan-button-inner::before {
        animation: edge-wave-1 2s ease-out infinite;
      }
      
      /* 添加多层光波扩散效果 */
      .scan-wave {
        position: absolute;
        border-radius: 50%;
        border: 0.75px solid rgba(168, 117, 255, 0.5);
        box-shadow: 0 0 4px rgba(168, 117, 255, 0.3);
        pointer-events: none;
        z-index: -1;
      }
      
      .wave-1 {
        width: 100%;
        height: 100%;
        animation: wave-anim 2s cubic-bezier(0.25, 0.46, 0.45, 0.94) infinite;
      }
      
      .wave-2 {
        width: 100%;
        height: 100%;
        animation: wave-anim 2s cubic-bezier(0.25, 0.46, 0.45, 0.94) infinite 0.66s;
      }
      
      .wave-3 {
        width: 100%;
        height: 100%;
        animation: wave-anim 2s cubic-bezier(0.25, 0.46, 0.45, 0.94) infinite 1.33s;
      }
      
      @keyframes wave-anim {
        0% {
          transform: scale(1);
          opacity: 0;
        }
        30% {
          transform: scale(1.4);
          opacity: 0.4;
        }
        60% {
          transform: scale(1.6);
          opacity: 0;
        }
        100% {
          transform: scale(1);
          opacity: 0;
        }
      }
      
      @keyframes edge-wave-1 {
        0% {
          transform: scale(1);
          opacity: 0.2;
        }
        30% {
          transform: scale(1.2);
          opacity: 0.5;
        }
        60% {
          transform: scale(1.35);
          opacity: 0;
        }
        100% {
          transform: scale(1);
          opacity: 0.2;
        }
      }
      
      /* 脉冲动画 */
      .scan-pulse {
        position: absolute;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        background: radial-gradient(circle, rgba(255, 255, 255, 0.7) 0%, rgba(168, 117, 255, 0.4) 40%, rgba(168, 117, 255, 0) 70%);
        z-index: 1;
        opacity: 0;
        transform-origin: center;
        pointer-events: none;
        animation: auto-pulse 2s cubic-bezier(0.25, 0.46, 0.45, 0.94) infinite;
      }
      
      @keyframes auto-pulse {
        0% {
          transform: scale(0.5);
          opacity: 0;
        }
        30% {
          transform: scale(0.8);
          opacity: 0.5;
        }
        60% {
          transform: scale(0.5);
          opacity: 0;
        }
        100% {
          transform: scale(0.5);
          opacity: 0;
        }
      }
      
      /* 图标样式 */
      .scan-icon-container {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 80px;
        height: 80px;
        z-index: 3;
      }
      
      .material-icons {
        font-family: 'Material Icons';
        font-weight: normal;
        font-style: normal;
        font-size: 24px;
        line-height: 1;
        letter-spacing: normal;
        text-transform: none;
        display: inline-block;
        white-space: nowrap;
        word-wrap: normal;
        direction: ltr;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
      }
      
      .heart-icon {
        font-size: 80px;
        color: #d4a9ff;
        text-shadow: 0 0 7px rgba(212, 169, 255, 0.7), 0 0 15px rgba(212, 169, 255, 0.5), 0 2px 5px rgba(0, 0, 0, 0.7);
        animation: heart-pulse 2s cubic-bezier(0.25, 0.46, 0.45, 0.94) infinite;
        transform-style: preserve-3d;
        transform: perspective(400px) translateZ(0);
        filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.5)) drop-shadow(0 0 5px rgba(212, 169, 255, 0.6));
      }
      
      @keyframes heart-pulse {
        0% {
          transform: perspective(400px) translateZ(0) scale(1);
          text-shadow: 0 0 7px rgba(212, 169, 255, 0.7), 0 0 15px rgba(212, 169, 255, 0.5), 0 2px 5px rgba(0, 0, 0, 0.7);
          filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.5)) drop-shadow(0 0 5px rgba(212, 169, 255, 0.6));
        }
        30% {
          transform: perspective(400px) translateZ(15px) scale(1.35);
          text-shadow: 0 0 15px rgba(212, 169, 255, 0.9), 0 0 25px rgba(212, 169, 255, 0.7), 0 7px 12px rgba(0, 0, 0, 0.8);
          filter: drop-shadow(0 4px 6px rgba(0, 0, 0, 0.6)) drop-shadow(0 0 10px rgba(212, 169, 255, 0.8));
        }
        60% {
          transform: perspective(400px) translateZ(0) scale(1);
          text-shadow: 0 0 7px rgba(212, 169, 255, 0.7), 0 0 15px rgba(212, 169, 255, 0.5), 0 2px 5px rgba(0, 0, 0, 0.7);
          filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.5)) drop-shadow(0 0 5px rgba(212, 169, 255, 0.6));
        }
        100% {
          transform: perspective(400px) translateZ(0) scale(1);
          text-shadow: 0 0 7px rgba(212, 169, 255, 0.7), 0 0 15px rgba(212, 169, 255, 0.5), 0 2px 5px rgba(0, 0, 0, 0.7);
          filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.5)) drop-shadow(0 0 5px rgba(212, 169, 255, 0.6));
        }
      }
      
      /* 协议提示文字样式 */
      .agreement-tip {
        text-align: center;
        font-size: 12px;
        color: rgba(255, 255, 255, 0.5);
        position: relative; /* 改为相对定位，不再固定在底部 */
        margin-top: 20px; /* 添加顶部间距 */
        left: 0;
        right: 0;
        padding: 0 15px;
        z-index: 10;
        font-family: "Yuanti SC", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei UI Light", sans-serif;
        font-weight: 300;
      }
      
      .agreement-tip a {
        color: var(--primary-light);
        text-decoration: none;
        position: relative;
        display: inline-block;
        text-shadow: 0 0 2px rgba(168, 117, 255, 0.3);
        transition: all 0.3s ease;
      }
      
      .agreement-tip a:hover {
        color: #fff;
        text-shadow: 0 0 5px rgba(168, 117, 255, 0.8), 0 0 10px rgba(168, 117, 255, 0.5);
      }
      
      /* 底部空间 */
      .bottom-space {
        height: 20px; /* 减小底部空间高度 */
      }
      
      /* 底部导航栏 */
      .tab-bar {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        height: calc(60px + var(--safe-area-inset-bottom)); /* 稍微减小导航栏高度 */
        background: #FFFFFF;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        display: flex;
        padding-bottom: var(--safe-area-inset-bottom);
        border-top: 1px solid rgba(0, 0, 0, 0.05);
        z-index: 100;
        font-family: "Yuanti SC", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei UI Light", sans-serif;
      }
      
      .tab-item {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: rgba(0, 0, 0, 0.5);
        font-size: 14px; /* 增大文字尺寸 */
        padding-top: 8px;
        position: relative;
        font-weight: 700; /* 增加字重，从500改为700，使文字更加粗体 */
      }
      
      .tab-item.active {
        color: var(--primary-light);
        font-weight: 800; /* 为激活状态的标签增加更高的字重 */
      }
      
      .tab-item.active .tab-icon {
        color: var(--primary-light);
        text-shadow: 0 0 12px rgba(168, 117, 255, 0.4); /* 增强活动状态的发光效果 */
      }
      
      .tab-icon {
        font-size: 28px; /* 增大图标尺寸 */
        margin-bottom: 3px; /* 增加间距 */
        color: rgba(0, 0, 0, 0.5);
      }
      
      /* 导航栏指示器 */
      .tab-indicator {
        display: none; /* 隐藏指示器 */
      }
      
      .tab-item.active .tab-indicator {
        opacity: 0; /* 确保活动状态下也不显示 */
      }
      
      /* 加载Material Icons字体 */
      @font-face {
        font-family: 'Material Icons';
        font-style: normal;
        font-weight: 400;
        src: url(https://fonts.gstatic.com/s/materialicons/v139/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format('woff2');
      }
      
      /* 媒体查询，适配不同尺寸的屏幕 */
      @media screen and (max-height: 700px) {
        .logo-container {
          height: 140px; /* 减小logo容器高度 */
          margin-bottom: 10px;
          margin-top: 0; /* 移除顶部间距 */
        }
        
        .banner-container {
          margin-top: 0;
          margin-bottom: 10px;
          max-height: 70px;
        }
        
        .banner-image {
          max-height: 70px;
        }
        
        .quick-actions {
          margin-top: 0;
        }
        
        .scan-button-wrapper {
          width: 140px; /* 稍微减小按钮尺寸 */
          height: 140px;
        }
        
        .scan-button-inner {
          width: 100px;
          height: 100px;
        }
        
        .heart-icon {
          font-size: 70px;
        }
        
        .neon-title {
          font-size: 26px;
        }
        
        .agreement-tip {
          margin-top: 15px; /* 调整间距 */
        }
        
        .action-container {
          margin-top: 15px; /* 中等屏幕上的顶部间距 */
        }
        
        .content {
          padding-top: calc(var(--safe-area-inset-top) + 5px); /* 中等屏幕上的顶部内边距 */
        }
      }
      
      @media screen and (max-height: 600px) {
        .content {
          padding-top: calc(var(--safe-area-inset-top) + 5px); /* 小屏幕上的顶部内边距 */
        }
        
        .banner-container {
          margin-top: 0;
          margin-bottom: 5px;
          max-height: 50px; /* 进一步减小banner高度 */
        }
        
        .banner-image {
          max-height: 50px;
        }
        
        .logo-container {
          height: 90px;
          margin-bottom: 5px;
          margin-top: 0; /* 移除顶部间距 */
        }
        
        .quick-actions {
          margin-top: 0;
          margin-bottom: 5px;
        }
        
        .neon-title {
          font-size: 20px; /* 减小字体大小 */
          margin-bottom: 0;
        }
        
        .scan-button-wrapper {
          width: 110px; /* 更小的按钮尺寸 */
          height: 110px;
          margin-top: 0;
        }
        
        .scan-button-inner {
          width: 75px;
          height: 75px;
        }
        
        .heart-icon {
          font-size: 50px;
        }
        
        .agreement-tip {
          margin-top: 10px; /* 调整间距 */
          font-size: 10px;
        }
        
        .action-container {
          margin-top: 10px; /* 小屏幕上的顶部间距 */
        }
        
        .tab-bar {
          height: calc(55px + var(--safe-area-inset-bottom)); /* 小屏幕上减小导航栏高度 */
        }
        
        .tab-icon {
          font-size: 26px; /* 小屏幕上稍微减小图标尺寸 */
        }
        
        .tab-item {
          font-size: 13px; /* 小屏幕上稍微减小文字尺寸 */
        }
      }
      
      /* 横屏适配 - 调整为更紧凑的布局 */
      @media screen and (orientation: landscape) and (max-height: 500px) {
        .content {
          flex-direction: row;
          flex-wrap: wrap;
          justify-content: center;
          align-items: center;
          padding-top: calc(var(--safe-area-inset-top) + 5px); /* 减小横屏模式下的顶部内边距 */
        }
        
        .welcome-section {
          width: 40%;
          margin-right: 5%;
          display: flex;
          flex-direction: column;
          justify-content: center; /* 添加垂直居中 */
        }
        
        .quick-actions {
          width: 55%;
          margin-top: 0;
          justify-content: center; /* 确保垂直居中 */
        }
        
        .logo-container {
          height: 70px; /* 减小高度 */
          margin-bottom: 5px;
          margin-top: 0; /* 移除顶部间距 */
        }
        
        .banner-container {
          margin-top: 0;
          margin-bottom: 0;
          max-height: 40px; /* 减小banner高度 */
        }
        
        .banner-image {
          max-height: 40px;
        }
        
        .agreement-tip {
          position: relative;
          bottom: auto;
          margin-top: 10px;
          margin-bottom: 5px;
          font-size: 10px;
        }
        
        .action-container {
          margin-top: 5px; /* 减小横屏模式下的顶部间距 */
        }
        
        .scan-button-wrapper {
          width: 100px; /* 横屏模式下减小按钮尺寸 */
          height: 100px;
        }
        
        .scan-button-inner {
          width: 70px;
          height: 70px;
        }
        
        .heart-icon {
          font-size: 45px;
        }
        
        .neon-title {
          font-size: 18px; /* 减小字体大小 */
        }
      }
    </style>
  </head>
  <body>
    <div class="container page-index">
      <!-- 页面背景 -->
      <div class="page-background">
        <!-- 背景图片 -->
        <img class="background-image" src="https://wuji1112-1312674791.cos.ap-guangzhou.myqcloud.com/static/shouye3.png" alt="背景">
      </div>
      
      <!-- 顶部状态栏占位 -->
      <div class="status-bar"></div>
      
      <!-- 页面内容 -->
      <div class="content">
        <!-- 顶部欢迎区域 -->
        <div class="welcome-section">
          <!-- 添加顶部banner -->
          <div class="banner-container">
            <img class="banner-image" src="https://wuji1112-1312674791.cos.ap-guangzhou.myqcloud.com/static/<EMAIL>" alt="Banner">
          </div>
          
          <!-- 添加logo -->
          <div class="logo-container">
            <div class="logo-background"></div>
            <img class="logo-image" src="https://wuji1112-1312674791.cos.ap-guangzhou.myqcloud.com/static/logo.svg" alt="Logo">
          </div>
        </div>
        
        <!-- 快捷功能区域 -->
        <div class="quick-actions">
          <div class="neon-title-container">
            <div class="neon-title">点亮爱心</div>
            <div class="neon-title">扫码开门</div>
          </div>
          
          <!-- 显示扫码按钮 -->
          <div class="action-container">
            <div class="scan-button-wrapper">
              <div class="scan-button-inner">
                <div class="scan-icon-container">
                  <i class="material-icons heart-icon">favorite</i>
                </div>
                <div class="scan-pulse"></div>
                <div class="scan-wave wave-1"></div>
                <div class="scan-wave wave-2"></div>
                <div class="scan-wave wave-3"></div>
              </div>
              
              <!-- 扫描框角 -->
              <div class="scan-corner scan-corner-top-left"></div>
              <div class="scan-corner scan-corner-top-right"></div>
              <div class="scan-corner scan-corner-bottom-left"></div>
              <div class="scan-corner scan-corner-bottom-right"></div>
            </div>
          </div>
          
          <!-- 添加协议提示文字 -->
          <div class="agreement-tip">
            点击开门即视为同意<a href="javascript:void(0);">《用户协议》</a>及<a href="javascript:void(0);">《隐私协议》</a>
          </div>
        </div>
      </div>
      
      <!-- 自定义TabBar -->
      <div class="tab-bar">
        <div class="tab-item active">
          <i class="material-icons tab-icon">meeting_room</i>
          <span>首页</span>
        </div>
        <div class="tab-item">
          <i class="material-icons tab-icon">diamond</i>
          <span>我的</span>
        </div>
      </div>
    </div>

    <script>
      // 禁止所有可能导致滚动的事件
      document.addEventListener('touchmove', function(e) {
        e.preventDefault();
      }, { passive: false });
      
      document.addEventListener('wheel', function(e) {
        e.preventDefault();
      }, { passive: false });
    </script>
  </body>
</html>
