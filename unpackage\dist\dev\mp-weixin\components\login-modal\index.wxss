






























































































































































/* 登录弹窗样式 */
.login-modal {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0.6);
	z-index: 10000; /* 提高z-index，确保在TabBar之上 */
	display: flex;
	justify-content: center;
	align-items: center;
	-webkit-backdrop-filter: blur(5px);
	        backdrop-filter: blur(5px);
}
.login-modal-content {
	width: 80%;
	max-width: 600rpx;
	background-color: #fff;
	border-radius: 20rpx;
	box-shadow: 0 0 20rpx rgba(0, 0, 0, 0.2);
	overflow: hidden;
	margin-bottom: 120rpx; /* 添加底部间距，避免遮挡TabBar */
}
.login-modal-header {
	position: relative;
	padding: 30rpx;
	text-align: center;
	border-bottom: 1rpx solid #f0f0f0;
}
.login-modal-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}
.close-icon {
	position: absolute;
	right: 20rpx;
	top: 50%;
	-webkit-transform: translateY(-50%);
	        transform: translateY(-50%);
	font-size: 40rpx;
	color: #999;
}
.login-modal-body {
	padding: 40rpx 30rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
}
.login-icon {
	font-size: 80rpx;
	color: #A875FF; /* 修改为紫色 */
	margin-bottom: 20rpx;
}
.login-modal-desc {
	font-size: 28rpx;
	color: #666;
	margin-bottom: 30rpx;
	text-align: center;
}
.auth-btn {
	width: 100%;
	height: 80rpx;
	line-height: 80rpx;
	background: linear-gradient(to right, #A875FF, #8B5CF6); /* 修改为紫色渐变 */
	color: #fff;
	border-radius: 40rpx;
	font-size: 28rpx;
	margin-top: 20rpx;
	text-align: center;
}
.auth-btn::after {
	border: none;
}
.auth-explanation {
	font-size: 24rpx;
	color: #999;
	margin-bottom: 20rpx;
	text-align: center;
	padding: 0 20rpx;
}
/* Material Icons 字体 */
@font-face {
	font-family: 'Material Icons';
	font-style: normal;
	font-weight: 400;
	src: url(https://fonts.gstatic.com/s/materialicons/v139/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format('woff2');
	font-display: block;
}
.material-icons {
	font-family: 'Material Icons';
	font-weight: normal;
	font-style: normal;
	font-size: 48rpx;
	line-height: 1;
	letter-spacing: normal;
	text-transform: none;
	display: inline-block;
	white-space: nowrap;
	word-wrap: normal;
	direction: ltr;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	text-rendering: optimizeLegibility;
}

