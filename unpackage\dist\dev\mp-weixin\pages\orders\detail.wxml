<view class="container page-order-detail"><view class="page-background"><image class="background-image" src="https://wuji1112-1312674791.cos.ap-guangzhou.myqcloud.com/static/shouye3.png" mode="aspectFill"></image><view class="frosted-overlay"></view></view><view class="navbar" style="{{(navbarStyle)}}"><view data-event-opts="{{[['tap',[['goBack',['$event']]]]]}}" class="navbar-left" bindtap="__e"><text class="material-icons md-24 text-primary">arrow_back</text></view><view class="navbar-title">订单详情</view><view class="navbar-right"></view></view><view class="page-content"><view class="content"><view class="{{['order-status-card',(orderDetail.rawStatus===1)?'active':'',(orderDetail.rawStatus===0)?'unpaid':'',(orderDetail.rawStatus===2)?'completed':'',(orderDetail.rawStatus===3)?'cancelled':'',(orderDetail.rawStatus!==0&&orderDetail.rawStatus!==1&&orderDetail.rawStatus!==2&&orderDetail.rawStatus!==3)?'inactive':'']}}"><view class="status-icon"><block wx:if="{{orderDetail.rawStatus===0}}"><text class="material-icons md-24">schedule</text></block><block wx:else><block wx:if="{{orderDetail.rawStatus===1}}"><text class="material-icons md-24">check_circle</text></block><block wx:else><block wx:if="{{orderDetail.rawStatus===2}}"><text class="material-icons md-24">task_alt</text></block><block wx:else><block wx:if="{{orderDetail.rawStatus===3}}"><text class="material-icons md-24">cancel</text></block><block wx:else><text class="material-icons md-24">help_outline</text></block></block></block></block></view><view class="status-text"><text class="status-title">{{orderDetail.status}}</text><block wx:if="{{orderDetail.rawStatus===0}}"><text class="status-desc">请尽快完成支付</text></block><block wx:else><block wx:if="{{orderDetail.rawStatus===1}}"><text class="status-desc">订单使用中</text></block><block wx:else><block wx:if="{{orderDetail.rawStatus===2}}"><text class="status-desc">感谢您的使用</text></block><block wx:else><block wx:if="{{orderDetail.rawStatus===3}}"><text class="status-desc">订单已取消</text></block><block wx:else><text class="status-desc">状态未知，请联系客服</text></block></block></block></block></view></view><view class="card mt-lg"><view class="card-header"><text class="card-icon">📃</text><text>订单信息</text></view><view class="card-content"><view class="detail-item flex justify-between"><text class="detail-label">订单编号</text><text class="detail-value">{{orderDetail.orderNo}}</text></view><view class="detail-item flex justify-between mt-sm"><text class="detail-label">下单时间</text><text class="detail-value">{{orderDetail.startTime}}</text></view><block wx:if="{{orderDetail.endTime}}"><view class="detail-item flex justify-between mt-sm"><text class="detail-label">结束时间</text><text class="detail-value">{{orderDetail.endTime}}</text></view></block><block wx:if="{{orderDetail.duration}}"><view class="detail-item flex justify-between mt-sm"><text class="detail-label">使用时长</text><text class="detail-value">{{orderDetail.duration}}</text></view></block><view class="detail-item flex justify-between mt-sm"><text class="detail-label">支付方式</text><text class="detail-value">{{orderDetail.payType||'微信支付'}}</text></view></view></view><view class="card mt-md"><view class="card-header"><text class="card-icon">🏪</text><text>门店信息</text></view><view class="card-content"><view class="detail-item flex justify-between"><text class="detail-label">门店名称</text><text class="detail-value">{{orderDetail.storeName}}</text></view><block wx:if="{{orderDetail.deviceName}}"><view class="detail-item flex justify-between mt-sm"><text class="detail-label">设备名称</text><text class="detail-value">{{orderDetail.deviceName}}</text></view></block><block wx:if="{{orderDetail.deviceNo}}"><view class="detail-item flex justify-between mt-sm"><text class="detail-label">设备编号</text><text class="detail-value">{{orderDetail.deviceNo}}</text></view></block></view></view><view class="card mt-md"><view class="card-header"><text class="card-icon">💰</text><text>支付信息</text></view><view class="card-content"><view class="detail-item flex justify-between"><text class="detail-label">消费金额</text><text class="detail-value primary-light">{{orderDetail.amount}}</text></view><block wx:if="{{orderDetail.rawStatus===0}}"><view class="detail-item flex justify-between mt-sm"><text class="detail-label">支付状态</text><text class="detail-value error-light">未支付</text></view></block><block wx:else><block wx:if="{{orderDetail.rawStatus===1||orderDetail.rawStatus===2}}"><view class="detail-item flex justify-between mt-sm"><text class="detail-label">支付状态</text><text class="detail-value success-light">已支付</text></view></block></block><block wx:if="{{orderDetail.payTime}}"><view class="detail-item flex justify-between mt-sm"><text class="detail-label">支付时间</text><text class="detail-value">{{orderDetail.payTime}}</text></view></block></view></view><view class="actions mt-lg"><block wx:if="{{orderDetail.rawStatus===0}}"><button data-event-opts="{{[['tap',[['payOrder',['$event']]]]]}}" class="btn btn-primary btn-block" bindtap="__e"><text class="btn-icon">💳</text>去支付</button></block><button data-event-opts="{{[['tap',[['reportProblem',['$event']]]]]}}" class="btn btn-outline btn-block mt-md" bindtap="__e"><text class="btn-icon">⚠️</text>设备异常报告</button></view></view></view></view>