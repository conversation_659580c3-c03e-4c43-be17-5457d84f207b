{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/contact/contact.vue?ed0a", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/contact/contact.vue?d00b", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/contact/contact.vue?d20d", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/contact/contact.vue?4e1f", "uni-app:///pages/contact/contact.vue", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/contact/contact.vue?a793", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/contact/contact.vue?f4e7"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "loading", "contactInfo", "onlineService", "workTime", "responseTime", "phoneService", "phoneNumber", "faqs", "onLoad", "methods", "goBack", "uni", "getContactInfo", "then", "catch", "console", "title", "icon", "finally", "getFaqs", "item", "expanded", "question", "answer", "contactOnline", "setTimeout", "content", "confirmText", "cancelText", "success", "callPhone", "fail", "toggleFaq"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACa;;;AAGnE;AACgM;AAChM,gBAAgB,uMAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAqvB,CAAgB,mvBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCyGzwB;EACAC;IACA;MACAC;MACAC;QACAC;UACAC;UACAC;QACA;QACAC;UACAC;UACAH;QACA;MACA;MACAI;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACAC;MACAC;IACA;IACA;IACAC;MAAA;MACA,mCACAC;QACA;UACA;QACA;MACA,GACAC;QACAC;QACAJ;UACAK;UACAC;QACA;MACA,GACAC;QACA;MACA;IACA;IACA;IACAC;MAAA;MACA,wBACAN;QACA;UACA;YAAA,uCACAO;cACAC;YAAA;UAAA,CACA;QACA;UACA;UACA,eACA;YACAC;YACAC;YACAF;UACA,GACA;YACAC;YACAC;YACAF;UACA,GACA;YACAC;YACAC;YACAF;UACA,GACA;YACAC;YACAC;YACAF;UACA,GACA;YACAC;YACAC;YACAF;UACA,EACA;QACA;MACA,GACAP;QACAC;QACA;QACA,eACA;UACAO;UACAC;UACAF;QACA,GACA;UACAC;UACAC;UACAF;QACA,GACA;UACAC;UACAC;UACAF;QACA,GACA;UACAC;UACAC;UACAF;QACA,GACA;UACAC;UACAC;UACAF;QACA,EACA;MACA;IACA;IACAG;MACAb;QACAK;QACAC;MACA;;MAEA;MACAQ;QACAd;UACAK;UACAU;UACAC;UACAC;UACAC;YACA;cACAlB;gBACAK;gBACAC;cACA;YACA;UACA;QACA;MACA;IACA;IACAa;MACA;MACAnB;QACAL;QACAuB;UACAd;QACA;QACAgB;UACAhB;UACAJ;YACAK;YACAC;UACA;QACA;MACA;IACA;IACAe;MACA;QACA;UACAZ;QACA;UACAA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC/QA;AAAA;AAAA;AAAA;AAAkkC,CAAgB,4hCAAG,EAAC,C;;;;;;;;;;;ACAtlC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/contact/contact.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/contact/contact.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./contact.vue?vue&type=template&id=476c8d48&\"\nvar renderjs\nimport script from \"./contact.vue?vue&type=script&lang=js&\"\nexport * from \"./contact.vue?vue&type=script&lang=js&\"\nimport style0 from \"./contact.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/contact/contact.vue\"\nexport default component.exports", "export * from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./contact.vue?vue&type=template&id=476c8d48&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./contact.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./contact.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container page-contact\">\r\n\t\t<!-- 页面背景 -->\r\n\t\t<view class=\"page-background\">\r\n\t\t\t<!-- 页面背景波浪装饰 -->\r\n\t\t\t<view class=\"wave-decoration\">\r\n\t\t\t\t<view class=\"wave wave1\"></view>\r\n\t\t\t\t<view class=\"wave wave2\"></view>\r\n\t\t\t\t<view class=\"wave wave3\"></view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 页面渐变背景 -->\r\n\t\t\t<view class=\"gradient-overlay\"></view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 导航栏 -->\r\n\t\t<view class=\"navbar\">\r\n\t\t\t<view class=\"navbar-left\" @click=\"goBack\">\r\n\t\t\t\t<text class=\"material-icons md-24 text-primary\">arrow_back</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"navbar-title\">联系客服</view>\r\n\t\t\t<view class=\"navbar-right\"></view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 页面内容 -->\r\n\t\t<view class=\"content\">\r\n\t\t\t<!-- 在线客服 -->\r\n\t\t\t<view class=\"section card-container\">\r\n\t\t\t\t<view class=\"section-title\">\r\n\t\t\t\t\t<view class=\"icon-wrapper neon-blue-bg\">\r\n\t\t\t\t\t\t<text class=\"material-icons neon-blue\">support_agent</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<text class=\"title-md\">在线客服</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"card contact-card\">\r\n\t\t\t\t\t<view class=\"contact-item\">\r\n\t\t\t\t\t\t<text class=\"text-secondary\">工作时间</text>\r\n\t\t\t\t\t\t<text class=\"text-primary\">{{ (contactInfo.onlineService && contactInfo.onlineService.workTime) || '每日 10:00 - 22:00' }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"contact-item mt-md\">\r\n\t\t\t\t\t\t<text class=\"text-secondary\">平均响应时间</text>\r\n\t\t\t\t\t\t<text class=\"text-primary\">{{ (contactInfo.onlineService && contactInfo.onlineService.responseTime) || '5分钟内' }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<button class=\"btn btn-primary mt-lg\" @click=\"contactOnline\">立即咨询</button>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 电话客服 -->\r\n\t\t\t<view class=\"section card-container mt-lg\">\r\n\t\t\t\t<view class=\"section-title\">\r\n\t\t\t\t\t<view class=\"icon-wrapper neon-green-bg\">\r\n\t\t\t\t\t\t<text class=\"material-icons neon-green\">phone</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<text class=\"title-md\">电话客服</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"card contact-card\">\r\n\t\t\t\t\t<view class=\"contact-item\">\r\n\t\t\t\t\t\t<text class=\"text-secondary\">客服热线</text>\r\n\t\t\t\t\t\t<text class=\"text-primary\">{{ (contactInfo.phoneService && contactInfo.phoneService.phoneNumber) || '************' }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"contact-item mt-md\">\r\n\t\t\t\t\t\t<text class=\"text-secondary\">工作时间</text>\r\n\t\t\t\t\t\t<text class=\"text-primary\">{{ (contactInfo.phoneService && contactInfo.phoneService.workTime) || '每日 9:00 - 21:00' }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<button class=\"btn btn-outline mt-lg\" @click=\"callPhone\">拨打电话</button>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 常见问题 -->\r\n\t\t\t<view class=\"section card-container mt-lg\">\r\n\t\t\t\t<view class=\"section-title\">\r\n\t\t\t\t\t<view class=\"icon-wrapper neon-yellow-bg\">\r\n\t\t\t\t\t\t<text class=\"material-icons neon-yellow\">help_outline</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<text class=\"title-md\">常见问题</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"faq-list\">\r\n\t\t\t\t\t<view class=\"card faq-item\" v-for=\"(item, index) in faqs\" :key=\"index\" @click=\"toggleFaq(index)\">\r\n\t\t\t\t\t\t<view class=\"faq-header flex justify-between items-center\">\r\n\t\t\t\t\t\t\t<text class=\"faq-title\">{{item.question}}</text>\r\n\t\t\t\t\t\t\t<text class=\"material-icons md-24 text-tertiary\">\r\n\t\t\t\t\t\t\t\t{{item.expanded ? 'keyboard_arrow_up' : 'keyboard_arrow_down'}}\r\n\t\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"faq-content\" v-if=\"item.expanded\">\r\n\t\t\t\t\t\t\t<view class=\"divider mt-sm mb-sm\"></view>\r\n\t\t\t\t\t\t\t<text class=\"text-secondary\">{{item.answer}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 底部空白区域，确保内容不被遮挡 -->\r\n\t\t\t<view class=\"bottom-space\"></view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 加载状态 -->\r\n\t\t<view class=\"loading-container\" v-if=\"loading\">\r\n\t\t\t<view class=\"loading-spinner\"></view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tloading: true,\r\n\t\t\t\tcontactInfo: {\r\n\t\t\t\t\tonlineService: {\r\n\t\t\t\t\t\tworkTime: '每日 10:00 - 22:00',\r\n\t\t\t\t\t\tresponseTime: '5分钟内'\r\n\t\t\t\t\t},\r\n\t\t\t\t\tphoneService: {\r\n\t\t\t\t\t\tphoneNumber: '************',\r\n\t\t\t\t\t\tworkTime: '每日 9:00 - 21:00'\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\tfaqs: []\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\tthis.getContactInfo();\r\n\t\t\tthis.getFaqs();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgoBack() {\r\n\t\t\t\tuni.navigateBack();\r\n\t\t\t},\r\n\t\t\t// 获取客服联系信息\r\n\t\t\tgetContactInfo() {\r\n\t\t\t\tthis.$api.help.getServiceContact()\r\n\t\t\t\t\t.then(res => {\r\n\t\t\t\t\t\tif (res.data) {\r\n\t\t\t\t\t\t\tthis.contactInfo = res.data;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch(err => {\r\n\t\t\t\t\t\tconsole.error('获取客服联系信息失败:', err);\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '获取客服信息失败',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.finally(() => {\r\n\t\t\t\t\t\tthis.loading = false;\r\n\t\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// 获取常见问题\r\n\t\t\tgetFaqs() {\r\n\t\t\t\tthis.$api.help.getFAQ()\r\n\t\t\t\t\t.then(res => {\r\n\t\t\t\t\t\tif (res.data && res.data.length) {\r\n\t\t\t\t\t\t\tthis.faqs = res.data.map(item => ({\r\n\t\t\t\t\t\t\t\t...item,\r\n\t\t\t\t\t\t\t\texpanded: false\r\n\t\t\t\t\t\t\t}));\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t// 使用默认FAQ数据\r\n\t\t\t\t\t\t\tthis.faqs = [\r\n\t\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\tquestion: '如何预订今夜城堡的服务？',\r\n\t\t\t\t\t\t\t\t\tanswer: '您可以通过我们的小程序直接预订，或者前往门店现场预订。建议提前预约以确保有空位。',\r\n\t\t\t\t\t\t\t\t\texpanded: false\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\tquestion: '如何取消或修改已预订的服务？',\r\n\t\t\t\t\t\t\t\t\tanswer: '您可以在\"我的订单\"中找到相应订单，点击\"取消预订\"按钮。如需修改，请先取消原订单再重新预订，或联系客服协助处理。',\r\n\t\t\t\t\t\t\t\t\texpanded: false\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\tquestion: '忘记带充电器怎么办？',\r\n\t\t\t\t\t\t\t\t\tanswer: '别担心，我们的每个房间都配备了各种型号的充电器，包括Type-C、Lightning和其他常见接口，可免费使用。',\r\n\t\t\t\t\t\t\t\t\texpanded: false\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\tquestion: '如何上报设备异常问题？',\r\n\t\t\t\t\t\t\t\t\tanswer: '您可以在\"我的\"页面中找到\"设备异常上报\"功能，填写相关信息并提交。我们会尽快处理并联系您。',\r\n\t\t\t\t\t\t\t\t\texpanded: false\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\tquestion: '会员有哪些特权？',\r\n\t\t\t\t\t\t\t\t\tanswer: '会员可享受折扣价格、优先预订、积分奖励、生日特惠等多项特权。详情可在\"我的\"-\"会员权益\"中查看。',\r\n\t\t\t\t\t\t\t\t\texpanded: false\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t];\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch(err => {\r\n\t\t\t\t\t\tconsole.error('获取常见问题失败:', err);\r\n\t\t\t\t\t\t// 使用默认FAQ数据\r\n\t\t\t\t\t\tthis.faqs = [\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tquestion: '如何预订今夜城堡的服务？',\r\n\t\t\t\t\t\t\t\tanswer: '您可以通过我们的小程序直接预订，或者前往门店现场预订。建议提前预约以确保有空位。',\r\n\t\t\t\t\t\t\t\texpanded: false\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tquestion: '如何取消或修改已预订的服务？',\r\n\t\t\t\t\t\t\t\tanswer: '您可以在\"我的订单\"中找到相应订单，点击\"取消预订\"按钮。如需修改，请先取消原订单再重新预订，或联系客服协助处理。',\r\n\t\t\t\t\t\t\t\texpanded: false\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tquestion: '忘记带充电器怎么办？',\r\n\t\t\t\t\t\t\t\tanswer: '别担心，我们的每个房间都配备了各种型号的充电器，包括Type-C、Lightning和其他常见接口，可免费使用。',\r\n\t\t\t\t\t\t\t\texpanded: false\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tquestion: '如何上报设备异常问题？',\r\n\t\t\t\t\t\t\t\tanswer: '您可以在\"我的\"页面中找到\"设备异常上报\"功能，填写相关信息并提交。我们会尽快处理并联系您。',\r\n\t\t\t\t\t\t\t\texpanded: false\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tquestion: '会员有哪些特权？',\r\n\t\t\t\t\t\t\t\tanswer: '会员可享受折扣价格、优先预订、积分奖励、生日特惠等多项特权。详情可在\"我的\"-\"会员权益\"中查看。',\r\n\t\t\t\t\t\t\t\texpanded: false\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t];\r\n\t\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tcontactOnline() {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '正在连接客服...',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\t// 模拟连接客服\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\ttitle: '客服提示',\r\n\t\t\t\t\t\tcontent: '当前客服繁忙，预计等待时间3分钟，是否继续等待？',\r\n\t\t\t\t\t\tconfirmText: '继续等待',\r\n\t\t\t\t\t\tcancelText: '稍后再试',\r\n\t\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ttitle: '已加入等待队列',\r\n\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t}, 1000);\r\n\t\t\t},\r\n\t\t\tcallPhone() {\r\n\t\t\t\tconst phoneNumber = (this.contactInfo.phoneService && this.contactInfo.phoneService.phoneNumber) || '4008889999';\r\n\t\t\t\tuni.makePhoneCall({\r\n\t\t\t\t\tphoneNumber: phoneNumber,\r\n\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\tconsole.log('拨打电话成功');\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\tconsole.error('拨打电话失败', err);\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '拨打电话失败',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\ttoggleFaq(index) {\r\n\t\t\t\tthis.faqs.forEach((item, i) => {\r\n\t\t\t\t\tif (i === index) {\r\n\t\t\t\t\t\titem.expanded = !item.expanded;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\titem.expanded = false;\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t/* 页面基础样式 */\r\n\t.page-contact {\r\n\t\tcolor: #ffffff;\r\n\t\theight: 100vh;\r\n\t\tmin-height: 100vh;\r\n\t\tbox-sizing: border-box;\r\n\t\tposition: relative;\r\n\t\toverflow: hidden;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t}\r\n\t\r\n\t/* 页面背景样式 */\r\n\t.page-background {\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tz-index: 0;\r\n\t\tbackground-color: #121212;\r\n\t}\r\n\t\r\n\t.gradient-overlay {\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tbackground: linear-gradient(to bottom, \r\n\t\t\trgba(18, 18, 18, 0) 0%, \r\n\t\t\trgba(18, 18, 18, 0.3) 20%, \r\n\t\t\trgba(18, 18, 18, 0.7) 50%,\r\n\t\t\trgba(18, 18, 18, 0.9) 80%,\r\n\t\t\t#121212 100%);\r\n\t\tz-index: 1;\r\n\t\tpointer-events: none;\r\n\t}\r\n\t\r\n\t/* 波浪装饰 */\r\n\t.wave-decoration {\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\toverflow: hidden;\r\n\t\tz-index: 0;\r\n\t}\r\n\t\r\n\t.wave {\r\n\t\tposition: absolute;\r\n\t\twidth: 250%;\r\n\t\theight: 120%;\r\n\t\ttop: -80%;\r\n\t\tleft: -75%;\r\n\t\tborder-radius: 43%;\r\n\t\tbackground: transparent;\r\n\t}\r\n\t\r\n\t.wave1 {\r\n\t\tbackground: linear-gradient(135deg, rgba(168, 117, 255, 0.3) 0%, rgba(139, 92, 246, 0.25) 100%);\r\n\t\tanimation: wave 25s linear infinite;\r\n\t\tz-index: 3;\r\n\t}\r\n\t\r\n\t.wave2 {\r\n\t\tbackground: linear-gradient(135deg, rgba(105, 30, 255, 0.25) 0%, rgba(96, 56, 176, 0.2) 100%);\r\n\t\tanimation: wave 22s linear infinite;\r\n\t\tanimation-delay: -5s;\r\n\t\tz-index: 2;\r\n\t}\r\n\t\r\n\t.wave3 {\r\n\t\tbackground: linear-gradient(135deg, rgba(147, 51, 234, 0.25) 0%, rgba(79, 70, 229, 0.15) 100%);\r\n\t\tanimation: wave 18s linear infinite;\r\n\t\tanimation-delay: -8s;\r\n\t\tz-index: 1;\r\n\t}\r\n\t\r\n\t@keyframes wave {\r\n\t\t0% {\r\n\t\t\ttransform: rotate(0deg);\r\n\t\t}\r\n\t\t100% {\r\n\t\t\ttransform: rotate(360deg);\r\n\t\t}\r\n\t}\r\n\t\r\n\t/* 顶部导航样式 */\r\n\t.navbar {\r\n\t\theight: 90rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t\tpadding: 0 30rpx;\r\n\t\tmargin-top: var(--status-bar-height);\r\n\t\tposition: relative;\r\n\t\tz-index: 100;\r\n\t}\r\n\t\r\n\t.navbar-title {\r\n\t\tfont-size: 34rpx;\r\n\t\tfont-weight: 600;\r\n\t}\r\n\t\r\n\t.navbar-left, .navbar-right {\r\n\t\twidth: 60rpx;\r\n\t\theight: 60rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t}\r\n\t\r\n\t/* 内容区域 */\r\n\t.content {\r\n\t\tflex: 1;\r\n\t\tpadding: 30rpx 30rpx;\r\n\t\tposition: relative;\r\n\t\tz-index: 5;\r\n\t\twidth: 100%;\r\n\t\tbox-sizing: border-box;\r\n\t\toverflow: auto;\r\n\t}\r\n\t\r\n\t/* 卡片容器 */\r\n\t.card-container {\r\n\t\tmargin-bottom: 40rpx;\r\n\t\twidth: 100%;\r\n\t}\r\n\t\r\n\t.section-title {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\t\r\n\t.icon-wrapper {\r\n\t\twidth: 60rpx;\r\n\t\theight: 60rpx;\r\n\t\tborder-radius: 50%;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tmargin-right: 16rpx;\r\n\t\ttransition: transform 0.3s ease;\r\n\t}\r\n\t\r\n\t.section-title:active .icon-wrapper {\r\n\t\ttransform: rotate(15deg);\r\n\t}\r\n\t\r\n\t.neon-blue-bg {\r\n\t\tbackground-color: rgba(0, 233, 255, 0.15);\r\n\t\tbox-shadow: 0 0 15rpx rgba(0, 233, 255, 0.3);\r\n\t}\r\n\t\r\n\t.neon-green-bg {\r\n\t\tbackground-color: rgba(0, 255, 133, 0.15);\r\n\t\tbox-shadow: 0 0 15rpx rgba(0, 255, 133, 0.3);\r\n\t}\r\n\t\r\n\t.neon-yellow-bg {\r\n\t\tbackground-color: rgba(255, 222, 89, 0.15);\r\n\t\tbox-shadow: 0 0 15rpx rgba(255, 222, 89, 0.3);\r\n\t}\r\n\t\r\n\t/* 卡片样式 */\r\n\t.card {\r\n\t\tborder-radius: 20rpx;\r\n\t\tbackground-color: rgba(255, 255, 255, 0.08);\r\n\t\tbackdrop-filter: blur(5px);\r\n\t\t-webkit-backdrop-filter: blur(5px);\r\n\t\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);\r\n\t\tborder: 1px solid rgba(255, 255, 255, 0.05);\r\n\t\ttransition: transform 0.3s ease, box-shadow 0.3s ease;\r\n\t\twidth: 100%;\r\n\t\tbox-sizing: border-box;\r\n\t\tmargin-left: auto;\r\n\t\tmargin-right: auto;\r\n\t}\r\n\t\r\n\t.contact-card {\r\n\t\tpadding: 30rpx;\r\n\t\tbackground: linear-gradient(to bottom, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));\r\n\t}\r\n\t\r\n\t.contact-item {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t}\r\n\t\r\n\t.contact-item .text-secondary {\r\n\t\tmargin-bottom: 8rpx;\r\n\t}\r\n\t\r\n\t.contact-item .text-primary {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: 500;\r\n\t}\r\n\t\r\n\t/* FAQ列表 */\r\n\t.faq-list {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tgap: 20rpx;\r\n\t\twidth: 100%;\r\n\t}\r\n\t\r\n\t.faq-item {\r\n\t\tpadding: 24rpx;\r\n\t\twidth: 100%;\r\n\t\tposition: relative;\r\n\t\toverflow: hidden;\r\n\t}\r\n\t\r\n\t.faq-item:after {\r\n\t\tcontent: '';\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tbackground: linear-gradient(to right, rgba(168, 117, 255, 0.05), transparent);\r\n\t\topacity: 0;\r\n\t\ttransition: opacity 0.3s ease;\r\n\t\tpointer-events: none;\r\n\t}\r\n\t\r\n\t.faq-item:active {\r\n\t\ttransform: scale(0.99);\r\n\t\tbox-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.2);\r\n\t}\r\n\t\r\n\t.faq-item:active:after {\r\n\t\topacity: 1;\r\n\t}\r\n\t\r\n\t.faq-header {\r\n\t\twidth: 100%;\r\n\t}\r\n\t\r\n\t.faq-title {\r\n\t\tfont-size: 30rpx;\r\n\t\tfont-weight: 500;\r\n\t\tcolor: #FFFFFF;\r\n\t}\r\n\t\r\n\t.faq-content {\r\n\t\tmargin-top: 10rpx;\r\n\t\tanimation: fadeIn 0.3s ease;\r\n\t}\r\n\t\r\n\t@keyframes fadeIn {\r\n\t\tfrom { opacity: 0; transform: translateY(-10rpx); }\r\n\t\tto { opacity: 1; transform: translateY(0); }\r\n\t}\r\n\t\r\n\t.divider {\r\n\t\theight: 1px;\r\n\t\tbackground-color: rgba(255, 255, 255, 0.1);\r\n\t}\r\n\t\r\n\t/* 按钮样式 */\r\n\t.btn {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tborder-radius: 15rpx;\r\n\t\tpadding: 25rpx;\r\n\t\tfont-size: 30rpx;\r\n\t\tfont-weight: 600;\r\n\t\ttransition: all 0.3s ease;\r\n\t}\r\n\t\r\n\t.btn-primary {\r\n\t\tbackground: linear-gradient(135deg, rgba(168, 117, 255, 0.8), rgba(139, 92, 246, 0.9));\r\n\t\tcolor: #FFFFFF;\r\n\t\tbox-shadow: 0 4rpx 20rpx rgba(168, 117, 255, 0.4);\r\n\t\tborder: none;\r\n\t}\r\n\t\r\n\t.btn-primary:active {\r\n\t\ttransform: scale(0.98);\r\n\t\tbox-shadow: 0 2rpx 10rpx rgba(168, 117, 255, 0.3);\r\n\t}\r\n\t\r\n\t.btn-outline {\r\n\t\tbackground-color: transparent;\r\n\t\tborder: 1px solid rgba(168, 117, 255, 0.4);\r\n\t\tcolor: var(--primary-light, #A875FF);\r\n\t}\r\n\t\r\n\t.btn-outline:active {\r\n\t\tbackground-color: rgba(168, 117, 255, 0.1);\r\n\t\ttransform: scale(0.98);\r\n\t}\r\n\t\r\n\t/* 底部空间 */\r\n\t.bottom-space {\r\n\t\theight: 60rpx;\r\n\t}\r\n\t\r\n\t/* Material Icons 字体 */\r\n\t@font-face {\r\n\t\tfont-family: 'Material Icons';\r\n\t\tfont-style: normal;\r\n\t\tfont-weight: 400;\r\n\t\tsrc: url(https://fonts.gstatic.com/s/materialicons/v139/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format('woff2');\r\n\t}\r\n\r\n\t.material-icons {\r\n\t\tfont-family: 'Material Icons';\r\n\t\tfont-weight: normal;\r\n\t\tfont-style: normal;\r\n\t\tfont-size: 24rpx;\r\n\t\tline-height: 1;\r\n\t\tletter-spacing: normal;\r\n\t\ttext-transform: none;\r\n\t\tdisplay: inline-block;\r\n\t\twhite-space: nowrap;\r\n\t\tword-wrap: normal;\r\n\t\tdirection: ltr;\r\n\t\t-webkit-font-smoothing: antialiased;\r\n\t\t-moz-osx-font-smoothing: grayscale;\r\n\t}\r\n\t\r\n\t.material-icons.md-24 {\r\n\t\tfont-size: 48rpx;\r\n\t}\r\n\t\r\n\t.material-icons.md-48 {\r\n\t\tfont-size: 96rpx;\r\n\t}\r\n\t\r\n\t.material-icons.text-primary {\r\n\t\tcolor: var(--text-primary, #FFFFFF);\r\n\t}\r\n\t\r\n\t.material-icons.text-tertiary {\r\n\t\tcolor: var(--text-tertiary, rgba(255, 255, 255, 0.5));\r\n\t}\r\n\t\r\n\t/* 彩色图标 */\r\n\t.material-icons.neon-blue {\r\n\t\tcolor: var(--neon-blue, #00E9FF);\r\n\t\ttext-shadow: 0 0 10rpx rgba(0, 233, 255, 0.5);\r\n\t\tanimation: glow-blue 3s infinite alternate;\r\n\t}\r\n\t\r\n\t.material-icons.neon-green {\r\n\t\tcolor: var(--neon-green, #00FF85);\r\n\t\ttext-shadow: 0 0 10rpx rgba(0, 255, 133, 0.5);\r\n\t\tanimation: glow-green 3s infinite alternate;\r\n\t}\r\n\t\r\n\t.material-icons.neon-yellow {\r\n\t\tcolor: var(--neon-yellow, #FFDE59);\r\n\t\ttext-shadow: 0 0 10rpx rgba(255, 222, 89, 0.5);\r\n\t\tanimation: glow-yellow 3s infinite alternate;\r\n\t}\r\n\t\r\n\t@keyframes glow-blue {\r\n\t\tfrom { text-shadow: 0 0 5rpx rgba(0, 233, 255, 0.3); }\r\n\t\tto { text-shadow: 0 0 15rpx rgba(0, 233, 255, 0.6); }\r\n\t}\r\n\t\r\n\t@keyframes glow-green {\r\n\t\tfrom { text-shadow: 0 0 5rpx rgba(0, 255, 133, 0.3); }\r\n\t\tto { text-shadow: 0 0 15rpx rgba(0, 255, 133, 0.6); }\r\n\t}\r\n\t\r\n\t@keyframes glow-yellow {\r\n\t\tfrom { text-shadow: 0 0 5rpx rgba(255, 222, 89, 0.3); }\r\n\t\tto { text-shadow: 0 0 15rpx rgba(255, 222, 89, 0.6); }\r\n\t}\r\n\t\r\n\t/* 辅助类 */\r\n\t.flex {\r\n\t\tdisplay: flex;\r\n\t}\r\n\t\r\n\t.justify-between {\r\n\t\tjustify-content: space-between;\r\n\t}\r\n\t\r\n\t.items-center {\r\n\t\talign-items: center;\r\n\t}\r\n\t\r\n\t.text-center {\r\n\t\ttext-align: center;\r\n\t}\r\n\t\r\n\t.mt-sm {\r\n\t\tmargin-top: 16rpx;\r\n\t}\r\n\t\r\n\t.mb-sm {\r\n\t\tmargin-bottom: 16rpx;\r\n\t}\r\n\t\r\n\t.mt-md {\r\n\t\tmargin-top: 30rpx;\r\n\t}\r\n\t\r\n\t.mt-lg {\r\n\t\tmargin-top: 60rpx;\r\n\t}\r\n\t\r\n\t/* 文本样式 */\r\n\t.title-md {\r\n\t\tfont-size: 36rpx;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #ffffff;\r\n\t\ttext-shadow: 0 0 10rpx rgba(255, 255, 255, 0.2);\r\n\t}\r\n\t\r\n\t.text-secondary {\r\n\t\tcolor: rgba(255, 255, 255, 0.7);\r\n\t\tfont-size: 28rpx;\r\n\t}\r\n\t\r\n\t.text-tertiary {\r\n\t\tcolor: rgba(255, 255, 255, 0.5);\r\n\t\tfont-size: 26rpx;\r\n\t}\r\n\t\r\n\t.text-primary {\r\n\t\tcolor: #FFFFFF;\r\n\t\tfont-size: 28rpx;\r\n\t}\r\n</style> ", "import mod from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./contact.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./contact.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754165306705\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}