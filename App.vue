<script>
	import { mapActions } from 'vuex';
	import LoginManager from './static/js/login.js';
	import { getSystemInfoSync } from './utils/systemInfoCompat.js';

	export default {
		globalData: {
			isSimulatedMode: false, // 是否使用模拟模式
			userInfo: null,
			hasLogin: false,
			isIphoneX: false,
			systemInfo: null
		},
		onLaunch: function() {
			console.log('App Launch');
			// 初始化检查登录状态
			this.checkLoginStatus();

			// 获取系统信息 - 使用兼容性工具
			const systemInfo = getSystemInfoSync();
			this.globalData.systemInfo = systemInfo;
			
			// 检查是否是iPhoneX系列
			const model = systemInfo.model;
			const screenHeight = systemInfo.screenHeight;
			const screenWidth = systemInfo.screenWidth;
			const isIphoneX = /iPhone X|iPhone 11|iPhone 12|iPhone 13|iPhone 14|iPhone 15/.test(model) || 
				(screenHeight / screenWidth > 2 && systemInfo.platform === 'ios');
			this.globalData.isIphoneX = isIphoneX;
		},
		onShow: function() {
			console.log('App Show');
		},
		onHide: function() {
			console.log('App Hide');
		},
		methods: {
			...mapActions(['checkLoginStatus'])
		}
	}
</script>

<style>
	/* 引入通用样式和图标 */
	@import './static/css/material-icons.css';
	
	/*每个页面公共css */
	page {
		background-color: #000000;
		color: #FFFFFF;
		font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
		height: 100%;
		overflow-x: hidden;
		box-sizing: border-box;
	}
	
	/* iOS安全区域适配 */
	.safe-area-inset-bottom {
		padding-bottom: constant(safe-area-inset-bottom); /* iOS 11.0 */
		padding-bottom: env(safe-area-inset-bottom); /* iOS 11.2+ */
	}
	
	.safe-area-inset-top {
		height: var(--status-bar-height);
		padding-top: constant(safe-area-inset-top); /* iOS 11.0 */
		padding-top: env(safe-area-inset-top); /* iOS 11.2+ */
	}
	
	.container {
		width: 100%;
		min-height: 100vh;
		display: flex;
		flex-direction: column;
		box-sizing: border-box;
		position: relative;
		padding-bottom: calc(170rpx + env(safe-area-inset-bottom)); /* 修改底部内边距适配新的导航栏高度 */
	}
	
	/* 自定义颜色变量 */
	:root {
		--primary: #8B5CF6;
		--primary-light: #A875FF;
		--neon-pink: #ff36f9;
		--text-primary: #FFFFFF;
		--text-secondary: rgba(255, 255, 255, 0.7);
		--text-tertiary: rgba(255, 255, 255, 0.5);
	}
	
	/* 通用文本颜色 */
	.text-primary {
		color: #FFFFFF;
	}
	
	.text-secondary {
		color: rgba(255, 255, 255, 0.7);
	}
	
	.text-tertiary {
		color: rgba(255, 255, 255, 0.5);
	}
	
	/* 通用边距 */
	.mt-xs {
		margin-top: 10rpx;
	}
	
	.mt-sm {
		margin-top: 20rpx;
	}
	
	.mt-md {
		margin-top: 30rpx;
	}
	
	.mt-lg {
		margin-top: 40rpx;
	}
	
	.mb-xs {
		margin-bottom: 10rpx;
	}
	
	.mb-sm {
		margin-bottom: 20rpx;
	}
	
	.mb-md {
		margin-bottom: 30rpx;
	}
	
	.mb-lg {
		margin-bottom: 40rpx;
	}
	
	/* 通用flex布局 */
	.flex {
		display: flex;
	}
	
	.flex-col {
		display: flex;
		flex-direction: column;
	}
	
	.justify-between {
		justify-content: space-between;
	}
	
	.justify-center {
		justify-content: center;
	}
	
	.items-center {
		align-items: center;
	}
	
	.flex-1 {
		flex: 1;
	}
	
	.text-center {
		text-align: center;
	}
</style>




















