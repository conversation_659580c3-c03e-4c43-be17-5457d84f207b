










































































































































































































































































































































































































































































































































































































































































































/* CSS变量定义 */
page {
	--primary-light: #A875FF;
	--neon-pink: #ff36f9;
	height: 100%;
	overflow: hidden;
}
/* 页面基础样式 */
.page-index {
	padding-top: 100rpx; /* 减少顶部间距 */
	padding-bottom: calc(120rpx + env(safe-area-inset-bottom)); /* 增加底部padding，为TabBar留出空间 */
	color: #ffffff;
	height: 100vh;
	min-height: 100vh;
	box-sizing: border-box;
	position: relative;
	overflow: hidden; /* 防止页面滚动 */
	touch-action: none; /* 禁止触摸操作 */
}
/* 页面背景样式 */
.page-background {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 0;
}
/* 背景图片样式 */
.background-image {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 0;
	object-fit: cover; /* 确保图片覆盖整个容器 */
}
.status-bar {
	width: 100%;
	background: transparent; /* 使顶部安全区域透明，与页面背景一致 */
	backdrop-filter: none;
	-webkit-backdrop-filter: none;
	position: fixed;
	top: 0;
	left: 0;
	z-index: 100;
}
.content {
	padding: 20rpx 30rpx;
	position: relative;
	z-index: 2;
	height: calc(100vh - 100rpx - 120rpx - env(safe-area-inset-bottom)); /* 修改高度计算，为TabBar留出空间 */
	display: flex;
	flex-direction: column;
	overflow: hidden; /* 防止内容滚动 */
	touch-action: none; /* 禁止触摸操作 */
}
/* 欢迎区域 */
.welcome-section {
	margin-bottom: 0; /* 减少底部间距 */
	margin-top: 0; /* 减少顶部间距 */
}
/* 顶部banner样式 */
.banner-container {
	position: relative;
	z-index: 2;
	margin-bottom: 10rpx; /* 减小底部间距 */
	margin-top: 30rpx; /* 增加顶部间距，让banner往下移 */
	width: 100%;
	display: flex;
	justify-content: center;
	overflow: visible;
	padding: 0;
}
.banner-image {
	width: 100%;
	max-width: 1000rpx;
	height: 180rpx;
	object-fit: contain;
}
.welcome-text {
	margin-left: 10rpx;
}
.user-info {
	position: relative;
	z-index: 2;
	margin-bottom: 20rpx;
}
.user-summary {
	margin-top: 30rpx;
	padding: 30rpx;
	background-color: rgba(255, 255, 255, 0.08);
	border-radius: 20rpx;
	backdrop-filter: blur(10px);
	-webkit-backdrop-filter: blur(10px);
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);
}
/* 快捷功能区域 */
.quick-actions {
	margin-top: 530rpx; /* 从500rpx增加到530rpx，使整体下移 */
	margin-bottom: 40rpx;
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: flex-start; /* 从顶部开始排列 */
}
/* 标题容器 */
.scan-title-container {
	margin-bottom: 40rpx;
	text-align: center;
	width: 100%;
	position: relative;
	top: 50rpx; /* 从30rpx增加到50rpx，使标题进一步向下移动 */
}
/* 增加标题之间的间距 */
.neon-title.mt-xs {
	margin-top: 15rpx; /* 增加两行标题之间的间距 */
}
/* 标题下方的爱心图标容器 */
.heart-icon-container {
	margin-top: 20rpx;
	display: flex;
	justify-content: center;
	align-items: center;
}
/* 标题下方的爱心图标 */
.title-heart-icon {
	font-size: 80rpx;
	color: #d4a9ff;
	text-shadow: 
		0 0 15rpx rgba(212, 169, 255, 0.7),
		0 0 30rpx rgba(212, 169, 255, 0.5);
	-webkit-animation: title-heart-pulse 2s ease-in-out infinite;
	        animation: title-heart-pulse 2s ease-in-out infinite;
}
@-webkit-keyframes title-heart-pulse {
0%, 100% {
		-webkit-transform: scale(1);
		        transform: scale(1);
		opacity: 0.9;
}
50% {
		-webkit-transform: scale(1.1);
		        transform: scale(1.1);
		opacity: 1;
}
}
@keyframes title-heart-pulse {
0%, 100% {
		-webkit-transform: scale(1);
		        transform: scale(1);
		opacity: 0.9;
}
50% {
		-webkit-transform: scale(1.1);
		        transform: scale(1.1);
		opacity: 1;
}
}
/* 霓虹灯标题效果增强 */
.neon-title {
	font-size: 50rpx; /* 增大字体大小 */
	line-height: 1.3;
	font-weight: 500;
	text-align: center;
	color: #fff;
	font-family: "YouYuan", "幼圆", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", sans-serif; /* 添加幼圆字体 */
	text-shadow: 
		0 0 5rpx rgba(255, 255, 255, 0.6),
		0 0 10rpx rgba(255, 255, 255, 0.4), 
		0 0 15rpx rgba(168, 117, 255, 0.5),
		0 0 25rpx rgba(168, 117, 255, 0.4);
	letter-spacing: 8rpx; /* 增加字间距 */
	-webkit-animation: soft-flicker 4s infinite alternate;
	        animation: soft-flicker 4s infinite alternate;
	-webkit-transform: scale(1, 0.95);
	        transform: scale(1, 0.95); /* 稍微压扁字体，模拟圆体效果 */
}
/* 添加霓虹灯闪烁动画 */
@-webkit-keyframes soft-flicker {
0%, 100% {
		text-shadow: 
			0 0 5rpx rgba(255, 255, 255, 0.6),
			0 0 10rpx rgba(255, 255, 255, 0.4), 
			0 0 15rpx rgba(168, 117, 255, 0.5),
			0 0 25rpx rgba(168, 117, 255, 0.4);
}
50% {
		text-shadow: 
			0 0 8rpx rgba(255, 255, 255, 0.7),
			0 0 15rpx rgba(255, 255, 255, 0.5), 
			0 0 20rpx rgba(168, 117, 255, 0.6),
			0 0 30rpx rgba(168, 117, 255, 0.5);
}
}
@keyframes soft-flicker {
0%, 100% {
		text-shadow: 
			0 0 5rpx rgba(255, 255, 255, 0.6),
			0 0 10rpx rgba(255, 255, 255, 0.4), 
			0 0 15rpx rgba(168, 117, 255, 0.5),
			0 0 25rpx rgba(168, 117, 255, 0.4);
}
50% {
		text-shadow: 
			0 0 8rpx rgba(255, 255, 255, 0.7),
			0 0 15rpx rgba(255, 255, 255, 0.5), 
			0 0 20rpx rgba(168, 117, 255, 0.6),
			0 0 30rpx rgba(168, 117, 255, 0.5);
}
}
/* 扫码按钮容器 */
.action-container {
	display: flex;
	justify-content: center;
	align-items: center;
	margin-top: -10rpx; /* 从10rpx改为-10rpx，使按钮往上移 */
	margin-bottom: 10rpx; /* 从20rpx改为10rpx，减少底部间距 */
	width: 100%;
	position: relative; /* 添加相对定位 */
	left: 0; /* 确保不偏移 */
}
/* 主扫码按钮样式 */
.scan-button-wrapper {
	position: relative;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	width: 320rpx; /* 从300rpx增加到320rpx */
	height: 320rpx; /* 从300rpx增加到320rpx */
	margin-top: 0; /* 移除顶部间距 */
	padding: 0; /* 移除内边距 */
	transition: box-shadow 0.3s, -webkit-transform 0.3s;
	transition: transform 0.3s, box-shadow 0.3s;
	transition: transform 0.3s, box-shadow 0.3s, -webkit-transform 0.3s;
	margin: 0 auto; /* 确保按钮居中 */
	left: 0; /* 确保不偏移 */
	right: 0; /* 确保不偏移 */
}
.scan-button-wrapper:active {
	-webkit-transform: scale(0.96);
	        transform: scale(0.96);
}
/* 扫码按钮内层 */
.scan-button-inner {
	position: relative;
	display: flex;
	justify-content: center;
	align-items: center;
	width: 220rpx;
	height: 220rpx;
	border-radius: 50%;
	background: rgba(168, 117, 255, 0.12);
	box-shadow: 
		0 12rpx 36rpx rgba(0, 0, 0, 0.35),
		0 20rpx 50rpx rgba(0, 0, 0, 0.2),
		inset 0 2rpx 10rpx rgba(255, 255, 255, 0.5),
		inset 0 -5rpx 15rpx rgba(0, 0, 0, 0.3);
	z-index: 2;
	overflow: visible;
	position: relative;
	-webkit-animation: circle-breathing 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
	        animation: circle-breathing 4s cubic-bezier(0.4, 0, 0.6, 1) infinite; /* 改为4秒，使用更平滑的缓动函数 */
}
/* 修改背景圆圈呼吸灯效果，显著增强效果并与爱心同步 */
@-webkit-keyframes circle-breathing {
0% {
		background: rgba(168, 117, 255, 0.08); /* 降低初始亮度 */
		box-shadow: 
			0 12rpx 36rpx rgba(0, 0, 0, 0.35),
			0 20rpx 50rpx rgba(0, 0, 0, 0.2),
			inset 0 2rpx 10rpx rgba(255, 255, 255, 0.4),
			inset 0 -5rpx 15rpx rgba(0, 0, 0, 0.3);
}
25% { /* 放大过程占25% */
		background: rgba(168, 117, 255, 0.45); /* 显著增强亮度 */
		box-shadow: 
			0 12rpx 36rpx rgba(168, 117, 255, 0.4), /* 添加紫色外发光 */
			0 20rpx 50rpx rgba(168, 117, 255, 0.25),
			inset 0 2rpx 20rpx rgba(255, 255, 255, 0.9), /* 增强内发光 */
			inset 0 -5rpx 15rpx rgba(0, 0, 0, 0.2);
}
50% { /* 保持最大状态占25% */
		background: rgba(168, 117, 255, 0.45); /* 保持增强的亮度 */
		box-shadow: 
			0 12rpx 36rpx rgba(168, 117, 255, 0.4), /* 添加紫色外发光 */
			0 20rpx 50rpx rgba(168, 117, 255, 0.25),
			inset 0 2rpx 20rpx rgba(255, 255, 255, 0.9), /* 增强内发光 */
			inset 0 -5rpx 15rpx rgba(0, 0, 0, 0.2);
}
75% { /* 缩小过程占25% */
		background: rgba(168, 117, 255, 0.08); /* 降低初始亮度 */
		box-shadow: 
			0 12rpx 36rpx rgba(0, 0, 0, 0.35),
			0 20rpx 50rpx rgba(0, 0, 0, 0.2),
			inset 0 2rpx 10rpx rgba(255, 255, 255, 0.4),
			inset 0 -5rpx 15rpx rgba(0, 0, 0, 0.3);
}
100% {
		background: rgba(168, 117, 255, 0.08); /* 降低初始亮度 */
		box-shadow: 
			0 12rpx 36rpx rgba(0, 0, 0, 0.35),
			0 20rpx 50rpx rgba(0, 0, 0, 0.2),
			inset 0 2rpx 10rpx rgba(255, 255, 255, 0.4),
			inset 0 -5rpx 15rpx rgba(0, 0, 0, 0.3);
}
}
@keyframes circle-breathing {
0% {
		background: rgba(168, 117, 255, 0.08); /* 降低初始亮度 */
		box-shadow: 
			0 12rpx 36rpx rgba(0, 0, 0, 0.35),
			0 20rpx 50rpx rgba(0, 0, 0, 0.2),
			inset 0 2rpx 10rpx rgba(255, 255, 255, 0.4),
			inset 0 -5rpx 15rpx rgba(0, 0, 0, 0.3);
}
25% { /* 放大过程占25% */
		background: rgba(168, 117, 255, 0.45); /* 显著增强亮度 */
		box-shadow: 
			0 12rpx 36rpx rgba(168, 117, 255, 0.4), /* 添加紫色外发光 */
			0 20rpx 50rpx rgba(168, 117, 255, 0.25),
			inset 0 2rpx 20rpx rgba(255, 255, 255, 0.9), /* 增强内发光 */
			inset 0 -5rpx 15rpx rgba(0, 0, 0, 0.2);
}
50% { /* 保持最大状态占25% */
		background: rgba(168, 117, 255, 0.45); /* 保持增强的亮度 */
		box-shadow: 
			0 12rpx 36rpx rgba(168, 117, 255, 0.4), /* 添加紫色外发光 */
			0 20rpx 50rpx rgba(168, 117, 255, 0.25),
			inset 0 2rpx 20rpx rgba(255, 255, 255, 0.9), /* 增强内发光 */
			inset 0 -5rpx 15rpx rgba(0, 0, 0, 0.2);
}
75% { /* 缩小过程占25% */
		background: rgba(168, 117, 255, 0.08); /* 降低初始亮度 */
		box-shadow: 
			0 12rpx 36rpx rgba(0, 0, 0, 0.35),
			0 20rpx 50rpx rgba(0, 0, 0, 0.2),
			inset 0 2rpx 10rpx rgba(255, 255, 255, 0.4),
			inset 0 -5rpx 15rpx rgba(0, 0, 0, 0.3);
}
100% {
		background: rgba(168, 117, 255, 0.08); /* 降低初始亮度 */
		box-shadow: 
			0 12rpx 36rpx rgba(0, 0, 0, 0.35),
			0 20rpx 50rpx rgba(0, 0, 0, 0.2),
			inset 0 2rpx 10rpx rgba(255, 255, 255, 0.4),
			inset 0 -5rpx 15rpx rgba(0, 0, 0, 0.3);
}
}
/* 图标样式 */
.scan-icon-container {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 160rpx; /* 从140rpx增加到160rpx */
	height: 160rpx; /* 从140rpx增加到160rpx */
	z-index: 3;
	position: relative; /* 添加相对定位 */
	margin: 0 auto; /* 确保容器居中 */
	left: 0; /* 确保不偏移 */
	right: 0; /* 确保不偏移 */
}
.scan-icon-container .material-icons.heart-icon {
	font-size: 160rpx;
	color: #c49aef; /* 使用更深的紫色 */
	background: none;
	-webkit-background-clip: initial;
	background-clip: initial;
	text-shadow: none;
	-webkit-animation: heart-natural-grow 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
	        animation: heart-natural-grow 4s cubic-bezier(0.4, 0, 0.6, 1) infinite; /* 改为4秒，使用更平滑的缓动函数 */
	-webkit-transform-style: preserve-3d;
	        transform-style: preserve-3d;
	-webkit-filter: none;
	        filter: none;
	position: absolute;
	top: 50%;
	left: 50%;
	-webkit-transform: translate(-50%, -50%);
	        transform: translate(-50%, -50%); /* 保持居中 */
	-webkit-transform-origin: center center;
	        transform-origin: center center;
	width: 100%; /* 确保图标占满容器宽度 */
	text-align: center; /* 确保文本居中 */
	display: flex; /* 添加flex布局 */
	justify-content: center; /* 水平居中 */
	align-items: center; /* 垂直居中 */
	height: 100%; /* 确保高度占满容器 */
	box-sizing: border-box; /* 确保盒模型计算正确 */
}
/* 重新设计爱心放大动画 - 自然放大效果，放大和缩小速度一致且更慢 */
@-webkit-keyframes heart-natural-grow {
0% {
		-webkit-transform: translate(-50%, -50%) scale(1);
		        transform: translate(-50%, -50%) scale(1);
		color: #c49aef;
}
25% { /* 放大过程占25% */
		-webkit-transform: translate(-50%, -50%) scale(1.35);
		        transform: translate(-50%, -50%) scale(1.35);
		color: #e5bcf7;
}
50% { /* 保持最大状态占25% */
		-webkit-transform: translate(-50%, -50%) scale(1.35);
		        transform: translate(-50%, -50%) scale(1.35);
		color: #e5bcf7;
}
75% { /* 缩小过程占25% */
		-webkit-transform: translate(-50%, -50%) scale(1);
		        transform: translate(-50%, -50%) scale(1);
		color: #c49aef;
}
100% { /* 保持原始状态 */
		-webkit-transform: translate(-50%, -50%) scale(1);
		        transform: translate(-50%, -50%) scale(1);
		color: #c49aef;
}
}
@keyframes heart-natural-grow {
0% {
		-webkit-transform: translate(-50%, -50%) scale(1);
		        transform: translate(-50%, -50%) scale(1);
		color: #c49aef;
}
25% { /* 放大过程占25% */
		-webkit-transform: translate(-50%, -50%) scale(1.35);
		        transform: translate(-50%, -50%) scale(1.35);
		color: #e5bcf7;
}
50% { /* 保持最大状态占25% */
		-webkit-transform: translate(-50%, -50%) scale(1.35);
		        transform: translate(-50%, -50%) scale(1.35);
		color: #e5bcf7;
}
75% { /* 缩小过程占25% */
		-webkit-transform: translate(-50%, -50%) scale(1);
		        transform: translate(-50%, -50%) scale(1);
		color: #c49aef;
}
100% { /* 保持原始状态 */
		-webkit-transform: translate(-50%, -50%) scale(1);
		        transform: translate(-50%, -50%) scale(1);
		color: #c49aef;
}
}
/* 文本容器 */
.scan-text {
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-top: 40rpx;
}
.scan-text .action-title {
	font-weight: 600;
	font-size: 36rpx;
	margin-bottom: 10rpx;
	color: rgba(255, 255, 255, 0.9);
	letter-spacing: 2rpx;
}
.scan-text .text-tertiary {
	font-size: 28rpx;
	color: rgba(255, 255, 255, 0.5);
}
/* Material Icons 字体 */
@font-face {
	font-family: 'Material Icons';
	font-style: normal;
	font-weight: 400;
	src: url(https://fonts.gstatic.com/s/materialicons/v139/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format('woff2');
	font-display: block;
}
.material-icons {
	font-family: 'Material Icons';
	font-weight: normal;
	font-style: normal;
	font-size: 48rpx;
	line-height: 1;
	letter-spacing: normal;
	text-transform: none;
	display: inline-block;
	white-space: nowrap;
	word-wrap: normal;
	direction: ltr;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	text-rendering: optimizeLegibility;
}
.material-icons.primary-light {
	color: var(--primary-light, #A875FF);
}
/* 状态标签 */
.status {
	padding: 8rpx 20rpx;
	border-radius: 30rpx;
	font-size: 24rpx;
}
.status.active {
	background-color: rgba(168, 117, 255, 0.2);
	color: var(--primary-light, #A875FF);
}
/* 辅助类 */
.flex {
	display: flex;
}
.flex-col {
	display: flex;
	flex-direction: column;
}
.justify-between {
	justify-content: space-between;
}
.items-center {
	align-items: center;
}
.text-center {
	text-align: center;
}
.mt-xs {
	margin-top: 8rpx;
}
.mt-sm {
	margin-top: 16rpx;
}
.mt-md {
	margin-top: 30rpx;
}
.mt-lg {
	margin-top: 40rpx; /* 减小顶部大间距 */
}
.mb-sm {
	margin-bottom: 16rpx;
}
.gap-md {
	gap: 24rpx;
}
.flex-1 {
	flex: 1;
}
/* 标题和文本样式 */
.title-lg {
	font-size: 46rpx;
	font-weight: 600;
	color: #ffffff;
	margin-bottom: 10rpx;
}
.title-md {
	font-size: 38rpx;
	font-weight: 600;
	color: #ffffff;
}
.title-sm {
	font-size: 32rpx;
	font-weight: 500;
	color: #ffffff;
	margin-bottom: 8rpx;
}
.text-secondary {
	color: rgba(255, 255, 255, 0.7);
}
.text-tertiary {
	color: rgba(255, 255, 255, 0.5);
}
.primary-light {
	color: var(--primary-light, #A875FF);
}
/* 底部空间 */
.bottom-space {
	height: 20rpx;
}
/* 卡片样式 */
.card {
	border-radius: 20rpx;
	background-color: rgba(255, 255, 255, 0.08);
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);
}
/* 协议提示文字 */
.agreement-tip {
	margin-top: 20rpx; /* 从30rpx减少到20rpx，减少与按钮的间距 */
	text-align: center;
	font-size: 24rpx;
	color: rgba(255, 255, 255, 0.6);
}

