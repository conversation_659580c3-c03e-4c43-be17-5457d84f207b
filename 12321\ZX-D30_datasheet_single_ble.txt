﻿ZX-D30
单 BLE 蓝牙模块技术手册

版本：V1.5
日期：2022/05/10



ZX-D30 技术手册

目录
1 模块介绍..............................................................................................2

1.1 概述.......................................................................................... 2
1.2 特性.......................................................................................... 3
1.3 应用.......................................................................................... 3
1.4 基础参数表............................................................................ 4
1.5 工作电流参数表...................................................................4
1.6 出厂默认配置参数.............................................................. 5

2 应用接口..............................................................................................6
2.1 模块引脚定义........................................................................6
2.2 引脚功能表............................................................................ 6
2.3 特殊引脚 IO 功能表............................................................7
2.4 电源设计.................................................................................8
2.5 串口电平转换参考电路..................................................... 8
2.6 应用原理图............................................................................ 9

3 回流焊曲线图..................................................................................10
4 Layout 注意事项..............................................................................10
5 AT 指令集.......................................................................................... 12
6 IO 直驱模式...................................................................................... 16

6.1 BLE 通讯服务 UUID 说明................................................. 16
6.2 IO 映射表.............................................................................. 16
6.3 引脚功能定义..................................................................... 17
6.4 APP 控制指令...................................................................... 18

7 更新记录........................................................................................... 22
8 联系我们........................................................................................... 23
9 免责申明和版权公告....................................................................23

深圳市智兴微科技有限公司 1 www.wlsiot.com



ZX-D30 技术手册

1.1 概述

ZX-D30 是深圳市智兴微科技有限公
司专为蓝牙无线数据传输打造的一款小尺
寸蓝牙低功耗BLE 模块，该模块为蓝牙 5.0
芯片，也支持 4.2 蓝牙协议，支持AT指令，
用户可根据提供的 AT 指令自行更改串口
波特率、设备名称等参数，也可以通过APP
直接下发数据控制模块的 IO口，操作灵活
使用简单。本模组具有极好的稳定性、超
低成本以及超低的功耗和接收灵敏度高等
优点，并且支持苹果、安卓APP 及微信小
程序连接，可适配客户各种开发项目。

深圳市智兴微科技有限公司 2 www.wlsiot.com



ZX-D30 技术手册

1.2 特性

 CPU：ARM968E-S
 内存大小：160KB FLASH+20KB RAM
 蓝牙 BLE5.0
 功耗可低至 0.5uA
 工作频率：2.4GHZ
 可视距离：80M
 传输速率：250Kbps/1Mbps
 发射功率：-20 dBm~4dBm
 接收灵敏度：-97dBm
 支持 UART，IIC，SPI，GPIO 硬件接口
 工作温度：-40℃~+125℃
 天线采用 PCB板载天线

1.3 应用

 智能家居
 定位追踪
 智能教育设备
 测量与监控系统
 工业传感器与控制
 医疗设备监测与无线控制

深圳市智兴微科技有限公司 3 www.wlsiot.com



ZX-D30 技术手册

1.4 基础参数表

参数名 描述 参数名 描述

型号 ZX-D30 模块尺寸 13×17x1.5 mm

蓝牙版本 BLE 5.0 通信距离 80M

工作频段 2.402GHz-2.480GHz ISM band 串口透传速率 BLE 4KB/S

工作电压 1.8V~3.6V 功能 BLE 透传、IO 控制

外设接口 UART/SPI/I2C/ADC/GPIO 天线 板载天线

调制方式 GFSK 工作温度 -40℃~+125℃

1.5 工作电流参数表

工作模式 状态 平均电流

深度睡眠(无广播) 无广播 0.5uA

100ms 广播间隔 290uA

200ms 广播间隔 150uA
广播状态(低功耗模式)

500ms 广播间隔 68uA

1000ms 广播间隔 39uA

广播状态(无低功耗模式) 100ms 广播间隔 1.78mA

连接状态 BLE 连接 2mA

深圳市智兴微科技有限公司 4 www.wlsiot.com



ZX-D30 技术手册

1.6 出厂默认配置参数

功能 出厂默认参数 指令

串口波特率 9600 AT+BAUD=3

蓝牙名称 D30LE_XXXXXX AT+NAME=D30LE_XXX XXX

BLE 服务 UUID FFE0 AT+SUUID=FFE0

BLE 读写特征值 UUID FFE1 AT+CUUID=FFE1

深圳市智兴微科技有限公司 5 www.wlsiot.com



ZX-D30 技术手册

2.1 模块引脚定义

2.2 引脚功能表

管脚 名称 类型 功能

1 P00 I/O TXD / 可编程输入输出引脚

2 P01 I/O RXD / 可编程输入输出引脚

3 RST I/O 复位引脚 (低电平有效)

4 P17 I/O 编号 E8, 直驱模式输出口

5 P16 I/O 编号 E7, 直驱模式输出口

深圳市智兴微科技有限公司 6 www.wlsiot.com



ZX-D30 技术手册

6 VCC POWER 电源 (1.8 - 3.6V)

7 GND GND 地

8 NC NC 悬空

9 P33 I/O 编号 E9, 直驱模式 ADC 口

10 P13 I/O 低功耗模式引脚(未连接状态下有效)

11 P12 I/O LED 状态指示灯引脚

12 P11 I/O 功能输入按键引脚

13 P10 I/O 蓝牙连接状态输出引脚

14 P07 I/O 编号 E5, 直驱模式输出口

15 P06 I/O 编号 E4, 直驱模式输出口

16 P05 I/O 编号 E3, 直驱模式输入口

17 P04 I/O 编号 E2, 直驱模式输入口

18 P03 I/O 编号 E1, 直驱模式输入口

19 P02 I/O 编号 E0, 直驱模式输入口

20 NC NC 悬空

21 NC NC 悬空

2.3 特殊引脚 IO功能表

IO 脚 功能描述

P10 蓝牙连接状态输出引脚：已连接（高电平）未连接（低电平）

P11 输入按键引脚：短按(断开蓝牙连接) 长按 3S(恢复出厂设置)

输入引脚(未连接状态下有效)：

低电平：进入低功耗模式(串口指令失效、LED 灯不烁)，功耗数据参见
P13

模块 1.5 章节电流数据

高电平：高电平退出低功耗模式

深圳市智兴微科技有限公司 7 www.wlsiot.com



ZX-D30 技术手册
LED 状态指示灯（引脚 P12 高电平点亮）

LED 显示 连接状态

匀速慢(500ms/on, 500ms/off) 未连接

长亮 已连接

2.4 电源设计

ZX-D30 的供电范围是 1.8V~3.6V，推荐 3.3V的工作电压最
佳。建议使用 LDO 供电，如使用 DC-DC 建议纹波控制在 30mV 以
内。

2.5 串口电平转换参考电路

注：如果单片机为 5V 串口连接模块时可参考上图电平转换电路,
网络M_TX/RX 为模块串口，网络 TX/RX 为单片机串口

深圳市智兴微科技有限公司 8 www.wlsiot.com



ZX-D30 技术手册

2.6 应用原理图

深圳市智兴微科技有限公司 9 www.wlsiot.com



ZX-D30 技术手册

蓝牙模块工作在 2.4G 无线频段，应尽量避免各种因素对无
线收发的影响，注意以下几点：

1、包围蓝牙模块的产品外壳避免使用金属，当使用部分金
属外壳时，应尽量让模块天线部分远离金属部分。

2、产品内部金属连接线或者金属螺钉，应尽量远离模块天
线部分。

3、PCB 布板：蓝牙模块的天线部分的是 PCB 天线，由于金
属会削弱天线的功能，在给模块布板的时候，模块天线下面严禁
铺地和走线，若能挖空更好。
深圳市智兴微科技有限公司 10 www.wlsiot.com



ZX-D30 技术手册

4、模块布局参考方案如下图所示：
方案 1（推荐）：将模组沿 PCB 板放置，且天线在板框外；
方案 2:将模组沿 PCB 板边放置，天线沿板边放置且下方挖

空；
方案 3:将模组沿 PCB 板边放置，天线沿板边放置下方均不

铺铜；

深圳市智兴微科技有限公司 11 www.wlsiot.com



ZX-D30 技术手册

1、<AT>测试指令.....................................................................................................................14

2、<AT+VERS>获取软件版本号..........................................................................................14

3、< AT+ADDR >设置/查询模块蓝牙地址......................................................................14

4、<AT+NAME>设置/查询设备名称（自动重启生效）............................................ 14

5、<AT+BAUD>设置/查询波特率（自动重启生效）..................................................15

6、<AT+SUUID>设置/查询 Service UUID(手动重启生效)..........................................15

7、<AT+CUUID>设置/查询 Chara UUID(手动重启生效）.........................................15

8、<AT+WUUID>设置/查询 WriteUUID（手动重启生效）...................................... 16

9、<AT+RESET> 软件重启 (500ms 后重启)................................................................. 16

10、<AT+DEFAULT> 软件重置 (500ms 后恢复默认设置)...................................... 16

11、<AT+DISC>断开蓝牙连接.............................................................................................16

12、<AT+ADVI>设置/查询广播时间间隔（自动重启生效）...................................17

13、<AT+SLEEP>进入休眠关机模式................................................................................. 17

14、<AT+PWRM>进入广播低功耗模式...........................................................................17

15、<AT+POWE> 设置/查询发射功率（自动重启生效）....................................... 17

深圳市智兴微科技有限公司 12 www.wlsiot.com



ZX-D30 技术手册
AT 指令的配置与收发注意要点：

模块串口为 3.3V TTL 电平，使用串口调试助手，按照 9600,
N, 8, 1 进行配置，修改 AT 指令时，推荐使用上图智兴微公司
自主开发的串口调试助手，右边集合各类 AT 指令，直接发送即
可。

使用其他串口工具发送 AT 指令时，务必在指令后面加入一个
回车，且只能有一个回车。

单片机发送 AT 指令时，需在指令结尾加入\r\n 或 0x0D 0x0A,
回车换行符。

注： AT 指令只有在蓝牙未连接的状态下有效，蓝牙连接成功
后自动转为透传模式，数据将不做解析完全透传给手机 APP。

(自动重启生效)：发完 AT 指令后写入 Flash 自动重启系统
(手动重启生效): 发完 AT 指令后需手动发“AT+RESET“指令重

启生效

深圳市智兴微科技有限公司 13 www.wlsiot.com



ZX-D30 技术手册

1、<AT>测试指令

指令 响应 参数

AT OK 无

2、<AT+VERS>获取软件版本号

指令 响应 参数

AT+VERS OK+G_VERS=<Param> 版本号

3、< AT+ADDR >设置/查询模块蓝牙地址

指令 响应 参数

AT+ADDR? OK+G_ADDR=<Param>
Param： 模块蓝牙地址

AT+ADDR=<Param> OK+S_ADDR=<Param>

例：设置蓝牙 MAC 地址
发送：AT+ADDR=F1:F2:F3:F4:F5:F6
返回：OK+S_ADDR= F1:F2:F3:F4:F5:F6

4、<AT+NAME>设置/查询设备名称（自动重启生效）

指令 响应 参数

AT+NAME? OK+G_NAME=<Param> Param：蓝牙设备名称默认

名称：

AT+NAME=<Param> OK+S_NAME=<Param> “D30LE_XXXXXX”

最长： 20 字节

例：修改蓝牙名：发送：AT+NAME=BLE_DEV——设置模块设
备名为：“BLE_DEV”返回：OK+S_NAME=BLE_DEV——设置模块设备
名为：“BLE_DEV”成功。

深圳市智兴微科技有限公司 14 www.wlsiot.com



ZX-D30 技术手册

5、<AT+BAUD>设置/查询波特率（自动重启生效）

指令 响应 参数

AT+BAUD? OK+G_BAUD=<Param> Param：波特率（bits/s）

取值如下（1~8）：

1——2400

2——4800

3——9600

4——19200
AT+BAUD=<Param> OK+S_BAUD=<Param>

5——38400

6——57600

7——115200

8——128000

默认设置： 3 (9600)

例：设置串口波特率：115200
发送：AT+BAUD=7
返回：OK+S_BAUD=7

6、<AT+SUUID>设置/查询 Service UUID(手动重启生效)

指令 响应 参数

AT+SUUID? OK+G_SUUID=<Param> Param： 0001~FFFF

AT+SUUID=<Param> OK+S_SUUID=<Param> 默认值： FFE0

7、<AT+CUUID>设置/查询 Chara UUID(手动重启生效）

指令 响应 参数

AT+CUUID? OK+G_CUUID=<Param> Param： 0001~FFFF 默认

AT+CUUID=<Param> OK+S_CUUID=<Param> 值：FFE1

深圳市智兴微科技有限公司 15 www.wlsiot.com



ZX-D30 技术手册

注：该 UUID 属性 <Write / Notify>

8、<AT+WUUID>设置/查询WriteUUID（手动重启生效）

指令 响应 参数

AT+WUUID? OK+G_WUUID=<Param> Param： 0001~FFFF 默认

AT+WUUID=<Param> OK+S_WUUID=<Param> 值：FFE2

注：该 UUID 属性 <Write>

9、<AT+RESET> 软件重启 (500ms 后重启)

指令 响应 参数

AT+RESET OK 无

10、<AT+DEFAULT> 软件重置 (500ms 后恢复默认设置)

指令 响应 参数

AT+DEFAULT OK 无

恢复模块默认出厂设置值，模块的所有设置均会被重置，恢复到出厂时状态，

恢复出厂设置后，模块延时 500ms 后重启，如无必要，请慎用。

11、<AT+DISC>断开蓝牙连接

指令 响应 参数

AT+DISC OK 无

注：该指令可在已连接的状态下执行，不受透传影响。

深圳市智兴微科技有限公司 16 www.wlsiot.com



ZX-D30 技术手册

12、<AT+ADVI>设置/查询广播时间间隔（自动重启生效）

指令 响应 参数

AT+ADVI? OK+G_ADVI=<Param> Param： 20-4000 (单位

ms)
AT+ADVI=<Param> OK+S_ADVI=<Param>

默认值：100

注意：1285ms 为 IOS 系统所建议的最大值. 也就是说，1285ms 是苹果允

许的，但是响应扫描和连接的时间会变长.

13、<AT+SLEEP>进入休眠关机模式

指令 响应 参数

AT+SLEEP OK 无

说明：进入休眠模式后，功耗低至 1uA，此时无蓝牙广播需要串口发任意数

据唤醒或 P11 IO 口低电平脉冲唤醒。

14、<AT+PWRM>进入广播低功耗模式

指令 响应 参数

AT+PWRM OK 无

说明：进入广播低功耗模式，指令执行后 LED 灯、按键、串口不再工作，低

功耗进行蓝牙广播，直到 APP 进行蓝牙连接串口重新生效。

15、<AT+POWE> 设置/查询发射功率（自动重启生效）

指令 响应 参数

AT+POWE? OK+G_POWE=<Param> Param： 发射功率值取值

如下（0~15）：默认：15
AT+POWE=<Param> OK+S_POWE=<Param>

注：取值越大距离越远

深圳市智兴微科技有限公司 17 www.wlsiot.com



ZX-D30 技术手册

6.1 BLE通讯服务 UUID说明

主服务 UUID 0000FFE0-0000-1000-8000-00805F9B34FB

透传读写属性 UUID 0000FFE1-0000-1000-8000-00805F9B34FB(Write/Notify)

透传写属性 UUID 0000FFE2-0000-1000-8000-00805F9B34FB(Write)

IO 控制读写属性 UUID 0000FFE3-0000-1000-8000-00805F9B34FB(Write/Notify)

6.2 IO映射表

编号 E0 E1 E2 E3 E4 E5 E6 E7 E8

PIN 19 18 17 16 15 14 5 4 9

引脚 P02 P03 P04 P05 P06 P07 P16 P17 P33

引脚映射图

深圳市智兴微科技有限公司 16 www.wlsiot.com



ZX-D30 技术手册

6.3 引脚功能定义

○1 4 路输出引脚(编号 E4-E7),APP 下发指令控制电平值；

○2 2 路输入引脚(编号 E2-E3),需 APP 主动查询引脚电平值；

○3 2 路中断输入(编号 E0-E1),电平变化后主动上报 APP；

○4 1 路 ADC 引脚(编号 E8)；

引脚说明：

1、4 路输出引脚高电平为 3.3V，低电平为 0V；

2、2 路输入脚可承受的输入电压在 3.6V以下；

3、2 路中断输入引脚默认为上拉高电平；

4、ADC 引脚可测量的电压范围在 0-1V 之间，精度为 8 位 AD，接外

部电路需分压到 0-1V该电压值进行采样；

深圳市智兴微科技有限公司 17 www.wlsiot.com



ZX-D30 技术手册

6.4 APP控制指令

6.4.1 输出 IO 控制

指令 应答 参数

Param:
P1：表示 IO 口(映射关系见
列表)

E4 - E7
特殊编号 EF 表示所有

IO 口
P2: 表示电平输出

0x00: 低电平
0x01: 高电平

CCD1<P1><P2><P3><P4:H><P4:L> 成功：2B4F4B P3: 单个脉冲方式
(长度：7 Byte) 失败：2B4552 0x00: 无脉冲

0x01: 高电平(持续时
间 P4 参数控制)-低电平
(P2 参数无效)

0x02: 低电平(持续时
间 P4 参数控制)-高电平
(P2 参数无效)
P4(2 字节):IO 高电平或低
电平输出时间(单位 100ms)

0000 或 FFFF 为无限期

例：
1.P17 控制为高电平
CC D1 E7 01 00 00 00
2.P07 控制为低电平
CC D1 E5 00 00 00 00
3.P17 输出 500ms 高电平脉冲
CC D1 E7 01 00 00 05
4.所有输出引脚高电平
CC D1 EF 01 00 00 00

深圳市智兴微科技有限公司 18 www.wlsiot.com



ZX-D30 技术手册

6.4.2 设置所有 4 路输出 IO 口高低电平状态(立即生效或掉

电保存)

指令 应答 参数

Param:
P1: 模式

0x00: 写入所有 IO 口
状态值立即执行，掉电不保
存

0x01: 写入所有 IO 口
状态值立即执行，掉电保
存，每次上电所有 IO 初始

CCD2<P1><P2><P3><P4><P5><P6>
成功：2B4F4B 失 化该设定值

<P7><P8><P9>
败：2B4552

(长度：11 Byte)
P2、P4、P6、P8：表示 IO 口
(映射关系见列表)

E4 - E7

P3、P5、P7、P9: 表示电平
输出

0x00: 低电平
0x01: 高电平

6.4.3 读取 4路输出口状态

指令 应答 参数

Param:
<P1-P4>:IO 映射的编号

CCD3 CCD304<P1><P2><P3><P4>
E4-E7 引脚状态

(长度：2 Byte) （7 字节）
0x00: 低电平
0x01: 高电平

深圳市智兴微科技有限公司 19 www.wlsiot.com



ZX-D30 技术手册

6.4.4 读取输入口状态

指令 应答 参数

Param:
<P1-P2> : IO 映射的编号

CCD4 CCD402<P1><P2>
E2-E3 状态

（长度：2 Byte） （5字节）
0x00: 低电平
0x01: 高电平

6.4.5 APP上报中断输入口状态 (模块主从上报给APP接收)

模块上传 APP 参数

Param:
CCD502<P1><P2> P1:IO 映射的编号 E0-E1 状态
(长度：5 Byte) P2: 0x00: 低电平

0x01: 高电平

6.4.6 APP 发指令读取 1路 ADC 值

指令 应答 参数

Param:
CCD6

CCD602<P1:H><P1:L> <P1:H><P1:L> :
（长度：2 Byte）

ADC 值 2 字节(mV 电压)

6.4.7 设置 ADC主动上报使能

指令 应答 参数

Param:
<P1>: 0: 不主动上报

CCD7<P1><P2> 成功：2B4F4B
1: 主动上报

（长度：4 Byte） 失败：2B4552
<P2>:(单位 100ms):主动上报频率
(1-255)

深圳市智兴微科技有限公司 20 www.wlsiot.com



ZX-D30 技术手册

6.4.8 上报 ADC值

模块上传 APP 参数

CCD802<P1:H><P1:L>
Param: <P1:H><P1:L>:2 字节电压值：单位 mV

（长度：5 Byte）

6.4.9 APP 发送修改蓝牙名称指令

指令 应答 参数

“AT+NAME?” “+NAME=<P1>”
Param:

成功：2B4F4B P1 : 蓝牙名称
“AT+NAME=”<P1>

失败：2B4552

6.4.10 APP 发送查询和修改波特率指令

指令 应答 参数

Param： 波特率（bits/s）

“AT+BAUD?” “+BAUD=<P1>” 取值如下（1~8）：

1——2400
2——4800
3——9600
4——19200

成功：2B4F4B 5——38400
“AT+BAUD=”<P1>

失败：2B4552 6——57600
7——115200
8——128000
默认设置： 3 (9600)

深圳市智兴微科技有限公司 21 www.wlsiot.com



ZX-D30 技术手册

版本 固件版本 时间 描述

1、 新增发射功率修改指令

V1.3 V1.7.6 2022/4/11 2、 新增(P13)IO 引脚输入控制进入低功
耗模式

1. 新增 IO 口控制，直驱模式
V1.4 V1.7.7 2022/5/6

2. 新增 APP 修改蓝牙名称功能

1. 新增 APP 查询蓝牙名称指令

V1.5 V1.7.8 2022/5/10 2. 新增 APP 查询修改串口波特率指令

3. 优化功耗，更新功耗数据

深圳市智兴微科技有限公司 22 www.wlsiot.com



ZX-D30 技术手册

深圳市智兴微科技有限公司

官方官网：www.wlsiot.com

样品购买：wlsiot.taobao.com

咨询热线：0755-27087743

公司地址：深圳市宝安区福永街道兴围锦灏大厦 1912

本文中的信息，包括供参考的 URL 地址，如有变更，恕不另
行通知。文档“按现状”提供，不负任何担保责任，包括对适销
性、适用于特定用途或非侵权性的任何担保，和任何提案、规格
或样品在他处提到的任何担保。本文档不负任何责任，包括使用
本文档内信息产生的侵犯任何专利权行为的责任。本文档在此未
以禁止反言或其他方式授予任何知识产权使用许可，不管是明示
许可还是暗示许可。文中所得测试数据均为测试所得，实际结果
可能略有差异。蓝牙联盟成员标志归蓝牙联盟所有。文中提到的
所有商标名称、商标和注册商标均属其各自所有者的财产，特此
声明。最终解释权归深圳市智兴微科技有限公司所有。

深圳市智兴微科技有限公司 23 www.wlsiot.com