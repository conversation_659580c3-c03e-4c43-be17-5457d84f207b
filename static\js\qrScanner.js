/**
 * 今夜城堡用户端二维码扫描工具
 * 封装了与二维码扫描相关的功能
 */

import LoginManager from './login.js';

/**
 * 二维码扫描器类
 */
const QRScanner = {
  /**
   * 扫描二维码
   * @param {Object} options 配置项
   * @param {Boolean} options.checkLogin 是否检查登录状态
   * @param {Boolean} options.onlyFromCamera 是否只允许从相机扫码
   * @param {Function} options.success 成功回调
   * @param {Function} options.fail 失败回调
   * @param {Function} options.complete 完成回调
   * @returns {Promise} Promise对象
   */
  scan(options = {}) {
    return new Promise((resolve, reject) => {
      const defaultOptions = {
        checkLogin: true,
        onlyFromCamera: true
      };
      
      const opts = { ...defaultOptions, ...options };
      
      // 如果需要检查登录状态
      if (opts.checkLogin) {
        // 检查是否已登录
        if (!LoginManager.isLoggedIn()) {
          // 未登录，先登录
          LoginManager.login()
            .then(() => {
              // 登录成功，继续扫码
              this.doScan(opts, resolve, reject);
            })
            .catch(err => {
              // 登录失败
              console.error('登录失败，无法扫码:', err);
              
              if (typeof opts.fail === 'function') {
                opts.fail(err);
              }
              
              reject(err);
            });
        } else {
          // 已登录，直接扫码
          this.doScan(opts, resolve, reject);
        }
      } else {
        // 不需要检查登录状态，直接扫码
        this.doScan(opts, resolve, reject);
      }
    });
  },
  
  /**
   * 执行扫码操作
   * @param {Object} options 配置项
   * @param {Function} resolve Promise resolve函数
   * @param {Function} reject Promise reject函数
   */
  doScan(options, resolve, reject) {
    // 检查是否已显示loading
    const hasLoading = options.showLoading !== false;
    
    // 如果需要显示loading，则显示
    if (hasLoading) {
      uni.showLoading({
        title: '准备扫码...',
        mask: true
      });
    }
    
    // 检查相机权限
    this.checkCameraPermission()
      .then(() => {
        // 有相机权限，开始扫码
        uni.scanCode({
          onlyFromCamera: options.onlyFromCamera,
          scanType: ['qrCode'],
          success: (res) => {
            console.log('扫码成功:', res);
            
            // 隐藏加载中
            if (hasLoading) {
              uni.hideLoading();
            }
            
            // 解析扫码结果
            const result = this.parseQRResult(res.result);
            
            if (typeof options.success === 'function') {
              options.success(result);
            }
            
            resolve(result);
          },
          fail: (err) => {
            console.error('扫码失败:', err);
            
            // 隐藏加载中
            if (hasLoading) {
              uni.hideLoading();
            }
            
            if (typeof options.fail === 'function') {
              options.fail(err);
            }
            
            reject(err);
          },
          complete: () => {
            // 确保loading被关闭
            if (hasLoading) {
              setTimeout(() => {
                uni.hideLoading();
              }, 100);
            }
            
            if (typeof options.complete === 'function') {
              options.complete();
            }
          }
        });
      })
      .catch(err => {
        console.error('相机权限检查失败:', err);
        
        // 隐藏加载中
        if (hasLoading) {
          uni.hideLoading();
        }
        
        if (typeof options.fail === 'function') {
          options.fail(err);
        }
        
        reject(err);
      });
  },
  
  /**
   * 检查相机权限
   * @returns {Promise} Promise对象
   */
  checkCameraPermission() {
    return new Promise((resolve, reject) => {
      // #ifdef MP-WEIXIN
      // 获取系统信息，检查平台类型
      const systemInfo = uni.getSystemInfoSync();
      const platform = systemInfo.platform;
      
      // 判断是否是鸿蒙系统
      const isHarmonyOS = platform === 'ohos' || (systemInfo.system && systemInfo.system.toLowerCase().includes('harmony'));
      
      // 如果是鸿蒙系统，直接解析，不进行权限检查（鸿蒙系统会自动处理权限）
      if (isHarmonyOS) {
        console.log('检测到鸿蒙系统，跳过相机权限检查');
        resolve();
        return;
      }
      
      // 其他平台正常检查权限
      uni.getSetting({
        success: (res) => {
          if (res.authSetting['scope.camera']) {
            // 已有相机权限
            resolve();
          } else {
            // 请求相机权限
            uni.authorize({
              scope: 'scope.camera',
              success: () => {
                resolve();
              },
              fail: (err) => {
                // 权限请求失败，引导用户手动开启
                uni.showModal({
                  title: '提示',
                  content: '需要相机权限才能扫码，请在设置中开启相机权限',
                  confirmText: '去设置',
                  success: (res) => {
                    if (res.confirm) {
                      uni.openSetting({
                        success: (settingRes) => {
                          if (settingRes.authSetting['scope.camera']) {
                            resolve();
                          } else {
                            reject(new Error('用户拒绝授予相机权限'));
                          }
                        },
                        fail: () => {
                          reject(new Error('打开设置页面失败'));
                        }
                      });
                    } else {
                      reject(new Error('用户取消授予相机权限'));
                    }
                  }
                });
              }
            });
          }
        },
        fail: (err) => {
          reject(err);
        }
      });
      // #endif
      
      // #ifndef MP-WEIXIN
      // 非微信小程序环境，默认有权限
      resolve();
      // #endif
    });
  },
  
  /**
   * 解析二维码结果
   * @param {String} result 扫码结果
   * @returns {Object} 解析后的结果对象
   */
  parseQRResult(result) {
    try {
      // 结果对象
      const parsedResult = {
        raw: result,
        type: 'unknown',
        data: {}
      };
      
      // 先检查是否包含常见设备标识符
      const deviceKeywords = ['device', 'mac', 'deviceid', '设备', '门锁'];
      const hasDeviceKeyword = deviceKeywords.some(keyword => 
        result.toLowerCase().includes(keyword.toLowerCase())
      );
      
      if (hasDeviceKeyword) {
        // 可能是设备二维码，尝试提取MAC地址或设备ID
        let deviceId = '';
        
        // 尝试匹配常见MAC地址格式 (XX:XX:XX:XX:XX:XX 或 XX-XX-XX-XX-XX-XX)
        const macPattern = /([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})/;
        const macMatch = result.match(macPattern);
        if (macMatch) {
          deviceId = macMatch[0];
        } else {
          // 尝试从URL参数或JSON中提取
          const deviceParamPatterns = [
            /[?&](device|mac|deviceid)=([^&]+)/i,
            /[?&](id|设备号)=([^&]+)/i
          ];
          
          for (const pattern of deviceParamPatterns) {
            const match = result.match(pattern);
            if (match && match[2]) {
              deviceId = match[2];
              break;
            }
          }
        }
        
        if (deviceId) {
          parsedResult.type = 'device';
          parsedResult.data.mac = deviceId;
          
          // 尝试提取房间ID和订单ID
          const roomMatch = result.match(/[?&](roomid|room|id)=([^&]+)/i);
          if (roomMatch && roomMatch[2]) {
            parsedResult.data.roomId = roomMatch[2];
          }
          
          const orderMatch = result.match(/[?&](orderid|order)=([^&]+)/i);
          if (orderMatch && orderMatch[2]) {
            parsedResult.data.orderId = orderMatch[2];
          }
          
          console.log('识别为设备二维码:', parsedResult);
          return parsedResult;
        }
      }
      
      // 尝试解析为URL
      let url;
      try {
        url = new URL(result);
      } catch (e) {
        // 不是有效URL，尝试其他格式
      }
      
      // 如果是URL
      if (url) {
        parsedResult.type = 'url';
        parsedResult.data.url = result;
        
        // 解析URL参数
        const params = {};
        url.searchParams.forEach((value, key) => {
          params[key] = value;
        });
        parsedResult.data.params = params;
        
        // 检查是否是设备二维码
        if (params.mac || params.deviceId || params.device || params.id) {
          parsedResult.type = 'device';
          parsedResult.data.isFromUrl = true; // 标记为URL格式的二维码
          
          // 优先使用id参数作为设备ID
          if (params.id) {
            parsedResult.data.id = params.id;
          }
          
          // 兼容其他参数名
          parsedResult.data.mac = params.mac || params.deviceId || params.device || params.id;
          parsedResult.data.roomId = params.roomId || params.room || '';
          parsedResult.data.orderId = params.orderId || params.order || '';
        }
        
        // 检查是否是订单二维码
        else if (params.orderId || params.order) {
          parsedResult.type = 'order';
          parsedResult.data.orderId = params.orderId || params.order;
        }
        
        // 检查是否是房间二维码
        else if (params.roomId || params.room) {
          parsedResult.type = 'room';
          parsedResult.data.roomId = params.roomId || params.room;
        }
        
        // 检查是否是合作商招募二维码
        else if (params.partner || params.zhaoshang) {
          parsedResult.type = 'partner';
        }
      } 
      // 如果是JSON字符串
      else if (result.startsWith('{') && result.endsWith('}')) {
        try {
          const jsonData = JSON.parse(result);
          parsedResult.type = 'json';
          parsedResult.data = jsonData;
          
          // 检查JSON数据类型
          if (jsonData.mac || jsonData.deviceId || jsonData.device || jsonData.id) {
            parsedResult.type = 'device';
            parsedResult.data.mac = jsonData.mac || jsonData.deviceId || jsonData.device || jsonData.id;
            parsedResult.data.roomId = jsonData.roomId || jsonData.room || '';
            parsedResult.data.orderId = jsonData.orderId || jsonData.order || '';
          } else if (jsonData.orderId || jsonData.order) {
            parsedResult.type = 'order';
          } else if (jsonData.roomId || jsonData.room) {
            parsedResult.type = 'room';
          }
        } catch (e) {
          console.error('JSON解析失败:', e);
        }
      }
      
      // 如果仍未识别出类型，但URL中包含特定关键字，进行额外检查
      if (parsedResult.type === 'url' || parsedResult.type === 'unknown') {
        // 检查是否包含设备相关关键字
        const lowerResult = result.toLowerCase();
        if (lowerResult.includes('device=') || lowerResult.includes('mac=') || 
            lowerResult.includes('deviceid=') || lowerResult.includes('id=')) {
          
          // 尝试从URL中提取设备ID
          const deviceMatches = [
            result.match(/device=([^&]+)/i),
            result.match(/mac=([^&]+)/i),
            result.match(/deviceid=([^&]+)/i),
            result.match(/id=([^&]+)/i)
          ];
          
          for (const match of deviceMatches) {
            if (match && match[1]) {
              parsedResult.type = 'device';
              parsedResult.data.isFromUrl = true; // 标记为URL格式的二维码
              
              // 如果是id参数，同时设置id字段
              if (match[0].toLowerCase().startsWith('id=')) {
                parsedResult.data.id = match[1];
              }
              
              parsedResult.data.mac = match[1];
              break;
            }
          }
        }
      }
      
      console.log('解析二维码结果:', parsedResult);
      return parsedResult;
    } catch (error) {
      console.error('解析二维码结果失败:', error);
      return {
        raw: result,
        type: 'unknown',
        data: {}
      };
    }
  },
  
  /**
   * 处理扫码结果
   * @param {Object} result 扫码结果
   * @returns {Promise} Promise对象
   */
  handleScanResult(result) {
    console.log('处理扫码结果:', result);
    
    return new Promise((resolve, reject) => {
      try {
        // 根据不同类型的二维码进行不同处理
        switch (result.type) {
          case 'device':
            // 设备二维码
            this.handleDeviceQR(result.data);
            resolve({ success: true, type: 'device', data: result.data });
            break;
          case 'order':
            // 订单二维码
            this.handleOrderQR(result.data);
            resolve({ success: true, type: 'order', data: result.data });
            break;
          case 'room':
            // 房间二维码
            this.handleRoomQR(result.data);
            resolve({ success: true, type: 'room', data: result.data });
            break;
          case 'partner':
            // 合作商招募二维码
            this.handlePartnerQR(result.data);
            resolve({ success: true, type: 'partner', data: result.data });
            break;
          case 'url':
            // 普通URL，打开网页
            uni.navigateTo({
              url: '/pages/webview/webview?url=' + encodeURIComponent(result.data.url)
            });
            resolve({ success: true, type: 'url', data: result.data });
            break;
          default:
            // 未知类型
            uni.showToast({
              title: '无法识别的二维码',
              icon: 'none'
            });
            reject(new Error('无法识别的二维码'));
            break;
        }
      } catch (error) {
        console.error('处理扫码结果出错:', error);
        reject(error);
      }
    });
  },
  
  /**
   * 处理设备二维码
   * @param {Object} data 设备数据
   */
  handleDeviceQR(data) {
    // 判断是否是URL格式的二维码（使用id参数）
    if (data.isFromUrl || data.id) {
      // 使用id参数跳转到设备页面
      uni.navigateTo({
        url: `/pages/scan/device?id=${data.id || data.mac || ''}&roomId=${data.roomId || ''}&orderId=${data.orderId || ''}&scanType=miniapp`
      });
    } else {
      // 蓝牙MAC地址格式，使用mac参数
      uni.navigateTo({
        url: `/pages/scan/device?mac=${data.mac || ''}&roomId=${data.roomId || ''}&orderId=${data.orderId || ''}&scanType=bluetooth`
      });
    }
  },
  
  /**
   * 处理订单二维码
   * @param {Object} data 订单数据
   */
  handleOrderQR(data) {
    // 跳转到订单详情页
    uni.navigateTo({
      url: `/pages/orders/detail?orderId=${data.orderId || ''}`
    });
  },
  
  /**
   * 处理房间二维码
   * @param {Object} data 房间数据
   */
  handleRoomQR(data) {
    // 跳转到房间详情页
    uni.navigateTo({
      url: `/pages/rooms/detail?roomId=${data.roomId || ''}`
    });
  },
  
  /**
   * 处理合作商招募二维码
   * @param {Object} data 合作商数据
   */
  handlePartnerQR(data) {
    // 跳转到合作商招募页
    uni.navigateTo({
      url: '/pages/partner/recruit'
    });
  }
};

export default QRScanner; 