<template>
	<view class="container page-scan">
		<!-- 页面背景 -->
		<view class="page-background">
			<!-- 背景图片 -->
			<image class="background-image" src="https://wuji1112-1312674791.cos.ap-guangzhou.myqcloud.com/static/shouye3.png" mode="aspectFill"></image>
			
			<!-- 页面渐变背景 - 保留磨砂效果 -->
			<view class="gradient-overlay"></view>
		</view>
		
		<!-- 顶部状态栏占位 -->
		<view class="status-bar safe-area-inset-top"></view>
		
		<!-- 顶部导航栏 -->
		<view class="nav-bar">
			<view class="nav-back" @click="goBack">
				<text class="material-icons">arrow_back</text>
			</view>
			<view class="nav-title">扫码连接</view>
		</view>
		
		<!-- 扫码区域 -->
		<view class="scan-content">
			<!-- 扫码框区域 -->
			<view class="scan-area">
				<view class="scan-frame">
					<view class="scan-line"></view>
					<view class="scan-corner scan-corner-top-left"></view>
					<view class="scan-corner scan-corner-top-right"></view>
					<view class="scan-corner scan-corner-bottom-left"></view>
					<view class="scan-corner scan-corner-bottom-right"></view>
				</view>
				<view class="scan-tips">
					<text>请将二维码放入框内，即可自动扫描</text>
				</view>
			</view>
		</view>
		
		<!-- 扫码加载遮罩层 -->
		<view class="scan-loading-mask" v-if="scanning">
			<view class="scan-loading">
				<view class="loading-spinner"></view>
				<text class="loading-text">正在扫描...</text>
			</view>
		</view>
	</view>
</template>

<script>
	import { lockService } from '@/utils/index.js';
	import API from '@/static/js/api.js';
	
	// 修复导出问题
	const scanPage = {
		name: 'scan',
		data() {
			return {
				scanning: false,
				isPageReady: false,
				scanAttempts: 0,
				maxScanAttempts: 3,
				processing: false
			}
		},
		onLoad() {
			console.log('扫码页面 onLoad');
			// 页面加载时标记为准备好
			this.isPageReady = true;
			
			// 延迟启动扫码，防止黑屏
			setTimeout(() => {
				// 开始扫码
				this.startScan();
			}, 500);
		},
		onShow() {
			console.log('扫码页面 onShow');
			// 页面显示时，如果准备好且未在扫码，则开始扫码
			if (this.isPageReady && !this.scanning) {
				setTimeout(() => {
					this.startScan();
				}, 300);
			}
		},
		onHide() {
			console.log('扫码页面 onHide');
			// 页面隐藏时停止扫码
			this.stopScan();
		},
		onUnload() {
			console.log('扫码页面 onUnload');
			// 页面卸载时停止扫码并标记为未准备好
			this.stopScan();
			this.isPageReady = false;
		},
		// 监听页面返回按钮
		onBackPress() {
			console.log('扫码页面 onBackPress');
			// 先停止扫码
			this.stopScan();
			return false; // 返回false，让系统执行默认的返回逻辑
		},
		methods: {
			// 返回上一页
			goBack() {
				// 先停止扫码
				this.stopScan();
				
				// 延迟后返回，避免返回失败
				setTimeout(() => {
					// 尝试使用navigateBack返回
					uni.navigateBack({
						delta: 1,
						fail: () => {
							// 如果返回失败，则跳转到首页
							uni.switchTab({
								url: '/pages/index/index',
								fail: () => {
									// 如果跳转到首页也失败，则重启小程序
									uni.reLaunch({
										url: '/pages/index/index'
									});
								}
							});
						}
					});
				}, 200);
			},
			
			// 开始扫码
			startScan() {
				// 防止重复调用
				if (this.scanning) {
					console.log('已经在扫码中，忽略本次调用');
					return;
				}
				
				// 检查是否超过最大尝试次数
				if (this.scanAttempts >= this.maxScanAttempts) {
					console.log('已达到最大扫码尝试次数');
					uni.showModal({
						title: '提示',
						content: '多次扫码失败，请返回后重试',
						showCancel: false,
						success: () => {
							this.goBack();
						}
					});
					return;
				}
				
				this.scanning = true;
				this.scanAttempts++;
				console.log(`开始扫码，第 ${this.scanAttempts} 次尝试`);
				
				// 直接执行扫码，微信小程序会自动请求相机权限
				this.doScanCode();
			},
			
			// 执行扫码操作
			doScanCode() {
				// 添加Loading提示
				uni.showLoading({
					title: '准备扫码...',
					mask: false
				});
				
				// 延迟执行扫码，避免黑屏问题
				setTimeout(() => {
					try {
						uni.hideLoading();
					} catch (e) {
						console.warn('隐藏loading失败:', e);
					}
					
					// 使用uni-app的扫码API
					uni.scanCode({
						scanType: ['qrCode'],
						success: (res) => {
							console.log('扫码结果:', res);
							this.handleScanResult(res.result);
						},
						fail: (err) => {
							console.error('扫码失败:', err);
							this.scanning = false;
							
							// 判断是否是因为权限问题导致的失败
							if (err.errMsg && err.errMsg.includes('authorize')) {
								this.showPermissionDeniedTip('camera');
							} else {
								uni.showToast({
									title: '扫码失败，请重试',
									icon: 'none'
								});
							}
						},
						complete: () => {
							this.scanning = false;
						}
					});
				}, 300);
			},
			
			// 停止扫码
			stopScan() {
				console.log('停止扫码');
				this.scanning = false;
			},
			
			// 处理扫描结果
			handleScanResult(result) {
				console.log('扫描结果原始数据:', result);
				
				// 显示加载中
				uni.showLoading({
					title: '正在处理...',
					mask: true
				});
				
				// 解析扫描结果
				try {
					console.log('开始解析扫描结果');
					
					// 检查是否是URL格式的二维码
					if (result.startsWith('http')) {
						console.log('检测到URL格式二维码');
						// 解析URL参数
						try {
							const url = new URL(result);
							
							// 检查多种可能的参数名
							const id = url.searchParams.get('id') || 
									  url.searchParams.get('deviceId') || 
									  url.searchParams.get('device') || 
									  url.searchParams.get('deviceCode');
							
							// 检查URL路径中是否包含设备ID
							let pathId = null;
							if (!id && url.pathname) {
								// 尝试从路径中提取ID
								const pathParts = url.pathname.split('/').filter(part => part.length > 0);
								if (pathParts.length > 0) {
									// 取最后一个路径部分作为可能的ID
									const lastPart = pathParts[pathParts.length - 1];
									// 如果是数字或看起来像设备ID的格式
									if (/^\d+$/.test(lastPart) || /^[a-zA-Z0-9-_]{4,}$/.test(lastPart)) {
										pathId = lastPart;
									}
								}
							}
							
							// 使用参数中的ID或路径中的ID
							const deviceId = id || pathId;
							
							if (deviceId) {
								console.log('从URL中提取到设备ID:', deviceId);
								// 隐藏加载提示
								uni.hideLoading();
								// 直接跳转到设备页面，使用scanType=miniapp标识是小程序扫码
								uni.navigateTo({
									url: `/pages/scan/device?id=${deviceId}&scanType=miniapp`,
									success: () => {
										console.log('导航到设备页面成功');
									},
									fail: (err) => {
										console.error('导航到设备页面失败:', err);
										uni.showToast({
											title: '导航到设备页面失败',
											icon: 'none'
										});
									}
								});
								return;
							} else if (url.host.includes('jycb') || result.includes('jycb')) {
								// 如果URL域名包含jycb但没有找到ID参数，可能是首页或其他页面
								console.log('检测到今夜城堡相关URL，但未找到设备ID');
								uni.hideLoading();
								uni.showToast({
									title: '未找到有效的设备信息',
									icon: 'none'
								});
								return;
							} else {
								console.log('URL中未找到设备ID参数，继续检查是否是蓝牙设备码');
							}
						} catch (err) {
							console.error('解析URL失败:', err);
							// 解析URL失败，继续检查是否是蓝牙设备码
						}
					}
					
					// 检查是否是有效的设备二维码
					const isValid = this.isValidDeviceCode(result);
					console.log('是否是有效的设备二维码:', isValid);
					
					if (isValid) {
						// 提取MAC地址
						const mac = this.extractMacFromCode(result);
						console.log('提取的MAC地址:', mac);
						
						if (mac) {
							console.log('准备初始化蓝牙环境');
							
							// 预初始化蓝牙环境
							lockService.init()
								.then((initResult) => {
									console.log('蓝牙环境初始化结果:', initResult);
									
									// 导航到设备页面
									uni.navigateTo({
										url: `/pages/scan/device?mac=${mac}&scanType=bluetooth`,
										success: () => {
											console.log('导航到设备页面成功');
											uni.hideLoading();
										},
										fail: (err) => {
											console.error('导航到设备页面失败:', err);
											uni.hideLoading();
											
											uni.showToast({
												title: '导航到设备页面失败',
												icon: 'none'
											});
										}
									});
								})
								.catch(err => {
									console.error('初始化蓝牙环境失败:', err);
									uni.hideLoading();
									
									uni.showToast({
										title: '初始化蓝牙环境失败，请确保蓝牙已开启',
										icon: 'none'
									});
								});
						} else {
							console.error('无法从二维码中提取MAC地址');
							uni.hideLoading();
							
							uni.showToast({
								title: '无效的设备二维码',
								icon: 'none'
							});
						}
					} else {
						console.error('无效的设备二维码格式');
						uni.hideLoading();
						
						uni.showToast({
							title: '无效的设备二维码',
							icon: 'none'
						});
					}
				} catch (err) {
					console.error('处理扫描结果失败:', err);
					uni.hideLoading();
					
					uni.showToast({
						title: '处理扫描结果失败',
						icon: 'none'
					});
				}
			},
			
			// 导航到设备页面
			navigateToDevicePage(deviceId, scanType) {
				console.log(`准备导航到设备页面, 参数: { ${scanType === 'bluetooth' ? 'mac' : 'id'}: ${deviceId}, scanType: ${scanType} }`);
				
				// 构建URL参数
				const params = scanType === 'bluetooth' 
					? `mac=${deviceId}&scanType=bluetooth` 
					: `id=${deviceId}&scanType=miniapp`;
					
				uni.navigateTo({
					url: `/pages/scan/device?${params}`,
					success: () => {
						console.log('导航到设备页面成功');
						uni.hideLoading();
					},
					fail: (err) => {
						console.error('导航到设备页面失败:', err);
						uni.hideLoading();
						
						uni.showToast({
							title: '导航到设备页面失败',
							icon: 'none'
						});
					}
				});
			},
			
			// 检查是否是有效的设备二维码
			isValidDeviceCode(code) {
				console.log('检查二维码是否有效:', code);
				// 检查是否包含MAC地址格式
				const isValid = /([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})|([0-9A-Fa-f]{12})/i.test(code);
				console.log('二维码有效性检查结果:', isValid);
				return isValid;
			},
			
			// 从二维码中提取MAC地址
			extractMacFromCode(code) {
				console.log('从二维码中提取MAC地址:', code);
				// 尝试匹配MAC地址格式
				const macMatch = code.match(/([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})|([0-9A-Fa-f]{12})/i);
				
				if (macMatch) {
					console.log('提取到MAC地址:', macMatch[0]);
					return macMatch[0];
				}
				
				console.log('未能从二维码中提取MAC地址');
				return null;
			},
			
			// 回退到传统方式处理二维码
			fallbackToTraditionalMethod(result) {
				console.log('回退到传统方式处理二维码');
				
				// 显示加载提示
				uni.showLoading({
					title: '处理中...',
					mask: true
				});
				
				// 使用锁控制服务处理二维码
				lockService.handleDeviceQRCode(result)
					.then(deviceInfo => {
						console.log('设备绑定状态检查成功:', deviceInfo);
						
						// 创建订单
						return lockService.createOrder();
					})
					.then(orderInfo => {
						console.log('创建订单成功:', orderInfo);
						try {
							uni.hideLoading();
						} catch (e) {
							console.warn('隐藏loading失败:', e);
						}
						this.processing = false;
						
						// 跳转到设备控制页面
						uni.redirectTo({
							url: `/pages/scan/device?mac=${lockService.deviceInfo.mac}&orderId=${orderInfo.orderId}`,
							fail: (err) => {
								console.error('跳转失败:', err);
								uni.showToast({
									title: '页面跳转失败',
									icon: 'none'
								});
							}
						});
					})
					.catch(err => {
						console.error('处理扫码结果失败:', err);
						try {
							uni.hideLoading();
						} catch (e) {
							console.warn('隐藏loading失败:', e);
						}
						this.processing = false;
						
						uni.showModal({
							title: '连接失败',
							content: err.message || '无法连接设备，请重试',
							showCancel: false,
							success: () => {
								// 重新开始扫码
								setTimeout(() => {
									this.startScan();
								}, 500);
							}
						});
					});
			},
			
			// 检查权限
			checkPermission(permissionType) {
				return new Promise((resolve) => {
					// #ifdef MP-WEIXIN
					// 微信小程序检查权限
					uni.getSetting({
						success: (res) => {
							if (res.authSetting && res.authSetting[`scope.${permissionType}`] !== false) {
								resolve(true);
							} else {
								resolve(false);
							}
						},
						fail: () => {
							resolve(false);
						}
					});
					// #endif
					
					// #ifdef APP-PLUS
					// App检查权限
					const permissionMap = {
						camera: 'CAMERA'
					};
					
					const permissionCode = permissionMap[permissionType];
					if (!permissionCode) {
						resolve(false);
						return;
					}
					
					const checkPermission = uni.requireNativePlugin('globalAPIs');
					if (checkPermission && checkPermission.hasPermission) {
						checkPermission.hasPermission([permissionCode], (res) => {
							resolve(res.result);
						});
					} else {
						resolve(true); // 如果无法检查，假设有权限
					}
					// #endif
					
					// #ifndef MP-WEIXIN || APP-PLUS
					// 其他平台默认有权限
					resolve(true);
					// #endif
				});
			},
			
			// 请求权限
			requestPermission(permissionType) {
				return new Promise((resolve, reject) => {
					// #ifdef MP-WEIXIN
					// 微信小程序请求权限
					uni.authorize({
						scope: `scope.${permissionType}`,
						success: () => {
							resolve();
						},
						fail: () => {
							reject();
						}
					});
					// #endif
					
					// #ifdef APP-PLUS
					// App请求权限
					const permissionMap = {
						camera: 'CAMERA'
					};
					
					const permissionCode = permissionMap[permissionType];
					if (!permissionCode) {
						reject();
						return;
					}
					
					const requestPermission = uni.requireNativePlugin('globalAPIs');
					if (requestPermission && requestPermission.requestPermission) {
						requestPermission.requestPermission([permissionCode], (res) => {
							if (res.result) {
								resolve();
							} else {
								reject();
							}
						});
					} else {
						resolve(); // 如果无法请求，假设成功
					}
					// #endif
					
					// #ifndef MP-WEIXIN || APP-PLUS
					// 其他平台默认成功
					resolve();
					// #endif
				});
			},
			
			// 显示权限被拒绝提示
			showPermissionDeniedTip(permissionType) {
				let permissionName = '';
				switch (permissionType) {
					case 'camera':
						permissionName = '相机';
						break;
					default:
						permissionName = permissionType;
				}
				
				uni.showModal({
					title: '权限提示',
					content: `需要${permissionName}权限才能使用扫码功能，请在设置中开启权限`,
					confirmText: '去设置',
					cancelText: '取消',
					success: (res) => {
						if (res.confirm) {
							// #ifdef MP-WEIXIN
							uni.openSetting({
								success: (settingRes) => {
									console.log('打开设置页面成功');
								}
							});
							// #endif
							
							// #ifdef APP-PLUS
							const openAppSetting = uni.requireNativePlugin('globalAPIs');
							if (openAppSetting && openAppSetting.openAppSetting) {
								openAppSetting.openAppSetting((res) => {
									console.log('打开设置页面成功');
								});
							}
							// #endif
						} else {
							this.goBack();
						}
					}
				});
			}
		}
	};
	
	export default scanPage;
</script>

<style>
	/* CSS变量定义 */
	page {
		--primary-light: #A875FF;
		--neon-pink: #ff36f9;
	}
	
	/* 扫码加载遮罩层 */
	.scan-loading-mask {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.7);
		z-index: 9999;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.scan-loading {
		background-color: rgba(30, 30, 30, 0.9);
		border-radius: 20rpx;
		padding: 40rpx 60rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		border: 2rpx solid rgba(168, 117, 255, 0.3);
		box-shadow: 0 0 20rpx rgba(168, 117, 255, 0.3);
	}
	
	.loading-spinner {
		width: 60rpx;
		height: 60rpx;
		border: 4rpx solid rgba(168, 117, 255, 0.3);
		border-top: 4rpx solid var(--primary-light);
		border-radius: 50%;
		animation: spin 1s linear infinite;
		margin-bottom: 20rpx;
	}
	
	.loading-text {
		color: #ffffff;
		font-size: 30rpx;
	}
	
	@keyframes spin {
		0% { transform: rotate(0deg); }
		100% { transform: rotate(360deg); }
	}
	
	/* 页面基础样式 */
	.page-scan {
		padding-top: 250rpx;
		padding-bottom: calc(100rpx + env(safe-area-inset-bottom));
		color: #ffffff;
		height: 100vh;
		min-height: 100vh;
		box-sizing: border-box;
		position: relative;
		overflow: hidden;
		display: flex;
		flex-direction: column;
	}
	
	/* 页面背景样式 */
	.page-background {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		z-index: 0;
	}
	
	/* 背景图片样式 */
	.background-image {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		z-index: 0;
		object-fit: cover;
	}
	
	.gradient-overlay {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background: linear-gradient(to bottom, 
			rgba(18, 18, 18, 0.3) 0%, 
			rgba(18, 18, 18, 0.5) 50%,
			rgba(18, 18, 18, 0.7) 100%);
		backdrop-filter: blur(5px);
		-webkit-backdrop-filter: blur(5px);
		z-index: 1;
		pointer-events: none;
	}
	
	.status-bar {
		width: 100%;
		background: transparent;
		backdrop-filter: none;
		-webkit-backdrop-filter: none;
		position: fixed;
		top: 0;
		left: 0;
		z-index: 100;
	}
	
	/* 导航栏 */
	.nav-bar {
		position: fixed;
		top: calc(env(safe-area-inset-top) + 60rpx);
		left: 15rpx;
		right: 15rpx;
		height: 110rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		background: transparent;
		backdrop-filter: none;
		-webkit-backdrop-filter: none;
		z-index: 100;
		padding: 0 30rpx;
		border-bottom: none;
		box-shadow: none;
		border-radius: 0 0 30rpx 30rpx;
	}
	
	.nav-back {
		position: absolute;
		left: 30rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 70rpx;
		height: 70rpx;
		border-radius: 50%;
		background-color: rgba(168, 117, 255, 0.15);
		border: 1px solid rgba(168, 117, 255, 0.3);
	}
	
	.nav-back .material-icons {
		font-size: 44rpx;
		color: rgba(255, 255, 255, 0.9);
		text-shadow: 0 0 8rpx rgba(168, 117, 255, 0.5);
	}
	
	.nav-title {
		font-size: 38rpx;
		font-weight: 600;
		color: #ffffff;
		text-shadow: 0 0 10rpx rgba(168, 117, 255, 0.5);
		letter-spacing: 2rpx;
	}
	
	/* 扫码内容区域 */
	.scan-content {
		position: relative;
		z-index: 2;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		flex: 1;
		padding: 0 30rpx;
		max-height: 55vh;
	}
	
	/* 扫码框 */
	.scan-area {
		position: relative;
		display: flex;
		flex-direction: column;
		align-items: center;
	}
	
	.scan-frame {
		position: relative;
		width: 500rpx;
		height: 500rpx;
		border-radius: 20rpx;
		overflow: hidden;
		background-color: rgba(0, 0, 0, 0.15);
		border: 1rpx solid rgba(255, 255, 255, 0.1);
		box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.3);
	}
	
	/* 扫描线动画 */
	.scan-line {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 10rpx;
		background: linear-gradient(to right, 
			rgba(168, 117, 255, 0) 0%, 
			rgba(168, 117, 255, 0.8) 50%, 
			rgba(168, 117, 255, 0) 100%);
		animation: scanLine 2s infinite linear;
		box-shadow: 0 0 15rpx rgba(168, 117, 255, 0.8);
	}
	
	@keyframes scanLine {
		0% {
			top: 0;
		}
		100% {
			top: 100%;
		}
	}
	
	/* 扫描框角 */
	.scan-corner {
		position: absolute;
		width: 70rpx;
		height: 70rpx;
		border-color: var(--primary-light, #A875FF);
		opacity: 0.9;
	}
	
	.scan-corner-top-left {
		top: 0;
		left: 0;
		border-top: 8rpx solid;
		border-left: 8rpx solid;
		border-top-left-radius: 16rpx;
	}
	
	.scan-corner-top-right {
		top: 0;
		right: 0;
		border-top: 8rpx solid;
		border-right: 8rpx solid;
		border-top-right-radius: 16rpx;
	}
	
	.scan-corner-bottom-left {
		bottom: 0;
		left: 0;
		border-bottom: 8rpx solid;
		border-left: 8rpx solid;
		border-bottom-left-radius: 16rpx;
	}
	
	.scan-corner-bottom-right {
		bottom: 0;
		right: 0;
		border-bottom: 8rpx solid;
		border-right: 8rpx solid;
		border-bottom-right-radius: 16rpx;
	}
	
	/* 扫码提示文字 */
	.scan-tips {
		margin-top: 40rpx;
		text-align: center;
		color: rgba(255, 255, 255, 0.85);
		font-size: 30rpx;
		background-color: rgba(168, 117, 255, 0.15);
		padding: 16rpx 30rpx;
		border-radius: 30rpx;
		border: 1rpx solid rgba(168, 117, 255, 0.2);
	}
	
	/* Material Icons 字体 */
	@font-face {
		font-family: 'Material Icons';
		font-style: normal;
		font-weight: 400;
		src: url(https://fonts.gstatic.com/s/materialicons/v139/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format('woff2');
	}

	.material-icons {
		font-family: 'Material Icons';
		font-weight: normal;
		font-style: normal;
		font-size: 48rpx;
		line-height: 1;
		letter-spacing: normal;
		text-transform: none;
		display: inline-block;
		white-space: nowrap;
		word-wrap: normal;
		direction: ltr;
		-webkit-font-smoothing: antialiased;
		-moz-osx-font-smoothing: grayscale;
	}
</style> 