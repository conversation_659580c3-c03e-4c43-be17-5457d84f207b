<view class="container page-report"><view class="page-background"><image class="background-image" src="https://wuji1112-1312674791.cos.ap-guangzhou.myqcloud.com/static/shouye3.png" mode="aspectFill"></image><view class="frosted-overlay"></view></view><view class="navbar" style="{{(navbarStyle)}}"><view data-event-opts="{{[['tap',[['goBack',['$event']]]]]}}" class="navbar-left" bindtap="__e"><text class="material-icons md-24 text-primary">arrow_back</text></view><view class="navbar-title">设备异常反馈</view><view class="navbar-right"></view></view><view class="content"><view class="form-body"><view class="form-group" style="margin-top:-20rpx;"><view class="form-label">选择订单</view><picker value="{{orderIndex}}" range="{{orderDisplays}}" data-event-opts="{{[['change',[['onOrderChange',['$event']]]]]}}" bindchange="__e"><view class="form-picker"><text>{{orderDisplays[orderIndex]||'请选择订单'}}</text><text class="material-icons md-24 text-tertiary">arrow_drop_down</text></view></picker></view><view class="form-group mt-lg"><view class="form-label">异常类型<text class="text-tertiary type-hint">(可多选)</text></view><view class="issue-types"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['toggleIssueType',['$0'],[[['issueTypes','',index,'value']]]]]]]}}" class="{{['issue-type-item',(item.g0)?'active':'']}}" bindtap="__e"><text class="material-icons issue-icon">{{item.$orig.icon}}</text><text>{{item.$orig.label}}</text></view></block></view></view><view class="form-group mt-md"><view class="form-label">上传图片<text class="text-tertiary">(最多3张)</text></view><view class="upload-container"><view class="upload-items"><block wx:for="{{uploadedImages}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="upload-item"><image src="{{item}}" mode="aspectFill"></image><view data-event-opts="{{[['tap',[['removeImage',[index]]]]]}}" class="delete-btn" bindtap="__e"><text class="material-icons md-18">close</text></view></view></block><block wx:if="{{$root.g1<3}}"><view data-event-opts="{{[['tap',[['chooseImage',['$event']]]]]}}" class="upload-btn" bindtap="__e"><text class="material-icons md-36 text-tertiary">add_photo_alternate</text></view></block></view></view></view><view class="form-group mt-md"><view class="form-label">问题描述<text class="text-tertiary">(最少10个字符)</text></view><textarea class="form-textarea" placeholder="请详细描述您遇到的问题，以便我们更好地解决..." maxlength="200" data-event-opts="{{[['input',[['__set_model',['','description','$event',[]]]]]]}}" value="{{description}}" bindinput="__e"></textarea><view class="text-count text-tertiary">{{$root.g2+"/200"}}</view></view><view class="form-group" style="margin-top:10rpx;"><view data-event-opts="{{[['tap',[['handleSubmit',['$event']]]]]}}" class="btn-wrapper" catchtap="__e"><button class="{{['btn','btn-submit',(isFormValid&&!submitting)?'btn-active':'',(!isFormValid||submitting)?'btn-disabled':'']}}" style="{{'opacity:'+(isFormValid?1:0.5)+';'}}" disabled="{{!isFormValid||submitting}}"><text class="btn-text">{{submitting?'提交中...':'提交'}}</text></button></view></view><view class="privacy-tip text-tertiary mt-md"><text>提交即表示您同意我们收集此信息以解决您的问题。</text></view></view><view class="bottom-space"></view></view><block wx:if="{{loading}}"><view class="loading-container"><view class="loading-spinner"></view></view></block></view>