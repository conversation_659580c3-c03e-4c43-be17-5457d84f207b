{"version": 3, "sources": ["webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/components/global-login/index.vue?05ef", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/components/global-login/index.vue?6ae8", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/components/global-login/index.vue?64cf", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/components/global-login/index.vue?bc5f", "uni-app:///components/global-login/index.vue"], "names": ["name", "components", "LoginModal", "data", "isLoginModalVisible", "computed", "isLoggedIn", "watch", "immediate", "handler", "methods", "showLogin", "onLoginSuccess", "onLoginFail", "onModalClose", "checkNeedLogin", "<PERSON><PERSON>ogin"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;;;AAGpD;AACgM;AAChM,gBAAgB,uMAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACtBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAmvB,CAAgB,ivBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;ACavwB;AAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAGA;EACAA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;IACA;EACA;EACAC,4BACA;IACAC;MAAA;IAAA;EACA,GACA;EACAC;IACA;IACA;MACAC;MACAC;QACA;MACA;IACA;EACA;EACAC,yCACA,uBACA,oBACA,kBACA,kBACA,eACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;MACA;MACA;IACA;EAAA;AAEA;AAAA,2B", "file": "components/global-login/index.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=ca60d206&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/global-login/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=ca60d206&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<!-- 登录弹窗组件 -->\r\n\t\t<login-modal \r\n\t\t\t:visible=\"isLoginModalVisible\" \r\n\t\t\t@login-success=\"onLoginSuccess\" \r\n\t\t\t@login-fail=\"onLoginFail\"\r\n\t\t\t@close=\"onModalClose\"\r\n\t\t></login-modal>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport { mapState, mapActions } from 'vuex';\r\n\timport LoginModal from '@/components/login-modal/index.vue';\r\n\t\r\n\texport default {\r\n\t\tname: 'global-login',\r\n\t\tcomponents: {\r\n\t\t\tLoginModal\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tisLoginModalVisible: false\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\t...mapState({\r\n\t\t\t\tisLoggedIn: state => state.isLoggedIn\r\n\t\t\t})\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\t// 监听store中的showLoginModal状态变化\r\n\t\t\t'$store.state.showLoginModal': {\r\n\t\t\t\timmediate: true,\r\n\t\t\t\thandler(newVal) {\r\n\t\t\t\t\tthis.isLoginModalVisible = newVal;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t...mapActions([\r\n\t\t\t\t'checkLoginStatus',\r\n\t\t\t\t'showLoginModal',\r\n\t\t\t\t'hideLoginModal',\r\n\t\t\t\t'loginSuccess'\r\n\t\t\t]),\r\n\t\t\t\r\n\t\t\t// 显示登录弹窗\r\n\t\t\tshowLogin() {\r\n\t\t\t\tthis.showLoginModal();\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 登录成功回调\r\n\t\t\tonLoginSuccess(userData) {\r\n\t\t\t\tthis.loginSuccess(userData);\r\n\t\t\t\tthis.$emit('login-success', userData);\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 登录失败回调\r\n\t\t\tonLoginFail(error) {\r\n\t\t\t\tthis.$emit('login-fail', error);\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 弹窗关闭回调\r\n\t\t\tonModalClose() {\r\n\t\t\t\tthis.hideLoginModal();\r\n\t\t\t\tthis.$emit('modal-close');\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 检查是否需要登录\r\n\t\t\tcheckNeedLogin() {\r\n\t\t\t\treturn !this.checkLoginStatus();\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 确保已登录，如果未登录则显示登录弹窗\r\n\t\t\tensureLogin() {\r\n\t\t\t\tif (!this.checkLoginStatus()) {\r\n\t\t\t\t\tthis.showLoginModal();\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\treturn true;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script> "], "sourceRoot": ""}