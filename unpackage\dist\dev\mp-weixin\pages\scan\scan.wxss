













































































































































































































































































































































































































































































































































































































































/* CSS变量定义 */
page {
	--primary-light: #A875FF;
	--neon-pink: #ff36f9;
}
/* 扫码加载遮罩层 */
.scan-loading-mask {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.7);
	z-index: 9999;
	display: flex;
	align-items: center;
	justify-content: center;
}
.scan-loading {
	background-color: rgba(30, 30, 30, 0.9);
	border-radius: 20rpx;
	padding: 40rpx 60rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	border: 2rpx solid rgba(168, 117, 255, 0.3);
	box-shadow: 0 0 20rpx rgba(168, 117, 255, 0.3);
}
.loading-spinner {
	width: 60rpx;
	height: 60rpx;
	border: 4rpx solid rgba(168, 117, 255, 0.3);
	border-top: 4rpx solid var(--primary-light);
	border-radius: 50%;
	-webkit-animation: spin 1s linear infinite;
	        animation: spin 1s linear infinite;
	margin-bottom: 20rpx;
}
.loading-text {
	color: #ffffff;
	font-size: 30rpx;
}
@-webkit-keyframes spin {
0% { -webkit-transform: rotate(0deg); transform: rotate(0deg);
}
100% { -webkit-transform: rotate(360deg); transform: rotate(360deg);
}
}
@keyframes spin {
0% { -webkit-transform: rotate(0deg); transform: rotate(0deg);
}
100% { -webkit-transform: rotate(360deg); transform: rotate(360deg);
}
}
/* 页面基础样式 */
.page-scan {
	padding-top: 250rpx;
	padding-bottom: calc(100rpx + env(safe-area-inset-bottom));
	color: #ffffff;
	height: 100vh;
	min-height: 100vh;
	box-sizing: border-box;
	position: relative;
	overflow: hidden;
	display: flex;
	flex-direction: column;
}
/* 页面背景样式 */
.page-background {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 0;
}
/* 背景图片样式 */
.background-image {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 0;
	object-fit: cover;
}
.gradient-overlay {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: linear-gradient(to bottom, 
		rgba(18, 18, 18, 0.3) 0%, 
		rgba(18, 18, 18, 0.5) 50%,
		rgba(18, 18, 18, 0.7) 100%);
	backdrop-filter: blur(5px);
	-webkit-backdrop-filter: blur(5px);
	z-index: 1;
	pointer-events: none;
}
.status-bar {
	width: 100%;
	background: transparent;
	backdrop-filter: none;
	-webkit-backdrop-filter: none;
	position: fixed;
	top: 0;
	left: 0;
	z-index: 100;
}
/* 导航栏 */
.nav-bar {
	position: fixed;
	top: calc(env(safe-area-inset-top) + 60rpx);
	left: 15rpx;
	right: 15rpx;
	height: 110rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background: transparent;
	backdrop-filter: none;
	-webkit-backdrop-filter: none;
	z-index: 100;
	padding: 0 30rpx;
	border-bottom: none;
	box-shadow: none;
	border-radius: 0 0 30rpx 30rpx;
}
.nav-back {
	position: absolute;
	left: 30rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	width: 70rpx;
	height: 70rpx;
	border-radius: 50%;
	background-color: rgba(168, 117, 255, 0.15);
	border: 1px solid rgba(168, 117, 255, 0.3);
}
.nav-back .material-icons {
	font-size: 44rpx;
	color: rgba(255, 255, 255, 0.9);
	text-shadow: 0 0 8rpx rgba(168, 117, 255, 0.5);
}
.nav-title {
	font-size: 38rpx;
	font-weight: 600;
	color: #ffffff;
	text-shadow: 0 0 10rpx rgba(168, 117, 255, 0.5);
	letter-spacing: 2rpx;
}
/* 扫码内容区域 */
.scan-content {
	position: relative;
	z-index: 2;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	flex: 1;
	padding: 0 30rpx;
	max-height: 55vh;
}
/* 扫码框 */
.scan-area {
	position: relative;
	display: flex;
	flex-direction: column;
	align-items: center;
}
.scan-frame {
	position: relative;
	width: 500rpx;
	height: 500rpx;
	border-radius: 20rpx;
	overflow: hidden;
	background-color: rgba(0, 0, 0, 0.15);
	border: 1rpx solid rgba(255, 255, 255, 0.1);
	box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.3);
}
/* 扫描线动画 */
.scan-line {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 10rpx;
	background: linear-gradient(to right, 
		rgba(168, 117, 255, 0) 0%, 
		rgba(168, 117, 255, 0.8) 50%, 
		rgba(168, 117, 255, 0) 100%);
	-webkit-animation: scanLine 2s infinite linear;
	        animation: scanLine 2s infinite linear;
	box-shadow: 0 0 15rpx rgba(168, 117, 255, 0.8);
}
@-webkit-keyframes scanLine {
0% {
		top: 0;
}
100% {
		top: 100%;
}
}
@keyframes scanLine {
0% {
		top: 0;
}
100% {
		top: 100%;
}
}
/* 扫描框角 */
.scan-corner {
	position: absolute;
	width: 70rpx;
	height: 70rpx;
	border-color: var(--primary-light, #A875FF);
	opacity: 0.9;
}
.scan-corner-top-left {
	top: 0;
	left: 0;
	border-top: 8rpx solid;
	border-left: 8rpx solid;
	border-top-left-radius: 16rpx;
}
.scan-corner-top-right {
	top: 0;
	right: 0;
	border-top: 8rpx solid;
	border-right: 8rpx solid;
	border-top-right-radius: 16rpx;
}
.scan-corner-bottom-left {
	bottom: 0;
	left: 0;
	border-bottom: 8rpx solid;
	border-left: 8rpx solid;
	border-bottom-left-radius: 16rpx;
}
.scan-corner-bottom-right {
	bottom: 0;
	right: 0;
	border-bottom: 8rpx solid;
	border-right: 8rpx solid;
	border-bottom-right-radius: 16rpx;
}
/* 扫码提示文字 */
.scan-tips {
	margin-top: 40rpx;
	text-align: center;
	color: rgba(255, 255, 255, 0.85);
	font-size: 30rpx;
	background-color: rgba(168, 117, 255, 0.15);
	padding: 16rpx 30rpx;
	border-radius: 30rpx;
	border: 1rpx solid rgba(168, 117, 255, 0.2);
}
/* Material Icons 字体 */
@font-face {
	font-family: 'Material Icons';
	font-style: normal;
	font-weight: 400;
	src: url(https://fonts.gstatic.com/s/materialicons/v139/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format('woff2');
}
.material-icons {
	font-family: 'Material Icons';
	font-weight: normal;
	font-style: normal;
	font-size: 48rpx;
	line-height: 1;
	letter-spacing: normal;
	text-transform: none;
	display: inline-block;
	white-space: nowrap;
	word-wrap: normal;
	direction: ltr;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

