


















































































































































































































































































/* 页面基础样式 */
.page-contact {
	color: #ffffff;
	height: 100vh;
	min-height: 100vh;
	box-sizing: border-box;
	position: relative;
	overflow: hidden;
	display: flex;
	flex-direction: column;
}
/* 页面背景样式 */
.page-background {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 0;
	background-color: #121212;
}
.gradient-overlay {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: linear-gradient(to bottom, 
		rgba(18, 18, 18, 0) 0%, 
		rgba(18, 18, 18, 0.3) 20%, 
		rgba(18, 18, 18, 0.7) 50%,
		rgba(18, 18, 18, 0.9) 80%,
		#121212 100%);
	z-index: 1;
	pointer-events: none;
}
/* 波浪装饰 */
.wave-decoration {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	overflow: hidden;
	z-index: 0;
}
.wave {
	position: absolute;
	width: 250%;
	height: 120%;
	top: -80%;
	left: -75%;
	border-radius: 43%;
	background: transparent;
}
.wave1 {
	background: linear-gradient(135deg, rgba(168, 117, 255, 0.3) 0%, rgba(139, 92, 246, 0.25) 100%);
	-webkit-animation: wave 25s linear infinite;
	        animation: wave 25s linear infinite;
	z-index: 3;
}
.wave2 {
	background: linear-gradient(135deg, rgba(105, 30, 255, 0.25) 0%, rgba(96, 56, 176, 0.2) 100%);
	-webkit-animation: wave 22s linear infinite;
	        animation: wave 22s linear infinite;
	-webkit-animation-delay: -5s;
	        animation-delay: -5s;
	z-index: 2;
}
.wave3 {
	background: linear-gradient(135deg, rgba(147, 51, 234, 0.25) 0%, rgba(79, 70, 229, 0.15) 100%);
	-webkit-animation: wave 18s linear infinite;
	        animation: wave 18s linear infinite;
	-webkit-animation-delay: -8s;
	        animation-delay: -8s;
	z-index: 1;
}
@-webkit-keyframes wave {
0% {
		-webkit-transform: rotate(0deg);
		        transform: rotate(0deg);
}
100% {
		-webkit-transform: rotate(360deg);
		        transform: rotate(360deg);
}
}
@keyframes wave {
0% {
		-webkit-transform: rotate(0deg);
		        transform: rotate(0deg);
}
100% {
		-webkit-transform: rotate(360deg);
		        transform: rotate(360deg);
}
}
/* 顶部导航样式 */
.navbar {
	height: 90rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 30rpx;
	margin-top: 25px;
	position: relative;
	z-index: 100;
}
.navbar-title {
	font-size: 34rpx;
	font-weight: 600;
}
.navbar-left, .navbar-right {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}
/* 内容区域 */
.content {
	flex: 1;
	padding: 30rpx 30rpx;
	position: relative;
	z-index: 5;
	width: 100%;
	box-sizing: border-box;
	overflow: auto;
}
/* 卡片容器 */
.card-container {
	margin-bottom: 40rpx;
	width: 100%;
}
.section-title {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
}
.icon-wrapper {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	display: flex;
	justify-content: center;
	align-items: center;
	margin-right: 16rpx;
	transition: -webkit-transform 0.3s ease;
	transition: transform 0.3s ease;
	transition: transform 0.3s ease, -webkit-transform 0.3s ease;
}
.section-title:active .icon-wrapper {
	-webkit-transform: rotate(15deg);
	        transform: rotate(15deg);
}
.neon-blue-bg {
	background-color: rgba(0, 233, 255, 0.15);
	box-shadow: 0 0 15rpx rgba(0, 233, 255, 0.3);
}
.neon-green-bg {
	background-color: rgba(0, 255, 133, 0.15);
	box-shadow: 0 0 15rpx rgba(0, 255, 133, 0.3);
}
.neon-yellow-bg {
	background-color: rgba(255, 222, 89, 0.15);
	box-shadow: 0 0 15rpx rgba(255, 222, 89, 0.3);
}
/* 卡片样式 */
.card {
	border-radius: 20rpx;
	background-color: rgba(255, 255, 255, 0.08);
	backdrop-filter: blur(5px);
	-webkit-backdrop-filter: blur(5px);
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);
	border: 1px solid rgba(255, 255, 255, 0.05);
	transition: box-shadow 0.3s ease, -webkit-transform 0.3s ease;
	transition: transform 0.3s ease, box-shadow 0.3s ease;
	transition: transform 0.3s ease, box-shadow 0.3s ease, -webkit-transform 0.3s ease;
	width: 100%;
	box-sizing: border-box;
	margin-left: auto;
	margin-right: auto;
}
.contact-card {
	padding: 30rpx;
	background: linear-gradient(to bottom, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
}
.contact-item {
	display: flex;
	flex-direction: column;
}
.contact-item .text-secondary {
	margin-bottom: 8rpx;
}
.contact-item .text-primary {
	font-size: 32rpx;
	font-weight: 500;
}
/* FAQ列表 */
.faq-list {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
	width: 100%;
}
.faq-item {
	padding: 24rpx;
	width: 100%;
	position: relative;
	overflow: hidden;
}
.faq-item:after {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: linear-gradient(to right, rgba(168, 117, 255, 0.05), transparent);
	opacity: 0;
	transition: opacity 0.3s ease;
	pointer-events: none;
}
.faq-item:active {
	-webkit-transform: scale(0.99);
	        transform: scale(0.99);
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.2);
}
.faq-item:active:after {
	opacity: 1;
}
.faq-header {
	width: 100%;
}
.faq-title {
	font-size: 30rpx;
	font-weight: 500;
	color: #FFFFFF;
}
.faq-content {
	margin-top: 10rpx;
	-webkit-animation: fadeIn 0.3s ease;
	        animation: fadeIn 0.3s ease;
}
@-webkit-keyframes fadeIn {
from { opacity: 0; -webkit-transform: translateY(-10rpx); transform: translateY(-10rpx);
}
to { opacity: 1; -webkit-transform: translateY(0); transform: translateY(0);
}
}
@keyframes fadeIn {
from { opacity: 0; -webkit-transform: translateY(-10rpx); transform: translateY(-10rpx);
}
to { opacity: 1; -webkit-transform: translateY(0); transform: translateY(0);
}
}
.divider {
	height: 1px;
	background-color: rgba(255, 255, 255, 0.1);
}
/* 按钮样式 */
.btn {
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 15rpx;
	padding: 25rpx;
	font-size: 30rpx;
	font-weight: 600;
	transition: all 0.3s ease;
}
.btn-primary {
	background: linear-gradient(135deg, rgba(168, 117, 255, 0.8), rgba(139, 92, 246, 0.9));
	color: #FFFFFF;
	box-shadow: 0 4rpx 20rpx rgba(168, 117, 255, 0.4);
	border: none;
}
.btn-primary:active {
	-webkit-transform: scale(0.98);
	        transform: scale(0.98);
	box-shadow: 0 2rpx 10rpx rgba(168, 117, 255, 0.3);
}
.btn-outline {
	background-color: transparent;
	border: 1px solid rgba(168, 117, 255, 0.4);
	color: var(--primary-light, #A875FF);
}
.btn-outline:active {
	background-color: rgba(168, 117, 255, 0.1);
	-webkit-transform: scale(0.98);
	        transform: scale(0.98);
}
/* 底部空间 */
.bottom-space {
	height: 60rpx;
}
/* Material Icons 字体 */
@font-face {
	font-family: 'Material Icons';
	font-style: normal;
	font-weight: 400;
	src: url(https://fonts.gstatic.com/s/materialicons/v139/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format('woff2');
}
.material-icons {
	font-family: 'Material Icons';
	font-weight: normal;
	font-style: normal;
	font-size: 24rpx;
	line-height: 1;
	letter-spacing: normal;
	text-transform: none;
	display: inline-block;
	white-space: nowrap;
	word-wrap: normal;
	direction: ltr;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}
.material-icons.md-24 {
	font-size: 48rpx;
}
.material-icons.md-48 {
	font-size: 96rpx;
}
.material-icons.text-primary {
	color: var(--text-primary, #FFFFFF);
}
.material-icons.text-tertiary {
	color: var(--text-tertiary, rgba(255, 255, 255, 0.5));
}
/* 彩色图标 */
.material-icons.neon-blue {
	color: var(--neon-blue, #00E9FF);
	text-shadow: 0 0 10rpx rgba(0, 233, 255, 0.5);
	-webkit-animation: glow-blue 3s infinite alternate;
	        animation: glow-blue 3s infinite alternate;
}
.material-icons.neon-green {
	color: var(--neon-green, #00FF85);
	text-shadow: 0 0 10rpx rgba(0, 255, 133, 0.5);
	-webkit-animation: glow-green 3s infinite alternate;
	        animation: glow-green 3s infinite alternate;
}
.material-icons.neon-yellow {
	color: var(--neon-yellow, #FFDE59);
	text-shadow: 0 0 10rpx rgba(255, 222, 89, 0.5);
	-webkit-animation: glow-yellow 3s infinite alternate;
	        animation: glow-yellow 3s infinite alternate;
}
@-webkit-keyframes glow-blue {
from { text-shadow: 0 0 5rpx rgba(0, 233, 255, 0.3);
}
to { text-shadow: 0 0 15rpx rgba(0, 233, 255, 0.6);
}
}
@keyframes glow-blue {
from { text-shadow: 0 0 5rpx rgba(0, 233, 255, 0.3);
}
to { text-shadow: 0 0 15rpx rgba(0, 233, 255, 0.6);
}
}
@-webkit-keyframes glow-green {
from { text-shadow: 0 0 5rpx rgba(0, 255, 133, 0.3);
}
to { text-shadow: 0 0 15rpx rgba(0, 255, 133, 0.6);
}
}
@keyframes glow-green {
from { text-shadow: 0 0 5rpx rgba(0, 255, 133, 0.3);
}
to { text-shadow: 0 0 15rpx rgba(0, 255, 133, 0.6);
}
}
@-webkit-keyframes glow-yellow {
from { text-shadow: 0 0 5rpx rgba(255, 222, 89, 0.3);
}
to { text-shadow: 0 0 15rpx rgba(255, 222, 89, 0.6);
}
}
@keyframes glow-yellow {
from { text-shadow: 0 0 5rpx rgba(255, 222, 89, 0.3);
}
to { text-shadow: 0 0 15rpx rgba(255, 222, 89, 0.6);
}
}
/* 辅助类 */
.flex {
	display: flex;
}
.justify-between {
	justify-content: space-between;
}
.items-center {
	align-items: center;
}
.text-center {
	text-align: center;
}
.mt-sm {
	margin-top: 16rpx;
}
.mb-sm {
	margin-bottom: 16rpx;
}
.mt-md {
	margin-top: 30rpx;
}
.mt-lg {
	margin-top: 60rpx;
}
/* 文本样式 */
.title-md {
	font-size: 36rpx;
	font-weight: 600;
	color: #ffffff;
	text-shadow: 0 0 10rpx rgba(255, 255, 255, 0.2);
}
.text-secondary {
	color: rgba(255, 255, 255, 0.7);
	font-size: 28rpx;
}
.text-tertiary {
	color: rgba(255, 255, 255, 0.5);
	font-size: 26rpx;
}
.text-primary {
	color: #FFFFFF;
	font-size: 28rpx;
}

