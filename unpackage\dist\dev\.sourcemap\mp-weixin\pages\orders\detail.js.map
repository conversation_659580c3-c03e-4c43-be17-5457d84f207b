{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/orders/detail.vue?0f78", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/orders/detail.vue?b84f", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/orders/detail.vue?ba6b", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/orders/detail.vue?e740", "uni-app:///pages/orders/detail.vue", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/orders/detail.vue?f8aa", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/orders/detail.vue?ed6a"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "orderId", "systemInfo", "navbarStyle", "isIOS", "orderDetail", "id", "orderNo", "storeName", "startTime", "endTime", "duration", "amount", "status", "rawStatus", "deviceName", "deviceNo", "deviceId", "payTime", "payType", "onLoad", "uni", "title", "icon", "setTimeout", "onReady", "methods", "calculateIOSAdaptation", "console", "finalTopPosition", "model", "marginTop", "position", "top", "goBack", "loadOrderDetail", "API", "then", "orderStatus", "catch", "getStatusText", "payOrder", "timeStamp", "nonceStr", "package", "signType", "paySign", "success", "fail", "content", "showCancel", "reportProblem", "url", "contactService"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACa;;;AAGlE;AACgM;AAChM,gBAAgB,uMAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAovB,CAAgB,kvBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC8IxwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACAC;MACA;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;MACAC;QACAC;QACAC;MACA;MACAC;QACAH;MACA;IACA;EACA;EACAI;IACA;IACA;EACA;EACAC;IACA;IACAC;MACA;QACA;QACA;QAEAC;QACAA;QAEA;UACA;UACA;UACA;UAEAA;UACAA;UACAA;UAEA;;UAEA;UACA;YACA;YACAC;YACAD;;YAEA;YACA;cACAC;cACAD;YACA;;YAEA;YACA;cACAC;cACAD;YACA;UACA;YACAC;UACA,wEACAC;YACAD;UACA;YACAA;UACA;UAEA;YACAE;YACAC;YACAC;UACA;UAEAL;UACAA;QACA;UACA;QACA;MACA;QACAA;QACA;MACA;IACA;IACAM;MACAb;IACA;IAEA;IACAc;MAAA;MACAd;QACAC;MACA;;MAEA;MACAc,2CACAC;QACAhB;;QAEA;QACA;QACA;UACAO;;UAEA;UACA;UACA;;UAEA;UACA;YACA;YACA;cACA;cACAU;cACAxB;YACA;cACA;cACAwB;cACAxB;YACA;UACA;YACA;YACA;cACAwB;cACAxB;YACA;cACAwB;cACAxB;YACA;UACA;UAEA;YACAR;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;UACA;UAEAS;QACA;MACA,GACAW;QACAlB;;QAEA;QACAA;UACAC;UACAC;QACA;;QAEA;QACA;UACAjB;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;QACA;MACA;IACA;IAEA;IACAqB;MACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACApB;QACAC;MACA;;MAEA;MACAc,yCACAC;QACAhB;QAEA;;QAEA;QACAA;UACAqB;UACAC;UACAC;UACAC;UACAC;UACAC;YACA;YACA1B;cACAC;cACAC;YACA;;YAEA;YACA;UACA;UACAyB;YACApB;;YAEA;YACAP;cACAC;cACA2B;cACAC;YACA;UACA;QACA;MACA,GACAX;QACAlB;;QAEA;QACAA;UACAC;UACA2B;UACAC;QACA;MACA;IACA;IAEA;IACAC;MACAvB;MACAP;QACA+B;MACA;IACA;IAEA;IACAC;MACAhC;QACA+B;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC3aA;AAAA;AAAA;AAAA;AAAikC,CAAgB,2hCAAG,EAAC,C;;;;;;;;;;;ACArlC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/orders/detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/orders/detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./detail.vue?vue&type=template&id=4ee14192&\"\nvar renderjs\nimport script from \"./detail.vue?vue&type=script&lang=js&\"\nexport * from \"./detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./detail.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/orders/detail.vue\"\nexport default component.exports", "export * from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=template&id=4ee14192&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"container page-order-detail\">\n\t\t<!-- 页面背景 -->\n\t\t<view class=\"page-background\">\n\t\t\t<!-- 背景图片 -->\n\t\t\t<image class=\"background-image\" src=\"https://wuji1112-1312674791.cos.ap-guangzhou.myqcloud.com/static/shouye3.png\" mode=\"aspectFill\"></image>\n\t\t\t\n\t\t\t<!-- 深磨砂效果叠加层 -->\n\t\t\t<view class=\"frosted-overlay\"></view>\n\t\t</view>\n\t\t\n\t\t<!-- 导航栏 -->\n\t\t<view class=\"navbar\" :style=\"navbarStyle\">\n\t\t\t<view class=\"navbar-left\" @click=\"goBack\">\n\t\t\t\t<text class=\"material-icons md-24 text-primary\">arrow_back</text>\n\t\t\t</view>\n\t\t\t<view class=\"navbar-title\">订单详情</view>\n\t\t\t<view class=\"navbar-right\"></view>\n\t\t</view>\n\t\t\n\t\t<!-- 页面内容区域 -->\n\t\t<view class=\"page-content\">\n\t\t\t<view class=\"content\">\n\t\t\t\t<!-- 订单状态卡片 -->\n\t\t\t\t<view class=\"order-status-card\" :class=\"{\n\t\t\t\t\t'active': orderDetail.rawStatus === 1,\n\t\t\t\t\t'unpaid': orderDetail.rawStatus === 0,\n\t\t\t\t\t'completed': orderDetail.rawStatus === 2,\n\t\t\t\t\t'cancelled': orderDetail.rawStatus === 3,\n\t\t\t\t\t'inactive': orderDetail.rawStatus !== 0 && orderDetail.rawStatus !== 1 && orderDetail.rawStatus !== 2 && orderDetail.rawStatus !== 3\n\t\t\t\t}\">\n\t\t\t\t\t<view class=\"status-icon\">\n\t\t\t\t\t\t<text v-if=\"orderDetail.rawStatus === 0\" class=\"material-icons md-24\">schedule</text>\n\t\t\t\t\t\t<text v-else-if=\"orderDetail.rawStatus === 1\" class=\"material-icons md-24\">check_circle</text>\n\t\t\t\t\t\t<text v-else-if=\"orderDetail.rawStatus === 2\" class=\"material-icons md-24\">task_alt</text>\n\t\t\t\t\t\t<text v-else-if=\"orderDetail.rawStatus === 3\" class=\"material-icons md-24\">cancel</text>\n\t\t\t\t\t\t<text v-else class=\"material-icons md-24\">help_outline</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"status-text\">\n\t\t\t\t\t\t<text class=\"status-title\">{{orderDetail.status}}</text>\n\t\t\t\t\t\t<text class=\"status-desc\" v-if=\"orderDetail.rawStatus === 0\">请尽快完成支付</text>\n\t\t\t\t\t\t<text class=\"status-desc\" v-else-if=\"orderDetail.rawStatus === 1\">订单使用中</text>\n\t\t\t\t\t\t<text class=\"status-desc\" v-else-if=\"orderDetail.rawStatus === 2\">感谢您的使用</text>\n\t\t\t\t\t\t<text class=\"status-desc\" v-else-if=\"orderDetail.rawStatus === 3\">订单已取消</text>\n\t\t\t\t\t\t<text class=\"status-desc\" v-else>状态未知，请联系客服</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 订单信息卡片 -->\n\t\t\t\t<view class=\"card mt-lg\">\n\t\t\t\t\t<view class=\"card-header\">\n\t\t\t\t\t\t<text class=\"card-icon\">📃</text>\n\t\t\t\t\t\t<text>订单信息</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"card-content\">\n\t\t\t\t\t\t<view class=\"detail-item flex justify-between\">\n\t\t\t\t\t\t\t<text class=\"detail-label\">订单编号</text>\n\t\t\t\t\t\t\t<text class=\"detail-value\">{{orderDetail.orderNo}}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"detail-item flex justify-between mt-sm\">\n\t\t\t\t\t\t\t<text class=\"detail-label\">下单时间</text>\n\t\t\t\t\t\t\t<text class=\"detail-value\">{{orderDetail.startTime}}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"detail-item flex justify-between mt-sm\" v-if=\"orderDetail.endTime\">\n\t\t\t\t\t\t\t<text class=\"detail-label\">结束时间</text>\n\t\t\t\t\t\t\t<text class=\"detail-value\">{{orderDetail.endTime}}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"detail-item flex justify-between mt-sm\" v-if=\"orderDetail.duration\">\n\t\t\t\t\t\t\t<text class=\"detail-label\">使用时长</text>\n\t\t\t\t\t\t\t<text class=\"detail-value\">{{orderDetail.duration}}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"detail-item flex justify-between mt-sm\">\n\t\t\t\t\t\t\t<text class=\"detail-label\">支付方式</text>\n\t\t\t\t\t\t\t<text class=\"detail-value\">{{orderDetail.payType || '微信支付'}}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 门店信息卡片 -->\n\t\t\t\t<view class=\"card mt-md\">\n\t\t\t\t\t<view class=\"card-header\">\n\t\t\t\t\t\t<text class=\"card-icon\">🏪</text>\n\t\t\t\t\t\t<text>门店信息</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"card-content\">\n\t\t\t\t\t\t<view class=\"detail-item flex justify-between\">\n\t\t\t\t\t\t\t<text class=\"detail-label\">门店名称</text>\n\t\t\t\t\t\t\t<text class=\"detail-value\">{{orderDetail.storeName}}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"detail-item flex justify-between mt-sm\" v-if=\"orderDetail.deviceName\">\n\t\t\t\t\t\t\t<text class=\"detail-label\">设备名称</text>\n\t\t\t\t\t\t\t<text class=\"detail-value\">{{orderDetail.deviceName}}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"detail-item flex justify-between mt-sm\" v-if=\"orderDetail.deviceNo\">\n\t\t\t\t\t\t\t<text class=\"detail-label\">设备编号</text>\n\t\t\t\t\t\t\t<text class=\"detail-value\">{{orderDetail.deviceNo}}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 支付信息卡片 -->\n\t\t\t\t<view class=\"card mt-md\">\n\t\t\t\t\t<view class=\"card-header\">\n\t\t\t\t\t\t<text class=\"card-icon\">💰</text>\n\t\t\t\t\t\t<text>支付信息</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"card-content\">\n\t\t\t\t\t\t<view class=\"detail-item flex justify-between\">\n\t\t\t\t\t\t\t<text class=\"detail-label\">消费金额</text>\n\t\t\t\t\t\t\t<text class=\"detail-value primary-light\">{{orderDetail.amount}}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"detail-item flex justify-between mt-sm\" v-if=\"orderDetail.rawStatus === 0\">\n\t\t\t\t\t\t\t<text class=\"detail-label\">支付状态</text>\n\t\t\t\t\t\t\t<text class=\"detail-value error-light\">未支付</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"detail-item flex justify-between mt-sm\" v-else-if=\"orderDetail.rawStatus === 1 || orderDetail.rawStatus === 2\">\n\t\t\t\t\t\t\t<text class=\"detail-label\">支付状态</text>\n\t\t\t\t\t\t\t<text class=\"detail-value success-light\">已支付</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"detail-item flex justify-between mt-sm\" v-if=\"orderDetail.payTime\">\n\t\t\t\t\t\t\t<text class=\"detail-label\">支付时间</text>\n\t\t\t\t\t\t\t<text class=\"detail-value\">{{orderDetail.payTime}}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 操作按钮 -->\n\t\t\t\t<view class=\"actions mt-lg\">\n\t\t\t\t\t<button v-if=\"orderDetail.rawStatus === 0\" class=\"btn btn-primary btn-block\" @click=\"payOrder\">\n\t\t\t\t\t\t<text class=\"btn-icon\">💳</text> 去支付\n\t\t\t\t\t</button>\n\t\t\t\t\n\t\t\t\t\t<button class=\"btn btn-outline btn-block mt-md\" @click=\"reportProblem\">\n\t\t\t\t\t\t<text class=\"btn-icon\">⚠️</text> 设备异常报告\n\t\t\t\t\t</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nimport API from '@/static/js/api.js';\n\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\torderId: null,\n\t\t\t// iOS适配相关\n\t\t\tsystemInfo: {},\n\t\t\tnavbarStyle: {},\n\t\t\tisIOS: false,\n\t\t\torderDetail: {\n\t\t\t\tid: '',\n\t\t\t\torderNo: '',\n\t\t\t\tstoreName: '',\n\t\t\t\tstartTime: '',\n\t\t\t\tendTime: '',\n\t\t\t\tduration: '',\n\t\t\t\tamount: '¥0.00',\n\t\t\t\tstatus: '未知状态',\n\t\t\t\trawStatus: 0,\n\t\t\t\tdeviceName: '',\n\t\t\t\tdeviceNo: '',\n\t\t\t\tdeviceId: '',\n\t\t\t\tpayTime: '',\n\t\t\t\tpayType: '微信支付'\n\t\t\t}\n\t\t}\n\t},\n\tonLoad(options) {\n\t\tif (options.id) {\n\t\t\tthis.orderId = options.id;\n\t\t\tthis.loadOrderDetail();\n\t\t} else {\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '订单ID不存在',\n\t\t\t\ticon: 'none'\n\t\t\t});\n\t\t\tsetTimeout(() => {\n\t\t\t\tuni.navigateBack();\n\t\t\t}, 1500);\n\t\t}\n\t},\n\tonReady() {\n\t\t// 获取系统信息并计算iOS适配参数\n\t\tthis.calculateIOSAdaptation();\n\t},\n\tmethods: {\n\t\t// 计算iOS适配参数\n\t\tcalculateIOSAdaptation() {\n\t\t\ttry {\n\t\t\t\tthis.systemInfo = uni.getSystemInfoSync();\n\t\t\t\tthis.isIOS = this.systemInfo.platform === 'ios';\n\n\t\t\t\tconsole.log('订单详情页 - 系统信息:', this.systemInfo);\n\t\t\t\tconsole.log('订单详情页 - 是否iOS:', this.isIOS);\n\n\t\t\t\tif (this.isIOS) {\n\t\t\t\t\tconst statusBarHeight = this.systemInfo.statusBarHeight || 44;\n\t\t\t\t\tconst model = this.systemInfo.model || '';\n\t\t\t\t\tconst safeAreaTop = this.systemInfo.safeArea ? this.systemInfo.safeArea.top : statusBarHeight;\n\n\t\t\t\t\tconsole.log('订单详情页 - 状态栏高度:', statusBarHeight);\n\t\t\t\t\tconsole.log('订单详情页 - 设备型号:', model);\n\t\t\t\t\tconsole.log('订单详情页 - 安全区域顶部:', safeAreaTop);\n\n\t\t\t\t\tlet finalTopPosition;\n\n\t\t\t\t\t// 使用与订单列表页面相同的超激进适配策略\n\t\t\t\t\tif (model.includes('iPhone 16 Pro')) {\n\t\t\t\t\t\t// 方案1：直接使用状态栏高度，无额外间距\n\t\t\t\t\t\tfinalTopPosition = statusBarHeight;\n\t\t\t\t\t\tconsole.log('订单详情页 - iPhone 16 Pro - 方案1（状态栏高度）:', finalTopPosition);\n\n\t\t\t\t\t\t// 方案2：如果还是太靠下，尝试更小的值\n\t\t\t\t\t\tif (finalTopPosition > 50) {\n\t\t\t\t\t\t\tfinalTopPosition = 44; // 使用标准状态栏高度\n\t\t\t\t\t\t\tconsole.log('订单详情页 - iPhone 16 Pro - 方案2（标准高度）:', finalTopPosition);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// 方案3：如果还是太靠下，尝试负值\n\t\t\t\t\t\tif (finalTopPosition > 45) {\n\t\t\t\t\t\t\tfinalTopPosition = statusBarHeight - 10; // 负偏移\n\t\t\t\t\t\t\tconsole.log('订单详情页 - iPhone 16 Pro - 方案3（负偏移）:', finalTopPosition);\n\t\t\t\t\t\t}\n\t\t\t\t\t} else if (model.includes('iPhone 15 Pro') || model.includes('iPhone 14 Pro')) {\n\t\t\t\t\t\tfinalTopPosition = statusBarHeight;\n\t\t\t\t\t} else if (model.includes('iPhone X') || model.includes('iPhone 11') ||\n\t\t\t\t\t\t\t  model.includes('iPhone 12') || model.includes('iPhone 13')) {\n\t\t\t\t\t\tfinalTopPosition = statusBarHeight;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tfinalTopPosition = statusBarHeight;\n\t\t\t\t\t}\n\n\t\t\t\t\tthis.navbarStyle = {\n\t\t\t\t\t\tmarginTop: finalTopPosition + 'px',\n\t\t\t\t\t\tposition: 'relative',\n\t\t\t\t\t\ttop: '0px'\n\t\t\t\t\t};\n\n\t\t\t\t\tconsole.log('订单详情页 - 超激进适配 - 最终顶部位置:', finalTopPosition + 'px');\n\t\t\t\t\tconsole.log('订单详情页 - 超激进适配 - 导航栏样式:', this.navbarStyle);\n\t\t\t\t} else {\n\t\t\t\t\tthis.navbarStyle = {};\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('订单详情页 - 计算iOS适配参数失败:', error);\n\t\t\t\tthis.navbarStyle = {};\n\t\t\t}\n\t\t},\n\t\tgoBack() {\n\t\t\tuni.navigateBack();\n\t\t},\n\t\t\n\t\t// 加载订单详情\n\t\tloadOrderDetail() {\n\t\t\tuni.showLoading({\n\t\t\t\ttitle: '加载中...'\n\t\t\t});\n\t\t\t\n\t\t\t// 调用API获取订单详情\n\t\t\tAPI.order.getDetail(this.orderId)\n\t\t\t\t.then(res => {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\n\t\t\t\t\t// 处理返回数据\n\t\t\t\t\tconst detail = res.data;\n\t\t\t\t\tif (detail) {\n\t\t\t\t\t\tconsole.log('订单详情数据:', detail);\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 确定订单状态\n\t\t\t\t\t\tlet orderStatus = '未知状态';\n\t\t\t\t\t\tlet rawStatus = 0;\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 根据payStatus和status确定状态\n\t\t\t\t\t\tif (detail.payStatus === 1) {\n\t\t\t\t\t\t\t// 已支付\n\t\t\t\t\t\t\tif (detail.endTime) {\n\t\t\t\t\t\t\t\t// 已结束\n\t\t\t\t\t\t\t\torderStatus = '已完成';\n\t\t\t\t\t\t\t\trawStatus = 2;\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t// 进行中\n\t\t\t\t\t\t\t\torderStatus = '进行中';\n\t\t\t\t\t\t\t\trawStatus = 1;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// 未支付或已取消\n\t\t\t\t\t\t\tif (detail.remark && detail.remark.includes('取消')) {\n\t\t\t\t\t\t\t\torderStatus = '已取消';\n\t\t\t\t\t\t\t\trawStatus = 3;\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\torderStatus = '未支付';\n\t\t\t\t\t\t\t\trawStatus = 0;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\tthis.orderDetail = {\n\t\t\t\t\t\t\tid: detail.id || detail.orderId,\n\t\t\t\t\t\t\torderNo: detail.orderNo || '',\n\t\t\t\t\t\t\tstoreName: detail.storeName || detail.shopName || '未知门店',\n\t\t\t\t\t\t\tstartTime: detail.startTime || detail.createTime || '',\n\t\t\t\t\t\t\tendTime: detail.endTime || '',\n\t\t\t\t\t\t\tduration: detail.duration ? `${detail.duration}分钟` : '',\n\t\t\t\t\t\t\tamount: detail.amount ? `¥${parseFloat(detail.amount).toFixed(2)}` : '¥0.00',\n\t\t\t\t\t\t\tstatus: orderStatus,\n\t\t\t\t\t\t\trawStatus: rawStatus,\n\t\t\t\t\t\t\tdeviceName: detail.deviceName || '',\n\t\t\t\t\t\t\tdeviceNo: detail.deviceNo || detail.deviceId || '',\n\t\t\t\t\t\t\tdeviceId: detail.deviceId || detail.deviceNo || '',\n\t\t\t\t\t\t\tpayTime: detail.payTime || '',\n\t\t\t\t\t\t\tpayType: '微信支付'\n\t\t\t\t\t\t};\n\t\t\t\t\t\t\n\t\t\t\t\t\tconsole.log('处理后的订单详情:', this.orderDetail);\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t\t.catch(err => {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\n\t\t\t\t\t// 显示错误提示\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: err.message || '获取订单详情失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\t// 加载失败时显示一些默认数据\n\t\t\t\t\tthis.orderDetail = {\n\t\t\t\t\t\tid: this.orderId,\n\t\t\t\t\t\torderNo: 'JYC202407100001',\n\t\t\t\t\t\tstoreName: '今夜城堡 - 西湖店',\n\t\t\t\t\t\tstartTime: '2024-07-10 14:00',\n\t\t\t\t\t\tendTime: '2024-07-10 15:00',\n\t\t\t\t\t\tduration: '60分钟',\n\t\t\t\t\t\tamount: '¥29.00',\n\t\t\t\t\t\tstatus: '已完成',\n\t\t\t\t\t\trawStatus: 2,\n\t\t\t\t\t\tdeviceName: '按摩椅A',\n\t\t\t\t\t\tdeviceNo: 'JY10010001',\n\t\t\t\t\t\tdeviceId: 'JY10010001',\n\t\t\t\t\t\tpayTime: '2024-07-10 14:01',\n\t\t\t\t\t\tpayType: '微信支付'\n\t\t\t\t\t};\n\t\t\t\t});\n\t\t},\n\t\t\n\t\t// 获取状态文本\n\t\tgetStatusText(status) {\n\t\t\tconst statusMap = {\n\t\t\t\t0: '未支付',\n\t\t\t\t1: '进行中',\n\t\t\t\t2: '已完成',\n\t\t\t\t3: '已取消'\n\t\t\t};\n\t\t\treturn statusMap[status] || '未知状态';\n\t\t},\n\t\t\n\t\t// 支付订单\n\t\tpayOrder() {\n\t\t\tuni.showLoading({\n\t\t\t\ttitle: '处理中...'\n\t\t\t});\n\t\t\t\n\t\t\t// 调用支付接口\n\t\t\tAPI.order.payment(this.orderId)\n\t\t\t\t.then(res => {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\n\t\t\t\t\tconst payInfo = res.data;\n\t\t\t\t\t\n\t\t\t\t\t// 调用微信支付\n\t\t\t\t\tuni.requestPayment({\n\t\t\t\t\t\ttimeStamp: payInfo.timeStamp,\n\t\t\t\t\t\tnonceStr: payInfo.nonceStr,\n\t\t\t\t\t\tpackage: payInfo.packageValue,\n\t\t\t\t\t\tsignType: payInfo.signType,\n\t\t\t\t\t\tpaySign: payInfo.paySign,\n\t\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\t\t// 支付成功\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '支付成功',\n\t\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 刷新订单详情\n\t\t\t\t\t\t\tthis.loadOrderDetail();\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\tconsole.error('支付失败:', err);\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 显示错误提示\n\t\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\t\ttitle: '支付失败',\n\t\t\t\t\t\t\t\tcontent: '支付未完成，请重试或选择其他支付方式',\n\t\t\t\t\t\t\t\tshowCancel: false\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t})\n\t\t\t\t.catch(err => {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\n\t\t\t\t\t// 显示错误提示\n\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\ttitle: '支付失败',\n\t\t\t\t\t\tcontent: err.message || '创建支付订单失败，请重试',\n\t\t\t\t\t\tshowCancel: false\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t},\n\t\t\n\t\t// 设备异常报告\n\t\treportProblem() {\n\t\t\tconsole.log('设备异常报告，订单ID:', this.orderId, '设备ID:', this.orderDetail.deviceId, '设备编号:', this.orderDetail.deviceNo);\n\t\t\tuni.navigateTo({\n\t\t\t\turl: `/pages/report/report?orderId=${this.orderId}&deviceId=${this.orderDetail.deviceId || this.orderDetail.deviceNo || ''}`\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 联系客服\n\t\tcontactService() {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: '/pages/contact/contact'\n\t\t\t});\n\t\t}\n\t}\n}\n</script>\n\n<style>\n/* iOS适配 */\n.page-order-detail {\n\t/* 适配iOS设备的安全区域和灵动岛 */\n\tpadding-top: env(safe-area-inset-top);\n}\n\n/* 页面基础样式 */\n.page-order-detail {\n\tcolor: #ffffff;\n\theight: 100vh;\n\tmin-height: 100vh;\n\tbox-sizing: border-box;\n\tposition: relative;\n\toverflow: hidden;\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n/* 页面背景样式 */\n.page-background {\n\tposition: fixed;\n\ttop: 0;\n\tleft: 0;\n\twidth: 100%;\n\theight: 100%;\n\tz-index: 0;\n}\n\n/* 背景图片样式 */\n.background-image {\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\twidth: 100%;\n\theight: 100%;\n\tz-index: 0;\n\tobject-fit: cover;\n}\n\n/* 深磨砂效果叠加层 */\n.frosted-overlay {\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\twidth: 100%;\n\theight: 100%;\n\tbackground-color: rgba(18, 18, 18, 0.7); /* 深色半透明背景 */\n\tbackdrop-filter: blur(8px); /* 较强模糊效果 */\n\t-webkit-backdrop-filter: blur(8px);\n\tz-index: 1;\n}\n\n/* 顶部导航样式 */\n.navbar {\n\theight: 90rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tpadding: 0 30rpx;\n\t/* 完全移除CSS的margin-top，只使用JavaScript动态设置 */\n\tmargin-top: 0;\n\tposition: relative;\n\tz-index: 100;\n}\n\n.navbar-title {\n\tfont-size: 40rpx;\n\tfont-weight: 600;\n}\n\n.navbar-left, .navbar-right {\n\twidth: 60rpx;\n\theight: 60rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.icon-back {\n\tfont-size: 40rpx;\n\tcolor: #A875FF;\n}\n\n/* 页面内容区域 */\n.page-content {\n\tflex: 1;\n\tdisplay: flex;\n\tflex-direction: column;\n\tposition: relative;\n\tz-index: 5;\n\tpadding: 0 30rpx;\n}\n\n.content {\n\tflex: 1;\n\tpadding: 20rpx 0;\n\tposition: relative;\n\twidth: 100%;\n\tbox-sizing: border-box;\n}\n\n/* 订单状态卡片 */\n.order-status-card {\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 30rpx;\n\tborder-radius: 16rpx;\n\tbackground: rgba(30, 30, 40, 0.5);\n\tbackdrop-filter: blur(10px);\n\t-webkit-backdrop-filter: blur(10px);\n\tborder: 1px solid rgba(168, 117, 255, 0.3);\n\tbox-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);\n\tmargin-top: 20rpx;\n}\n\n.order-status-card.active {\n\tbackground: rgba(168, 117, 255, 0.15);\n}\n\n.order-status-card.active .status-icon {\n\tcolor: #A875FF;\n}\n\n.order-status-card.active .status-title {\n\tcolor: #A875FF;\n}\n\n.order-status-card.unpaid {\n\tbackground: rgba(255, 152, 0, 0.15);\n}\n\n.order-status-card.unpaid .status-icon {\n\tcolor: #FF9800;\n}\n\n.order-status-card.unpaid .status-title {\n\tcolor: #FF9800;\n}\n\n.order-status-card.completed {\n\tbackground: rgba(76, 175, 80, 0.15);\n}\n\n.order-status-card.completed .status-icon {\n\tcolor: #4CAF50;\n}\n\n.order-status-card.completed .status-title {\n\tcolor: #4CAF50;\n}\n\n.order-status-card.cancelled {\n\tbackground: rgba(244, 67, 54, 0.15);\n}\n\n.order-status-card.cancelled .status-icon {\n\tcolor: #F44336;\n}\n\n.order-status-card.cancelled .status-title {\n\tcolor: #F44336;\n}\n\n.order-status-card.inactive {\n\tbackground: rgba(255, 255, 255, 0.07);\n}\n\n.order-status-card.inactive .status-icon {\n\tcolor: rgba(255, 255, 255, 0.5);\n}\n\n.order-status-card.inactive .status-title {\n\tcolor: rgba(255, 255, 255, 0.5);\n}\n\n.status-icon {\n\tmargin-right: 20rpx;\n\tfont-size: 40rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.status-text {\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n.status-title {\n\tfont-size: 32rpx;\n\tfont-weight: 500;\n}\n\n.status-desc {\n\tfont-size: 24rpx;\n\tcolor: rgba(255, 255, 255, 0.6);\n\tmargin-top: 4rpx;\n}\n\n/* 卡片样式 */\n.card {\n\tbackground: rgba(30, 30, 40, 0.5);\n\tborder-radius: 16rpx;\n\toverflow: hidden;\n\tbackdrop-filter: blur(10px);\n\t-webkit-backdrop-filter: blur(10px);\n\tborder: 1px solid rgba(255, 255, 255, 0.1);\n\tbox-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);\n}\n\n.card-header {\n\tpadding: 20rpx 30rpx;\n\tborder-bottom: 1px solid rgba(255, 255, 255, 0.1);\n\tfont-size: 28rpx;\n\tfont-weight: 500;\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.card-icon {\n\tmargin-right: 10rpx;\n}\n\n.card-content {\n\tpadding: 20rpx 30rpx;\n}\n\n/* 详情项样式 */\n.detail-item {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\tmargin-bottom: 10rpx;\n}\n\n.detail-label {\n\tcolor: rgba(255, 255, 255, 0.7);\n\tfont-size: 28rpx;\n}\n\n.detail-value {\n\tcolor: #FFFFFF;\n\tfont-size: 28rpx;\n}\n\n/* 按钮样式 */\n.btn {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tborder-radius: 40rpx;\n\tpadding: 20rpx;\n\tfont-size: 28rpx;\n\tfont-weight: 600;\n\ttransition: all 0.3s ease;\n}\n\n.btn-primary {\n\tbackground: linear-gradient(135deg, rgba(168, 117, 255, 0.8), rgba(139, 92, 246, 0.9));\n\tcolor: #FFFFFF;\n\tbox-shadow: 0 4rpx 20rpx rgba(168, 117, 255, 0.4);\n\tborder: none;\n}\n\n.btn-outline {\n\tbackground-color: transparent;\n\tborder: 1px solid rgba(168, 117, 255, 0.4);\n\tcolor: #A875FF;\n}\n\n.btn-block {\n\twidth: 100%;\n}\n\n.btn-icon {\n\tmargin-right: 8rpx;\n}\n\n/* 辅助类 */\n.primary-light {\n\tcolor: #A875FF;\n\tfont-weight: 600;\n}\n\n.success-light {\n\tcolor: #4CAF50;\n}\n\n.error-light {\n\tcolor: #F44336;\n}\n\n.actions {\n\tpadding: 20rpx 0 40rpx;\n}\n\n.flex {\n\tdisplay: flex;\n}\n\n.justify-between {\n\tjustify-content: space-between;\n}\n\n.mt-sm {\n\tmargin-top: 10rpx;\n}\n\n.mt-md {\n\tmargin-top: 20rpx;\n}\n\n.mt-lg {\n\tmargin-top: 30rpx;\n}\n\n/* Material Icons 字体 */\n@font-face {\n\tfont-family: 'Material Icons';\n\tfont-style: normal;\n\tfont-weight: 400;\n\tsrc: url(https://fonts.gstatic.com/s/materialicons/v139/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format('woff2');\n}\n\n.material-icons {\n\tfont-family: 'Material Icons';\n\tfont-weight: normal;\n\tfont-style: normal;\n\tfont-size: 24rpx;\n\tline-height: 1;\n\tletter-spacing: normal;\n\ttext-transform: none;\n\tdisplay: inline-block;\n\twhite-space: nowrap;\n\tword-wrap: normal;\n\tdirection: ltr;\n\t-webkit-font-smoothing: antialiased;\n\t-moz-osx-font-smoothing: grayscale;\n}\n\n.material-icons.md-18 {\n\tfont-size: 36rpx;\n}\n\n.material-icons.md-24 {\n\tfont-size: 48rpx;\n}\n\n.material-icons.md-48 {\n\tfont-size: 96rpx;\n}\n\n.material-icons.text-primary {\n\tcolor: var(--text-primary, #FFFFFF);\n}\n\n.material-icons.text-tertiary {\n\tcolor: var(--text-tertiary, rgba(255, 255, 255, 0.5));\n}\n\n.icon-inline {\n\tmargin-right: 8rpx;\n\tvertical-align: middle;\n}\n\n/* 移除所有CSS适配，完全依赖JavaScript动态设置 */\n</style>", "import mod from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754165306714\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}