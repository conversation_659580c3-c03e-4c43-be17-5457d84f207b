/* iOS 安全区域处理 */

/* 基础安全区域处理 */
.safe-area-inset-bottom {
  padding-bottom: constant(safe-area-inset-bottom) !important; /* iOS 11.0 */
  padding-bottom: env(safe-area-inset-bottom) !important; /* iOS 11.2+ */
}

.safe-area-inset-top {
  padding-top: constant(safe-area-inset-top) !important; /* iOS 11.0 */
  padding-top: env(safe-area-inset-top) !important; /* iOS 11.2+ */
}

/* 底部安全区域背景色 */
.uni-safe-area-bottom {
  background-color: rgba(30, 30, 30, 0.95) !important;
}

/* 针对不同机型的安全区域处理 */
@supports (padding-bottom: env(safe-area-inset-bottom)) {
  /* 全局安全区域样式 - 这会添加一个固定在底部的元素作为安全区域背景 */
  uni-page-body::after {
    content: '';
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: env(safe-area-inset-bottom);
    background-color: rgba(30, 30, 30, 0.95);
    z-index: 9999;
    pointer-events: none;
  }
  
  /* 内容容器底部添加安全区域高度的padding */
  .container {
    padding-bottom: calc(20rpx + env(safe-area-inset-bottom)) !important;
  }
  
  /* TabBar底部添加安全区域高度 */
  .tab-bar {
    padding-bottom: env(safe-area-inset-bottom) !important;
  }
}

/* 容器底部留出TabBar和安全区域高度的空间 */
.page-container {
  padding-bottom: calc(170rpx + constant(safe-area-inset-bottom)) !important;
  padding-bottom: calc(170rpx + env(safe-area-inset-bottom)) !important;
}

/* 安卓设备页面容器底部间距优化 */
/* #ifdef MP-WEIXIN */
.page-container.android-page,
.container.android-page {
  padding-bottom: calc(170rpx + constant(safe-area-inset-bottom)) !important;
  padding-bottom: calc(170rpx + env(safe-area-inset-bottom)) !important;
}

/* 安卓设备底部空白区域优化 */
.android-page .bottom-space {
  height: 40rpx !important; /* 安卓设备增加底部空白高度 */
}

/* 安卓设备内容区域底部间距 */
.android-page .content {
  padding-bottom: 20rpx !important; /* 安卓设备内容区域底部间距 */
}
/* #endif */

/* 通用安卓设备适配（所有平台） */
.android-page {
  /* 确保安卓设备有足够的底部间距 */
  min-height: calc(100vh - 170rpx) !important;
}

.android-page .tab-bar-placeholder {
  height: 170rpx !important; /* 安卓设备TabBar占位高度 */
}

/* 解决TabBar和安全区域在iOS上的显示问题 */
.uni-tabbar {
  padding-bottom: constant(safe-area-inset-bottom) !important;
  padding-bottom: env(safe-area-inset-bottom) !important;
  height: calc(170rpx + constant(safe-area-inset-bottom)) !important;
  height: calc(170rpx + env(safe-area-inset-bottom)) !important;
}

/* 修复iOS上底部内容被安全区域遮挡的问题 */
.fix-safe-bottom {
  margin-bottom: constant(safe-area-inset-bottom);
  margin-bottom: env(safe-area-inset-bottom);
}

/* 底部固定元素适配安全区域 */
.fixed-bottom {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  background-color: rgba(30, 30, 30, 0.95); /* 与TabBar一致的背景色 */
  z-index: 999;
} 