# 今夜城堡微信小程序API文档

## 1. 概述

本文档描述了今夜城堡微信小程序的API接口，包括用户登录、设备使用、订单管理、支付功能和用户反馈等功能。所有接口均采用RESTful风格，返回JSON格式数据。

## 2. 接口基础信息

- **基础URL**: `/api/wx/miniapp`
- **认证方式**: 大部分接口需要登录认证，使用Sa-Token进行身份验证
- **返回格式**: 所有接口返回统一的`Result<T>`格式
  ```json
  {
    "code": 200,      // 状态码，200表示成功
    "message": "操作成功", // 消息
    "data": {}        // 返回数据，根据接口不同而不同
  }
  ```

## 3. 用户登录与信息

### 3.1 微信小程序登录

- **URL**: `/api/wx/miniapp/login`
- **方法**: POST
- **描述**: 通过微信小程序code进行登录
- **请求参数**:
  ```json
  {
    "code": "wx_code",          // 必填，微信登录code
    "nickName": "用户昵称",      // 可选，用户昵称
    "avatarUrl": "头像URL",     // 可选，用户头像URL
    "gender": 1,               // 可选，性别：0-未知，1-男，2-女
    "country": "国家",          // 可选，国家
    "province": "省份",         // 可选，省份
    "city": "城市",             // 可选，城市
    "language": "zh_CN",       // 可选，语言
    "phoneCode": "手机号凭证"    // 可选，用于获取用户手机号的凭证
  }
  ```
- **返回数据**:
  ```json
  {
    "userId": 10001,           // 用户ID
    "nickName": "用户昵称",      // 用户昵称
    "avatarUrl": "头像URL",     // 用户头像URL
    "gender": 1,               // 性别：0-未知，1-男，2-女
    "mobile": "13800138000",   // 手机号
    "status": 1,               // 用户状态：1-正常，0-禁用
    "registerTime": "2023-01-01 12:00:00", // 注册时间
    "lastLoginTime": "2023-01-02 12:00:00", // 最后登录时间
    "token": "sa_token_value", // 登录token
    "tokenExpire": 7200        // token有效期（秒）
  }
  ```

### 3.2 获取用户信息

- **URL**: `/api/wx/miniapp/user/info`
- **方法**: GET
- **描述**: 获取当前登录用户信息
- **请求参数**: 无
- **返回数据**: 与登录接口返回数据相同

## 4. 设备管理

### 4.1 扫描设备二维码

- **URL**: `/api/wx/miniapp/device/scan`
- **方法**: GET
- **描述**: 扫描设备二维码获取设备信息
- **请求参数**:
  - `deviceCode`: 设备二维码或设备mac（必填，URL参数）
- **返回数据**:
  ```json
  {
    "deviceId": 1001,          // 设备ID
    "deviceNo": "JY10010001",  // 设备编号
    "deviceName": "按摩椅A",    // 设备名称
    "deviceType": "按摩椅",     // 设备类型
    "status": 1,               // 设备状态：1-正常，2-维护中，3-故障
    "onlineStatus": 1,         // 设备在线状态：1-在线，0-离线
    "battery": 90,             // 设备电量
    "latitude": 30.123456,     // 设备位置-纬度
    "longitude": 120.123456,   // 设备位置-经度
    "address": "杭州市西湖区",  // 设备位置-地址
    "shopId": 101,             // 所属门店ID
    "shopName": "西湖店",       // 所属门店名称
    "hourlyRate": 20.00,       // 使用费率（元/小时）
    "available": true,         // 是否可用
    "unavailableReason": null, // 不可用原因
    "qrCode": "https://...",   // 设备二维码
    "currentOrderId": null     // 当前使用中的订单ID
  }
  ```

### 4.2 获取设备当前状态

- **URL**: `/api/wx/miniapp/device/status/{deviceId}`
- **方法**: GET
- **描述**: 获取设备当前状态
- **请求参数**:
  - `deviceId`: 设备ID（必填，路径参数）
- **返回数据**: 与扫描设备二维码接口返回数据相同

### 4.3 开锁使用设备

- **URL**: `/api/wx/miniapp/device/unlock`
- **方法**: POST
- **描述**: 开锁使用设备
- **请求参数**:
  ```json
  {
    "orderId": 10001,          // 必填，订单ID
    "deviceId": 1001,          // 必填，设备ID
    "deviceCode": "JY10010001", // 必填，设备编码
    "operationType": 1,        // 操作类型：1-开锁，2-关锁
    "latitude": 30.123456,     // 位置信息-纬度
    "longitude": 120.123456,   // 位置信息-经度
    "address": "杭州市西湖区",  // 位置信息-地址
    "deviceStatus": "normal",  // 设备状态信息
    "battery": 90              // 设备电量
  }
  ```
- **返回数据**: `true`或`false`表示操作是否成功

### 4.4 结束使用设备

- **URL**: `/api/wx/miniapp/device/end-use/{orderId}`
- **方法**: POST
- **描述**: 结束使用设备
- **请求参数**:
  - `orderId`: 订单ID（必填，路径参数）
- **返回数据**: 与获取订单详情接口返回数据相同

## 5. 订单管理

### 5.1 创建设备使用订单

- **URL**: `/api/wx/miniapp/order/create`
- **方法**: POST
- **描述**: 创建设备使用订单
- **请求参数**:
  ```json
  {
    "deviceId": 1001,          // 必填，设备ID
    "deviceCode": "JY10010001", // 必填，设备编码
    "duration": 60,            // 可选，设备使用时长（分钟）
    "remark": "备注信息",       // 可选，备注
    "latitude": 30.123456,     // 可选，位置信息-纬度
    "longitude": 120.123456,   // 可选，位置信息-经度
    "address": "杭州市西湖区"   // 可选，位置信息-地址
  }
  ```
- **返回数据**:
  ```json
  {
    "orderId": 10001,          // 订单ID
    "orderNo": "JY202301010001", // 订单编号
    "userId": 10001,           // 用户ID
    "deviceId": 1001,          // 设备ID
    "deviceNo": "JY10010001",  // 设备编号
    "deviceName": "按摩椅A",    // 设备名称
    "shopId": 101,             // 所属门店ID
    "shopName": "西湖店",       // 所属门店名称
    "status": 0,               // 订单状态：0-未支付，1-已支付(使用中)，2-已完成，3-已取消
    "amount": 20.00,           // 订单金额
    "payAmount": 20.00,        // 实付金额
    "duration": 60,            // 使用时长（分钟）
    "startTime": null,         // 开始时间
    "endTime": null,           // 结束时间
    "createTime": "2023-01-01 12:00:00", // 创建时间
    "payTime": null,           // 支付时间
    "payType": 1,              // 支付方式：1-微信支付
    "payStatus": 0,            // 支付状态：0-未支付，1-已支付
    "useStatus": 0,            // 设备使用状态：0-未使用，1-使用中，2-已结束
    "remark": "备注信息",       // 备注
    "latitude": 30.123456,     // 设备位置-纬度
    "longitude": 120.123456,   // 设备位置-经度
    "address": "杭州市西湖区",  // 设备位置-地址
    "qrCode": "https://..."    // 设备二维码
  }
  ```

### 5.2 支付订单

- **URL**: `/api/wx/miniapp/order/pay/{orderId}`
- **方法**: POST
- **描述**: 支付设备使用订单
- **请求参数**:
  - `orderId`: 订单ID（必填，路径参数）
- **返回数据**:
  ```json
  {
    "orderId": 10001,          // 订单ID
    "orderNo": "JY202301010001", // 订单编号
    "payAmount": 20.00,        // 支付金额
    "payType": 1,              // 支付方式：1-微信支付
    "payStatus": 0,            // 支付状态：0-未支付，1-已支付
    "appId": "wx123456",       // 微信支付参数-appId
    "timeStamp": "1609459200", // 微信支付参数-timeStamp
    "nonceStr": "abcdef",      // 微信支付参数-nonceStr
    "packageValue": "prepay_id=wx123456", // 微信支付参数-package
    "signType": "MD5",         // 微信支付参数-signType
    "paySign": "abcdef123456", // 微信支付参数-paySign
    "payResult": null,         // 支付结果
    "payMessage": null         // 支付结果消息
  }
  ```

### 5.3 获取用户订单列表

- **URL**: `/api/wx/miniapp/order/list`
- **方法**: GET
- **描述**: 获取当前用户的订单列表
- **请求参数**:
  - `status`: 订单状态（可选，URL参数）
  - `page`: 页码，默认1（可选，URL参数）
  - `size`: 每页数量，默认10（可选，URL参数）
- **返回数据**: 订单信息数组，每个元素与获取订单详情接口返回数据相同

### 5.4 获取订单详情

- **URL**: `/api/wx/miniapp/order/{orderId}`
- **方法**: GET
- **描述**: 获取订单详情
- **请求参数**:
  - `orderId`: 订单ID（必填，路径参数）
- **返回数据**: 与创建订单接口返回数据相同

## 6. 支付管理

### 6.1 创建支付订单

- **URL**: `/api/wx/miniapp/pay/create/{orderId}`
- **方法**: POST
- **描述**: 创建微信支付订单
- **请求参数**:
  - `orderId`: 订单ID（必填，路径参数）
- **返回数据**: 与支付订单接口返回数据相同

### 6.2 查询支付状态

- **URL**: `/api/wx/miniapp/pay/query/{orderId}`
- **方法**: GET
- **描述**: 查询微信支付订单状态
- **请求参数**:
  - `orderId`: 订单ID（必填，路径参数）
- **返回数据**: `true`或`false`表示是否已支付

### 6.3 关闭支付订单

- **URL**: `/api/wx/miniapp/pay/close/{orderId}`
- **方法**: POST
- **描述**: 关闭微信支付订单
- **请求参数**:
  - `orderId`: 订单ID（必填，路径参数）
- **返回数据**: `true`或`false`表示操作是否成功

### 6.4 申请退款

- **URL**: `/api/wx/miniapp/pay/refund/{orderId}`
- **方法**: POST
- **描述**: 申请微信支付退款
- **请求参数**:
  - `orderId`: 订单ID（必填，路径参数）
  - `refundAmount`: 退款金额（必填，URL参数）
  - `refundReason`: 退款原因（可选，URL参数）
- **返回数据**: `true`或`false`表示操作是否成功

## 7. 用户反馈

### 7.1 提交反馈

- **URL**: `/api/wx/miniapp/feedback/submit`
- **方法**: POST
- **描述**: 用户提交反馈信息
- **请求参数**:
  ```json
  {
    "deviceId": 1001,          // 可选，设备ID
    "orderId": "JY202301010001", // 可选，订单ID
    "issueType": 1,            // 必填，问题类型：1-设备问题 2-订单问题 3-其他问题
    "content": "设备无法使用",   // 必填，反馈内容
    "images": ["url1", "url2"], // 可选，图片URL列表
    "contactInfo": "13800138000" // 可选，联系方式
  }
  ```
- **返回数据**: 反馈ID

### 7.2 获取用户反馈列表

- **URL**: `/api/wx/miniapp/feedback/list`
- **方法**: GET
- **描述**: 获取当前用户的反馈列表
- **请求参数**:
  - `page`: 页码，默认1（可选，URL参数）
  - `size`: 每页数量，默认10（可选，URL参数）
  - `status`: 状态：0-待处理 1-处理中 2-已处理 3-已关闭（可选，URL参数）
- **返回数据**: 分页的反馈信息列表
  ```json
  {
    "records": [
      {
        "id": 1001,            // 反馈ID
        "userId": 10001,       // 用户ID
        "nickname": "用户昵称",  // 用户昵称
        "avatar": "头像URL",    // 用户头像
        "feedbackType": "user", // 反馈类型
        "deviceId": 1001,      // 设备ID
        "deviceName": "按摩椅A", // 设备名称
        "orderId": "JY202301010001", // 订单ID
        "issueType": 1,        // 问题类型
        "issueTypeName": "设备问题", // 问题类型名称
        "content": "设备无法使用", // 反馈内容
        "images": "url1,url2", // 图片URL字符串
        "imageList": ["url1", "url2"], // 图片URL列表
        "contactInfo": "13800138000", // 联系方式
        "status": 0,           // 状态
        "statusName": "待处理", // 状态名称
        "handlerId": null,     // 处理人ID
        "handlerName": null,   // 处理人姓名
        "result": null,        // 处理结果
        "handleTime": null,    // 处理时间
        "createTime": "2023-01-01 12:00:00", // 创建时间
        "updateTime": "2023-01-01 12:00:00"  // 更新时间
      }
    ],
    "total": 10,               // 总记录数
    "size": 10,                // 每页记录数
    "current": 1,              // 当前页码
    "pages": 1                 // 总页数
  }
  ```

### 7.3 获取反馈详情

- **URL**: `/api/wx/miniapp/feedback/{feedbackId}`
- **方法**: GET
- **描述**: 获取反馈详情信息
- **请求参数**:
  - `feedbackId`: 反馈ID（必填，路径参数）
- **返回数据**: 与反馈列表中的单条记录数据结构相同

## 8. 使用示例

### 8.1 登录流程

1. 小程序端通过`wx.login()`获取临时登录凭证code
2. 调用`/api/wx/miniapp/login`接口，传入code和用户信息
3. 后端验证code并生成token返回
4. 小程序端保存token，后续请求在header中携带token

```javascript
// 小程序端示例代码
wx.login({
  success: (res) => {
    if (res.code) {
      wx.request({
        url: 'https://api.example.com/api/wx/miniapp/login',
        method: 'POST',
        data: {
          code: res.code,
          nickName: userInfo.nickName,
          avatarUrl: userInfo.avatarUrl,
          gender: userInfo.gender
        },
        success: (loginRes) => {
          if (loginRes.data.code === 200) {
            // 保存token
            wx.setStorageSync('token', loginRes.data.data.token);
          }
        }
      });
    }
  }
});
```

### 8.2 设备使用流程

1. 扫描设备二维码获取设备信息
2. 创建设备使用订单
3. 支付订单
4. 开锁使用设备
5. 使用完成后结束使用

```javascript
// 1. 扫描设备二维码
wx.scanCode({
  success: (res) => {
    const deviceCode = res.result;
    wx.request({
      url: `https://api.example.com/api/wx/miniapp/device/scan?deviceCode=${deviceCode}`,
      header: { 'Authorization': wx.getStorageSync('token') },
      success: (deviceRes) => {
        if (deviceRes.data.code === 200) {
          const deviceInfo = deviceRes.data.data;
          // 2. 创建订单
          createOrder(deviceInfo);
        }
      }
    });
  }
});

// 创建订单
function createOrder(deviceInfo) {
  wx.request({
    url: 'https://api.example.com/api/wx/miniapp/order/create',
    method: 'POST',
    header: { 'Authorization': wx.getStorageSync('token') },
    data: {
      deviceId: deviceInfo.deviceId,
      deviceCode: deviceInfo.deviceNo,
      duration: 60 // 使用60分钟
    },
    success: (orderRes) => {
      if (orderRes.data.code === 200) {
        const orderInfo = orderRes.data.data;
        // 3. 支付订单
        payOrder(orderInfo.orderId);
      }
    }
  });
}

// 支付订单
function payOrder(orderId) {
  wx.request({
    url: `https://api.example.com/api/wx/miniapp/order/pay/${orderId}`,
    method: 'POST',
    header: { 'Authorization': wx.getStorageSync('token') },
    success: (payRes) => {
      if (payRes.data.code === 200) {
        const payInfo = payRes.data.data;
        // 调用微信支付
        wx.requestPayment({
          timeStamp: payInfo.timeStamp,
          nonceStr: payInfo.nonceStr,
          package: payInfo.packageValue,
          signType: payInfo.signType,
          paySign: payInfo.paySign,
          success: () => {
            // 4. 支付成功后开锁使用
            unlockDevice(orderId, payInfo.deviceId, payInfo.deviceNo);
          }
        });
      }
    }
  });
}

// 开锁使用
function unlockDevice(orderId, deviceId, deviceCode) {
  wx.request({
    url: 'https://api.example.com/api/wx/miniapp/device/unlock',
    method: 'POST',
    header: { 'Authorization': wx.getStorageSync('token') },
    data: {
      orderId: orderId,
      deviceId: deviceId,
      deviceCode: deviceCode,
      operationType: 1
    },
    success: (unlockRes) => {
      if (unlockRes.data.code === 200 && unlockRes.data.data) {
        wx.showToast({ title: '开锁成功' });
      }
    }
  });
}

// 5. 结束使用
function endUse(orderId) {
  wx.request({
    url: `https://api.example.com/api/wx/miniapp/device/end-use/${orderId}`,
    method: 'POST',
    header: { 'Authorization': wx.getStorageSync('token') },
    success: (endRes) => {
      if (endRes.data.code === 200) {
        wx.showToast({ title: '使用结束' });
      }
    }
  });
}
```

## 9. 错误码说明

| 错误码 | 说明 |
|-------|------|
| 200   | 成功 |
| 401   | 未登录或登录已过期 |
| 403   | 无权限访问 |
| 404   | 资源不存在 |
| 500   | 服务器内部错误 |
| 1001  | 参数错误 |
| 1002  | 设备不可用 |
| 1003  | 订单创建失败 |
| 1004  | 支付失败 |
| 1005  | 设备操作失败 |

## 10. 注意事项

1. 所有需要登录的接口都需要在请求头中携带token
2. 支付相关接口需要在小程序端调用微信支付API
3. 图片上传需要先调用专门的上传接口获取URL，再提交表单
4. 设备使用时需要注意处理网络异常情况
5. 反馈提交时，图片URL需要先通过上传接口获取 