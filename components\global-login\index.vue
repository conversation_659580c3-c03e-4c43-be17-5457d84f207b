<template>
	<view>
		<!-- 登录弹窗组件 -->
		<login-modal 
			:visible="isLoginModalVisible" 
			@login-success="onLoginSuccess" 
			@login-fail="onLoginFail"
			@close="onModalClose"
		></login-modal>
	</view>
</template>

<script>
	import { mapState, mapActions } from 'vuex';
	import LoginModal from '@/components/login-modal/index.vue';
	
	export default {
		name: 'global-login',
		components: {
			LoginModal
		},
		data() {
			return {
				isLoginModalVisible: false
			}
		},
		computed: {
			...mapState({
				isLoggedIn: state => state.isLoggedIn
			})
		},
		watch: {
			// 监听store中的showLoginModal状态变化
			'$store.state.showLoginModal': {
				immediate: true,
				handler(newVal) {
					this.isLoginModalVisible = newVal;
				}
			}
		},
		methods: {
			...mapActions([
				'checkLoginStatus',
				'showLoginModal',
				'hideLoginModal',
				'loginSuccess'
			]),
			
			// 显示登录弹窗
			showLogin() {
				this.showLoginModal();
			},
			
			// 登录成功回调
			onLoginSuccess(userData) {
				this.loginSuccess(userData);
				this.$emit('login-success', userData);
			},
			
			// 登录失败回调
			onLoginFail(error) {
				this.$emit('login-fail', error);
			},
			
			// 弹窗关闭回调
			onModalClose() {
				this.hideLoginModal();
				this.$emit('modal-close');
			},
			
			// 检查是否需要登录
			checkNeedLogin() {
				return !this.checkLoginStatus();
			},
			
			// 确保已登录，如果未登录则显示登录弹窗
			ensureLogin() {
				if (!this.checkLoginStatus()) {
					this.showLoginModal();
					return false;
				}
				return true;
			}
		}
	}
</script> 