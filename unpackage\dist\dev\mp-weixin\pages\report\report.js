(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pages/report/report"],{

/***/ 85:
/*!*****************************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/main.js?{"page":"pages%2Freport%2Freport"} ***!
  \*****************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _report = _interopRequireDefault(__webpack_require__(/*! ./pages/report/report.vue */ 86));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_report.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 86:
/*!**********************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/report/report.vue ***!
  \**********************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _report_vue_vue_type_template_id_e403a974___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./report.vue?vue&type=template&id=e403a974& */ 87);
/* harmony import */ var _report_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./report.vue?vue&type=script&lang=js& */ 89);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _report_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _report_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _report_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./report.vue?vue&type=style&index=0&lang=css& */ 91);
/* harmony import */ var _HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 36);

var renderjs





/* normalize component */

var component = Object(_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _report_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _report_vue_vue_type_template_id_e403a974___WEBPACK_IMPORTED_MODULE_0__["render"],
  _report_vue_vue_type_template_id_e403a974___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  null,
  null,
  false,
  _report_vue_vue_type_template_id_e403a974___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pages/report/report.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 87:
/*!*****************************************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/report/report.vue?vue&type=template&id=e403a974& ***!
  \*****************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_report_vue_vue_type_template_id_e403a974___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./report.vue?vue&type=template&id=e403a974& */ 88);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_report_vue_vue_type_template_id_e403a974___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_report_vue_vue_type_template_id_e403a974___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_report_vue_vue_type_template_id_e403a974___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_report_vue_vue_type_template_id_e403a974___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 88:
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/report/report.vue?vue&type=template&id=e403a974& ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var l0 = _vm.__map(_vm.issueTypes, function (item, index) {
    var $orig = _vm.__get_orig(item)
    var g0 = _vm.selectedIssueTypes.includes(item.value)
    return {
      $orig: $orig,
      g0: g0,
    }
  })
  var g1 = _vm.uploadedImages.length
  var g2 = _vm.description.length
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        l0: l0,
        g1: g1,
        g2: g2,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 89:
/*!***********************************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/report/report.vue?vue&type=script&lang=js& ***!
  \***********************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_report_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./report.vue?vue&type=script&lang=js& */ 90);
/* harmony import */ var _HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_report_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_report_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_report_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_report_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_report_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 90:
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/report/report.vue?vue&type=script&lang=js& ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni, wx) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _toConsumableArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/toConsumableArray */ 18));
var _typeof2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/typeof */ 13));
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
var _default = {
  data: function data() {
    return {
      loading: true,
      submitting: false,
      // iOS适配相关
      systemInfo: {},
      navbarStyle: {},
      isIOS: false,
      // 设备平台
      platform: '',
      // 订单相关
      orderIndex: 0,
      orderList: [],
      orderDisplays: ['请选择订单'],
      // 问题类型
      issueTypes: [{
        label: '机柜损坏',
        value: 'cabinet_damaged',
        icon: 'door_front',
        type: 1
      }, {
        label: '门锁损坏',
        value: 'lock_damaged',
        icon: 'lock_open',
        type: 1
      }, {
        label: '娃娃损坏',
        value: 'doll_damaged',
        icon: 'girl',
        type: 1
      }, {
        label: '无法对话',
        value: 'no_dialogue',
        icon: 'mic_off',
        type: 1
      }, {
        label: '清洁问题',
        value: 'cleaning_issue',
        icon: 'cleaning_services',
        type: 1
      }, {
        label: '其他问题',
        value: 'other',
        icon: 'help_outline',
        type: 3
      }],
      selectedIssueTypes: [],
      // 问题描述
      description: '',
      // 上传图片
      uploadedImages: [],
      uploadedImageUrls: [],
      // 从订单详情页传入的参数
      orderId: null,
      deviceId: null
    };
  },
  onLoad: function onLoad(options) {
    console.log('report.vue onLoad options:', options);

    // 获取设备平台信息
    this.detectPlatform();

    // 接收从订单详情页传入的参数
    if (options.orderId) {
      this.orderId = options.orderId;
      console.log('接收到订单ID:', this.orderId);
    }
    if (options.deviceId) {
      this.deviceId = options.deviceId;
      console.log('接收到设备ID:', this.deviceId);
    }

    // 获取订单列表
    this.getOrderList();
  },
  onReady: function onReady() {
    // 获取系统信息并计算iOS适配参数
    this.calculateIOSAdaptation();
  },
  computed: {
    isFormValid: function isFormValid() {
      // 表单验证
      // 修改为必须选择订单（orderIndex > 0），除非有传入的orderId
      var orderSelected = this.orderIndex > 0 || this.orderId;
      var issueTypeSelected = this.selectedIssueTypes.length > 0;
      var descriptionValid = this.description && this.description.trim().length >= 10;
      console.log('表单验证详情:', {
        orderSelected: orderSelected,
        orderIndex: this.orderIndex,
        orderId: this.orderId,
        issueTypeSelected: issueTypeSelected,
        selectedTypes: this.selectedIssueTypes,
        descriptionValid: descriptionValid,
        descriptionLength: this.description ? this.description.trim().length : 0
      });
      return orderSelected && issueTypeSelected && descriptionValid;
    },
    selectedOrderId: function selectedOrderId() {
      // 如果已有orderId，直接返回
      if (this.orderId) {
        return this.orderId;
      }

      // 否则从选择的订单中获取
      if (this.orderIndex > 0 && this.orderList && this.orderList.length > 0 && this.orderIndex <= this.orderList.length) {
        var selectedOrder = this.orderList[this.orderIndex - 1];
        if (selectedOrder) {
          return selectedOrder.orderId || selectedOrder.id || null;
        }
      }
      return null;
    },
    // 获取选中的问题类型
    getSelectedIssueType: function getSelectedIssueType() {
      if (this.selectedIssueTypes.length === 0) {
        return 1; // 默认为设备问题
      }

      // 如果选中了多个问题类型，优先级：其他问题 > 订单问题 > 设备问题
      if (this.selectedIssueTypes.includes('other')) {
        return 3; // 其他问题
      }

      // 默认为设备问题
      return 1;
    }
  },
  methods: {
    // 计算iOS适配参数
    calculateIOSAdaptation: function calculateIOSAdaptation() {
      try {
        this.systemInfo = uni.getSystemInfoSync();
        this.isIOS = this.systemInfo.platform === 'ios';
        console.log('举报页面 - 系统信息:', this.systemInfo);
        console.log('举报页面 - 是否iOS:', this.isIOS);
        if (this.isIOS) {
          var statusBarHeight = this.systemInfo.statusBarHeight || 44;
          var model = this.systemInfo.model || '';
          var safeAreaTop = this.systemInfo.safeArea ? this.systemInfo.safeArea.top : statusBarHeight;
          console.log('举报页面 - 状态栏高度:', statusBarHeight);
          console.log('举报页面 - 设备型号:', model);
          console.log('举报页面 - 安全区域顶部:', safeAreaTop);
          var finalTopPosition;

          // 使用与订单页面相同的超激进适配策略
          if (model.includes('iPhone 16 Pro')) {
            // 方案1：直接使用状态栏高度，无额外间距
            finalTopPosition = statusBarHeight;
            console.log('举报页面 - iPhone 16 Pro - 方案1（状态栏高度）:', finalTopPosition);

            // 方案2：如果还是太靠下，尝试更小的值
            if (finalTopPosition > 50) {
              finalTopPosition = 44; // 使用标准状态栏高度
              console.log('举报页面 - iPhone 16 Pro - 方案2（标准高度）:', finalTopPosition);
            }

            // 方案3：如果还是太靠下，尝试负值
            if (finalTopPosition > 45) {
              finalTopPosition = statusBarHeight - 10; // 负偏移
              console.log('举报页面 - iPhone 16 Pro - 方案3（负偏移）:', finalTopPosition);
            }
          } else if (model.includes('iPhone 15 Pro') || model.includes('iPhone 14 Pro')) {
            finalTopPosition = statusBarHeight;
          } else if (model.includes('iPhone X') || model.includes('iPhone 11') || model.includes('iPhone 12') || model.includes('iPhone 13')) {
            finalTopPosition = statusBarHeight;
          } else {
            finalTopPosition = statusBarHeight;
          }
          this.navbarStyle = {
            marginTop: finalTopPosition + 'px',
            position: 'relative',
            top: '0px'
          };
          console.log('举报页面 - 超激进适配 - 最终顶部位置:', finalTopPosition + 'px');
          console.log('举报页面 - 超激进适配 - 导航栏样式:', this.navbarStyle);
        } else {
          this.navbarStyle = {};
        }
      } catch (error) {
        console.error('举报页面 - 计算iOS适配参数失败:', error);
        this.navbarStyle = {};
      }
    },
    goBack: function goBack() {
      uni.navigateBack();
    },
    // 获取订单列表
    getOrderList: function getOrderList() {
      var _this = this;
      try {
        this.loading = true;

        // 显示加载中
        uni.showLoading({
          title: '加载中...'
        });

        // 直接获取所有设备订单列表
        this.getActiveOrders();

        // 如果已经有传入的orderId，则尝试获取该订单的详情
        if (this.orderId) {
          // 检查API是否可用
          if (!this.$api || !this.$api.order || typeof this.$api.order.getDetail !== 'function') {
            console.error('API对象不存在或order.getDetail方法不可用');
            return;
          }
          this.$api.order.getDetail(this.orderId).then(function (res) {
            if (res && res.data) {
              console.log('获取到指定订单详情:', res.data);

              // 确保orderList已初始化
              if (!_this.orderList) {
                _this.orderList = [];
              }

              // 检查当前订单列表是否已包含该订单
              var existingOrderIndex = _this.orderList.findIndex(function (order) {
                return order && (order.orderId === _this.orderId || order.id === _this.orderId);
              });
              if (existingOrderIndex === -1) {
                // 如果订单列表中不包含指定的订单，则添加到列表中
                _this.orderList.unshift(res.data);
                console.log('将指定订单添加到列表首位');

                // 重新生成用于显示的订单列表
                _this.generateOrderDisplays();

                // 默认选中该订单
                _this.orderIndex = 1;
              } else {
                // 如果已包含，则选中该订单
                _this.orderIndex = existingOrderIndex + 1; // +1 是因为第一项是"请选择订单"
                console.log('订单列表中已包含指定订单，选中索引:', _this.orderIndex);
              }
            }
          }).catch(function (err) {
            console.error('获取指定订单详情失败:', err);
            uni.showToast({
              title: '获取订单详情失败',
              icon: 'none'
            });
          });
        }
      } catch (error) {
        console.error('getOrderList执行出错:', error);
        uni.hideLoading();
        this.loading = false;
        uni.showToast({
          title: '获取订单列表出错',
          icon: 'none'
        });
      }
    },
    // 获取所有设备订单列表
    getActiveOrders: function getActiveOrders() {
      var _this2 = this;
      try {
        if (!this.$api || !this.$api.order || typeof this.$api.order.getList !== 'function') {
          console.error('API对象不存在或order.getList方法不可用');
          uni.hideLoading();
          this.loading = false;

          // 显示错误提示
          uni.showToast({
            title: '获取订单列表失败，API不可用',
            icon: 'none'
          });
          return;
        }

        // 直接获取所有设备订单列表，不限制状态
        this.$api.order.getList(0, 1, 20) // 获取所有订单，第一页，每页20条
        .then(function (res) {
          uni.hideLoading();
          _this2.loading = false;

          // 处理返回数据，增强兼容性
          var orderList = [];
          try {
            // 兼容不同的返回格式
            if (res && res.data) {
              if (Array.isArray(res.data)) {
                // 直接返回数组的情况
                orderList = res.data;
              } else if (res.data && Array.isArray(res.data.list)) {
                // 包含list数组的情况
                orderList = res.data.list;
              } else if (res.data && Array.isArray(res.data.records)) {
                // 包含records数组的情况
                orderList = res.data.records;
              } else if (res.data && (0, _typeof2.default)(res.data) === 'object') {
                // 其他情况，尝试将对象转为数组
                orderList = [res.data];
              }
            }
          } catch (error) {
            console.error('处理订单数据出错:', error);
          }
          console.log('获取到订单列表:', orderList);
          if (orderList && orderList.length > 0) {
            // 使用获取到的订单列表
            _this2.orderList = orderList;

            // 生成用于显示的订单列表
            _this2.generateOrderDisplays();

            // 不再默认选择第一个订单，保持orderIndex为0，即"请选择订单"
            // 只有在有传入orderId的情况下，才会在getOrderList方法中设置选中项
          } else {
            console.log('没有获取到订单列表或列表为空');
            // 如果没有订单
            if (!_this2.orderId) {
              uni.showModal({
                title: '提示',
                content: '您当前没有可用订单，无法上报设备异常',
                showCancel: false,
                success: function success() {
                  uni.navigateBack();
                }
              });
            }
          }
        }).catch(function (err) {
          console.error('获取订单列表失败:', err);
          uni.hideLoading();
          _this2.loading = false;
          uni.showToast({
            title: '获取订单列表失败',
            icon: 'none'
          });

          // 如果获取列表失败且没有传入的orderId，则提示用户
          if (!_this2.orderId) {
            uni.showModal({
              title: '提示',
              content: '获取订单列表失败，请稍后重试',
              showCancel: false,
              success: function success() {
                uni.navigateBack();
              }
            });
          }
        });
      } catch (error) {
        console.error('getActiveOrders执行出错:', error);
        uni.hideLoading();
        this.loading = false;

        // 显示错误提示
        uni.showToast({
          title: '获取订单列表出错',
          icon: 'none'
        });
      }
    },
    // 格式化日期字符串，兼容iOS
    formatDateString: function formatDateString(dateStr) {
      if (!dateStr) return null;
      try {
        // 尝试直接解析（兼容标准格式）
        var directDate = new Date(dateStr);
        // 检查是否为有效日期
        if (!isNaN(directDate.getTime())) {
          return directDate;
        }

        // 处理 "yyyy-MM-dd HH:mm:ss" 格式
        if (dateStr.includes('-') && dateStr.includes(':')) {
          // 将 "yyyy-MM-dd HH:mm:ss" 转换为 "yyyy/MM/dd HH:mm:ss"
          var formattedStr = dateStr.replace(/-/g, '/');
          return new Date(formattedStr);
        }

        // 处理其他可能的格式
        // 提取日期部分
        var dateParts = dateStr.match(/(\d{4})[-\/](\d{1,2})[-\/](\d{1,2})/);
        if (dateParts) {
          var year = parseInt(dateParts[1]);
          var month = parseInt(dateParts[2]) - 1; // 月份从0开始
          var day = parseInt(dateParts[3]);

          // 提取时间部分
          var timeParts = dateStr.match(/(\d{1,2}):(\d{1,2}):(\d{1,2})/);
          if (timeParts) {
            var hour = parseInt(timeParts[1]);
            var minute = parseInt(timeParts[2]);
            var second = parseInt(timeParts[3]);
            return new Date(year, month, day, hour, minute, second);
          } else {
            return new Date(year, month, day);
          }
        }

        // 如果无法解析，返回当前日期
        console.error('无法解析日期:', dateStr);
        return new Date();
      } catch (e) {
        console.error('日期解析错误:', e, dateStr);
        return new Date(); // 出错时返回当前日期
      }
    },
    // 生成订单显示列表
    generateOrderDisplays: function generateOrderDisplays() {
      var _this3 = this;
      try {
        // 订单状态映射
        var statusMap = {
          0: '未支付',
          1: '进行中',
          2: '已完成',
          3: '已取消'
        };

        // 确保orderList存在且为数组
        if (!this.orderList || !Array.isArray(this.orderList)) {
          console.error('订单列表不是有效数组:', this.orderList);
          this.orderDisplays = ['请选择订单'];
          return;
        }
        this.orderDisplays = ['请选择订单'].concat((0, _toConsumableArray2.default)(this.orderList.map(function (order) {
          if (!order) return '未知订单';
          try {
            // 获取日期（使用兼容iOS的方法）
            var dateTimeStr = order.createTime || order.startTime || '';
            var date = _this3.formatDateString(dateTimeStr);
            var dateStr = date ? "".concat(date.getMonth() + 1, "\u6708").concat(date.getDate(), "\u65E5") : '未知日期';

            // 获取房间号/设备号
            var roomNumber = order.roomNumber || order.deviceNo || order.deviceId || '';

            // 获取状态
            var status = order.payStatus === 1 ? '已完成' : statusMap[order.status || 0] || '未知状态';

            // 获取门店名称
            var storeName = _this3.getStoreName(order.storeName || order.shopName || '未知门店');

            // 返回格式化的订单显示文本
            return "".concat(dateStr, " - ").concat(storeName, " (").concat(roomNumber, ") [").concat(status, "]");
          } catch (innerError) {
            console.error('处理单个订单数据出错:', innerError, order);
            return '订单数据处理错误';
          }
        })));
      } catch (error) {
        console.error('生成订单显示列表出错:', error);
        this.orderDisplays = ['请选择订单'];
      }
    },
    // 处理门店名称，移除"今夜城堡 - "前缀
    getStoreName: function getStoreName(fullName) {
      if (!fullName) return '未知门店';
      return String(fullName).replace('今夜城堡 - ', '');
    },
    onOrderChange: function onOrderChange(e) {
      this.orderIndex = e.detail.value;
      // 如果选择了新订单，清除之前传入的orderId
      if (this.orderIndex > 0) {
        this.orderId = null;
      }
    },
    toggleIssueType: function toggleIssueType(value) {
      var index = this.selectedIssueTypes.indexOf(value);
      if (index !== -1) {
        this.selectedIssueTypes.splice(index, 1);
      } else {
        this.selectedIssueTypes.push(value);
      }
    },
    chooseImage: function chooseImage() {
      var _this4 = this;
      uni.chooseImage({
        count: 3 - this.uploadedImages.length,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: function success(res) {
          // 添加选择的图片
          _this4.uploadedImages = [].concat((0, _toConsumableArray2.default)(_this4.uploadedImages), (0, _toConsumableArray2.default)(res.tempFilePaths));
        }
      });
    },
    removeImage: function removeImage(index) {
      this.uploadedImages.splice(index, 1);
    },
    // 上传图片（带备用方案）
    uploadImages: function uploadImages() {
      var _this5 = this;
      return new Promise(function (resolve, reject) {
        try {
          // 如果没有图片需要上传，直接返回空数组
          if (_this5.uploadedImages.length === 0) {
            resolve([]);
            return;
          }

          // 检查API是否可用
          if (!_this5.$api || !_this5.$api.report || typeof _this5.$api.report.uploadImage !== 'function') {
            console.error('API对象不存在或report.uploadImage方法不可用，尝试备用上传方法');
            // 如果主API不可用，尝试使用备用方法上传所有图片
            var fallbackPromises = _this5.uploadedImages.map(function (imagePath) {
              return _this5.uploadImageFallback(imagePath);
            });
            Promise.all(fallbackPromises).then(function (imageUrls) {
              console.log('所有图片通过备用方法上传成功:', imageUrls);
              _this5.uploadedImageUrls = imageUrls;
              resolve(imageUrls);
            }).catch(function (err) {
              console.error('备用上传方法失败:', err);
              reject(err);
            });
            return;
          }
          var uploadPromises = _this5.uploadedImages.map(function (imagePath) {
            // 先尝试使用专用的反馈图片上传API
            return _this5.$api.report.uploadImage(imagePath).then(function (res) {
              console.log('图片上传响应:', res);
              // 检查响应是否有效
              if (!res || !res.data) {
                throw new Error('上传图片响应无效');
              }

              // 从响应中获取上传后的图片URL
              // 适配不同的返回格式
              if (res.data && res.data.url) {
                return res.data.url;
              } else if (res.data && res.data.imageUrl) {
                return res.data.imageUrl;
              } else if (typeof res.data === 'string') {
                // 如果直接返回了URL字符串
                return res.data;
              } else {
                console.error('上传图片返回格式异常:', res);
                throw new Error('上传图片失败，返回数据格式不正确');
              }
            }).catch(function (err) {
              console.error('专用API上传失败，尝试通用上传API:', err);
              // 如果专用API失败，尝试使用通用上传API
              return _this5.uploadImageFallback(imagePath);
            });
          });
          Promise.all(uploadPromises).then(function (imageUrls) {
            console.log('所有图片上传成功:', imageUrls);
            _this5.uploadedImageUrls = imageUrls;
            resolve(imageUrls);
          }).catch(function (err) {
            console.error('上传图片失败:', err);
            reject(err);
          });
        } catch (error) {
          console.error('上传图片过程中发生未捕获的错误:', error);
          reject(error);
        }
      });
    },
    // 备用上传方法
    uploadImageFallback: function uploadImageFallback(imagePath) {
      var _this6 = this;
      return new Promise(function (resolve, reject) {
        try {
          if (!imagePath) {
            console.error('备用上传方法：图片路径无效');
            reject(new Error('图片路径无效'));
            return;
          }

          // 尝试使用通用上传API
          if (_this6.$api && _this6.$api.upload && typeof _this6.$api.upload.file === 'function') {
            console.log('尝试使用通用上传API');
            _this6.$api.upload.file(imagePath, 'feedback').then(function (res) {
              console.log('通用API上传响应:', res);
              if (!res || !res.data) {
                throw new Error('通用上传API响应无效');
              }
              if (res.data && res.data.url) {
                return resolve(res.data.url);
              } else if (res.data && res.data.imageUrl) {
                return resolve(res.data.imageUrl);
              } else if (typeof res.data === 'string') {
                return resolve(res.data);
              } else {
                throw new Error('通用上传API返回格式不正确');
              }
            }).catch(function (err) {
              console.error('通用上传API也失败:', err);
              // 最后尝试使用uni.uploadFile直接上传
              _this6.uploadImageDirect(imagePath).then(resolve).catch(reject);
            });
          } else {
            // 如果没有通用上传API，直接使用uni.uploadFile
            console.log('没有通用上传API，直接使用uni.uploadFile');
            _this6.uploadImageDirect(imagePath).then(resolve).catch(reject);
          }
        } catch (error) {
          console.error('备用上传方法中发生未捕获的错误:', error);
          // 尝试最后的直接上传方法
          try {
            _this6.uploadImageDirect(imagePath).then(resolve).catch(reject);
          } catch (finalError) {
            console.error('所有上传方法都失败:', finalError);
            reject(finalError);
          }
        }
      });
    },
    // 直接使用uni.uploadFile上传
    uploadImageDirect: function uploadImageDirect(imagePath) {
      return new Promise(function (resolve, reject) {
        try {
          if (!imagePath) {
            console.error('直接上传方法：图片路径无效');
            reject(new Error('图片路径无效'));
            return;
          }
          console.log('尝试直接使用uni.uploadFile上传');

          // 检查uni对象是否可用
          if (typeof uni === 'undefined' || typeof uni.uploadFile !== 'function') {
            console.error('uni对象不存在或uploadFile方法不可用');
            reject(new Error('上传功能不可用'));
            return;
          }
          uni.uploadFile({
            url: '/api/upload/image',
            filePath: imagePath,
            name: 'file',
            formData: {
              type: 'feedback'
            },
            success: function success(res) {
              console.log('直接上传响应:', res);
              try {
                if (!res || !res.data) {
                  throw new Error('上传响应无效');
                }
                var data = JSON.parse(res.data);
                if (data.code === 200 && data.data) {
                  if (data.data.url) {
                    resolve(data.data.url);
                  } else if (data.data.imageUrl) {
                    resolve(data.data.imageUrl);
                  } else if (typeof data.data === 'string') {
                    resolve(data.data);
                  } else {
                    reject(new Error('直接上传返回格式不正确'));
                  }
                } else {
                  reject(new Error(data.message || '直接上传失败'));
                }
              } catch (e) {
                console.error('解析上传响应失败:', e);
                reject(new Error('解析上传响应失败'));
              }
            },
            fail: function fail(err) {
              console.error('直接上传失败:', err);
              reject(err);
            }
          });
        } catch (error) {
          console.error('直接上传方法中发生未捕获的错误:', error);
          reject(error);
        }
      });
    },
    // 检查网络状态
    checkNetworkStatus: function checkNetworkStatus() {
      return new Promise(function (resolve, reject) {
        uni.getNetworkType({
          success: function success(res) {
            console.log('当前网络状态:', res);
            if (res.networkType === 'none') {
              uni.showToast({
                title: '网络连接不可用，请检查网络设置',
                icon: 'none',
                duration: 3000
              });
              reject(new Error('网络连接不可用'));
            } else {
              resolve(res.networkType);
            }
          },
          fail: function fail(err) {
            console.error('获取网络状态失败:', err);
            // 即使获取网络状态失败，也继续尝试提交
            resolve('unknown');
          }
        });
      });
    },
    // 提交异常报告
    submitReport: function submitReport() {
      var _this7 = this;
      try {
        console.log('开始提交异常报告');
        if (!this.isFormValid) {
          console.log('表单验证失败，无法提交');
          uni.showToast({
            title: '请完善表单信息',
            icon: 'none'
          });
          return;
        }

        // 检查API是否可用
        if (!this.$api || !this.$api.report || typeof this.$api.report.submit !== 'function') {
          console.error('API对象不存在或report.submit方法不可用');
          uni.showToast({
            title: 'API不可用，无法提交',
            icon: 'none'
          });
          return;
        }

        // 设置提交状态
        this.submitting = true;

        // 显示加载中
        uni.showLoading({
          title: '提交中...',
          mask: true // 添加遮罩防止重复点击
        });

        // 先检查网络状态
        this.checkNetworkStatus().then(function () {
          console.log('网络状态检查通过，开始上传图片');
          // 网络正常，上传图片
          return _this7.uploadImages();
        }).then(function (imageUrls) {
          console.log('图片上传成功，准备提交数据', imageUrls);
          // 获取选中的问题类型
          var issueType = 1; // 默认为设备问题

          // 如果选中了多个问题类型，优先级：其他问题 > 订单问题 > 设备问题
          if (_this7.selectedIssueTypes.includes('other')) {
            issueType = 3; // 其他问题
          }

          // 获取当前选中的订单信息
          var currentOrder = null;
          if (_this7.orderIndex > 0 && _this7.orderList && _this7.orderList.length > 0 && _this7.orderIndex <= _this7.orderList.length) {
            currentOrder = _this7.orderList[_this7.orderIndex - 1];
            if (!currentOrder) {
              console.warn('选中的订单索引有效，但订单对象为空');
            }
          }

          // 获取订单ID，优先使用传入的orderId，其次使用选中的订单ID
          var orderId = _this7.orderId || (currentOrder ? currentOrder.orderId || currentOrder.id || '' : '');

          // 获取设备ID，优先使用传入的deviceId，其次使用订单中的deviceId或deviceNo
          var deviceId = _this7.deviceId;
          if (!deviceId && currentOrder) {
            if (currentOrder.deviceId) {
              // 如果是数字字符串，转换为数字
              deviceId = /^\d+$/.test(currentOrder.deviceId) ? parseInt(currentOrder.deviceId) : currentOrder.deviceId;
            } else if (currentOrder.deviceNo) {
              deviceId = /^\d+$/.test(currentOrder.deviceNo) ? parseInt(currentOrder.deviceNo) : currentOrder.deviceNo;
            }
          }

          // 获取订单状态
          var orderStatus = '';
          if (currentOrder) {
            var statusMap = {
              0: '未支付',
              1: '进行中',
              2: '已完成',
              3: '已取消'
            };
            orderStatus = currentOrder.payStatus === 1 ? '已完成' : statusMap[currentOrder.status || 0] || '未知状态';
          }
          console.log('提交异常报告 - 当前订单:', currentOrder);
          console.log('提交异常报告 - 使用订单ID:', orderId);
          console.log('提交异常报告 - 使用设备ID:', deviceId);

          // 准备异常报告数据
          var reportData = {
            orderId: orderId,
            deviceId: deviceId,
            issueType: issueType,
            content: "\u95EE\u9898\u7C7B\u578B\uFF1A".concat(_this7.selectedIssueTypes.map(function (type) {
              var issueType = _this7.issueTypes.find(function (item) {
                return item.value === type;
              });
              return issueType ? issueType.label : '';
            }).join('、'), "\n\n").concat(orderStatus ? "\u8BA2\u5355\u72B6\u6001\uFF1A".concat(orderStatus, "\n\n") : '').concat(_this7.description),
            images: imageUrls,
            contactInfo: uni.getStorageSync('userInfo') ? uni.getStorageSync('userInfo').phone || '' : ''
          };
          console.log('提交异常报告数据:', reportData);

          // 提交异常报告
          return _this7.$api.report.submit(reportData);
        }).then(function (res) {
          uni.hideLoading();
          console.log('异常报告提交响应:', res);

          // 检查返回结果
          if (res.code && res.code !== 200) {
            throw new Error(res.message || '提交失败');
          }

          // 显示成功提示
          uni.showModal({
            title: '提交成功',
            content: '感谢您的反馈，我们会尽快处理您的问题。客服将在15分钟内与您联系，请保持电话畅通。',
            showCancel: false,
            confirmText: '确定',
            success: function success(res) {
              if (res.confirm) {
                // 返回上一页
                uni.navigateBack();
              }
            }
          });
        }).catch(function (err) {
          console.error('提交异常报告失败:', err);
          uni.hideLoading();

          // 显示错误信息
          var errorMsg = '提交失败，请稍后重试';
          if (err && err.message) {
            if (err.message.includes('上传图片失败')) {
              errorMsg = '图片上传失败，请重新选择图片';
            } else if (err.message.includes('网络连接不可用')) {
              errorMsg = '网络连接不可用，请检查网络设置后重试';
            } else if (typeof err.message === 'string') {
              errorMsg = err.message;
            }
          }

          // 检查网络连接问题
          if (err && err.errMsg && err.errMsg.includes('request:fail')) {
            errorMsg = '网络连接失败，请检查网络设置后重试';
          }

          // 检查服务器错误
          if (err && err.statusCode) {
            if (err.statusCode === 401) {
              errorMsg = '登录已过期，请重新登录';
              // 跳转到登录页
              uni.navigateTo({
                url: '/pages/login/login'
              });
            } else if (err.statusCode >= 500) {
              errorMsg = '服务器错误，请稍后重试';
            }
          }
          uni.showToast({
            title: errorMsg,
            icon: 'none',
            duration: 3000
          });
        }).finally(function () {
          _this7.submitting = false;
        });
      } catch (error) {
        console.error('提交过程中发生未捕获的错误:', error);
        uni.hideLoading();
        this.submitting = false;
        uni.showToast({
          title: '提交过程发生错误，请稍后重试',
          icon: 'none',
          duration: 3000
        });
      }
    },
    // 处理提交按钮点击事件
    handleSubmit: function handleSubmit(e) {
      var _this8 = this;
      // 阻止事件冒泡
      if (e && typeof e.stopPropagation === 'function') {
        e.stopPropagation();
      }
      console.log('提交按钮被点击', new Date().toISOString());
      console.log('表单验证状态:', this.isFormValid);
      console.log('提交中状态:', this.submitting);
      console.log('当前平台:', this.platform);
      console.log('订单选择:', this.orderIndex > 0 ? "\u5DF2\u9009\u62E9\u7B2C".concat(this.orderIndex, "\u4E2A\u8BA2\u5355") : '未选择订单');
      console.log('问题类型:', this.selectedIssueTypes);
      console.log('描述长度:', this.description ? this.description.trim().length : 0);

      // 如果正在提交中，阻止重复提交
      if (this.submitting) {
        console.log('正在提交中，阻止重复提交');
        uni.showToast({
          title: '正在提交中，请稍候...',
          icon: 'none'
        });
        return;
      }

      // 表单验证
      if (!this.isFormValid) {
        var errorMsg = '请完善表单信息';
        if (this.orderIndex <= 0 && !this.orderId) {
          errorMsg = '请先选择一个订单';
        } else if (this.selectedIssueTypes.length === 0) {
          errorMsg = '请选择至少一种异常类型';
        } else if (!this.description || this.description.trim().length < 10) {
          errorMsg = '问题描述至少需要10个字符';
        }
        console.log('表单验证失败，原因:', errorMsg);
        uni.showToast({
          title: errorMsg,
          icon: 'none',
          duration: 2000
        });
        return;
      }

      // 根据平台选择不同的提交方式
      console.log('开始调用提交方法');
      if (this.platform === 'ios') {
        // iOS平台使用特殊处理
        this.submitReportForIOS();
      } else {
        // 其他平台使用标准方法
        setTimeout(function () {
          _this8.submitReport();
        }, 100); // 延迟100ms调用，避免可能的事件冲突
      }
    },
    // iOS平台特定的提交方法
    submitReportForIOS: function submitReportForIOS() {
      var _this9 = this;
      try {
        console.log('iOS平台特定提交方法');

        // 检查API是否可用
        if (!this.$api || !this.$api.report || typeof this.$api.report.submit !== 'function') {
          console.error('API对象不存在或report.submit方法不可用');
          uni.showToast({
            title: 'API不可用，无法提交',
            icon: 'none'
          });
          return;
        }

        // 设置提交状态
        this.submitting = true;

        // 显示加载中
        uni.showLoading({
          title: '提交中...',
          mask: true // 添加遮罩防止重复点击
        });

        // 先检查网络状态
        uni.getNetworkType({
          success: function success(res) {
            if (res.networkType === 'none') {
              uni.hideLoading();
              _this9.submitting = false;
              uni.showToast({
                title: '网络连接不可用，请检查网络设置',
                icon: 'none',
                duration: 3000
              });
              return;
            }

            // 网络正常，上传图片
            _this9.uploadImages().then(function (imageUrls) {
              console.log('图片上传成功，准备提交数据', imageUrls);

              // 获取选中的问题类型
              var issueType = 1; // 默认为设备问题
              if (_this9.selectedIssueTypes.includes('other')) {
                issueType = 3; // 其他问题
              }

              // 获取当前选中的订单信息
              var currentOrder = null;
              if (_this9.orderIndex > 0 && _this9.orderList && _this9.orderList.length > 0 && _this9.orderIndex <= _this9.orderList.length) {
                currentOrder = _this9.orderList[_this9.orderIndex - 1];
                if (!currentOrder) {
                  console.warn('iOS方法中：选中的订单索引有效，但订单对象为空');
                }
              }

              // 获取订单ID
              var orderId = _this9.orderId || (currentOrder ? currentOrder.orderId || currentOrder.id || '' : '');

              // 获取设备ID
              var deviceId = _this9.deviceId;
              if (!deviceId && currentOrder) {
                if (currentOrder.deviceId) {
                  deviceId = /^\d+$/.test(currentOrder.deviceId) ? parseInt(currentOrder.deviceId) : currentOrder.deviceId;
                } else if (currentOrder.deviceNo) {
                  deviceId = /^\d+$/.test(currentOrder.deviceNo) ? parseInt(currentOrder.deviceNo) : currentOrder.deviceNo;
                }
              }

              // 获取订单状态
              var orderStatus = '';
              if (currentOrder) {
                var statusMap = {
                  0: '未支付',
                  1: '进行中',
                  2: '已完成',
                  3: '已取消'
                };
                orderStatus = currentOrder.payStatus === 1 ? '已完成' : statusMap[currentOrder.status || 0] || '未知状态';
              }

              // 准备异常报告数据
              var reportData = {
                orderId: orderId,
                deviceId: deviceId,
                issueType: issueType,
                content: "\u95EE\u9898\u7C7B\u578B\uFF1A".concat(_this9.selectedIssueTypes.map(function (type) {
                  var issueType = _this9.issueTypes.find(function (item) {
                    return item.value === type;
                  });
                  return issueType ? issueType.label : '';
                }).join('、'), "\n\n").concat(orderStatus ? "\u8BA2\u5355\u72B6\u6001\uFF1A".concat(orderStatus, "\n\n") : '').concat(_this9.description),
                images: imageUrls,
                contactInfo: uni.getStorageSync('userInfo') ? uni.getStorageSync('userInfo').phone || '' : ''
              };
              console.log('提交异常报告数据:', reportData);

              // 提交异常报告
              _this9.$api.report.submit(reportData).then(function (res) {
                uni.hideLoading();
                console.log('异常报告提交响应:', res);

                // 检查返回结果
                if (res.code && res.code !== 200) {
                  throw new Error(res.message || '提交失败');
                }

                // 显示成功提示
                uni.showModal({
                  title: '提交成功',
                  content: '感谢您的反馈，我们会尽快处理您的问题。客服将在15分钟内与您联系，请保持电话畅通。',
                  showCancel: false,
                  confirmText: '确定',
                  success: function success(res) {
                    if (res.confirm) {
                      // 返回上一页
                      uni.navigateBack();
                    }
                  }
                });
              }).catch(function (err) {
                _this9.handleSubmitError(err);
              }).finally(function () {
                _this9.submitting = false;
              });
            }).catch(function (err) {
              _this9.handleSubmitError(err);
              _this9.submitting = false;
            });
          },
          fail: function fail() {
            uni.hideLoading();
            _this9.submitting = false;
            uni.showToast({
              title: '网络状态检查失败，请稍后重试',
              icon: 'none',
              duration: 3000
            });
          }
        });
      } catch (error) {
        console.error('iOS提交过程中发生未捕获的错误:', error);
        uni.hideLoading();
        this.submitting = false;
        uni.showToast({
          title: '提交过程发生错误，请稍后重试',
          icon: 'none',
          duration: 3000
        });
      }
    },
    // 处理提交错误
    handleSubmitError: function handleSubmitError(err) {
      try {
        console.error('提交异常报告失败:', err);

        // 确保加载状态被清除
        uni.hideLoading();

        // 显示错误信息
        var errorMsg = '提交失败，请稍后重试';

        // 处理不同类型的错误
        if (err) {
          // 处理Error对象或带message属性的对象
          if (err.message) {
            if (err.message.includes('上传图片失败')) {
              errorMsg = '图片上传失败，请重新选择图片';
            } else if (err.message.includes('网络连接不可用')) {
              errorMsg = '网络连接不可用，请检查网络设置后重试';
            } else if (err.message.includes('API不可用')) {
              errorMsg = '服务暂时不可用，请稍后重试';
            } else if (typeof err.message === 'string') {
              // 限制错误消息长度，避免过长的错误信息
              errorMsg = err.message.length > 50 ? err.message.substring(0, 50) + '...' : err.message;
            }
          }

          // 处理网络请求错误
          if (err.errMsg) {
            if (err.errMsg.includes('request:fail')) {
              errorMsg = '网络连接失败，请检查网络设置后重试';
            } else if (err.errMsg.includes('timeout')) {
              errorMsg = '请求超时，请稍后重试';
            }
          }

          // 处理HTTP状态码错误
          if (err.statusCode) {
            if (err.statusCode === 401) {
              errorMsg = '登录已过期，请重新登录';
              // 跳转到登录页
              setTimeout(function () {
                uni.navigateTo({
                  url: '/pages/login/login'
                });
              }, 1500);
            } else if (err.statusCode === 403) {
              errorMsg = '您没有权限执行此操作';
            } else if (err.statusCode === 404) {
              errorMsg = '请求的资源不存在';
            } else if (err.statusCode >= 500) {
              errorMsg = '服务器错误，请稍后重试';
            }
          }

          // 处理响应数据中的错误信息
          if (err.data) {
            if (err.data.message) {
              errorMsg = err.data.message;
            } else if (err.data.msg) {
              errorMsg = err.data.msg;
            } else if (typeof err.data === 'string' && err.data.length < 50) {
              errorMsg = err.data;
            }
          }
        }

        // 显示错误提示
        uni.showToast({
          title: errorMsg,
          icon: 'none',
          duration: 3000
        });
      } catch (error) {
        console.error('处理错误信息时发生异常:', error);
        uni.showToast({
          title: '提交失败，请稍后重试',
          icon: 'none',
          duration: 3000
        });
      }
    },
    // 检测设备平台
    detectPlatform: function detectPlatform() {
      var _this10 = this;
      try {
        // 在微信小程序环境中
        if (typeof wx !== 'undefined') {
          // 优先使用getSystemInfoSync
          try {
            var sysInfo = wx.getSystemInfoSync();
            this.platform = sysInfo.platform;
            console.log('当前设备平台(getSystemInfoSync):', this.platform);
            return;
          } catch (e) {
            console.error('getSystemInfoSync失败:', e);
          }

          // 尝试使用getAppBaseInfo
          try {
            if (wx.getAppBaseInfo) {
              var info = wx.getAppBaseInfo();
              this.platform = info.platform;
              console.log('当前设备平台(getAppBaseInfo):', this.platform);
              return;
            }
          } catch (e) {
            console.error('getAppBaseInfo失败:', e);
          }
        }

        // 使用uni-app的API作为备选
        uni.getSystemInfo({
          success: function success(res) {
            _this10.platform = res.platform || 'unknown';
            console.log('当前设备平台(uni.getSystemInfo):', _this10.platform);
          },
          fail: function fail(err) {
            console.error('获取系统信息失败:', err);
            _this10.platform = 'unknown';
            console.log('设置默认平台: unknown');
          }
        });
      } catch (err) {
        console.error('检测平台出错:', err);
        this.platform = 'unknown';
        console.log('设置默认平台: unknown');
      }
    }
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"]))

/***/ }),

/***/ 91:
/*!*******************************************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/report/report.vue?vue&type=style&index=0&lang=css& ***!
  \*******************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_report_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--6-oneOf-1-2!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src??ref--6-oneOf-1-3!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./report.vue?vue&type=style&index=0&lang=css& */ 92);
/* harmony import */ var _HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_report_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_report_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_report_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_report_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_report_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 92:
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--6-oneOf-1-2!./node_modules/postcss-loader/src??ref--6-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/report/report.vue?vue&type=style&index=0&lang=css& ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[85,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/report/report.js.map