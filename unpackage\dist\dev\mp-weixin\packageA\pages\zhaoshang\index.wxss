


















































































































































/* CSS变量定义 */
page {
	--primary-light: #A875FF;
	--neon-pink: #ff36f9;
	--form-purple: #5433e0;
	--border-color: rgba(165, 166, 185, 0.51);
	--text-color: #333333;
	--placeholder-color: rgba(153, 153, 153, 0.6);
}
/* 页面基础样式 */
.page-zhaoshang {
	width: 100%;
	height: 100vh; /* 使用固定高度 */
	box-sizing: border-box;
	position: relative;
	margin: 0;
	padding: 0;
}
/* 页面背景样式 */
.page-background {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 0;
}
/* 背景图片样式 */
.background-image {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 0;
	object-fit: cover;
}
.gradient-overlay {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: linear-gradient(to bottom, 
		rgba(224, 168, 51, 0.7) 0%, 
		rgba(230, 149, 15, 0.7) 50%,
		rgba(240, 155, 25, 0.7) 100%);
	backdrop-filter: blur(5px);
	-webkit-backdrop-filter: blur(5px);
	z-index: 1;
	pointer-events: none;
}
/* 顶部状态栏占位 */
.status-bar {
	width: 100%;
	height: 25px;
	position: fixed;
	top: 0;
	z-index: 100;
	background-color: rgba(224, 168, 51, 0.8); /* 金色系，与页面风格一致 */
}
.ios-device .status-bar {
	height: 44px; /* 增加iOS设备的状态栏高度，为灵动岛留出空间 */
}
/* 内容包装器 */
.content-wrapper {
	position: relative;
	z-index: 2;
	width: 100%;
	height: 100vh; /* 设置高度 */
	box-sizing: border-box;
}
/* 顶部图片样式 */
.top-image {
	width: 100%;
	display: block;
	flex-shrink: 0;
	margin-top: -10rpx; /* 添加负边距使图片上移 */
	/* 移除色相旋转滤镜，使用原始颜色 */
}
/* iOS设备特定样式 */
.ios-device .top-image {
	margin-top: 60rpx; /* 将负边距改为正边距，使图片向下移动避免被灵动岛挡住 */
	margin-bottom: 0;
}
/* 表单容器 */
.form-container {
	width: 100%;
	padding: 30rpx 40rpx;
	background: rgba(255, 255, 255, 1); /* 纯白色背景 */
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
	box-sizing: border-box;
	margin-bottom: 0;
}
/* 表单标题 */
.form-title {
	font-size: 40rpx;
	color: #5433e0; /* 从金色系改回紫色系 */
	font-weight: bold;
	text-align: center;
	margin: 30rpx 0 40rpx;
}
/* 表单项容器 */
.form-item {
	margin-bottom: 40rpx;
	width: 100%;
}
/* 标签容器 */
.label-container {
	display: flex;
	flex-direction: row;
	align-items: center;
	margin-bottom: 10rpx;
}
/* 必填星号 */
.required {
	color: #ba4d67;
	font-size: 21rpx;
	margin-left: 5rpx;
}
.form-label {
	font-size: 24rpx;
	font-weight: normal;
	color: #444950;
}
.form-input {
	width: 100%;
	height: 78rpx;
	background: #ffffff;
	border: 1px solid rgba(165, 166, 185, 0.51);
	border-radius: 10rpx;
	padding: 0 30rpx;
	font-size: 28rpx;
	color: #333333;
	box-sizing: border-box;
}
/* iOS设备特定样式 - 输入框 */
.ios-device .form-input {
	height: 70rpx; /* iOS设备上更小的高度 */
}
.placeholder-style {
	color: rgba(153, 153, 153, 0.6);
}
/* 提交按钮 */
.submit-button {
	width: 100%;
	height: 80rpx;
	background: linear-gradient(135deg, #6a4dff, #9b4dff); /* 从金色系改回紫色渐变 */
	border-radius: 40rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-top: 60rpx;
	margin-bottom: 30rpx;
	box-shadow: 0 8rpx 16rpx rgba(155, 77, 255, 0.3); /* 调整阴影颜色为紫色系 */
}
.submit-text {
	font-size: 32rpx;
	color: #ffffff;
	font-weight: 500;
}
/* 底部图片样式 */
.bottom-image {
	width: 100%;
	display: block;
}
/* 返回按钮 */
.floating-back {
	position: fixed;
	left: 20rpx;
	width: 70rpx;
	height: 70rpx;
	border-radius: 50%;
	background-color: rgba(255, 197, 84, 0.15); /* 调整为金色系 */
	border: 1px solid rgba(255, 197, 84, 0.3); /* 调整为金色系 */
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 10;
}
.floating-back .material-icons {
	font-size: 48rpx;
	color: #ffffff;
}
/* Material Icons 字体 */
@font-face {
	font-family: 'Material Icons';
	font-style: normal;
	font-weight: 400;
	src: url(https://fonts.gstatic.com/s/materialicons/v139/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format('woff2');
}
.material-icons {
	font-family: 'Material Icons';
	font-weight: normal;
	font-style: normal;
	font-size: 48rpx;
	line-height: 1;
	letter-spacing: normal;
	text-transform: none;
	display: inline-block;
	white-space: nowrap;
	word-wrap: normal;
	direction: ltr;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

