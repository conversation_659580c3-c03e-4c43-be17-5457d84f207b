<template>
	<view class="tab-bar"
		:class="{
			'safe-area-inset-bottom': hasBottomSafeArea,
			'android-tab-bar': isAndroid
		}"
		:style="tabBarStyle">
		<view class="tab-bar-border"></view>
		<view v-for="(item, index) in list" :key="index"
			class="tab-bar-item"
			:class="{
				'active': isActive(item.pagePath),
				'android-tab-item': isAndroid
			}"
			@tap="switchTab(item.pagePath)">
			<text class="material-icons" :class="{'android-icon': isAndroid}">{{ item.icon }}</text>
			<text class="tab-text" :class="{'android-text': isAndroid}">{{ item.text }}</text>
		</view>
	</view>
</template>

<script>
	export default {
		name: "custom-tab-bar",
		data() {
			return {
				currentPath: '',
				hasBottomSafeArea: false,
				isAndroid: false,
				systemInfo: null,
				tabBarStyle: {
					opacity: 0 // 初始隐藏，避免闪烁
				},
				list: [
					{
						pagePath: "/pages/index/index",
						text: "首页",
						icon: "meeting_room"
					},
					{
						pagePath: "/pages/profile/profile",
						text: "我的",
						icon: "diamond"
					}
				],
				// 标记是否为微信小程序环境
				isMpWeixin: false,
				// 路径检查定时器
				pathCheckTimer: null
			}
		},
		created() {
			// 检测当前环境
			// #ifdef MP-WEIXIN
			this.isMpWeixin = true;
			// #endif
			
			// 初始化系统信息和安全区域检查
			try {
				// 获取系统信息
				this.systemInfo = uni.getSystemInfoSync();
				console.log('TabBar - 系统信息:', this.systemInfo);

				// 检测是否为安卓设备
				this.isAndroid = this.systemInfo.platform === 'android';

				// 尝试从App实例获取
				const app = getApp();
				if (app && app.globalData) {
					this.hasBottomSafeArea = app.globalData.isIphoneX;
				} else {
					// 回退到本地存储
					const isIPhoneX = uni.getStorageSync('isIPhoneX');
					this.hasBottomSafeArea = isIPhoneX;

					// 如果仍然没有，尝试检测系统信息
					if (this.hasBottomSafeArea === '') {
						const model = this.systemInfo.model;
						const screenHeight = this.systemInfo.screenHeight;
						const screenWidth = this.systemInfo.screenWidth;
						this.hasBottomSafeArea = /iPhone X|iPhone 11|iPhone 12|iPhone 13|iPhone 14|iPhone 15/.test(model) ||
							(screenHeight / screenWidth > 2 && this.systemInfo.platform === 'ios');

						// 存储结果供后续使用
						uni.setStorageSync('isIPhoneX', this.hasBottomSafeArea);
					}
				}
				console.log('底部安全区域检测结果:', this.hasBottomSafeArea);
				console.log('是否为安卓设备:', this.isAndroid);
			} catch(e) {
				console.error('获取安全区域信息失败', e);
			}
			
			// 延迟显示TabBar，避免闪烁
			setTimeout(() => {
				this.tabBarStyle.opacity = 1;
				this.updateCurrentPath();
				
				// 启动定时器，持续监测页面路径变化
				this.startPathCheck();
			}, 200);
		},
		onShow() {
			// 页面显示时更新当前路径
			this.updateCurrentPath();
			
			// 确保定时器在显示时启动
			this.startPathCheck();
		},
		onHide() {
			// 页面隐藏时停止定时器
			this.stopPathCheck();
		},
		beforeDestroy() {
			// 组件销毁前停止定时器
			this.stopPathCheck();
		},
		methods: {
			// 启动路径检查定时器
			startPathCheck() {
				// 先清除可能存在的定时器
				this.stopPathCheck();
				
				// 设置新的定时器，每200ms检查一次路径
				this.pathCheckTimer = setInterval(() => {
					this.updateCurrentPath();
				}, 200);
			},
			
			// 停止路径检查定时器
			stopPathCheck() {
				if (this.pathCheckTimer) {
					clearInterval(this.pathCheckTimer);
					this.pathCheckTimer = null;
				}
			},
			
			// 更新当前页面路径
			updateCurrentPath() {
				try {
					const pages = getCurrentPages();
					if (pages && pages.length > 0) {
						const currentPage = pages[pages.length - 1];
						const route = '/' + currentPage.route;
						
						// 只有当路径变化时才记录和更新
						if (this.currentPath !== route) {
							console.log('TabBar更新当前路径:', route);
							this.currentPath = route;
						}
					}
				} catch (e) {
					console.error('获取当前页面路径失败', e);
				}
			},
			
			// 判断是否为当前活动项
			isActive(pagePath) {
				// 首先尝试刷新当前路径
				this.updateCurrentPath();
				
				// 完全匹配路径
				if (this.currentPath === pagePath) {
					return true;
				}
				
				// 兼容不带斜杠的路径
				const normalizedPath = pagePath.replace(/^\//, '');
				if (this.currentPath === normalizedPath || this.currentPath === '/' + normalizedPath) {
					return true;
				}
				
				// 页面路径的简化比较（忽略查询参数等）
				const currentSimple = this.currentPath.split('?')[0];
				const targetSimple = pagePath.split('?')[0];
				if (currentSimple === targetSimple) {
					return true;
				}
				
				// 特殊处理招商页面 - 在招商页面时仍然高亮首页Tab
				if (this.currentPath.includes('/packageA/pages/zhaoshang/')) {
					if (pagePath === '/pages/index/index') {
						return true;
					}
				}
				
				return false;
			},
			
			switchTab(path) {
				// 如果已经在当前页面，不做任何操作
				if (this.isActive(path)) return;
				
				// 切换前先设置透明度为0，避免闪烁
				this.tabBarStyle.opacity = 0;
				
				// 立即更新当前路径
				const normalizedPath = path.replace(/^\//, '');
				this.currentPath = '/' + normalizedPath;
				
				// 微信小程序环境下不调用hideTabBar
				if (!this.isMpWeixin) {
					// 先隐藏TabBar，避免闪烁
					uni.hideTabBar({
						animation: false
					});
				}
				
				console.log('切换Tab到:', path);
				
				// 延迟一小段时间再跳转，确保状态更新
				setTimeout(() => {
					uni.switchTab({
						url: `/${normalizedPath}`,
						success: () => {
							console.log('Tab切换成功');
							
							// 强制更新当前路径
							setTimeout(() => {
								this.updateCurrentPath();
								
								// 恢复TabBar透明度
								setTimeout(() => {
									this.tabBarStyle.opacity = 1;
								}, 50);
							}, 50);
						},
						fail: (err) => {
							console.error('Tab切换失败', err);
							// 如果是非tabBar页面，尝试使用navigateTo
							uni.navigateTo({
								url: path,
								success: () => {
									// 强制更新当前路径
									setTimeout(() => {
										this.updateCurrentPath();
										
										// 恢复TabBar透明度
										setTimeout(() => {
											this.tabBarStyle.opacity = 1;
										}, 50);
									}, 50);
								}
							});
						}
					});
				}, 50);
			},
			// 更新选中状态的方法，由页面调用（为了向后兼容）
			setSelected(index) {
				// 获取对应路径
				const path = this.list[index] ? this.list[index].pagePath : '';
				if (path) {
					// 强制更新当前路径
					const normalizedPath = path.replace(/^\//, '');
					this.currentPath = '/' + normalizedPath;
					
					// 触发视图更新
					this.$forceUpdate();
				}
			}
		}
	}
</script>

<style scoped>
	/* Material Icons 字体 */
	@font-face {
		font-family: 'Material Icons';
		font-style: normal;
		font-weight: 400;
		src: url(https://fonts.gstatic.com/s/materialicons/v139/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format('woff2'),
			 url(https://fonts.googleapis.com/icon?family=Material+Icons) format('truetype');
		font-display: block;
	}
	
	:root {
		--primary: #8B5CF6;
		--primary-dark: #8B5CF6;
		--primary-light: #A875FF;
	}
	
	.tab-bar {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		height: 120rpx; /* 增加基础高度，适配安卓设备 */
		background: #FFFFFF;
		display: flex;
		flex-wrap: nowrap; /* 不换行 */
		z-index: 999;
		backdrop-filter: blur(10px);
		-webkit-backdrop-filter: blur(10px);
		box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
		border-top: 1px solid rgba(200, 200, 200, 0.3);
		transition: all 0.3s ease, opacity 0.3s ease;
		width: 100%; /* 确保宽度为100% */
		justify-content: space-around; /* 使用space-around而不是默认的stretch，减少中间空白 */
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom, 20rpx); /* 安卓下默认20rpx */
		min-height: 120rpx; /* 确保最小高度 */
	}

	/* 安卓设备专属样式优化 */
	.android-tab-bar {
		height: 160rpx !important; /* 安卓设备增加高度 */
		padding-bottom: 40rpx !important; /* 安卓设备增加底部内边距 */
		min-height: 160rpx !important; /* 确保安卓设备最小高度 */
	}
	
	/* 微信小程序环境下的安卓设备优化 */
	/* #ifdef MP-WEIXIN */
	.android-tab-bar {
		height: 170rpx !important; /* 微信小程序中安卓设备需要更大高度 */
		padding-bottom: 45rpx !important; /* 微信小程序中安卓设备需要更大底部内边距 */
		min-height: 170rpx !important; /* 确保微信小程序中安卓设备最小高度 */
	}

	.android-tab-item {
		height: 170rpx !important; /* 微信小程序中安卓设备项目高度 */
		margin-top: 25rpx !important; /* 微信小程序中安卓设备上边距 */
		padding-bottom: 20rpx !important; /* 微信小程序中安卓设备底部内边距 */
	}

	.android-icon {
		font-size: 60rpx !important; /* 微信小程序中安卓设备图标尺寸 */
		margin-top: 20rpx !important; /* 微信小程序中安卓设备图标上边距 */
	}

	.android-text {
		font-size: 32rpx !important; /* 微信小程序中安卓设备文字尺寸 */
		margin-top: 10rpx !important; /* 微信小程序中安卓设备文字上边距 */
		font-weight: 600 !important; /* 微信小程序中安卓设备文字加粗 */
	}
	/* #endif */

	.tab-bar.safe-area-inset-bottom {
		padding-bottom: env(safe-area-inset-bottom); /* 添加安全区域内边距 */
	}
	
	.safe-area-placeholder {
		width: 100%;
		height: env(safe-area-inset-bottom);
		background: #FFFFFF !important;
	}
	
	.tab-bar-border {
		position: absolute;
		left: 0;
		top: 0;
		width: 100%;
		height: 1px;
		background: rgba(200, 200, 200, 0.3);
		transform: scaleY(0.5);
	}
	
	.tab-bar-item {
		flex: 1 1 0; /* 平均分配宽度 */
		min-width: 120rpx; /* 设置最小宽度 */
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		color: rgba(100, 100, 100, 0.8);
		transition: all 0.3s cubic-bezier(0.19, 1, 0.22, 1);
		position: relative;
		padding-top: 0;
		padding-bottom: 0;
		margin-top: 15rpx; /* 增加上边距，适配更高的tab-bar */
		height: 120rpx; /* 增加项目高度与tab-bar一致 */
		min-height: 120rpx; /* 确保最小高度 */
	}

	/* 安卓设备TabBar项目样式优化 */
	.android-tab-item {
		height: 140rpx !important; /* 安卓设备增加项目高度 */
		margin-top: 20rpx !important; /* 安卓设备增加上边距 */
		padding-bottom: 12rpx !important; /* 安卓设备增加底部内边距 */
	}
	
	.material-icons {
		font-family: 'Material Icons';
		font-weight: normal;
		font-style: normal;
		font-size: 46rpx;
		line-height: 1;
		letter-spacing: normal;
		text-transform: none;
		display: block;
		white-space: nowrap;
		word-wrap: normal;
		direction: ltr;
		-webkit-font-smoothing: antialiased;
		-moz-osx-font-smoothing: grayscale;
		transition: all 0.3s ease;
		margin: 0 auto;
		text-align: center;
		text-shadow: 0 0 5rpx rgba(255, 255, 255, 0.3);
		margin-top: 8rpx; /* 调整图标上边距 */
	}

	/* 安卓设备图标样式优化 */
	.android-icon {
		font-size: 56rpx !important; /* 安卓设备增大图标尺寸 */
		margin-top: 16rpx !important; /* 安卓设备增加图标上边距 */
	}
	
	.tab-text {
		font-size: 24rpx; /* 减小字体大小 */
		margin-top: 0; /* 减少与图标的间距 */
		transition: all 0.3s ease;
		text-align: center;
		width: 100%;
		font-weight: 500;
		text-shadow: 0 0 5rpx rgba(255, 255, 255, 0.3);
	}

	/* 安卓设备文字样式优化 */
	.android-text {
		font-size: 28rpx !important; /* 安卓设备增大文字尺寸 */
		margin-top: 6rpx !important; /* 安卓设备增加与图标的间距 */
		font-weight: 600 !important; /* 安卓设备增加字重 */
	}
	
	.tab-bar-item.active {
		color: var(--primary-light, #A875FF);
		transform: translateY(0);
	}
	
	.tab-bar-item.active .material-icons {
		text-shadow: 0 0 10rpx rgba(168, 117, 255, 0.4);
		transform: scale(1.1);
	}
	
	.tab-bar-item.active .tab-text {
		font-weight: 600;
	}
	
	/* 添加按下效果 */
	.tab-bar-item:active {
		opacity: 0.8;
	}
</style> 