(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pages/scan/scan"],{

/***/ 100:
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--6-oneOf-1-2!./node_modules/postcss-loader/src??ref--6-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/scan/scan.vue?vue&type=style&index=0&lang=css& ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ }),

/***/ 93:
/*!*************************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/main.js?{"page":"pages%2Fscan%2Fscan"} ***!
  \*************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _scan = _interopRequireDefault(__webpack_require__(/*! ./pages/scan/scan.vue */ 94));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_scan.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 94:
/*!******************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/scan/scan.vue ***!
  \******************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _scan_vue_vue_type_template_id_1db32b26___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./scan.vue?vue&type=template&id=1db32b26& */ 95);
/* harmony import */ var _scan_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./scan.vue?vue&type=script&lang=js& */ 97);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _scan_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _scan_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _scan_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./scan.vue?vue&type=style&index=0&lang=css& */ 99);
/* harmony import */ var _HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 36);

var renderjs





/* normalize component */

var component = Object(_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _scan_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _scan_vue_vue_type_template_id_1db32b26___WEBPACK_IMPORTED_MODULE_0__["render"],
  _scan_vue_vue_type_template_id_1db32b26___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  null,
  null,
  false,
  _scan_vue_vue_type_template_id_1db32b26___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pages/scan/scan.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 95:
/*!*************************************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/scan/scan.vue?vue&type=template&id=1db32b26& ***!
  \*************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_scan_vue_vue_type_template_id_1db32b26___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./scan.vue?vue&type=template&id=1db32b26& */ 96);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_scan_vue_vue_type_template_id_1db32b26___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_scan_vue_vue_type_template_id_1db32b26___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_scan_vue_vue_type_template_id_1db32b26___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_scan_vue_vue_type_template_id_1db32b26___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 96:
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/scan/scan.vue?vue&type=template&id=1db32b26& ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 97:
/*!*******************************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/scan/scan.vue?vue&type=script&lang=js& ***!
  \*******************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_scan_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./scan.vue?vue&type=script&lang=js& */ 98);
/* harmony import */ var _HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_scan_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_scan_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_scan_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_scan_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_scan_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 98:
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/scan/scan.vue?vue&type=script&lang=js& ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _index = __webpack_require__(/*! @/utils/index.js */ 39);
var _api = _interopRequireDefault(__webpack_require__(/*! @/static/js/api.js */ 32));
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//

// 修复导出问题
var scanPage = {
  name: 'scan',
  data: function data() {
    return {
      scanning: false,
      isPageReady: false,
      scanAttempts: 0,
      maxScanAttempts: 3,
      processing: false
    };
  },
  onLoad: function onLoad() {
    var _this = this;
    console.log('扫码页面 onLoad');
    // 页面加载时标记为准备好
    this.isPageReady = true;

    // 延迟启动扫码，防止黑屏
    setTimeout(function () {
      // 开始扫码
      _this.startScan();
    }, 500);
  },
  onShow: function onShow() {
    var _this2 = this;
    console.log('扫码页面 onShow');
    // 页面显示时，如果准备好且未在扫码，则开始扫码
    if (this.isPageReady && !this.scanning) {
      setTimeout(function () {
        _this2.startScan();
      }, 300);
    }
  },
  onHide: function onHide() {
    console.log('扫码页面 onHide');
    // 页面隐藏时停止扫码
    this.stopScan();
  },
  onUnload: function onUnload() {
    console.log('扫码页面 onUnload');
    // 页面卸载时停止扫码并标记为未准备好
    this.stopScan();
    this.isPageReady = false;
  },
  // 监听页面返回按钮
  onBackPress: function onBackPress() {
    console.log('扫码页面 onBackPress');
    // 先停止扫码
    this.stopScan();
    return false; // 返回false，让系统执行默认的返回逻辑
  },

  methods: {
    // 返回上一页
    goBack: function goBack() {
      // 先停止扫码
      this.stopScan();

      // 延迟后返回，避免返回失败
      setTimeout(function () {
        // 尝试使用navigateBack返回
        uni.navigateBack({
          delta: 1,
          fail: function fail() {
            // 如果返回失败，则跳转到首页
            uni.switchTab({
              url: '/pages/index/index',
              fail: function fail() {
                // 如果跳转到首页也失败，则重启小程序
                uni.reLaunch({
                  url: '/pages/index/index'
                });
              }
            });
          }
        });
      }, 200);
    },
    // 开始扫码
    startScan: function startScan() {
      var _this3 = this;
      // 防止重复调用
      if (this.scanning) {
        console.log('已经在扫码中，忽略本次调用');
        return;
      }

      // 检查是否超过最大尝试次数
      if (this.scanAttempts >= this.maxScanAttempts) {
        console.log('已达到最大扫码尝试次数');
        uni.showModal({
          title: '提示',
          content: '多次扫码失败，请返回后重试',
          showCancel: false,
          success: function success() {
            _this3.goBack();
          }
        });
        return;
      }
      this.scanning = true;
      this.scanAttempts++;
      console.log("\u5F00\u59CB\u626B\u7801\uFF0C\u7B2C ".concat(this.scanAttempts, " \u6B21\u5C1D\u8BD5"));

      // 直接执行扫码，微信小程序会自动请求相机权限
      this.doScanCode();
    },
    // 执行扫码操作
    doScanCode: function doScanCode() {
      var _this4 = this;
      // 添加Loading提示
      uni.showLoading({
        title: '准备扫码...',
        mask: false
      });

      // 延迟执行扫码，避免黑屏问题
      setTimeout(function () {
        try {
          uni.hideLoading();
        } catch (e) {
          console.warn('隐藏loading失败:', e);
        }

        // 使用uni-app的扫码API
        uni.scanCode({
          scanType: ['qrCode'],
          success: function success(res) {
            console.log('扫码结果:', res);
            _this4.handleScanResult(res.result);
          },
          fail: function fail(err) {
            console.error('扫码失败:', err);
            _this4.scanning = false;

            // 判断是否是因为权限问题导致的失败
            if (err.errMsg && err.errMsg.includes('authorize')) {
              _this4.showPermissionDeniedTip('camera');
            } else {
              uni.showToast({
                title: '扫码失败，请重试',
                icon: 'none'
              });
            }
          },
          complete: function complete() {
            _this4.scanning = false;
          }
        });
      }, 300);
    },
    // 停止扫码
    stopScan: function stopScan() {
      console.log('停止扫码');
      this.scanning = false;
    },
    // 处理扫描结果
    handleScanResult: function handleScanResult(result) {
      console.log('扫描结果原始数据:', result);

      // 显示加载中
      uni.showLoading({
        title: '正在处理...',
        mask: true
      });

      // 解析扫描结果
      try {
        console.log('开始解析扫描结果');

        // 检查是否是URL格式的二维码
        if (result.startsWith('http')) {
          console.log('检测到URL格式二维码');
          // 解析URL参数
          try {
            var url = new URL(result);

            // 检查多种可能的参数名
            var id = url.searchParams.get('id') || url.searchParams.get('deviceId') || url.searchParams.get('device') || url.searchParams.get('deviceCode');

            // 检查URL路径中是否包含设备ID
            var pathId = null;
            if (!id && url.pathname) {
              // 尝试从路径中提取ID
              var pathParts = url.pathname.split('/').filter(function (part) {
                return part.length > 0;
              });
              if (pathParts.length > 0) {
                // 取最后一个路径部分作为可能的ID
                var lastPart = pathParts[pathParts.length - 1];
                // 如果是数字或看起来像设备ID的格式
                if (/^\d+$/.test(lastPart) || /^[a-zA-Z0-9-_]{4,}$/.test(lastPart)) {
                  pathId = lastPart;
                }
              }
            }

            // 使用参数中的ID或路径中的ID
            var deviceId = id || pathId;
            if (deviceId) {
              console.log('从URL中提取到设备ID:', deviceId);
              // 隐藏加载提示
              uni.hideLoading();
              // 直接跳转到设备页面，使用scanType=miniapp标识是小程序扫码
              uni.navigateTo({
                url: "/pages/scan/device?id=".concat(deviceId, "&scanType=miniapp"),
                success: function success() {
                  console.log('导航到设备页面成功');
                },
                fail: function fail(err) {
                  console.error('导航到设备页面失败:', err);
                  uni.showToast({
                    title: '导航到设备页面失败',
                    icon: 'none'
                  });
                }
              });
              return;
            } else if (url.host.includes('jycb') || result.includes('jycb')) {
              // 如果URL域名包含jycb但没有找到ID参数，可能是首页或其他页面
              console.log('检测到今夜城堡相关URL，但未找到设备ID');
              uni.hideLoading();
              uni.showToast({
                title: '未找到有效的设备信息',
                icon: 'none'
              });
              return;
            } else {
              console.log('URL中未找到设备ID参数，继续检查是否是蓝牙设备码');
            }
          } catch (err) {
            console.error('解析URL失败:', err);
            // 解析URL失败，继续检查是否是蓝牙设备码
          }
        }

        // 检查是否是有效的设备二维码
        var isValid = this.isValidDeviceCode(result);
        console.log('是否是有效的设备二维码:', isValid);
        if (isValid) {
          // 提取MAC地址
          var mac = this.extractMacFromCode(result);
          console.log('提取的MAC地址:', mac);
          if (mac) {
            console.log('准备初始化蓝牙环境');

            // 预初始化蓝牙环境
            _index.lockService.init().then(function (initResult) {
              console.log('蓝牙环境初始化结果:', initResult);

              // 导航到设备页面
              uni.navigateTo({
                url: "/pages/scan/device?mac=".concat(mac, "&scanType=bluetooth"),
                success: function success() {
                  console.log('导航到设备页面成功');
                  uni.hideLoading();
                },
                fail: function fail(err) {
                  console.error('导航到设备页面失败:', err);
                  uni.hideLoading();
                  uni.showToast({
                    title: '导航到设备页面失败',
                    icon: 'none'
                  });
                }
              });
            }).catch(function (err) {
              console.error('初始化蓝牙环境失败:', err);
              uni.hideLoading();
              uni.showToast({
                title: '初始化蓝牙环境失败，请确保蓝牙已开启',
                icon: 'none'
              });
            });
          } else {
            console.error('无法从二维码中提取MAC地址');
            uni.hideLoading();
            uni.showToast({
              title: '无效的设备二维码',
              icon: 'none'
            });
          }
        } else {
          console.error('无效的设备二维码格式');
          uni.hideLoading();
          uni.showToast({
            title: '无效的设备二维码',
            icon: 'none'
          });
        }
      } catch (err) {
        console.error('处理扫描结果失败:', err);
        uni.hideLoading();
        uni.showToast({
          title: '处理扫描结果失败',
          icon: 'none'
        });
      }
    },
    // 导航到设备页面
    navigateToDevicePage: function navigateToDevicePage(deviceId, scanType) {
      console.log("\u51C6\u5907\u5BFC\u822A\u5230\u8BBE\u5907\u9875\u9762, \u53C2\u6570: { ".concat(scanType === 'bluetooth' ? 'mac' : 'id', ": ").concat(deviceId, ", scanType: ").concat(scanType, " }"));

      // 构建URL参数
      var params = scanType === 'bluetooth' ? "mac=".concat(deviceId, "&scanType=bluetooth") : "id=".concat(deviceId, "&scanType=miniapp");
      uni.navigateTo({
        url: "/pages/scan/device?".concat(params),
        success: function success() {
          console.log('导航到设备页面成功');
          uni.hideLoading();
        },
        fail: function fail(err) {
          console.error('导航到设备页面失败:', err);
          uni.hideLoading();
          uni.showToast({
            title: '导航到设备页面失败',
            icon: 'none'
          });
        }
      });
    },
    // 检查是否是有效的设备二维码
    isValidDeviceCode: function isValidDeviceCode(code) {
      console.log('检查二维码是否有效:', code);
      // 检查是否包含MAC地址格式
      var isValid = /([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})|([0-9A-Fa-f]{12})/i.test(code);
      console.log('二维码有效性检查结果:', isValid);
      return isValid;
    },
    // 从二维码中提取MAC地址
    extractMacFromCode: function extractMacFromCode(code) {
      console.log('从二维码中提取MAC地址:', code);
      // 尝试匹配MAC地址格式
      var macMatch = code.match(/([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})|([0-9A-Fa-f]{12})/i);
      if (macMatch) {
        console.log('提取到MAC地址:', macMatch[0]);
        return macMatch[0];
      }
      console.log('未能从二维码中提取MAC地址');
      return null;
    },
    // 回退到传统方式处理二维码
    fallbackToTraditionalMethod: function fallbackToTraditionalMethod(result) {
      var _this5 = this;
      console.log('回退到传统方式处理二维码');

      // 显示加载提示
      uni.showLoading({
        title: '处理中...',
        mask: true
      });

      // 使用锁控制服务处理二维码
      _index.lockService.handleDeviceQRCode(result).then(function (deviceInfo) {
        console.log('设备绑定状态检查成功:', deviceInfo);

        // 创建订单
        return _index.lockService.createOrder();
      }).then(function (orderInfo) {
        console.log('创建订单成功:', orderInfo);
        try {
          uni.hideLoading();
        } catch (e) {
          console.warn('隐藏loading失败:', e);
        }
        _this5.processing = false;

        // 跳转到设备控制页面
        uni.redirectTo({
          url: "/pages/scan/device?mac=".concat(_index.lockService.deviceInfo.mac, "&orderId=").concat(orderInfo.orderId),
          fail: function fail(err) {
            console.error('跳转失败:', err);
            uni.showToast({
              title: '页面跳转失败',
              icon: 'none'
            });
          }
        });
      }).catch(function (err) {
        console.error('处理扫码结果失败:', err);
        try {
          uni.hideLoading();
        } catch (e) {
          console.warn('隐藏loading失败:', e);
        }
        _this5.processing = false;
        uni.showModal({
          title: '连接失败',
          content: err.message || '无法连接设备，请重试',
          showCancel: false,
          success: function success() {
            // 重新开始扫码
            setTimeout(function () {
              _this5.startScan();
            }, 500);
          }
        });
      });
    },
    // 检查权限
    checkPermission: function checkPermission(permissionType) {
      return new Promise(function (resolve) {
        // 微信小程序检查权限
        uni.getSetting({
          success: function success(res) {
            if (res.authSetting && res.authSetting["scope.".concat(permissionType)] !== false) {
              resolve(true);
            } else {
              resolve(false);
            }
          },
          fail: function fail() {
            resolve(false);
          }
        });
      });
    },
    // 请求权限
    requestPermission: function requestPermission(permissionType) {
      return new Promise(function (resolve, reject) {
        // 微信小程序请求权限
        uni.authorize({
          scope: "scope.".concat(permissionType),
          success: function success() {
            resolve();
          },
          fail: function fail() {
            reject();
          }
        });
      });
    },
    // 显示权限被拒绝提示
    showPermissionDeniedTip: function showPermissionDeniedTip(permissionType) {
      var _this6 = this;
      var permissionName = '';
      switch (permissionType) {
        case 'camera':
          permissionName = '相机';
          break;
        default:
          permissionName = permissionType;
      }
      uni.showModal({
        title: '权限提示',
        content: "\u9700\u8981".concat(permissionName, "\u6743\u9650\u624D\u80FD\u4F7F\u7528\u626B\u7801\u529F\u80FD\uFF0C\u8BF7\u5728\u8BBE\u7F6E\u4E2D\u5F00\u542F\u6743\u9650"),
        confirmText: '去设置',
        cancelText: '取消',
        success: function success(res) {
          if (res.confirm) {
            uni.openSetting({
              success: function success(settingRes) {
                console.log('打开设置页面成功');
              }
            });
          } else {
            _this6.goBack();
          }
        }
      });
    }
  }
};
var _default = scanPage;
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 99:
/*!***************************************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/scan/scan.vue?vue&type=style&index=0&lang=css& ***!
  \***************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_scan_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--6-oneOf-1-2!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src??ref--6-oneOf-1-3!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./scan.vue?vue&type=style&index=0&lang=css& */ 100);
/* harmony import */ var _HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_scan_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_scan_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_scan_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_scan_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_scan_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ })

},[[93,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/scan/scan.js.map