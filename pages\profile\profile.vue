<template>
	<view class="container page-profile">
		<!-- 页面背景 -->
		<view class="page-background">
			<!-- 背景图片 -->
			<image class="background-image" src="https://wuji1112-1312674791.cos.ap-guangzhou.myqcloud.com/static/shouye3.png" mode="aspectFill"></image>
			
			<!-- 磨砂效果叠加层 -->
			<view class="frosted-overlay"></view>
		</view>
		
		<!-- 顶部状态栏占位 -->
		<view class="status-bar safe-area-inset-top"></view>
		
		<!-- 页面内容 -->
		<view class="content">
			<!-- 个人信息区域 - 移除背景 -->
			<view class="profile-header">
				<view class="avatar-container">
					<view class="avatar-wrapper">
						<image class="avatar" :src="userInfo.avatar || '../../static/images/avatar.svg'" mode="aspectFill"></image>
					</view>
				</view>
				<view class="user-info">
					<view class="user-name-row">
						<text class="title-md">{{userInfo.nickname || '隐私用户'}}</text>
						<view class="status" :class="{'active': isLoggedIn}">{{isLoggedIn ? '已登录' : '未登录'}}</view>
					</view>
					<view class="text-tertiary">用户ID：{{userInfo.userId || '未登录'}}</view>
				</view>
			</view>
			
			<!-- 功能菜单 -->
			<view class="menu-section mt-md">
				
				<!-- 菜单列表 -->
				<view class="menu-list">
					<!-- 我的订单 -->
					<view class="menu-item" @click="navigateTo('/pages/orders/orders')">
						<view class="menu-icon">
							<text class="material-icons neon-pink">receipt_long</text>
						</view>
						<view class="menu-content">
							<view class="menu-title">我的订单</view>
							<view class="text-tertiary">已有{{userInfo.orderCount || 0}}个订单记录</view>
						</view>
						<view class="menu-arrow">
							<text class="material-icons text-tertiary">arrow_forward_ios</text>
						</view>
					</view>
					
					<!-- 招募合作 -->
					<view class="menu-item" @click="navigateTo('/packageA/pages/zhaoshang/index')">
						<view class="menu-icon">
							<text class="material-icons neon-blue">group_add</text>
						</view>
						<view class="menu-content">
							<view class="menu-title">招募合作</view>
							<view class="text-tertiary">0元加盟成为合伙人</view>
						</view>
						<view class="menu-arrow">
							<text class="material-icons text-tertiary">arrow_forward_ios</text>
						</view>
					</view>
					
					<!-- 设备异常上报 -->
					<view class="menu-item" @click="goToReportPage">
						<view class="menu-icon">
							<text class="material-icons neon-yellow">build</text>
						</view>
						<view class="menu-content">
							<view class="menu-title">设备异常反馈</view>
							<view class="text-tertiary">报告设备使用问题</view>
						</view>
						<view class="menu-arrow">
							<text class="material-icons text-tertiary">arrow_forward_ios</text>
						</view>
					</view>
					
					
				</view>
			</view>
		</view>
		
		<!-- 自定义TabBar -->
		<custom-tab-bar ref="tabBar"></custom-tab-bar>
	</view>
</template>

<script>
	import customTabBar from "@/custom-tab-bar/index.vue"
	import API from '@/static/js/api.js';
	
	export default {
		components: {
			customTabBar
		},
		data() {
			return {
				title: '我的',
				isLoggedIn: false,
				userInfo: {
					userId: '',
					nickname: '',
					avatar: '',
					orderCount: 0,
					registerTime: ''
				},
				loading: false
			}
		},
		onLoad() {
			console.log('我的页面加载');
			// 检查登录状态
			this.checkLoginStatus();
		},
		onShow() {
			// 确保TabBar正确显示当前页面
			this.updateTabBar();
			
			// 每次显示页面时刷新用户信息
			if (this.isLoggedIn) {
				this.getUserInfo();
				// 获取订单数量
				this.getOrderCount();
			}
		},
		onReady() {
			// 页面渲染完成后，再次更新TabBar
			setTimeout(() => {
				this.updateTabBar();
			}, 100);
		},
		onPullDownRefresh() {
			// 下拉刷新，重新获取用户信息
			this.getUserInfo();
			// 同时获取订单数量
			this.getOrderCount();
		},
		methods: {
			// 更新TabBar状态
			updateTabBar() {
				if (this.$refs.tabBar) {
					// 如果有updateCurrentPath方法，则调用
					if (typeof this.$refs.tabBar.updateCurrentPath === 'function') {
						this.$refs.tabBar.updateCurrentPath();
					}
					
					// 如果有startPathCheck方法，则调用
					if (typeof this.$refs.tabBar.startPathCheck === 'function') {
						this.$refs.tabBar.startPathCheck();
					}
					
					// 强制重新渲染TabBar
					if (typeof this.$refs.tabBar.$forceUpdate === 'function') {
						this.$refs.tabBar.$forceUpdate();
					}
				}
			},
			navigateTo(url) {
				uni.navigateTo({
					url: url
				});
			},
			// 前往设备异常反馈页面
			goToReportPage() {
				console.log('前往设备异常反馈页面');
				uni.navigateTo({
					url: '/pages/report/report'
				});
			},
			checkLoginStatus() {
				// 从缓存获取token
				const token = uni.getStorageSync('token');
				this.isLoggedIn = !!token;
				
				if (this.isLoggedIn) {
					// 已登录，获取用户信息
					this.getUserInfo();
					// 获取订单数量
					this.getOrderCount();
				} else {
					// 未登录，使用默认信息
					this.userInfo = {
						userId: '未登录',
						nickname: '未登录用户',
						avatar: '',
						orderCount: 0,
						registerTime: ''
					};
				}
			},
			
			// 获取用户信息
			getUserInfo() {
				if (this.loading) return;
				
				this.loading = true;
				
				// 调用API获取用户信息
				API.user.getInfo()
					.then(res => {
						this.loading = false;
						uni.stopPullDownRefresh();
						
						// 更新用户信息
						this.userInfo = {
							userId: res.data.userId || '未知ID',
							nickname: res.data.nickname || '隐私用户',
							avatar: res.data.avatar || '',
							orderCount: res.data.orderCount || 0,
							registerTime: res.data.registerTime || ''
						};
					})
					.catch(err => {
						this.loading = false;
						uni.stopPullDownRefresh();
						
						console.error('获取用户信息失败:', err);
						
						// 如果是未登录错误，更新登录状态
						if (err.code === 401) {
							this.isLoggedIn = false;
							this.userInfo = {
								userId: '未登录',
								nickname: '未登录用户',
								avatar: '',
								orderCount: 0,
								registerTime: ''
							};
						}
						
						// 显示错误提示
						uni.showToast({
							title: err.message || '获取用户信息失败',
							icon: 'none'
						});
					});
			},
			
			// 获取订单数量
			getOrderCount() {
				if (!this.isLoggedIn) return;
				
				API.order.getOrderCount()
					.then(res => {
						if (res.data !== undefined) {
							this.userInfo.orderCount = res.data;
						}
					})
					.catch(err => {
						console.error('获取订单数量失败:', err);
					});
			},
			
			// 退出登录
			logout() {
				uni.showModal({
					title: '退出登录',
					content: '确定要退出当前账号吗？',
					success: (res) => {
						if (res.confirm) {
							// 清除token
							uni.removeStorageSync('token');
							
							// 更新登录状态
							this.isLoggedIn = false;
							this.userInfo = {
								userId: '未登录',
								nickname: '未登录用户',
								avatar: '',
								orderCount: 0,
								registerTime: ''
							};
							
							// 显示提示
							uni.showToast({
								title: '已退出登录',
								icon: 'success'
							});
						}
					}
				});
			}
		}
	}
</script>

<style>
	/* CSS变量定义 */
	page {
		--primary-light: #A875FF;
		--neon-pink: #ff36f9;
		--neon-blue: #00BFFF;
		--neon-yellow: #FFD700;
		--divider-color: rgba(255, 255, 255, 0.08);
	}
	
	/* 页面基础样式 */
	.page-profile {
		padding-top: 140rpx;
		padding-bottom: calc(170rpx + env(safe-area-inset-bottom));
		color: #ffffff;
		height: 100vh;
		min-height: 100vh;
		box-sizing: border-box;
		position: relative;
		overflow: hidden;
	}
	
	/* 页面背景样式 */
	.page-background {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		z-index: 0;
	}
	
	/* 背景图片样式 */
	.background-image {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		z-index: 0;
		object-fit: cover;
	}
	
	/* 磨砂效果叠加层 */
	.frosted-overlay {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-color: rgba(18, 18, 18, 0.4);
		backdrop-filter: blur(5px);
		-webkit-backdrop-filter: blur(5px);
		z-index: 1;
	}
	
	.status-bar {
		width: 100%;
		background: rgba(18, 18, 18, 0.8);
		backdrop-filter: blur(10px);
		-webkit-backdrop-filter: blur(10px);
		position: fixed;
		top: 0;
		left: 0;
		z-index: 100;
	}
	
	.content {
		padding: 30rpx;
		position: relative;
		z-index: 2;
		height: calc(100vh - 140rpx - env(safe-area-inset-bottom) - 170rpx);
		display: flex;
		flex-direction: column;
		overflow-y: auto;
		overflow-x: hidden;
		-webkit-overflow-scrolling: touch;
	}
	
	/* 个人信息区域 - 移除背景相关样式 */
	.profile-header {
		display: flex;
		align-items: center;
		padding: 40rpx;
		margin-bottom: 30rpx;
	}
	
	.avatar-container {
		margin-right: 30rpx;
	}
	
	/* 美化头像样式 */
	.avatar-wrapper {
		width: 120rpx;
		height: 120rpx;
		border-radius: 60rpx;
		background: linear-gradient(145deg, rgba(168, 117, 255, 0.8), rgba(255, 54, 249, 0.5));
		padding: 4rpx;
		box-shadow: 0 0 20rpx rgba(168, 117, 255, 0.5);
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.avatar {
		width: 112rpx;
		height: 112rpx;
		border-radius: 56rpx;
		background-color: rgba(120, 50, 200, 0.7);
		border: 1px solid rgba(255, 255, 255, 0.3);
		box-shadow: inset 0 0 10rpx rgba(255, 255, 255, 0.2);
	}
	
	/* 用户名和状态横向排列 */
	.user-name-row {
		display: flex;
		align-items: center;
		margin-bottom: 10rpx;
	}
	
	.user-name-row .title-md {
		margin-right: 16rpx;
	}
	
	/* 菜单区域 */
	.menu-section {
		margin-bottom: 30rpx;
		flex: 1;
		display: flex;
		flex-direction: column;
	}
	
	.menu-list {
		display: flex;
		flex-direction: column;
		margin-bottom: 30rpx;
		background-color: rgba(168, 117, 255, 0.05);
		border-radius: 20rpx;
		overflow: hidden;
		border: 2rpx solid rgba(168, 117, 255, 0.4);
		box-shadow: 0 0 20rpx rgba(168, 117, 255, 0.2);
	}
	
	.menu-item {
		display: flex;
		align-items: center;
		padding: 36rpx;
		transition: background-color 0.2s;
		position: relative;
	}
	
	/* 除最后一个菜单项外，其他菜单项底部添加分隔线 */
	.menu-item:not(:last-child)::after {
		content: '';
		position: absolute;
		left: 90rpx;
		right: 30rpx;
		bottom: 0;
		height: 1px;
		background-color: var(--divider-color);
	}
	
	.menu-item:active {
		background-color: rgba(255, 255, 255, 0.05);
	}
	
	.menu-icon {
		margin-right: 20rpx;
		width: 70rpx;
		height: 70rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		border-radius: 50%;
		background-color: transparent;
	}
	
	.menu-icon .material-icons {
		font-size: 44rpx;
	}
	
	.menu-content {
		flex: 1;
	}
	
	.menu-title {
		font-size: 36rpx;
		font-weight: 500;
		color: #ffffff;
		margin-bottom: 8rpx;
	}
	
	.menu-arrow {
		width: 40rpx;
		display: flex;
		justify-content: center;
		align-items: center;
	}
	
	.menu-arrow .material-icons {
		font-size: 28rpx;
	}
	
	/* 关于区域 */
	.about-section {
		margin-top: 40rpx;
		margin-bottom: 80rpx;
		padding-bottom: 100rpx;
	}
	
	/* Material Icons 字体 */
	@font-face {
		font-family: 'Material Icons';
		font-style: normal;
		font-weight: 400;
		src: url(https://fonts.gstatic.com/s/materialicons/v139/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format('woff2');
	}

	.material-icons {
		font-family: 'Material Icons';
		font-weight: normal;
		font-style: normal;
		font-size: 24rpx;
		line-height: 1;
		letter-spacing: normal;
		text-transform: none;
		display: inline-block;
		white-space: nowrap;
		word-wrap: normal;
		direction: ltr;
		-webkit-font-smoothing: antialiased;
		-moz-osx-font-smoothing: grayscale;
	}
	
	.material-icons.primary-light {
		color: var(--primary-light, #A875FF);
	}
	
	.material-icons.text-tertiary {
		color: var(--text-tertiary, rgba(255, 255, 255, 0.5));
	}
	
	/* 辅助类 */
	.flex {
		display: flex;
	}
	
	.justify-between {
		justify-content: space-between;
	}
	
	.items-center {
		align-items: center;
	}
	
	.text-center {
		text-align: center;
	}
	
	.mt-sm {
		margin-top: 10rpx;
	}
	
	.mt-md {
		margin-top: 20rpx;
	}
	
	.mt-lg {
		margin-top: 40rpx;
	}
	
	.mb-sm {
		margin-bottom: 10rpx;
	}
	
	/* 状态标签 */
	.status {
		padding: 6rpx 16rpx;
		border-radius: 30rpx;
		font-size: 24rpx;
		display: inline-block;
	}
	
	.status.active {
		background-color: rgba(168, 117, 255, 0.2);
		color: var(--primary-light, #A875FF);
	}
	
	/* 文本样式 */
	.title-md {
		font-size: 36rpx;
		font-weight: 600;
		color: #ffffff;
	}
	
	.title-sm {
		font-size: 30rpx;
		font-weight: 500;
		color: #ffffff;
	}
	
	.text-secondary {
		color: rgba(255, 255, 255, 0.7);
	}
	
	.text-tertiary {
		color: rgba(255, 255, 255, 0.5);
		font-size: 30rpx;
	}
	
	.text-primary {
		color: #ffffff;
	}
	
	.primary-light {
		color: var(--primary-light, #A875FF);
	}
	
	.neon-pink {
		color: var(--neon-pink, #ff36f9);
	}
	
	.neon-blue {
		color: var(--neon-blue, #00BFFF);
	}
	
	.neon-yellow {
		color: var(--neon-yellow, #FFD700);
	}
	
	/* 按钮样式 */
	.btn {
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 8rpx;
		padding: 16rpx 24rpx;
		font-size: 28rpx;
		font-weight: 500;
	}
	
	.btn-outline {
		background-color: transparent;
		border: 1px solid rgba(255, 255, 255, 0.2);
		color: #ffffff;
	}
	
	.btn-sm {
		padding: 12rpx 20rpx;
		font-size: 24rpx;
	}
</style> 