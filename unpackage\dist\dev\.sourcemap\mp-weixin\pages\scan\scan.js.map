{"version": 3, "sources": ["webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/scan/scan.vue?7b31", "uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/scan/scan.vue?8a86", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/scan/scan.vue?8ca0", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/scan/scan.vue?108b", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/scan/scan.vue?548a", "uni-app:///pages/scan/scan.vue", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/scan/scan.vue?9b44"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "data", "scanning", "isPageReady", "scanAttempts", "maxScanAttempts", "processing", "onLoad", "console", "setTimeout", "onShow", "onHide", "onUnload", "onBackPress", "methods", "goBack", "uni", "delta", "fail", "url", "startScan", "title", "content", "showCancel", "success", "doScanCode", "mask", "scanType", "icon", "complete", "stopScan", "handleScanResult", "pathId", "lockService", "then", "catch", "navigateToDevicePage", "deviceId", "isValidDeviceCode", "extractMacFromCode", "fallbackToTraditionalMethod", "checkPermission", "resolve", "requestPermission", "scope", "reject", "showPermissionDeniedTip", "permissionName", "confirmText", "cancelText", "scanPage"], "mappings": ";;;;;;;;;AAAA;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;;;;ACNL;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiH;AACjH;AACwD;AACL;AACa;;;AAGhE;AACgM;AAChM,gBAAgB,uMAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,+EAAM;AACR,EAAE,wFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAkvB,CAAgB,gvBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACkDtwB;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IAAA;IACAC;IACA;IACA;;IAEA;IACAC;MACA;MACA;IACA;EACA;EACAC;IAAA;IACAF;IACA;IACA;MACAC;QACA;MACA;IACA;EACA;EACAE;IACAH;IACA;IACA;EACA;EACAI;IACAJ;IACA;IACA;IACA;EACA;EACA;EACAK;IACAL;IACA;IACA;IACA;EACA;;EACAM;IACA;IACAC;MACA;MACA;;MAEA;MACAN;QACA;QACAO;UACAC;UACAC;YACA;YACAF;cACAG;cACAD;gBACA;gBACAF;kBACAG;gBACA;cACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;QACAZ;QACA;MACA;;MAEA;MACA;QACAA;QACAQ;UACAK;UACAC;UACAC;UACAC;YACA;UACA;QACA;QACA;MACA;MAEA;MACA;MACAhB;;MAEA;MACA;IACA;IAEA;IACAiB;MAAA;MACA;MACAT;QACAK;QACAK;MACA;;MAEA;MACAjB;QACA;UACAO;QACA;UACAR;QACA;;QAEA;QACAQ;UACAW;UACAH;YACAhB;YACA;UACA;UACAU;YACAV;YACA;;YAEA;YACA;cACA;YACA;cACAQ;gBACAK;gBACAO;cACA;YACA;UACA;UACAC;YACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MACAtB;MACA;IACA;IAEA;IACAuB;MACAvB;;MAEA;MACAQ;QACAK;QACAK;MACA;;MAEA;MACA;QACAlB;;QAEA;QACA;UACAA;UACA;UACA;YACA;;YAEA;YACA,uCACAW,oCACAA,kCACAA;;YAEA;YACA;YACA;cACA;cACA;gBAAA;cAAA;cACA;gBACA;gBACA;gBACA;gBACA;kBACAa;gBACA;cACA;YACA;;YAEA;YACA;YAEA;cACAxB;cACA;cACAQ;cACA;cACAA;gBACAG;gBACAK;kBACAhB;gBACA;gBACAU;kBACAV;kBACAQ;oBACAK;oBACAO;kBACA;gBACA;cACA;cACA;YACA;cACA;cACApB;cACAQ;cACAA;gBACAK;gBACAO;cACA;cACA;YACA;cACApB;YACA;UACA;YACAA;YACA;UACA;QACA;;QAEA;QACA;QACAA;QAEA;UACA;UACA;UACAA;UAEA;YACAA;;YAEA;YACAyB,0BACAC;cACA1B;;cAEA;cACAQ;gBACAG;gBACAK;kBACAhB;kBACAQ;gBACA;gBACAE;kBACAV;kBACAQ;kBAEAA;oBACAK;oBACAO;kBACA;gBACA;cACA;YACA,GACAO;cACA3B;cACAQ;cAEAA;gBACAK;gBACAO;cACA;YACA;UACA;YACApB;YACAQ;YAEAA;cACAK;cACAO;YACA;UACA;QACA;UACApB;UACAQ;UAEAA;YACAK;YACAO;UACA;QACA;MACA;QACApB;QACAQ;QAEAA;UACAK;UACAO;QACA;MACA;IACA;IAEA;IACAQ;MACA5B;;MAEA;MACA,sDACA6B,gDACAA;MAEArB;QACAG;QACAK;UACAhB;UACAQ;QACA;QACAE;UACAV;UACAQ;UAEAA;YACAK;YACAO;UACA;QACA;MACA;IACA;IAEA;IACAU;MACA9B;MACA;MACA;MACAA;MACA;IACA;IAEA;IACA+B;MACA/B;MACA;MACA;MAEA;QACAA;QACA;MACA;MAEAA;MACA;IACA;IAEA;IACAgC;MAAA;MACAhC;;MAEA;MACAQ;QACAK;QACAK;MACA;;MAEA;MACAO,8CACAC;QACA1B;;QAEA;QACA;MACA,GACA0B;QACA1B;QACA;UACAQ;QACA;UACAR;QACA;QACA;;QAEA;QACAQ;UACAG;UACAD;YACAV;YACAQ;cACAK;cACAO;YACA;UACA;QACA;MACA,GACAO;QACA3B;QACA;UACAQ;QACA;UACAR;QACA;QACA;QAEAQ;UACAK;UACAC;UACAC;UACAC;YACA;YACAf;cACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAgC;MACA;QAEA;QACAzB;UACAQ;YACA;cACAkB;YACA;cACAA;YACA;UACA;UACAxB;YACAwB;UACA;QACA;MA6BA;IACA;IAEA;IACAC;MACA;QAEA;QACA3B;UACA4B;UACApB;YACAkB;UACA;UACAxB;YACA2B;UACA;QACA;MAiCA;IACA;IAEA;IACAC;MAAA;MACA;MACA;QACA;UACAC;UACA;QACA;UACAA;MAAA;MAGA/B;QACAK;QACAC;QACA0B;QACAC;QACAzB;UACA;YAEAR;cACAQ;gBACAhB;cACA;YACA;UAWA;YACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,eAEA0C;AAAA,2B;;;;;;;;;;;;;AC1mBA;AAAA;AAAA;AAAA;AAA+jC,CAAgB,yhCAAG,EAAC,C", "file": "pages/scan/scan.js", "sourcesContent": ["// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754165306715\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/scan/scan.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./scan.vue?vue&type=template&id=1db32b26&\"\nvar renderjs\nimport script from \"./scan.vue?vue&type=script&lang=js&\"\nexport * from \"./scan.vue?vue&type=script&lang=js&\"\nimport style0 from \"./scan.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/scan/scan.vue\"\nexport default component.exports", "export * from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./scan.vue?vue&type=template&id=1db32b26&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./scan.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./scan.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"container page-scan\">\n\t\t<!-- 页面背景 -->\n\t\t<view class=\"page-background\">\n\t\t\t<!-- 背景图片 -->\n\t\t\t<image class=\"background-image\" src=\"https://wuji1112-1312674791.cos.ap-guangzhou.myqcloud.com/static/shouye3.png\" mode=\"aspectFill\"></image>\n\t\t\t\n\t\t\t<!-- 页面渐变背景 - 保留磨砂效果 -->\n\t\t\t<view class=\"gradient-overlay\"></view>\n\t\t</view>\n\t\t\n\t\t<!-- 顶部状态栏占位 -->\n\t\t<view class=\"status-bar safe-area-inset-top\"></view>\n\t\t\n\t\t<!-- 顶部导航栏 -->\n\t\t<view class=\"nav-bar\">\n\t\t\t<view class=\"nav-back\" @click=\"goBack\">\n\t\t\t\t<text class=\"material-icons\">arrow_back</text>\n\t\t\t</view>\n\t\t\t<view class=\"nav-title\">扫码连接</view>\n\t\t</view>\n\t\t\n\t\t<!-- 扫码区域 -->\n\t\t<view class=\"scan-content\">\n\t\t\t<!-- 扫码框区域 -->\n\t\t\t<view class=\"scan-area\">\n\t\t\t\t<view class=\"scan-frame\">\n\t\t\t\t\t<view class=\"scan-line\"></view>\n\t\t\t\t\t<view class=\"scan-corner scan-corner-top-left\"></view>\n\t\t\t\t\t<view class=\"scan-corner scan-corner-top-right\"></view>\n\t\t\t\t\t<view class=\"scan-corner scan-corner-bottom-left\"></view>\n\t\t\t\t\t<view class=\"scan-corner scan-corner-bottom-right\"></view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"scan-tips\">\n\t\t\t\t\t<text>请将二维码放入框内，即可自动扫描</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 扫码加载遮罩层 -->\n\t\t<view class=\"scan-loading-mask\" v-if=\"scanning\">\n\t\t\t<view class=\"scan-loading\">\n\t\t\t\t<view class=\"loading-spinner\"></view>\n\t\t\t\t<text class=\"loading-text\">正在扫描...</text>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport { lockService } from '@/utils/index.js';\n\timport API from '@/static/js/api.js';\n\t\n\t// 修复导出问题\n\tconst scanPage = {\n\t\tname: 'scan',\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tscanning: false,\n\t\t\t\tisPageReady: false,\n\t\t\t\tscanAttempts: 0,\n\t\t\t\tmaxScanAttempts: 3,\n\t\t\t\tprocessing: false\n\t\t\t}\n\t\t},\n\t\tonLoad() {\n\t\t\tconsole.log('扫码页面 onLoad');\n\t\t\t// 页面加载时标记为准备好\n\t\t\tthis.isPageReady = true;\n\t\t\t\n\t\t\t// 延迟启动扫码，防止黑屏\n\t\t\tsetTimeout(() => {\n\t\t\t\t// 开始扫码\n\t\t\t\tthis.startScan();\n\t\t\t}, 500);\n\t\t},\n\t\tonShow() {\n\t\t\tconsole.log('扫码页面 onShow');\n\t\t\t// 页面显示时，如果准备好且未在扫码，则开始扫码\n\t\t\tif (this.isPageReady && !this.scanning) {\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tthis.startScan();\n\t\t\t\t}, 300);\n\t\t\t}\n\t\t},\n\t\tonHide() {\n\t\t\tconsole.log('扫码页面 onHide');\n\t\t\t// 页面隐藏时停止扫码\n\t\t\tthis.stopScan();\n\t\t},\n\t\tonUnload() {\n\t\t\tconsole.log('扫码页面 onUnload');\n\t\t\t// 页面卸载时停止扫码并标记为未准备好\n\t\t\tthis.stopScan();\n\t\t\tthis.isPageReady = false;\n\t\t},\n\t\t// 监听页面返回按钮\n\t\tonBackPress() {\n\t\t\tconsole.log('扫码页面 onBackPress');\n\t\t\t// 先停止扫码\n\t\t\tthis.stopScan();\n\t\t\treturn false; // 返回false，让系统执行默认的返回逻辑\n\t\t},\n\t\tmethods: {\n\t\t\t// 返回上一页\n\t\t\tgoBack() {\n\t\t\t\t// 先停止扫码\n\t\t\t\tthis.stopScan();\n\t\t\t\t\n\t\t\t\t// 延迟后返回，避免返回失败\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t// 尝试使用navigateBack返回\n\t\t\t\t\tuni.navigateBack({\n\t\t\t\t\t\tdelta: 1,\n\t\t\t\t\t\tfail: () => {\n\t\t\t\t\t\t\t// 如果返回失败，则跳转到首页\n\t\t\t\t\t\t\tuni.switchTab({\n\t\t\t\t\t\t\t\turl: '/pages/index/index',\n\t\t\t\t\t\t\t\tfail: () => {\n\t\t\t\t\t\t\t\t\t// 如果跳转到首页也失败，则重启小程序\n\t\t\t\t\t\t\t\t\tuni.reLaunch({\n\t\t\t\t\t\t\t\t\t\turl: '/pages/index/index'\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}, 200);\n\t\t\t},\n\t\t\t\n\t\t\t// 开始扫码\n\t\t\tstartScan() {\n\t\t\t\t// 防止重复调用\n\t\t\t\tif (this.scanning) {\n\t\t\t\t\tconsole.log('已经在扫码中，忽略本次调用');\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 检查是否超过最大尝试次数\n\t\t\t\tif (this.scanAttempts >= this.maxScanAttempts) {\n\t\t\t\t\tconsole.log('已达到最大扫码尝试次数');\n\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\ttitle: '提示',\n\t\t\t\t\t\tcontent: '多次扫码失败，请返回后重试',\n\t\t\t\t\t\tshowCancel: false,\n\t\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\t\tthis.goBack();\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tthis.scanning = true;\n\t\t\t\tthis.scanAttempts++;\n\t\t\t\tconsole.log(`开始扫码，第 ${this.scanAttempts} 次尝试`);\n\t\t\t\t\n\t\t\t\t// 直接执行扫码，微信小程序会自动请求相机权限\n\t\t\t\tthis.doScanCode();\n\t\t\t},\n\t\t\t\n\t\t\t// 执行扫码操作\n\t\t\tdoScanCode() {\n\t\t\t\t// 添加Loading提示\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: '准备扫码...',\n\t\t\t\t\tmask: false\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\t// 延迟执行扫码，避免黑屏问题\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\ttry {\n\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\tconsole.warn('隐藏loading失败:', e);\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 使用uni-app的扫码API\n\t\t\t\t\tuni.scanCode({\n\t\t\t\t\t\tscanType: ['qrCode'],\n\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\tconsole.log('扫码结果:', res);\n\t\t\t\t\t\t\tthis.handleScanResult(res.result);\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\tconsole.error('扫码失败:', err);\n\t\t\t\t\t\t\tthis.scanning = false;\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 判断是否是因为权限问题导致的失败\n\t\t\t\t\t\t\tif (err.errMsg && err.errMsg.includes('authorize')) {\n\t\t\t\t\t\t\t\tthis.showPermissionDeniedTip('camera');\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ttitle: '扫码失败，请重试',\n\t\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t},\n\t\t\t\t\t\tcomplete: () => {\n\t\t\t\t\t\t\tthis.scanning = false;\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}, 300);\n\t\t\t},\n\t\t\t\n\t\t\t// 停止扫码\n\t\t\tstopScan() {\n\t\t\t\tconsole.log('停止扫码');\n\t\t\t\tthis.scanning = false;\n\t\t\t},\n\t\t\t\n\t\t\t// 处理扫描结果\n\t\t\thandleScanResult(result) {\n\t\t\t\tconsole.log('扫描结果原始数据:', result);\n\t\t\t\t\n\t\t\t\t// 显示加载中\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: '正在处理...',\n\t\t\t\t\tmask: true\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\t// 解析扫描结果\n\t\t\t\ttry {\n\t\t\t\t\tconsole.log('开始解析扫描结果');\n\t\t\t\t\t\n\t\t\t\t\t// 检查是否是URL格式的二维码\n\t\t\t\t\tif (result.startsWith('http')) {\n\t\t\t\t\t\tconsole.log('检测到URL格式二维码');\n\t\t\t\t\t\t// 解析URL参数\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\tconst url = new URL(result);\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 检查多种可能的参数名\n\t\t\t\t\t\t\tconst id = url.searchParams.get('id') || \n\t\t\t\t\t\t\t\t\t  url.searchParams.get('deviceId') || \n\t\t\t\t\t\t\t\t\t  url.searchParams.get('device') || \n\t\t\t\t\t\t\t\t\t  url.searchParams.get('deviceCode');\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 检查URL路径中是否包含设备ID\n\t\t\t\t\t\t\tlet pathId = null;\n\t\t\t\t\t\t\tif (!id && url.pathname) {\n\t\t\t\t\t\t\t\t// 尝试从路径中提取ID\n\t\t\t\t\t\t\t\tconst pathParts = url.pathname.split('/').filter(part => part.length > 0);\n\t\t\t\t\t\t\t\tif (pathParts.length > 0) {\n\t\t\t\t\t\t\t\t\t// 取最后一个路径部分作为可能的ID\n\t\t\t\t\t\t\t\t\tconst lastPart = pathParts[pathParts.length - 1];\n\t\t\t\t\t\t\t\t\t// 如果是数字或看起来像设备ID的格式\n\t\t\t\t\t\t\t\t\tif (/^\\d+$/.test(lastPart) || /^[a-zA-Z0-9-_]{4,}$/.test(lastPart)) {\n\t\t\t\t\t\t\t\t\t\tpathId = lastPart;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 使用参数中的ID或路径中的ID\n\t\t\t\t\t\t\tconst deviceId = id || pathId;\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\tif (deviceId) {\n\t\t\t\t\t\t\t\tconsole.log('从URL中提取到设备ID:', deviceId);\n\t\t\t\t\t\t\t\t// 隐藏加载提示\n\t\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\t\t// 直接跳转到设备页面，使用scanType=miniapp标识是小程序扫码\n\t\t\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\t\t\turl: `/pages/scan/device?id=${deviceId}&scanType=miniapp`,\n\t\t\t\t\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\t\t\t\t\tconsole.log('导航到设备页面成功');\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\t\t\t\tconsole.error('导航到设备页面失败:', err);\n\t\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\t\ttitle: '导航到设备页面失败',\n\t\t\t\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t\t} else if (url.host.includes('jycb') || result.includes('jycb')) {\n\t\t\t\t\t\t\t\t// 如果URL域名包含jycb但没有找到ID参数，可能是首页或其他页面\n\t\t\t\t\t\t\t\tconsole.log('检测到今夜城堡相关URL，但未找到设备ID');\n\t\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ttitle: '未找到有效的设备信息',\n\t\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tconsole.log('URL中未找到设备ID参数，继续检查是否是蓝牙设备码');\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} catch (err) {\n\t\t\t\t\t\t\tconsole.error('解析URL失败:', err);\n\t\t\t\t\t\t\t// 解析URL失败，继续检查是否是蓝牙设备码\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 检查是否是有效的设备二维码\n\t\t\t\t\tconst isValid = this.isValidDeviceCode(result);\n\t\t\t\t\tconsole.log('是否是有效的设备二维码:', isValid);\n\t\t\t\t\t\n\t\t\t\t\tif (isValid) {\n\t\t\t\t\t\t// 提取MAC地址\n\t\t\t\t\t\tconst mac = this.extractMacFromCode(result);\n\t\t\t\t\t\tconsole.log('提取的MAC地址:', mac);\n\t\t\t\t\t\t\n\t\t\t\t\t\tif (mac) {\n\t\t\t\t\t\t\tconsole.log('准备初始化蓝牙环境');\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 预初始化蓝牙环境\n\t\t\t\t\t\t\tlockService.init()\n\t\t\t\t\t\t\t\t.then((initResult) => {\n\t\t\t\t\t\t\t\t\tconsole.log('蓝牙环境初始化结果:', initResult);\n\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t// 导航到设备页面\n\t\t\t\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\t\t\t\turl: `/pages/scan/device?mac=${mac}&scanType=bluetooth`,\n\t\t\t\t\t\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\t\t\t\t\t\tconsole.log('导航到设备页面成功');\n\t\t\t\t\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\t\t\t\t\tconsole.error('导航到设备页面失败:', err);\n\t\t\t\t\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\t\t\ttitle: '导航到设备页面失败',\n\t\t\t\t\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\t.catch(err => {\n\t\t\t\t\t\t\t\t\tconsole.error('初始化蓝牙环境失败:', err);\n\t\t\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\ttitle: '初始化蓝牙环境失败，请确保蓝牙已开启',\n\t\t\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tconsole.error('无法从二维码中提取MAC地址');\n\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '无效的设备二维码',\n\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconsole.error('无效的设备二维码格式');\n\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '无效的设备二维码',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t} catch (err) {\n\t\t\t\t\tconsole.error('处理扫描结果失败:', err);\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '处理扫描结果失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 导航到设备页面\n\t\t\tnavigateToDevicePage(deviceId, scanType) {\n\t\t\t\tconsole.log(`准备导航到设备页面, 参数: { ${scanType === 'bluetooth' ? 'mac' : 'id'}: ${deviceId}, scanType: ${scanType} }`);\n\t\t\t\t\n\t\t\t\t// 构建URL参数\n\t\t\t\tconst params = scanType === 'bluetooth' \n\t\t\t\t\t? `mac=${deviceId}&scanType=bluetooth` \n\t\t\t\t\t: `id=${deviceId}&scanType=miniapp`;\n\t\t\t\t\t\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: `/pages/scan/device?${params}`,\n\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\tconsole.log('导航到设备页面成功');\n\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t},\n\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\tconsole.error('导航到设备页面失败:', err);\n\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '导航到设备页面失败',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 检查是否是有效的设备二维码\n\t\t\tisValidDeviceCode(code) {\n\t\t\t\tconsole.log('检查二维码是否有效:', code);\n\t\t\t\t// 检查是否包含MAC地址格式\n\t\t\t\tconst isValid = /([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})|([0-9A-Fa-f]{12})/i.test(code);\n\t\t\t\tconsole.log('二维码有效性检查结果:', isValid);\n\t\t\t\treturn isValid;\n\t\t\t},\n\t\t\t\n\t\t\t// 从二维码中提取MAC地址\n\t\t\textractMacFromCode(code) {\n\t\t\t\tconsole.log('从二维码中提取MAC地址:', code);\n\t\t\t\t// 尝试匹配MAC地址格式\n\t\t\t\tconst macMatch = code.match(/([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})|([0-9A-Fa-f]{12})/i);\n\t\t\t\t\n\t\t\t\tif (macMatch) {\n\t\t\t\t\tconsole.log('提取到MAC地址:', macMatch[0]);\n\t\t\t\t\treturn macMatch[0];\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tconsole.log('未能从二维码中提取MAC地址');\n\t\t\t\treturn null;\n\t\t\t},\n\t\t\t\n\t\t\t// 回退到传统方式处理二维码\n\t\t\tfallbackToTraditionalMethod(result) {\n\t\t\t\tconsole.log('回退到传统方式处理二维码');\n\t\t\t\t\n\t\t\t\t// 显示加载提示\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: '处理中...',\n\t\t\t\t\tmask: true\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\t// 使用锁控制服务处理二维码\n\t\t\t\tlockService.handleDeviceQRCode(result)\n\t\t\t\t\t.then(deviceInfo => {\n\t\t\t\t\t\tconsole.log('设备绑定状态检查成功:', deviceInfo);\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 创建订单\n\t\t\t\t\t\treturn lockService.createOrder();\n\t\t\t\t\t})\n\t\t\t\t\t.then(orderInfo => {\n\t\t\t\t\t\tconsole.log('创建订单成功:', orderInfo);\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\t\tconsole.warn('隐藏loading失败:', e);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tthis.processing = false;\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 跳转到设备控制页面\n\t\t\t\t\t\tuni.redirectTo({\n\t\t\t\t\t\t\turl: `/pages/scan/device?mac=${lockService.deviceInfo.mac}&orderId=${orderInfo.orderId}`,\n\t\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\t\tconsole.error('跳转失败:', err);\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ttitle: '页面跳转失败',\n\t\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t})\n\t\t\t\t\t.catch(err => {\n\t\t\t\t\t\tconsole.error('处理扫码结果失败:', err);\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\t\tconsole.warn('隐藏loading失败:', e);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tthis.processing = false;\n\t\t\t\t\t\t\n\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\ttitle: '连接失败',\n\t\t\t\t\t\t\tcontent: err.message || '无法连接设备，请重试',\n\t\t\t\t\t\t\tshowCancel: false,\n\t\t\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\t\t\t// 重新开始扫码\n\t\t\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\t\t\tthis.startScan();\n\t\t\t\t\t\t\t\t}, 500);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 检查权限\n\t\t\tcheckPermission(permissionType) {\n\t\t\t\treturn new Promise((resolve) => {\n\t\t\t\t\t// #ifdef MP-WEIXIN\n\t\t\t\t\t// 微信小程序检查权限\n\t\t\t\t\tuni.getSetting({\n\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\tif (res.authSetting && res.authSetting[`scope.${permissionType}`] !== false) {\n\t\t\t\t\t\t\t\tresolve(true);\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tresolve(false);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfail: () => {\n\t\t\t\t\t\t\tresolve(false);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\t// #endif\n\t\t\t\t\t\n\t\t\t\t\t// #ifdef APP-PLUS\n\t\t\t\t\t// App检查权限\n\t\t\t\t\tconst permissionMap = {\n\t\t\t\t\t\tcamera: 'CAMERA'\n\t\t\t\t\t};\n\t\t\t\t\t\n\t\t\t\t\tconst permissionCode = permissionMap[permissionType];\n\t\t\t\t\tif (!permissionCode) {\n\t\t\t\t\t\tresolve(false);\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tconst checkPermission = uni.requireNativePlugin('globalAPIs');\n\t\t\t\t\tif (checkPermission && checkPermission.hasPermission) {\n\t\t\t\t\t\tcheckPermission.hasPermission([permissionCode], (res) => {\n\t\t\t\t\t\t\tresolve(res.result);\n\t\t\t\t\t\t});\n\t\t\t\t\t} else {\n\t\t\t\t\t\tresolve(true); // 如果无法检查，假设有权限\n\t\t\t\t\t}\n\t\t\t\t\t// #endif\n\t\t\t\t\t\n\t\t\t\t\t// #ifndef MP-WEIXIN || APP-PLUS\n\t\t\t\t\t// 其他平台默认有权限\n\t\t\t\t\tresolve(true);\n\t\t\t\t\t// #endif\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 请求权限\n\t\t\trequestPermission(permissionType) {\n\t\t\t\treturn new Promise((resolve, reject) => {\n\t\t\t\t\t// #ifdef MP-WEIXIN\n\t\t\t\t\t// 微信小程序请求权限\n\t\t\t\t\tuni.authorize({\n\t\t\t\t\t\tscope: `scope.${permissionType}`,\n\t\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\t\tresolve();\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfail: () => {\n\t\t\t\t\t\t\treject();\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\t// #endif\n\t\t\t\t\t\n\t\t\t\t\t// #ifdef APP-PLUS\n\t\t\t\t\t// App请求权限\n\t\t\t\t\tconst permissionMap = {\n\t\t\t\t\t\tcamera: 'CAMERA'\n\t\t\t\t\t};\n\t\t\t\t\t\n\t\t\t\t\tconst permissionCode = permissionMap[permissionType];\n\t\t\t\t\tif (!permissionCode) {\n\t\t\t\t\t\treject();\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tconst requestPermission = uni.requireNativePlugin('globalAPIs');\n\t\t\t\t\tif (requestPermission && requestPermission.requestPermission) {\n\t\t\t\t\t\trequestPermission.requestPermission([permissionCode], (res) => {\n\t\t\t\t\t\t\tif (res.result) {\n\t\t\t\t\t\t\t\tresolve();\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\treject();\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t} else {\n\t\t\t\t\t\tresolve(); // 如果无法请求，假设成功\n\t\t\t\t\t}\n\t\t\t\t\t// #endif\n\t\t\t\t\t\n\t\t\t\t\t// #ifndef MP-WEIXIN || APP-PLUS\n\t\t\t\t\t// 其他平台默认成功\n\t\t\t\t\tresolve();\n\t\t\t\t\t// #endif\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 显示权限被拒绝提示\n\t\t\tshowPermissionDeniedTip(permissionType) {\n\t\t\t\tlet permissionName = '';\n\t\t\t\tswitch (permissionType) {\n\t\t\t\t\tcase 'camera':\n\t\t\t\t\t\tpermissionName = '相机';\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tdefault:\n\t\t\t\t\t\tpermissionName = permissionType;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '权限提示',\n\t\t\t\t\tcontent: `需要${permissionName}权限才能使用扫码功能，请在设置中开启权限`,\n\t\t\t\t\tconfirmText: '去设置',\n\t\t\t\t\tcancelText: '取消',\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\t// #ifdef MP-WEIXIN\n\t\t\t\t\t\t\tuni.openSetting({\n\t\t\t\t\t\t\t\tsuccess: (settingRes) => {\n\t\t\t\t\t\t\t\t\tconsole.log('打开设置页面成功');\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t// #endif\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// #ifdef APP-PLUS\n\t\t\t\t\t\t\tconst openAppSetting = uni.requireNativePlugin('globalAPIs');\n\t\t\t\t\t\t\tif (openAppSetting && openAppSetting.openAppSetting) {\n\t\t\t\t\t\t\t\topenAppSetting.openAppSetting((res) => {\n\t\t\t\t\t\t\t\t\tconsole.log('打开设置页面成功');\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t// #endif\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tthis.goBack();\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t};\n\t\n\texport default scanPage;\n</script>\n\n<style>\n\t/* CSS变量定义 */\n\tpage {\n\t\t--primary-light: #A875FF;\n\t\t--neon-pink: #ff36f9;\n\t}\n\t\n\t/* 扫码加载遮罩层 */\n\t.scan-loading-mask {\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tbackground-color: rgba(0, 0, 0, 0.7);\n\t\tz-index: 9999;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t}\n\t\n\t.scan-loading {\n\t\tbackground-color: rgba(30, 30, 30, 0.9);\n\t\tborder-radius: 20rpx;\n\t\tpadding: 40rpx 60rpx;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tborder: 2rpx solid rgba(168, 117, 255, 0.3);\n\t\tbox-shadow: 0 0 20rpx rgba(168, 117, 255, 0.3);\n\t}\n\t\n\t.loading-spinner {\n\t\twidth: 60rpx;\n\t\theight: 60rpx;\n\t\tborder: 4rpx solid rgba(168, 117, 255, 0.3);\n\t\tborder-top: 4rpx solid var(--primary-light);\n\t\tborder-radius: 50%;\n\t\tanimation: spin 1s linear infinite;\n\t\tmargin-bottom: 20rpx;\n\t}\n\t\n\t.loading-text {\n\t\tcolor: #ffffff;\n\t\tfont-size: 30rpx;\n\t}\n\t\n\t@keyframes spin {\n\t\t0% { transform: rotate(0deg); }\n\t\t100% { transform: rotate(360deg); }\n\t}\n\t\n\t/* 页面基础样式 */\n\t.page-scan {\n\t\tpadding-top: 250rpx;\n\t\tpadding-bottom: calc(100rpx + env(safe-area-inset-bottom));\n\t\tcolor: #ffffff;\n\t\theight: 100vh;\n\t\tmin-height: 100vh;\n\t\tbox-sizing: border-box;\n\t\tposition: relative;\n\t\toverflow: hidden;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t}\n\t\n\t/* 页面背景样式 */\n\t.page-background {\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tz-index: 0;\n\t}\n\t\n\t/* 背景图片样式 */\n\t.background-image {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tz-index: 0;\n\t\tobject-fit: cover;\n\t}\n\t\n\t.gradient-overlay {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tbackground: linear-gradient(to bottom, \n\t\t\trgba(18, 18, 18, 0.3) 0%, \n\t\t\trgba(18, 18, 18, 0.5) 50%,\n\t\t\trgba(18, 18, 18, 0.7) 100%);\n\t\tbackdrop-filter: blur(5px);\n\t\t-webkit-backdrop-filter: blur(5px);\n\t\tz-index: 1;\n\t\tpointer-events: none;\n\t}\n\t\n\t.status-bar {\n\t\twidth: 100%;\n\t\tbackground: transparent;\n\t\tbackdrop-filter: none;\n\t\t-webkit-backdrop-filter: none;\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tz-index: 100;\n\t}\n\t\n\t/* 导航栏 */\n\t.nav-bar {\n\t\tposition: fixed;\n\t\ttop: calc(env(safe-area-inset-top) + 60rpx);\n\t\tleft: 15rpx;\n\t\tright: 15rpx;\n\t\theight: 110rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tbackground: transparent;\n\t\tbackdrop-filter: none;\n\t\t-webkit-backdrop-filter: none;\n\t\tz-index: 100;\n\t\tpadding: 0 30rpx;\n\t\tborder-bottom: none;\n\t\tbox-shadow: none;\n\t\tborder-radius: 0 0 30rpx 30rpx;\n\t}\n\t\n\t.nav-back {\n\t\tposition: absolute;\n\t\tleft: 30rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\twidth: 70rpx;\n\t\theight: 70rpx;\n\t\tborder-radius: 50%;\n\t\tbackground-color: rgba(168, 117, 255, 0.15);\n\t\tborder: 1px solid rgba(168, 117, 255, 0.3);\n\t}\n\t\n\t.nav-back .material-icons {\n\t\tfont-size: 44rpx;\n\t\tcolor: rgba(255, 255, 255, 0.9);\n\t\ttext-shadow: 0 0 8rpx rgba(168, 117, 255, 0.5);\n\t}\n\t\n\t.nav-title {\n\t\tfont-size: 38rpx;\n\t\tfont-weight: 600;\n\t\tcolor: #ffffff;\n\t\ttext-shadow: 0 0 10rpx rgba(168, 117, 255, 0.5);\n\t\tletter-spacing: 2rpx;\n\t}\n\t\n\t/* 扫码内容区域 */\n\t.scan-content {\n\t\tposition: relative;\n\t\tz-index: 2;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tflex: 1;\n\t\tpadding: 0 30rpx;\n\t\tmax-height: 55vh;\n\t}\n\t\n\t/* 扫码框 */\n\t.scan-area {\n\t\tposition: relative;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t}\n\t\n\t.scan-frame {\n\t\tposition: relative;\n\t\twidth: 500rpx;\n\t\theight: 500rpx;\n\t\tborder-radius: 20rpx;\n\t\toverflow: hidden;\n\t\tbackground-color: rgba(0, 0, 0, 0.15);\n\t\tborder: 1rpx solid rgba(255, 255, 255, 0.1);\n\t\tbox-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.3);\n\t}\n\t\n\t/* 扫描线动画 */\n\t.scan-line {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\twidth: 100%;\n\t\theight: 10rpx;\n\t\tbackground: linear-gradient(to right, \n\t\t\trgba(168, 117, 255, 0) 0%, \n\t\t\trgba(168, 117, 255, 0.8) 50%, \n\t\t\trgba(168, 117, 255, 0) 100%);\n\t\tanimation: scanLine 2s infinite linear;\n\t\tbox-shadow: 0 0 15rpx rgba(168, 117, 255, 0.8);\n\t}\n\t\n\t@keyframes scanLine {\n\t\t0% {\n\t\t\ttop: 0;\n\t\t}\n\t\t100% {\n\t\t\ttop: 100%;\n\t\t}\n\t}\n\t\n\t/* 扫描框角 */\n\t.scan-corner {\n\t\tposition: absolute;\n\t\twidth: 70rpx;\n\t\theight: 70rpx;\n\t\tborder-color: var(--primary-light, #A875FF);\n\t\topacity: 0.9;\n\t}\n\t\n\t.scan-corner-top-left {\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tborder-top: 8rpx solid;\n\t\tborder-left: 8rpx solid;\n\t\tborder-top-left-radius: 16rpx;\n\t}\n\t\n\t.scan-corner-top-right {\n\t\ttop: 0;\n\t\tright: 0;\n\t\tborder-top: 8rpx solid;\n\t\tborder-right: 8rpx solid;\n\t\tborder-top-right-radius: 16rpx;\n\t}\n\t\n\t.scan-corner-bottom-left {\n\t\tbottom: 0;\n\t\tleft: 0;\n\t\tborder-bottom: 8rpx solid;\n\t\tborder-left: 8rpx solid;\n\t\tborder-bottom-left-radius: 16rpx;\n\t}\n\t\n\t.scan-corner-bottom-right {\n\t\tbottom: 0;\n\t\tright: 0;\n\t\tborder-bottom: 8rpx solid;\n\t\tborder-right: 8rpx solid;\n\t\tborder-bottom-right-radius: 16rpx;\n\t}\n\t\n\t/* 扫码提示文字 */\n\t.scan-tips {\n\t\tmargin-top: 40rpx;\n\t\ttext-align: center;\n\t\tcolor: rgba(255, 255, 255, 0.85);\n\t\tfont-size: 30rpx;\n\t\tbackground-color: rgba(168, 117, 255, 0.15);\n\t\tpadding: 16rpx 30rpx;\n\t\tborder-radius: 30rpx;\n\t\tborder: 1rpx solid rgba(168, 117, 255, 0.2);\n\t}\n\t\n\t/* Material Icons 字体 */\n\t@font-face {\n\t\tfont-family: 'Material Icons';\n\t\tfont-style: normal;\n\t\tfont-weight: 400;\n\t\tsrc: url(https://fonts.gstatic.com/s/materialicons/v139/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format('woff2');\n\t}\n\n\t.material-icons {\n\t\tfont-family: 'Material Icons';\n\t\tfont-weight: normal;\n\t\tfont-style: normal;\n\t\tfont-size: 48rpx;\n\t\tline-height: 1;\n\t\tletter-spacing: normal;\n\t\ttext-transform: none;\n\t\tdisplay: inline-block;\n\t\twhite-space: nowrap;\n\t\tword-wrap: normal;\n\t\tdirection: ltr;\n\t\t-webkit-font-smoothing: antialiased;\n\t\t-moz-osx-font-smoothing: grayscale;\n\t}\n</style> ", "import mod from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./scan.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./scan.vue?vue&type=style&index=0&lang=css&\""], "sourceRoot": ""}