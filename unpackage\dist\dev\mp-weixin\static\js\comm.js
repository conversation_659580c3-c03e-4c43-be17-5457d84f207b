/**
 * 蓝牙管理模块
 * 封装了蓝牙设备的连接、断开、搜索等基础功能
 */

// 工具函数

/**
 * 判断元素是否在数组中
 * @param {Array} arr - 数组
 * @param {String} key - 键名
 * @param {any} val - 值
 * @returns {Number} 索引，-1表示不存在
 */
function inArray(arr, key, val) {
  for (let i = 0; i < arr.length; i++) {
    if (arr[i][key] === val) {
      return i;
    }
  }
  return -1;
}

/**
 * ArrayBuffer转16进制字符串
 * @param {ArrayBuffer} buffer - 要转换的ArrayBuffer
 * @returns {string} 16进制字符串，每个字节之间有空格
 */
function ab2hex(buffer) {
  const hexArr = Array.prototype.map.call(
    new Uint8Array(buffer),
    function (bit) {
      return ('00' + bit.toString(16)).slice(-2) + " ";
    }
  );
  return hexArr.join('').toUpperCase();
}

/**
 * ArrayBuffer转字符串
 * @param {ArrayBuffer} arrayBuffer - 要转换的ArrayBuffer
 * @returns {string} 转换后的字符串
 */
function ab2Str(arrayBuffer) {
  const unit8Arr = new Uint8Array(arrayBuffer);
  const encodedString = String.fromCharCode.apply(null, unit8Arr);
  return encodedString;
}

/**
 * 字符串转字节数组
 * @param {string} str - 要转换的字符串
 * @returns {Array} 字节数组
 */
function stringToBytes(str) {
  let ch, st, re = [];
  for (let i = 0; i < str.length; i++) {
    ch = str.charCodeAt(i);  // get char  
    st = [];                 // set up "stack"  
    do {
      st.push(ch & 0xFF);  // push byte to stack  
      ch = ch >> 8;          // shift value down by 1 byte  
    }
    while (ch);
    // add stack contents to result  
    // done because chars have "wrong" endianness  
    re = re.concat(st.reverse());
  }
  // return an array of bytes  
  return re;
}

/**
 * 16进制字符串转换为ArrayBuffer
 * @param {string} hexString - 16进制字符串，可以包含空格
 * @returns {ArrayBuffer} 转换后的ArrayBuffer
 */
function hexStringToArrayBuffer(hexString) {
  // 去除所有空格
  hexString = hexString.replace(/\s/g, "");
  
  // 确保字符串长度为偶数
  if (hexString.length % 2 != 0) {
    hexString = "0" + hexString;
  }
  
  const buffer = new ArrayBuffer(hexString.length / 2);
  const dataView = new DataView(buffer);
  
  for (let i = 0; i < hexString.length; i += 2) {
    dataView.setUint8(i / 2, parseInt(hexString.substr(i, 2), 16));
  }
  
  return buffer;
}

// 蓝牙管理类
const BleManager = {
  // 设备列表
  devices: [],
  
  // 连接超时时间(毫秒)
  connectionTimeout: 15000,
  
  // 连接超时定时器
  _connectionTimer: null,
  
  /**
   * 检查蓝牙是否可用
   * @returns {Promise} 蓝牙状态
   */
  isBluetoothEnabled() {
    return new Promise((resolve, reject) => {
      uni.getBluetoothAdapterState({
        success: (res) => {
          resolve(res.available && res.discovering === false);
        },
        fail: (err) => {
          reject(err);
        }
      });
    });
  },
  
  /**
   * 开启蓝牙
   * @returns {Promise} 开启结果
   */
  enableBluetooth() {
    return new Promise((resolve, reject) => {
      uni.openBluetoothAdapter({
        success: (res) => {
          resolve(res);
        },
        fail: (err) => {
          reject(err);
        }
      });
    });
  },
  
  /**
   * 搜索设备
   * @param {Function} onDeviceFound 发现设备回调
   * @param {Number} timeout 超时时间(毫秒)
   * @returns {Promise} 搜索结果
   */
  searchDevices(onDeviceFound, timeout = 10000) {
    return new Promise((resolve, reject) => {
      // 清空设备列表
      this.devices = [];
      
      // 开启蓝牙
      this.enableBluetooth().then(() => {
        // 开始搜索
        uni.startBluetoothDevicesDiscovery({
          allowDuplicatesKey: true,
          success: (res) => {
            console.log('开始搜索蓝牙设备', res);
            
            // 监听设备发现事件
            uni.onBluetoothDeviceFound((res) => {
              res.devices.forEach(device => {
                // 检查设备是否已在列表中
                if (inArray(this.devices, 'deviceId', device.deviceId) === -1) {
                  this.devices.push(device);
                  
                  // 回调通知发现设备
                  if (onDeviceFound) {
                    onDeviceFound(device);
                  }
                }
              });
            });
            
            // 设置超时
            setTimeout(() => {
              // 停止搜索
              uni.stopBluetoothDevicesDiscovery({
                success: (res) => {
                  console.log('停止搜索蓝牙设备', res);
                  resolve(this.devices);
                }
              });
            }, timeout);
          },
          fail: (err) => {
            console.error('搜索蓝牙设备失败', err);
            reject(err);
          }
        });
      }).catch(reject);
    });
  },
  
  /**
   * 连接设备
   * @param {Object} device 设备对象
   * @returns {Promise} 连接结果
   */
  connect(device) {
    return new Promise((resolve, reject) => {
      // 停止搜索
      uni.stopBluetoothDevicesDiscovery();
      
      // 确保deviceId是字符串类型
      const deviceId = String(device.deviceId);
      
      // 连接设备
      uni.createBLEConnection({
        deviceId: deviceId,
        success: (res) => {
          console.log('连接设备成功', res);
          resolve(device);
        },
        fail: (err) => {
          console.error('连接设备失败', err);
          reject(err);
        }
      });
    });
  },
  
  /**
   * 断开设备连接
   * @param {String} deviceId 设备ID
   * @returns {Promise} 断开结果
   */
  disconnect(deviceId) {
    return new Promise((resolve, reject) => {
      // 确保deviceId是字符串类型
      const deviceIdStr = String(deviceId);
      
      // 检查deviceId是否有效
      if (!deviceIdStr || deviceIdStr === 'undefined' || deviceIdStr === 'null') {
        console.warn('断开连接失败: 无效的设备ID');
        resolve(); // 无效ID视为已断开
        return;
      }
      
      uni.closeBLEConnection({
        deviceId: deviceIdStr,
        success: (res) => {
          console.log('断开设备连接成功', res);
          resolve(res);
        },
        fail: (err) => {
          console.error('断开设备连接失败', err);
          reject(err);
        }
      });
    });
  },
  
  /**
   * 获取设备服务
   * @param {String} deviceId 设备ID
   * @returns {Promise} 服务列表
   */
  getDeviceServices(deviceId) {
    return new Promise((resolve, reject) => {
      uni.getBLEDeviceServices({
        deviceId: deviceId,
        success: (res) => {
          console.log('获取设备服务成功', res.services);
          resolve(res.services);
        },
        fail: (err) => {
          console.error('获取设备服务失败', err);
          reject(err);
        }
      });
    });
  },
  
  /**
   * 获取服务特征值
   * @param {String} deviceId 设备ID
   * @param {String} serviceId 服务ID
   * @returns {Promise} 特征值列表
   */
  getDeviceCharacteristics(deviceId, serviceId) {
    return new Promise((resolve, reject) => {
      uni.getBLEDeviceCharacteristics({
        deviceId: deviceId,
        serviceId: serviceId,
        success: (res) => {
          console.log('获取特征值成功', res.characteristics);
          resolve(res.characteristics);
        },
        fail: (err) => {
          console.error('获取特征值失败', err);
          reject(err);
        }
      });
    });
  },
  
  /**
   * 启用特征值通知
   * @param {String} deviceId 设备ID
   * @param {String} serviceId 服务ID
   * @param {String} characteristicId 特征值ID
   * @returns {Promise} 操作结果
   */
  enableNotification(deviceId, serviceId, characteristicId) {
    return new Promise((resolve, reject) => {
      uni.notifyBLECharacteristicValueChange({
        deviceId: deviceId,
        serviceId: serviceId,
        characteristicId: characteristicId,
        state: true,
        success: (res) => {
          console.log('启用特征值通知成功', res);
          resolve(res);
        },
        fail: (err) => {
          console.error('启用特征值通知失败', err);
          reject(err);
        }
      });
    });
  },
  
  /**
   * 写入特征值
   * @param {String} deviceId 设备ID
   * @param {String} serviceId 服务ID
   * @param {String} characteristicId 特征值ID
   * @param {ArrayBuffer} value 数据
   * @returns {Promise} 操作结果
   */
  writeCharacteristicValue(deviceId, serviceId, characteristicId, value) {
    return new Promise((resolve, reject) => {
      uni.writeBLECharacteristicValue({
        deviceId: deviceId,
        serviceId: serviceId,
        characteristicId: characteristicId,
        value: value,
        success: (res) => {
          console.log('写入特征值成功', res);
          resolve(res);
        },
        fail: (err) => {
          console.error('写入特征值失败', err);
          reject(err);
        }
      });
    });
  },
  
  /**
   * 读取特征值
   * @param {String} deviceId 设备ID
   * @param {String} serviceId 服务ID
   * @param {String} characteristicId 特征值ID
   * @returns {Promise} 操作结果
   */
  readCharacteristicValue(deviceId, serviceId, characteristicId) {
    return new Promise((resolve, reject) => {
      uni.readBLECharacteristicValue({
        deviceId: deviceId,
        serviceId: serviceId,
        characteristicId: characteristicId,
        success: (res) => {
          console.log('读取特征值成功', res);
          resolve(res);
        },
        fail: (err) => {
          console.error('读取特征值失败', err);
          reject(err);
        }
      });
    });
  }
};

export default BleManager; 