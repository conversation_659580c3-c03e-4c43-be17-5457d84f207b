


























































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































/* CSS变量定义 */
page {
	--primary-light: #A875FF;
	--primary-dark: #8C29FF;
	--neon-pink: #ff36f9;
	--neon-blue: #00BFFF;
	--neon-yellow: #FFD700;
	--neon-red: #FF454A;
	overflow: hidden; /* 禁用页面滚动 */
}
/* iOS适配 */
.page-report {
	/* 适配iOS设备的安全区域和灵动岛 */
	padding-top: env(safe-area-inset-top);
}
/* 页面基础样式 */
.page-report {
	color: #ffffff;
	height: 100vh;
	min-height: 100vh;
	box-sizing: border-box;
	position: relative;
	overflow: hidden; /* 禁用容器滚动 */
	display: flex;
	flex-direction: column;
}
/* 页面背景样式 */
.page-background {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 0;
}
/* 背景图片样式 */
.background-image {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 0;
	object-fit: cover;
}
/* 深磨砂效果叠加层 */
.frosted-overlay {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(18, 18, 18, 0.7); /* 深色半透明背景 */
	backdrop-filter: blur(8px); /* 较强模糊效果 */
	-webkit-backdrop-filter: blur(8px);
	z-index: 1;
}
/* 顶部导航样式 */
.navbar {
	height: 90rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 30rpx;
	/* 完全移除CSS的margin-top，只使用JavaScript动态设置 */
	margin-top: 0;
	position: relative;
	z-index: 100;
}
.navbar-title {
	font-size: 40rpx;
	font-weight: 600;
}
.navbar-left, .navbar-right {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}
/* 内容区域 */
.content {
	flex: 1;
	padding: 30rpx 30rpx;
	position: relative;
	z-index: 5;
	width: 100%;
	box-sizing: border-box;
	overflow: hidden; /* 修改为hidden，禁用滚动 */
}
/* 表单容器 */
.form-body {
	padding: 30rpx;
}
.form-group {
	margin-bottom: 30rpx;
	width: 100%;
}
.form-group.mt-lg {
	margin-top: 50rpx;
}
.form-group.mt-md {
	margin-top: 40rpx;
}
.form-label {
	font-size: 36rpx;
	font-weight: 500;
	margin-bottom: 16rpx;
	color: #c18fff;
	text-shadow: 0 0 10rpx rgba(193, 143, 255, 0.3);
}
.type-hint {
	margin-left: 15rpx;
}
.form-picker {
	background-color: rgba(255, 255, 255, 0.08);
	border: 1px solid rgba(168, 117, 255, 0.2);
	border-radius: 15rpx;
	padding: 20rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	color: var(--text-primary, #FFFFFF);
	transition: all 0.3s ease;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.form-picker:active {
	background-color: rgba(255, 255, 255, 0.12);
	-webkit-transform: scale(0.99);
	        transform: scale(0.99);
}
.issue-types {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 10rpx;
	margin-top: 10rpx;
	width: 100%;
}
.issue-type-item {
	display: flex;
	align-items: center;
	padding: 8rpx 12rpx;
	background-color: rgba(255, 255, 255, 0.08);
	border: 1px solid rgba(168, 117, 255, 0.2);
	border-radius: 16rpx;
	font-size: 26rpx;
	transition: all 0.3s ease;
	height: 60rpx;
}
.issue-type-item .issue-icon {
	font-size: 34rpx;
	margin-right: 6rpx;
	position: relative;
	top: 1rpx;
}
.issue-type-item.active {
	background-color: rgba(168, 117, 255, 0.15);
	border-color: var(--primary-light, #A875FF);
	color: var(--primary-light, #A875FF);
	box-shadow: 0 0 10rpx rgba(168, 117, 255, 0.3);
}
.issue-type-item.active .issue-icon {
	color: var(--primary-light, #A875FF);
}
.issue-type-item:active {
	-webkit-transform: scale(0.95);
	        transform: scale(0.95);
}
.form-textarea {
	background-color: rgba(255, 255, 255, 0.08);
	border: 1px solid rgba(168, 117, 255, 0.2);
	border-radius: 15rpx;
	padding: 20rpx;
	width: 100%;
	height: 150rpx;
	box-sizing: border-box;
	color: var(--text-primary, #FFFFFF);
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
	transition: border-color 0.3s ease, box-shadow 0.3s ease;
	font-size: 28rpx;
}
.form-textarea:focus {
	border-color: rgba(168, 117, 255, 0.5);
	box-shadow: 0 0 12rpx rgba(168, 117, 255, 0.2);
}
.form-input {
	background-color: rgba(255, 255, 255, 0.08);
	border: 1px solid rgba(168, 117, 255, 0.2);
	border-radius: 15rpx;
	padding: 28rpx 20rpx;
	width: 100%;
	box-sizing: border-box;
	color: var(--text-primary, #FFFFFF);
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
	transition: border-color 0.3s ease, box-shadow 0.3s ease;
	font-size: 30rpx;
	height: 90rpx;
	line-height: 1.5;
}
.form-input:focus {
	border-color: rgba(168, 117, 255, 0.5);
	box-shadow: 0 0 12rpx rgba(168, 117, 255, 0.2);
}
.text-count {
	text-align: right;
	font-size: 24rpx;
	margin-top: 10rpx;
	color: rgba(255, 255, 255, 0.4);
}
.upload-container {
	margin-top: 10rpx;
	width: 100%;
}
.upload-items {
	display: flex;
	flex-wrap: wrap;
	gap: 20rpx;
	width: 100%;
}
.upload-item {
	width: 180rpx;
	height: 180rpx;
	position: relative;
	border-radius: 15rpx;
	overflow: hidden;
	box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.2);
	border: 1px solid rgba(168, 117, 255, 0.2);
	transition: box-shadow 0.3s ease, -webkit-transform 0.3s ease;
	transition: transform 0.3s ease, box-shadow 0.3s ease;
	transition: transform 0.3s ease, box-shadow 0.3s ease, -webkit-transform 0.3s ease;
}
.upload-item:active {
	-webkit-transform: scale(0.95);
	        transform: scale(0.95);
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}
.upload-item image {
	width: 100%;
	height: 100%;
}
.delete-btn {
	position: absolute;
	top: 8rpx;
	right: 8rpx;
	width: 36rpx;
	height: 36rpx;
	background-color: rgba(0, 0, 0, 0.6);
	border-radius: 18rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #fff;
	transition: all 0.3s ease;
}
.delete-btn:active {
	-webkit-transform: scale(0.9);
	        transform: scale(0.9);
	background-color: rgba(255, 69, 58, 0.8);
}
.upload-btn {
	width: 180rpx;
	height: 180rpx;
	border: 1px dashed rgba(168, 117, 255, 0.4);
	border-radius: 15rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: rgba(168, 117, 255, 0.05);
	transition: all 0.3s ease;
}
.upload-btn:active {
	background-color: rgba(168, 117, 255, 0.15);
	-webkit-transform: scale(0.95);
	        transform: scale(0.95);
}
.privacy-tip {
	font-size: 24rpx;
	text-align: center;
	color: rgba(255, 255, 255, 0.4);
}
/* 按钮包装器，增加点击区域 */
.btn-wrapper {
	width: 100%;
	padding: 10rpx 0;
	display: flex;
	justify-content: center;
}
/* 按钮样式 */
.btn {
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 15rpx;
	padding: 25rpx;
	font-size: 30rpx;
	font-weight: 600;
	transition: all 0.3s ease;
}
.btn-submit {
	background: linear-gradient(135deg, #c18fff, #a875ff);
	color: #FFFFFF;
	box-shadow: 0 4rpx 20rpx rgba(193, 143, 255, 0.6);
	border: none;
	width: 60%;
	margin: 0 auto;
	padding: 16rpx;
	border-radius: 50rpx;
	position: relative;
	overflow: hidden;
}
.btn-active:active {
	-webkit-transform: scale(0.95);
	        transform: scale(0.95);
	background: linear-gradient(135deg, #b06aff, #9c63ff);
	box-shadow: 0 2rpx 10rpx rgba(193, 143, 255, 0.5);
}
.btn-disabled {
	background: linear-gradient(135deg, #c18fff99, #a875ff99);
	box-shadow: none;
}
.btn-text {
	font-size: 28rpx;
	font-weight: 700;
	letter-spacing: 3rpx;
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
	color: #FFFFFF;
	z-index: 1;
}
/* 底部空间 */
.bottom-space {
	height: 60rpx;
}
/* Material Icons 字体 */
@font-face {
	font-family: 'Material Icons';
	font-style: normal;
	font-weight: 400;
	src: url(https://fonts.gstatic.com/s/materialicons/v139/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format('woff2');
}
.material-icons {
	font-family: 'Material Icons';
	font-weight: normal;
	font-style: normal;
	font-size: 24rpx;
	line-height: 1;
	letter-spacing: normal;
	text-transform: none;
	display: inline-block;
	white-space: nowrap;
	word-wrap: normal;
	direction: ltr;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}
.material-icons.md-18 {
	font-size: 36rpx;
}
.material-icons.md-24 {
	font-size: 48rpx;
}
.material-icons.md-36 {
	font-size: 72rpx;
}
.material-icons.text-primary {
	color: var(--text-primary, #FFFFFF);
}
.material-icons.text-tertiary {
	color: var(--text-tertiary, rgba(255, 255, 255, 0.5));
}
.mr-sm {
	margin-right: 10rpx;
}
.mt-sm {
	margin-top: 16rpx;
}
.mt-md {
	margin-top: 30rpx;
}
.mt-lg {
	margin-top: 60rpx;
}
/* 文本样式 */
.title-sm {
	font-size: 32rpx;
	font-weight: 600;
	color: #ffffff;
	margin-bottom: 6rpx;
}
.text-secondary {
	color: rgba(255, 255, 255, 0.7);
	font-size: 28rpx;
}
.text-tertiary {
	color: rgba(255, 255, 255, 0.5);
	font-size: 26rpx;
}
.text-primary {
	color: #FFFFFF;
	font-size: 28rpx;
}
/* 移除所有CSS适配，完全依赖JavaScript动态设置 */

