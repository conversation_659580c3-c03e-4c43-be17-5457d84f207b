<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>手机预览 - 今夜城堡</title>
    <style>
        html, body {
            margin: 0;
            padding: 0;
            background-color: #f0f2f5;
            font-family: "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", sans-serif;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 100%;
            width: 100%;
            overflow: hidden;
            position: fixed;
        }

        /* 隐藏所有元素的滚动条 */
        ::-webkit-scrollbar {
            display: none;
            width: 0;
        }

        * {
            -ms-overflow-style: none;  /* IE 和 Edge */
            scrollbar-width: none;  /* Firefox */
        }

        .content-wrapper {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding-top: 0;
            max-height: 100vh;
            max-width: 100%;
            padding: 10px;
            box-sizing: border-box;
            overflow: hidden;
            height: 100%;
        }

        .phone-container {
            position: relative;
            width: 375px;
            height: 812px;
            background-color: #1a1a1a;
            border-radius: 50px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3),
                        0 0 0 6px #333,
                        inset 0 0 10px rgba(0, 0, 0, 0.5);
            padding: 12px;
            overflow: hidden;
            transform: scale(0.85);
            transition: transform 0.3s ease, top 0.3s ease, left 0.3s ease;
            flex-shrink: 0;
            transform-origin: center center;
        }

        .phone-container:hover {
            transform: scale(0.9);
        }
        
        /* 新增放大模式 */
        .phone-container.zoomed {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) scale(1.5);
            z-index: 1000;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            box-shadow: 0 30px 60px rgba(0, 0, 0, 0.5),
                        0 0 0 8px #444,
                        inset 0 0 15px rgba(0, 0, 0, 0.7);
        }
        
        .phone-container.zoomed:hover {
            transform: translate(-50%, -50%) scale(1.5);
            box-shadow: 0 30px 60px rgba(0, 0, 0, 0.5),
                        0 0 0 8px #444,
                        inset 0 0 15px rgba(0, 0, 0, 0.7);
        }
        
        /* 遮罩层 */
        .overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            z-index: 999;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease;
        }
        
        .overlay.active {
            opacity: 1;
            visibility: visible;
        }
        
        /* 放大按钮 - 增强可见性 */
        .zoom-button {
            position: absolute;
            bottom: 25px;
            right: 25px;
            width: 60px;
            height: 60px;
            background-color: rgba(168, 117, 255, 0.8);
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            z-index: 1000;
            transition: all 0.3s ease;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
            border: 2px solid rgba(255, 255, 255, 0.5);
            animation: pulse 2s infinite;
            pointer-events: auto;
        }
        
        .zoom-button:hover {
            background-color: rgba(168, 117, 255, 0.9);
            transform: scale(1.1);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);
        }
        
        .zoom-button svg {
            width: 30px;
            height: 30px;
            fill: white;
        }
        
        /* 确保在所有设备上都能看到放大按钮 */
        @media (max-width: 500px), (max-height: 700px) {
            .zoom-button {
                bottom: 15px;
                right: 15px;
                width: 45px;
                height: 45px;
            }
        }
        
        /* 添加一个提示标签 */
        .zoom-button::after {
            content: "放大";
            position: absolute;
            bottom: -25px;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 3px 8px;
            border-radius: 4px;
            font-size: 12px;
            opacity: 0;
            transition: opacity 0.3s ease;
            white-space: nowrap;
        }
        
        .zoom-button:hover::after {
            opacity: 1;
        }
        
        /* 关闭按钮 */
        .close-button {
            position: absolute;
            top: -50px;
            right: 0;
            width: 40px;
            height: 40px;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            z-index: 1001;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }
        
        .phone-container.zoomed .close-button {
            opacity: 1;
            visibility: visible;
        }
        
        .close-button:hover {
            background-color: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }
        
        .close-button svg {
            width: 24px;
            height: 24px;
            fill: white;
        }
        
        /* 旋转按钮 */
        .rotate-button {
            position: absolute;
            top: -50px;
            right: 50px;
            width: 40px;
            height: 40px;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            z-index: 1001;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }
        
        .phone-container.zoomed .rotate-button {
            opacity: 1;
            visibility: visible;
        }
        
        .rotate-button:hover {
            background-color: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }
        
        .rotate-button svg {
            width: 24px;
            height: 24px;
            fill: white;
        }
        
        /* 横屏模式 */
        .phone-container.landscape {
            transform: scale(0.85) rotate(90deg);
        }
        
        .phone-container.landscape:hover {
            transform: scale(0.9) rotate(90deg);
        }
        
        .phone-container.zoomed.landscape {
            transform: translate(-50%, -50%) scale(1.2) rotate(90deg);
        }
        
        .phone-container.zoomed.landscape:hover {
            transform: translate(-50%, -50%) scale(1.2) rotate(90deg);
        }
        
        /* 手机刘海屏 - 更加逼真 */
        .phone-notch {
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 180px;
            height: 30px;
            background-color: #1a1a1a;
            border-bottom-left-radius: 18px;
            border-bottom-right-radius: 18px;
            z-index: 150;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
            box-shadow: inset 0 -1px 2px rgba(255, 255, 255, 0.1);
        }

        /* 刘海屏内部元素 */
        .notch-content {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100%;
            width: 100%;
            padding: 0 15px;
        }

        /* 听筒 */
        .earpiece {
            width: 50px;
            height: 6px;
            background-color: #333;
            border-radius: 3px;
            margin: 0 10px;
            background: linear-gradient(to bottom, #333, #222);
            box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.8);
        }

        /* 前置摄像头 */
        .front-camera {
            width: 10px;
            height: 10px;
            background-color: #333;
            border-radius: 50%;
            margin-right: 10px;
            position: relative;
            background: radial-gradient(circle at 30% 30%, #444, #222);
            box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.8);
        }

        /* 摄像头内部镜头 */
        .front-camera::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 6px;
            height: 6px;
            background-color: #111;
            border-radius: 50%;
            box-shadow: inset 0 0 1px rgba(0, 0, 0, 0.9);
        }

        /* 传感器 */
        .sensor {
            width: 8px;
            height: 8px;
            background-color: #333;
            border-radius: 50%;
            margin-left: 10px;
            background: radial-gradient(circle at 40% 40%, #444, #222);
            position: relative;
        }

        /* 传感器闪烁效果 */
        .sensor::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 4px;
            height: 4px;
            background-color: rgba(0, 200, 0, 0.1);
            border-radius: 50%;
            animation: sensor-blink 5s infinite;
        }

        @keyframes sensor-blink {
            0%, 95%, 100% {
                opacity: 0;
            }
            96%, 99% {
                opacity: 1;
            }
        }

        /* 手机侧边按钮 */
        .phone-button-left {
            position: absolute;
            left: -6px;
            top: 120px;
            width: 5px;
            height: 60px;
            background-color: #333;
            border-radius: 3px 0 0 3px;
        }

        .phone-button-right-1 {
            position: absolute;
            right: -6px;
            top: 100px;
            width: 5px;
            height: 30px;
            background-color: #333;
            border-radius: 0 3px 3px 0;
        }

        .phone-button-right-2 {
            position: absolute;
            right: -6px;
            top: 150px;
            width: 5px;
            height: 70px;
            background-color: #333;
            border-radius: 0 3px 3px 0;
        }

        /* 状态栏 */
        .status-bar {
            position: absolute;
            top: 8px;
            left: 0;
            width: 100%;
            height: 24px;
            padding: 0 25px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-sizing: border-box;
            z-index: 101;
            color: white;
            font-size: 14px;
            pointer-events: none; /* 确保不会阻挡点击 */
        }

        /* 左侧时间 */
        .status-bar .time {
            font-weight: 600;
            margin-left: 15px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
        }

        /* 右侧图标 */
        .status-bar .status-icons {
            display: flex;
            align-items: center;
            margin-right: 15px;
        }

        .status-icons .signal,
        .status-icons .wifi,
        .status-icons .battery {
            margin-left: 6px;
        }

        /* Face ID指示灯 */
        .face-id-indicator {
            position: absolute;
            top: 4px;
            left: calc(50% + 50px);
            width: 6px;
            height: 6px;
            background-color: rgba(0, 200, 0, 0);
            border-radius: 50%;
            z-index: 151;
            transition: background-color 0.3s ease;
        }

        .face-id-indicator.active {
            background-color: rgba(0, 200, 0, 0.7);
            box-shadow: 0 0 4px rgba(0, 200, 0, 0.5);
            animation: face-id-scan 2s ease-out;
        }

        .phone-screen {
            width: 100%;
            height: 100%;
            border-radius: 40px;
            overflow: hidden;
            position: relative;
            background-color: #000;
            z-index: 100; /* 确保低于放大按钮的z-index */
        }

        .phone-home-indicator {
            position: absolute;
            bottom: 8px;
            left: 50%;
            transform: translateX(-50%);
            width: 140px;
            height: 5px;
            background-color: white;
            border-radius: 3px;
            z-index: 100;
        }

        iframe {
            width: 100%;
            height: 100%;
            border: none;
            overflow: hidden;
            display: block;
            background-color: #000;
            pointer-events: none; /* 禁止iframe内部的交互，防止滚动 */
        }

        .instructions {
            max-width: 600px;
            margin: 10px 0 0;
            padding: 12px;
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
            overflow: hidden;
            max-height: 15vh;
        }

        .instructions h2 {
            margin-top: 0;
            color: #333;
            font-size: 20px;
            margin-bottom: 10px;
        }

        .instructions p {
            color: #666;
            line-height: 1.5;
            font-size: 14px;
            margin-bottom: 10px;
        }

        .instructions .tips {
            margin-top: 10px;
            padding: 10px;
            background-color: #f8f9fa;
            border-left: 4px solid var(--primary-light, #A875FF);
            border-radius: 4px;
        }

        /* 响应式调整 */
        @media (max-height: 900px) {
            .phone-container {
                transform: scale(0.7);
                margin-bottom: 0;
            }
            
            .phone-container:hover {
                transform: scale(0.75);
            }
            
            .instructions {
                max-height: 15vh;
                padding: 10px;
                margin-top: 10px;
            }
        }

        @media (max-height: 700px) {
            .phone-container {
                transform: scale(0.55);
                margin-bottom: 0;
            }
            
            .phone-container:hover {
                transform: scale(0.6);
            }
            
            .instructions {
                max-height: 12vh;
                margin-top: 0;
            }
        }

        @media (max-width: 500px) {
            .phone-container {
                transform: scale(0.65);
            }
            
            .phone-container:hover {
                transform: scale(0.7);
            }
            
            .instructions {
                font-size: 14px;
                padding: 10px;
            }
            
            .instructions h2 {
                font-size: 18px;
            }
        }

        @media (max-width: 400px) {
            .phone-container {
                transform: scale(0.5);
                margin-bottom: 0;
            }
            
            .phone-container:hover {
                transform: scale(0.55);
            }
        }
        
        /* 优化放大模式下覆盖响应式样式 */
        @media (max-height: 900px), (max-height: 700px), (max-width: 500px), (max-width: 400px) {
            .phone-container.zoomed {
                transform: translate(-50%, -50%) scale(1.3);
            }
            
            .phone-container.zoomed:hover {
                transform: translate(-50%, -50%) scale(1.3);
            }
            
            .phone-container.zoomed.landscape {
                transform: translate(-50%, -50%) scale(1) rotate(90deg);
            }
            
            .phone-container.zoomed.landscape:hover {
                transform: translate(-50%, -50%) scale(1) rotate(90deg);
            }
        }
        
        /* 小屏幕设备上的放大模式调整 */
        @media (max-height: 700px) {
            .phone-container.zoomed {
                transform: translate(-50%, -50%) scale(1);
            }
            
            .phone-container.zoomed:hover {
                transform: translate(-50%, -50%) scale(1);
            }
            
            .phone-container.zoomed.landscape {
                transform: translate(-50%, -50%) scale(0.8) rotate(90deg);
            }
            
            .phone-container.zoomed.landscape:hover {
                transform: translate(-50%, -50%) scale(0.8) rotate(90deg);
            }
        }

        /* 超小屏幕设备上的放大模式调整 */
        @media (max-height: 600px) {
            .phone-container.zoomed {
                transform: translate(-50%, -50%) scale(0.8);
            }
            
            .phone-container.zoomed:hover {
                transform: translate(-50%, -50%) scale(0.8);
            }
            
            .phone-container.zoomed.landscape {
                transform: translate(-50%, -50%) scale(0.6) rotate(90deg);
            }
            
            .phone-container.zoomed.landscape:hover {
                transform: translate(-50%, -50%) scale(0.6) rotate(90deg);
            }
        }

        /* 添加脉冲动画 */
        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(168, 117, 255, 0.7), 0 4px 8px rgba(0, 0, 0, 0.3);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(168, 117, 255, 0), 0 4px 8px rgba(0, 0, 0, 0.3);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(168, 117, 255, 0), 0 4px 8px rgba(0, 0, 0, 0.3);
            }
        }

        /* 放大按钮在放大模式下隐藏 */
        .phone-container.zoomed .zoom-button {
            opacity: 0;
            visibility: hidden;
        }

        /* Face ID扫描动画 */
        @keyframes face-id-scan {
            0% {
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                opacity: 0;
            }
        }
    </style>
</head>
<body>
    <!-- 添加遮罩层 -->
    <div class="overlay" id="overlay"></div>
    
    <div class="content-wrapper">
        <div class="phone-container" id="phoneContainer">
            <!-- 添加关闭按钮 -->
            <div class="close-button" id="closeButton">
                <svg viewBox="0 0 24 24">
                    <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                </svg>
            </div>
            
            <!-- 添加旋转按钮 -->
            <div class="rotate-button" id="rotateButton">
                <svg viewBox="0 0 24 24">
                    <path d="M7.34 6.41L.86 12.9l6.49 6.48 6.49-6.48-6.5-6.49zM3.69 12.9l3.66-3.66L11 12.9l-3.66 3.66-3.65-3.66zm15.67-6.26C17.61 4.88 15.3 4 13 4V.76L8.76 5 13 9.24V6c1.79 0 3.58.68 4.95 2.05 2.73 2.73 2.73 7.17 0 9.9C16.58 19.32 14.79 20 13 20c-.97 0-1.94-.21-2.84-.61l-1.49 1.49C10.02 21.62 11.51 22 13 22c2.3 0 4.61-.88 6.36-2.64 3.52-3.51 3.52-9.21 0-12.72z"/>
                </svg>
            </div>
            
            <!-- 添加放大按钮 -->
            <div class="zoom-button" id="zoomButton">
                <svg viewBox="0 0 24 24">
                    <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                    <path d="M12 10h-2v2H9v-2H7V9h2V7h1v2h2v1z"/>
                </svg>
            </div>
            
            <!-- 手机刘海屏 -->
            <div class="phone-notch">
                <div class="notch-content">
                    <div class="earpiece"></div>
                    <div class="front-camera"></div>
                    <div class="sensor"></div>
                </div>
            </div>
            
            <!-- 手机侧边按钮 -->
            <div class="phone-button-left"></div>
            <div class="phone-button-right-1"></div>
            <div class="phone-button-right-2"></div>
            
            <!-- 状态栏 -->
            <div class="status-bar">
                <div class="time" id="currentTime">--:--</div>
                <div class="status-icons">
                    <div class="signal">
                        <svg width="18" height="12" viewBox="0 0 18 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M1 8H3V10H1V8ZM5 6H7V10H5V6ZM9 4H11V10H9V4ZM13 2H15V10H13V2Z" fill="white"/>
                        </svg>
                    </div>
                    <div class="wifi">
                        <svg width="16" height="12" viewBox="0 0 16 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M8 9.5C8.55228 9.5 9 9.94772 9 10.5C9 11.0523 8.55228 11.5 8 11.5C7.44772 11.5 7 11.0523 7 10.5C7 9.94772 7.44772 9.5 8 9.5Z" fill="white"/>
                            <path d="M8 6.5C9.38071 6.5 10.5 7.61929 10.5 9H11.5C11.5 7.067 9.933 5.5 8 5.5C6.067 5.5 4.5 7.067 4.5 9H5.5C5.5 7.61929 6.61929 6.5 8 6.5Z" fill="white"/>
                            <path d="M8 3.5C11.0376 3.5 13.5 5.96243 13.5 9H14.5C14.5 5.41015 11.5899 2.5 8 2.5C4.41015 2.5 1.5 5.41015 1.5 9H2.5C2.5 5.96243 4.96243 3.5 8 3.5Z" fill="white"/>
                            <path d="M8 0.5C12.6944 0.5 16.5 4.30558 16.5 9H17.5C17.5 3.75329 13.2467 -0.5 8 -0.5C2.75329 -0.5 -1.5 3.75329 -1.5 9H-0.5C-0.5 4.30558 3.30558 0.5 8 0.5Z" fill="white"/>
                        </svg>
                    </div>
                    <div class="battery">
                        <svg width="28" height="14" viewBox="0 0 28 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect x="1" y="2" width="22" height="10" rx="2" stroke="white" stroke-width="2"/>
                            <rect x="3" y="4" width="18" height="6" fill="white"/>
                            <path d="M25 5V9C25.8333 8.66667 26.2667 8.33333 26.8 7.5C27.2667 6.66667 26.8333 5.66667 26 5H25Z" fill="white"/>
                        </svg>
                    </div>
                </div>
            </div>
            
            <!-- 手机屏幕 -->
            <div class="phone-screen">
                <iframe src="index.html" frameborder="0" scrolling="no" style="overflow:hidden;"></iframe>
            </div>
            
            <!-- 底部指示条 -->
            <div class="phone-home-indicator"></div>
        </div>

        <div class="instructions">
            <h2>今夜城堡 - 移动应用预览</h2>
            <p>上方展示的是"今夜城堡"应用的首页效果，采用了iPhone样式的手机框架进行展示。页面包含霓虹灯效果标题、动态扫码按钮和底部导航栏。</p>
            <div class="tips">
                <p style="margin-bottom: 0;"><strong>提示：</strong>点击右下角<span style="color: #A875FF; font-weight: bold;">紫色放大按钮</span>可进入全屏预览模式。在全屏模式下，点击旋转按钮可切换横竖屏显示，点击关闭按钮或按<span style="color: #A875FF; font-weight: bold;">ESC键</span>可退出全屏。</p>
            </div>
        </div>
    </div>

    <script>
        // 获取DOM元素
        const phoneContainer = document.getElementById('phoneContainer');
        const overlay = document.getElementById('overlay');
        const zoomButton = document.getElementById('zoomButton');
        const closeButton = document.getElementById('closeButton');
        const rotateButton = document.getElementById('rotateButton');
        const currentTime = document.getElementById('currentTime');
        
        // 更新时间
        function updateTime() {
            const now = new Date();
            const hours = now.getHours().toString().padStart(2, '0');
            const minutes = now.getMinutes().toString().padStart(2, '0');
            currentTime.textContent = `${hours}:${minutes}`;
        }
        
        // 初始化时间并每分钟更新
        updateTime();
        setInterval(updateTime, 60000);
        
        // 切换全屏模式函数
        function toggleFullscreen() {
            console.log("toggleFullscreen 被调用");
            console.log("当前状态:", phoneContainer.classList.contains('zoomed') ? "已放大" : "未放大");
            
            // 检查是否已经处于全屏模式
            const isZoomed = phoneContainer.classList.contains('zoomed');
            
            if (isZoomed) {
                // 如果已经是放大状态，则退出
                exitFullscreen();
            } else {
                // 如果不是放大状态，则进入全屏
                console.log("准备进入全屏模式");
                
                // 确保之前的状态已清除
                phoneContainer.classList.remove('zoomed');
                overlay.classList.remove('active');
                
                // 使用Fullscreen API
                if (document.documentElement.requestFullscreen) {
                    document.documentElement.requestFullscreen().then(() => {
                        console.log("成功进入浏览器全屏模式");
                    }).catch(err => {
                        console.error("全屏请求被拒绝:", err);
                        // 如果无法使用API，则回退到CSS模拟方式
                        fallbackFullscreen();
                    });
                } else if (document.documentElement.webkitRequestFullscreen) {
                    document.documentElement.webkitRequestFullscreen();
                } else if (document.documentElement.msRequestFullscreen) {
                    document.documentElement.msRequestFullscreen();
                } else if (document.documentElement.mozRequestFullScreen) {
                    document.documentElement.mozRequestFullScreen();
                } else {
                    // 浏览器不支持Fullscreen API，使用CSS模拟方式
                    fallbackFullscreen();
                    return;
                }
                
                // 添加类名
                phoneContainer.classList.add('zoomed');
                overlay.classList.add('active');
                document.body.style.overflow = 'hidden';
                
                console.log("全屏模式已启用");
            }
        }
        
        // CSS模拟全屏方式（作为备选方案）
        function fallbackFullscreen() {
            console.log("使用CSS模拟全屏");
            
            // 直接设置样式，不依赖CSS过渡
            phoneContainer.style.position = 'fixed';
            phoneContainer.style.top = '50%';
            phoneContainer.style.left = '50%';
            phoneContainer.style.zIndex = '1000';
            
            // 根据屏幕大小设置合适的缩放比例
            const scale = window.innerHeight < 700 ? 1 : 1.5;
            phoneContainer.style.transform = `translate(-50%, -50%) scale(${scale})`;
            
            // 添加类名
            phoneContainer.classList.add('zoomed');
            overlay.classList.add('active');
            document.body.style.overflow = 'hidden';
        }
        
        // 退出全屏模式函数
        function exitFullscreen() {
            console.log("退出全屏模式");
            
            // 退出浏览器全屏模式
            if (document.exitFullscreen) {
                if (document.fullscreenElement) {
                    document.exitFullscreen().catch(err => {
                        console.error("退出全屏出错:", err);
                    });
                }
            } else if (document.webkitExitFullscreen) {
                document.webkitExitFullscreen();
            } else if (document.msExitFullscreen) {
                document.msExitFullscreen();
            } else if (document.mozCancelFullScreen) {
                document.mozCancelFullScreen();
            }
            
            // 移除类名
            phoneContainer.classList.remove('zoomed');
            phoneContainer.classList.remove('landscape');
            overlay.classList.remove('active');
            document.body.style.overflow = '';
            
            // 清除直接设置的样式
            phoneContainer.style.position = '';
            phoneContainer.style.top = '';
            phoneContainer.style.left = '';
            phoneContainer.style.zIndex = '';
            
            // 根据窗口大小重置缩放比例
            resetPhoneScale();
            
            console.log("已退出全屏模式");
        }
        
        // 页面加载完成后调整显示
        document.addEventListener('DOMContentLoaded', function() {
            resetPhoneScale();
            
            // 禁止所有可能导致滚动的事件
            document.addEventListener('touchmove', function(e) {
                if (!e.target.closest('.instructions')) {
                    e.preventDefault();
                }
            }, { passive: false });
            
            // 禁止iframe内的滚动
            const iframe = document.querySelector('iframe');
            iframe.onload = function() {
                try {
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                    iframeDoc.body.style.overflow = 'hidden';
                    iframeDoc.documentElement.style.overflow = 'hidden';
                } catch (e) {
                    console.log('无法访问iframe内容');
                }
            };
            
            // 确保放大按钮可见并可点击
            highlightZoomButton();
            setTimeout(resetZoomButtonHighlight, 3000);
            
            // 清除所有可能的事件监听器
            zoomButton.removeEventListener('click', toggleFullscreen);
            zoomButton.onclick = null;
            
            // 只使用一种方式绑定点击事件
            zoomButton.addEventListener('click', function(event) {
                event.preventDefault();
                event.stopPropagation();
                console.log('放大按钮点击 - 单一事件监听器');
                
                // 调用全屏切换函数
                toggleFullscreen();
                
                return false;
            });
            
            // 监听全屏状态变化
            document.addEventListener('fullscreenchange', handleFullscreenChange);
            document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
            document.addEventListener('mozfullscreenchange', handleFullscreenChange);
            document.addEventListener('MSFullscreenChange', handleFullscreenChange);
            
            // 移除自动进入全屏模式的代码
            console.log("页面加载完成，不自动进入全屏模式");
        });
        
        // 处理全屏状态变化
        function handleFullscreenChange() {
            if (document.fullscreenElement || 
                document.webkitFullscreenElement || 
                document.mozFullScreenElement || 
                document.msFullscreenElement) {
                // 进入全屏
                console.log("已进入浏览器全屏模式");
                
                // 确保我们的UI也反映了全屏状态
                if (!phoneContainer.classList.contains('zoomed')) {
                    phoneContainer.classList.add('zoomed');
                    overlay.classList.add('active');
                }
            } else {
                // 退出全屏
                console.log("已退出浏览器全屏模式");
                
                // 确保我们的UI也反映了非全屏状态
                if (phoneContainer.classList.contains('zoomed')) {
                    phoneContainer.classList.remove('zoomed');
                    phoneContainer.classList.remove('landscape');
                    overlay.classList.remove('active');
                    resetPhoneScale();
                }
            }
        }
        
        // 根据设备大小调整缩放比例
        function adjustZoomScale() {
            const windowHeight = window.innerHeight;
            const windowWidth = window.innerWidth;
            const isLandscape = phoneContainer.classList.contains('landscape');
            
            let scale = 1.5; // 默认缩放比例
            
            if (isLandscape) {
                if (windowHeight < 600) {
                    scale = 0.6;
                } else if (windowHeight < 700) {
                    scale = 0.8;
                } else if (windowHeight < 900 || windowWidth < 500) {
                    scale = 1;
                } else {
                    scale = 1.2;
                }
                phoneContainer.style.transform = `translate(-50%, -50%) scale(${scale}) rotate(90deg)`;
            } else {
                if (windowHeight < 600) {
                    scale = 0.8;
                } else if (windowHeight < 700) {
                    scale = 1;
                } else if (windowHeight < 900 || windowWidth < 500) {
                    scale = 1.3;
                } else {
                    scale = 1.5;
                }
                phoneContainer.style.transform = `translate(-50%, -50%) scale(${scale})`;
            }
        }
        
        // 重置手机缩放比例
        function resetPhoneScale() {
            const windowHeight = window.innerHeight;
            const windowWidth = window.innerWidth;
            
            if (windowHeight < 600) {
                phoneContainer.style.transform = 'scale(0.5)';
            } else if (windowHeight < 700) {
                phoneContainer.style.transform = 'scale(0.6)';
            } else if (windowHeight < 900) {
                phoneContainer.style.transform = 'scale(0.7)';
            } else if (windowWidth < 500) {
                phoneContainer.style.transform = 'scale(0.65)';
            } else {
                phoneContainer.style.transform = 'scale(0.85)';
            }
        }
        
        // 高亮显示放大按钮，引导用户点击
        function highlightZoomButton() {
            zoomButton.style.animation = 'pulse 1.5s infinite';
            zoomButton.style.boxShadow = '0 0 0 4px rgba(168, 117, 255, 0.7), 0 4px 8px rgba(0, 0, 0, 0.3)';
        }
        
        // 重置放大按钮高亮
        function resetZoomButtonHighlight() {
            zoomButton.style.animation = '';
            zoomButton.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.3)';
        }

        // 关闭按钮点击事件
        closeButton.addEventListener('click', exitFullscreen);
        
        // 旋转按钮点击事件
        rotateButton.addEventListener('click', () => {
            phoneContainer.classList.toggle('landscape');
            
            // 根据是否处于横屏模式调整缩放比例
            if (phoneContainer.classList.contains('landscape')) {
                const scale = window.innerHeight < 700 ? 0.8 : 1.2;
                phoneContainer.style.transform = `translate(-50%, -50%) scale(${scale}) rotate(90deg)`;
            } else {
                const scale = window.innerHeight < 700 ? 1 : 1.5;
                phoneContainer.style.transform = `translate(-50%, -50%) scale(${scale})`;
            }
        });
        
        // 点击遮罩层关闭放大模式
        overlay.addEventListener('click', exitFullscreen);
        
        // 按ESC键关闭放大模式
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && phoneContainer.classList.contains('zoomed')) {
                exitFullscreen();
            }
            
            // 按F12键切换全屏模式
            if (e.key === 'F12') {
                e.preventDefault(); // 阻止默认的F12开发者工具
                toggleFullscreen();
            }
        });
        
        // 监听窗口大小变化，自动调整手机预览的显示
        window.addEventListener('resize', function() {
            if (phoneContainer.classList.contains('zoomed')) {
                // 根据是否处于横屏模式调整缩放比例
                if (phoneContainer.classList.contains('landscape')) {
                    const scale = window.innerHeight < 700 ? 0.8 : 1.2;
                    phoneContainer.style.transform = `translate(-50%, -50%) scale(${scale}) rotate(90deg)`;
                } else {
                    const scale = window.innerHeight < 700 ? 1 : 1.5;
                    phoneContainer.style.transform = `translate(-50%, -50%) scale(${scale})`;
                }
            } else {
                resetPhoneScale();
            }
        });

        // 更新提示文本，移除自动全屏的提示
        document.addEventListener('DOMContentLoaded', function() {
            // 更新提示文本
            const tipsElement = document.querySelector('.tips');
            if (tipsElement) {
                tipsElement.innerHTML = '<p style="margin-bottom: 0;"><strong>提示：</strong>点击右下角<span style="color: #A875FF; font-weight: bold;">紫色放大按钮</span>可进入全屏预览模式。在全屏模式下，点击旋转按钮可切换横竖屏显示，点击关闭按钮或按<span style="color: #A875FF; font-weight: bold;">ESC键</span>可退出全屏。</p>';
            }
        });
    </script>
</body>
</html> 