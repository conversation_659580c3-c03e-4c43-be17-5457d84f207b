/* pages/comm/comm.wxss */
page {
  color: #333;
  background: linear-gradient(135deg, #f6f0ff, #e1bee7, #d1c4e9);
  min-height: 100vh;
}

.app-container {
  display: flex;
  flex-direction: column;
  padding: 20px;
  box-sizing: border-box;
  height: 100vh;
}

/* 顶部标题和设备信息 */
.app-header {
  margin-bottom: 20px;
}

.app-title {
  font-size: 24px;
  font-weight: bold;
  color: #6a1b9a;
  text-align: center;
  margin-bottom: 10px;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
}

.device-connection {
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  padding: 12px 15px;
  box-shadow: 0 2px 8px rgba(156, 39, 176, 0.15);
  font-size: 14px;
  color: #673ab7;
}

.connection-info {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.device-mac {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
}

.connection-status {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 2px;
}

.status-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-right: 5px;
}

.connected {
  background-color: #4caf50;
  box-shadow: 0 0 5px #4caf50;
}

.disconnected {
  background-color: #f44336;
  box-shadow: 0 0 5px #f44336;
}

.status-text {
  font-weight: 500;
}

/* 控制面板 */
.control-panel {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 20px;
}

.status-card {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(156, 39, 176, 0.15);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.card-title {
  font-size: 16px;
  font-weight: bold;
  color: #6a1b9a;
}

.card-action {
  color: #9c27b0;
  font-size: 14px;
  text-decoration: underline;
}

/* 锁状态样式 */
.lock-status {
  display: flex;
  align-items: center;
}

.status-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  margin-right: 15px;
  box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

.unlocked {
  background: linear-gradient(135deg, #4caf50, #8bc34a);
  color: white;
}

.locked {
  background: linear-gradient(135deg, #f44336, #e53935);
  color: white;
}

.unknown {
  background: linear-gradient(135deg, #9e9e9e, #757575);
  color: white;
}

.status-info {
  flex: 1;
}

.status-label {
  font-size: 14px;
  color: #9e9e9e;
  margin-bottom: 5px;
}

.status-value {
  font-size: 18px;
  font-weight: 500;
}

.unlocked-text {
  color: #4caf50;
}

.locked-text {
  color: #f44336;
}

.unknown-text {
  color: #9e9e9e;
}

/* 电池状态样式 */
.battery-status {
  display: flex;
  flex-direction: column;
}

.battery-level-container {
  width: 100%;
  height: 16px;
  background: rgba(0,0,0,0.05);
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 15px;
}

.battery-level {
  height: 100%;
  border-radius: 8px;
  transition: width 0.8s ease;
}

.high {
  background: linear-gradient(to right, #4caf50, #8bc34a);
}

.medium {
  background: linear-gradient(to right, #ff9800, #ffeb3b);
}

.low {
  background: linear-gradient(to right, #f44336, #ff9800);
}

.battery-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.battery-percentage {
  display: flex;
  flex-direction: column;
}

.percentage-value {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

.percentage-label {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 10px;
  color: white;
  margin-top: 5px;
  align-self: flex-start;
}

.high-text {
  background-color: #4caf50;
}

.medium-text {
  background-color: #ff9800;
}

.low-text {
  background-color: #f44336;
}

.battery-voltage {
  font-size: 14px;
  color: #673ab7;
}

/* 快捷操作按钮 */
.action-buttons {
  display: flex;
  justify-content: space-between;
  gap: 10px;
  margin-bottom: 20px;
}

.action-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 15px 0;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
  color: white;
  font-size: 14px;
  border: none;
}

.btn-icon {
  font-size: 24px;
  margin-bottom: 5px;
}

.unlock {
  background: linear-gradient(135deg, #9c27b0, #7b1fa2);
}

.buzzer {
  background: linear-gradient(135deg, #673ab7, #512da8);
}

.status {
  background: linear-gradient(135deg, #3f51b5, #303f9f);
}

.back {
  background: linear-gradient(135deg, #607d8b, #455a64);
}

/* 数据传输区域 */
.data-section {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(156, 39, 176, 0.15);
  margin-bottom: 15px;
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 150px;
}

.data-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(156, 39, 176, 0.1);
}

.data-title {
  font-size: 16px;
  font-weight: bold;
  color: #6a1b9a;
}

.data-stats {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #673ab7;
  background-color: rgba(156, 39, 176, 0.05);
  padding: 4px 10px;
  border-radius: 10px;
}

.data-display {
  flex: 1;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  padding: 10px;
  margin-bottom: 12px;
  border: 1px solid rgba(156, 39, 176, 0.1);
}

.data-content {
  font-family: monospace;
  font-size: 14px;
  color: #333;
  word-break: break-all;
  line-height: 1.4;
}

.data-input-area {
  display: flex;
  gap: 10px;
}

.data-input {
  flex: 1;
  background: white;
  border-radius: 8px;
  padding: 10px 12px;
  font-size: 14px;
  border: 1px solid rgba(156, 39, 176, 0.2);
}

.data-send-btn {
  background: linear-gradient(135deg, #9c27b0, #673ab7);
  color: white;
  border-radius: 8px;
  font-size: 14px;
  padding: 0 20px;
  border: none;
  box-shadow: 0 2px 5px rgba(156, 39, 176, 0.3);
}

.btn-hover {
  background: linear-gradient(135deg, #7b1fa2, #512da8);
  transform: translateY(1px);
}

/* 底部提示 */
.footer-tip {
  text-align: center;
  font-size: 14px;
  color: #7e57c2;
  padding: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.footer-divider {
  width: 30%;
  height: 2px;
  background: linear-gradient(to right, transparent, rgba(126, 87, 194, 0.5), transparent);
  margin-bottom: 10px;
  border-radius: 2px;
}

/* 模态框样式 */
.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 999;
}

.modal-dialog {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  background: white;
  border-radius: 12px;
  overflow: hidden;
  z-index: 1000;
  box-shadow: 0 5px 15px rgba(0,0,0,0.3);
}

.modal-title {
  padding: 15px;
  text-align: center;
  font-size: 18px;
  font-weight: bold;
  color: #6a1b9a;
  border-bottom: 1px solid rgba(156, 39, 176, 0.1);
}

.modal-content {
  padding: 15px;
}

.modal-input {
  border: 1px solid rgba(156, 39, 176, 0.2);
  border-radius: 8px;
  padding: 10px;
}

.input {
  width: 100%;
  font-size: 16px;
}

.modal-footer {
  display: flex;
  border-top: 1px solid rgba(156, 39, 176, 0.1);
}

.btn-cancel, .btn-confirm {
  flex: 1;
  padding: 15px 0;
  text-align: center;
  font-size: 16px;
}

.btn-cancel {
  color: #9e9e9e;
  border-right: 1px solid rgba(156, 39, 176, 0.1);
}

.btn-confirm {
  color: #9c27b0;
  font-weight: 500;
}

/* 简化的提示框 */
.dialog-screen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 999;
  display: flex;
  justify-content: center;
  align-items: center;
}

.dialog-box {
  width: 80%;
  max-width: 300px;
  background: white;
  border-radius: 16px;
  overflow: hidden;
  z-index: 1000;
  box-shadow: 0 8px 20px rgba(0,0,0,0.25);
}

.dialog-content {
  padding: 25px 20px;
  text-align: center;
  font-size: 16px;
  color: #6a1b9a;
  line-height: 1.5;
  font-weight: 500;
}

/* 设备MAC地址区域样式 */
.device-mac {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.mac-label {
  font-size: 13px;
  color: #7e57c2;
  font-weight: 500;
  margin-right: 5px;
}

.mac-value {
  font-family: monospace;
  font-size: 13px;
  color: #333;
  background-color: rgba(0, 0, 0, 0.03);
  padding: 3px 8px;
  border-radius: 4px;
  max-width: 140px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin: 0 5px;
}

/* MAC地址复制按钮样式 */
.copy-mac-btn {
  margin-left: 5px;
  background: linear-gradient(135deg, #7e57c2, #673ab7);
  color: white;
  padding: 3px 10px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(126, 87, 194, 0.3);
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
}

.copy-icon {
  margin-right: 3px;
  font-size: 13px;
}

.copy-mac-btn:active {
  transform: scale(0.95);
  background: linear-gradient(135deg, #673ab7, #512da8);
  box-shadow: 0 1px 2px rgba(126, 87, 194, 0.3);
}

/* 二维码按钮样式 */
.qrcode-btn {
  margin-left: 5px;
  background: linear-gradient(135deg, #3f51b5, #303f9f);
  color: white;
  padding: 3px 10px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(63, 81, 181, 0.3);
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
}

.qrcode-icon {
  margin-right: 3px;
  font-size: 13px;
}

.qrcode-btn:active {
  transform: scale(0.95);
  background: linear-gradient(135deg, #303f9f, #1a237e);
  box-shadow: 0 1px 2px rgba(63, 81, 181, 0.3);
}

/* 二维码模态框样式 */
.qrcode-modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  z-index: 999;
  display: flex;
  justify-content: center;
  align-items: center;
}

.qrcode-modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 85%;
  max-width: 320px;
  background: white;
  border-radius: 16px;
  overflow: hidden;
  z-index: 1000;
  box-shadow: 0 8px 20px rgba(0,0,0,0.25);
  display: flex;
  flex-direction: column;
}

.qrcode-modal-header {
  padding: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(156, 39, 176, 0.1);
}

.qrcode-modal-title {
  font-size: 18px;
  font-weight: bold;
  color: #6a1b9a;
}

.qrcode-modal-close {
  font-size: 24px;
  color: #9e9e9e;
  padding: 0 10px;
}

.qrcode-modal-content {
  padding: 25px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.qrcode-canvas-container {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 10px 0 20px 0;
}

.qrcode-canvas {
  background: white;
  padding: 20px;
  border: 1px solid rgba(156, 39, 176, 0.1);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  margin: 0 auto;
}

.qrcode-device-info {
  margin-top: 15px;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.qrcode-device-mac, .qrcode-device-url {
  font-size: 14px;
  color: #673ab7;
  text-align: center;
  word-break: break-all;
}

.qrcode-modal-footer {
  padding: 15px;
  border-top: 1px solid rgba(156, 39, 176, 0.1);
  display: flex;
  justify-content: center;
}

.qrcode-save-btn {
  background: linear-gradient(135deg, #9c27b0, #673ab7);
  color: white;
  border-radius: 30px;
  font-size: 16px;
  padding: 8px 30px;
  border: none;
  box-shadow: 0 4px 8px rgba(156, 39, 176, 0.3);
  transition: all 0.3s ease;
}

.qrcode-save-btn:active {
  transform: scale(0.95);
  background: linear-gradient(135deg, #7b1fa2, #512da8);
  box-shadow: 0 2px 4px rgba(156, 39, 176, 0.3);
}
