<view data-event-opts="{{[['touchmove',[['',['$event']]]]]}}" class="container page-index" bindtouchmove="__e"><view class="page-background"><image class="background-image" src="https://wuji1112-1312674791.cos.ap-guangzhou.myqcloud.com/static/编组 <EMAIL>" mode="aspectFill"></image></view><view class="status-bar safe-area-inset-top"></view><view data-event-opts="{{[['touchmove',[['',['$event']]]]]}}" class="content" catchtouchmove="__e"><view class="welcome-section"><view data-event-opts="{{[['tap',[['navigateToZhaoshang',['$event']]]]]}}" class="banner-container" bindtap="__e"><image class="banner-image" src="https://wuji1112-1312674791.cos.ap-guangzhou.myqcloud.com/static/<EMAIL>" mode="contain"></image></view><view class="welcome-text"></view></view><view class="quick-actions"><view class="scan-title-container"><view class="neon-title">点击爱心</view><view class="neon-title mt-xs">扫码开门</view></view><view class="action-container"><view data-event-opts="{{[['tap',[['scanQRCode',['$event']]]]]}}" class="scan-button-wrapper" bindtap="__e"><view class="scan-icon-container"><text class="material-icons heart-icon">favorite</text></view></view></view><view class="agreement-tip text-tertiary"><text>点击开门即视为同意《用户协议》及《隐私协议》</text></view></view><view class="bottom-space"></view></view><custom-tab-bar class="vue-ref" vue-id="8dd740cc-1" data-ref="tabBar" bind:__l="__l"></custom-tab-bar><global-login class="vue-ref" vue-id="8dd740cc-2" data-ref="globalLogin" bind:__l="__l"></global-login></view>