const tabBar = {
	init() {
		// 初始化底部导航
		console.log('初始化TabBar...');
		this.update();
	},
	
	update(selectedIndex = 0) {
		// 获取当前页面路径并更新选中状态
		try {
			const pages = getCurrentPages();
			const currentPage = pages[pages.length - 1];
			const currentPath = '/' + currentPage.route;
			
			// TabBar页面列表
			const tabBarPages = [
				"/pages/index/index",
				"/pages/profile/profile"
			];
			
			// 查找当前路径的索引
			let index = tabBarPages.findIndex(page => page === currentPath);
			
			// 如果提供了选中索引，优先使用
			if (selectedIndex !== undefined) {
				index = selectedIndex;
			}
			
			// 如果找到匹配的索引，设置选中
			if (index !== -1) {
				if (currentPage.$vm && currentPage.$vm.$refs.tabBar) {
					currentPage.$vm.$refs.tabBar.selected = index;
				} else if (currentPage.$vm) {
					// 尝试广播消息给tabBar组件
					uni.$emit('tabBarUpdate', { index });
				}
			}
		} catch(e) {
			console.error('更新TabBar失败', e);
		}
	},
	
	// 为特定页面隐藏TabBar
	hide() {
		// 使用原生方法隐藏TabBar
		uni.hideTabBar({
			animation: false
		});
	},
	
	// 为特定页面显示TabBar
	show() {
		// 获取当前页面
		const pages = getCurrentPages();
		const currentPage = pages[pages.length - 1];
		
		// 仅在TabBar页面中显示
		const tabBarPages = [
			"pages/index/index",
			"pages/profile/profile"
		];
		
		if (currentPage && tabBarPages.includes(currentPage.route)) {
			// 设置TabBar的显示状态
			const app = getApp();
			if (app && app.$vm) {
				app.$vm.$refs.tabBar && (app.$vm.$refs.tabBar.style.display = 'flex');
			}
		}
	}
};

export default tabBar; 