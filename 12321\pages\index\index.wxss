page {
  color: #333;
  background: linear-gradient(135deg, #f6f0ff, #e1bee7, #d1c4e9);
  min-height: 100vh;
}

.app-container {
  display: flex;
  flex-direction: column;
  padding: 20px;
  box-sizing: border-box;
  height: 100vh;
}

.app-header {
  margin-bottom: 20px;
  text-align: center;
}

.app-title {
  font-size: 24px;
  font-weight: bold;
  color: #6a1b9a;
  margin-bottom: 5px;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
}

.app-subtitle {
  font-size: 14px;
  color: #9575cd;
  font-weight: 300;
}

/* 状态指示器 */
.status-indicators {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 10px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 4px 10px;
  border-radius: 15px;
  font-size: 12px;
}

.status-ok {
  background-color: rgba(76, 175, 80, 0.2);
  color: #2e7d32;
}

.status-error {
  background-color: rgba(244, 67, 54, 0.2);
  color: #c62828;
}

.status-busy {
  background-color: rgba(255, 152, 0, 0.2);
  color: #e65100;
  animation: pulse 1.5s infinite;
}

.status-idle {
  background-color: rgba(33, 150, 243, 0.2);
  color: #1565c0;
}

@keyframes pulse {
  0% { opacity: 0.6; }
  50% { opacity: 1; }
  100% { opacity: 0.6; }
}

/* 华为设备特殊提示 */
.huawei-tips {
  background-color: rgba(204, 0, 0, 0.08);
  border-left: 3px solid #c00;
  margin: 10px 0;
  border-radius: 0 5px 5px 0;
  padding: 10px;
}

.huawei-tips-content {
  display: flex;
  align-items: center;
}

.huawei-tips-icon {
  font-size: 20px;
  margin-right: 10px;
}

.huawei-tips-text {
  display: flex;
  flex-direction: column;
}

.huawei-tips-detail {
  font-size: 12px;
  color: #666;
  margin-top: 5px;
}

.huawei-tips-actions {
  margin-top: 10px;
  display: flex;
  justify-content: flex-end;
}

.huawei-tips-button {
  background-color: #c00;
  color: white;
  font-size: 12px;
  padding: 0 15px;
  height: 30px;
  line-height: 30px;
  border-radius: 15px;
}

.huawei-tips-button::after {
  border: none;
}

.huawei-tips-button:active {
  opacity: 0.8;
}

/* 操作锁定提示 */
.operation-lock-tips {
  background-color: rgba(255, 152, 0, 0.1);
  border-left: 3px solid #ff9800;
  margin: 10px 0;
  border-radius: 0 5px 5px 0;
  padding: 12px;
  animation: fade-in-out 2s infinite;
}

@keyframes fade-in-out {
  0% { background-color: rgba(255, 152, 0, 0.05); }
  50% { background-color: rgba(255, 152, 0, 0.15); }
  100% { background-color: rgba(255, 152, 0, 0.05); }
}

.operation-lock-content {
  display: flex;
  align-items: center;
}

.operation-lock-icon {
  font-size: 20px;
  margin-right: 12px;
}

.operation-lock-text {
  display: flex;
  flex-direction: column;
}

.operation-lock-detail {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

/* 搜索容器 */
.search-container {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16px;
  padding: 15px;
  margin-bottom: 20px;
  box-shadow: 0 4px 12px rgba(156, 39, 176, 0.15);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.search-status {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 15px;
  color: #673ab7;
  font-size: 14px;
}

.devices-count {
  margin-top: 5px;
  font-weight: bold;
  color: #9c27b0;
}

.scan-button {
  width: 80%;
  background: linear-gradient(135deg, #9c27b0, #673ab7);
  color: white;
  border-radius: 30px;
  font-size: 16px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 12px 0;
  box-shadow: 0 4px 8px rgba(156, 39, 176, 0.3);
  transition: all 0.3s ease;
}

.scan-icon {
  margin-right: 8px;
  font-size: 18px;
}

.scan-button-hover {
  background: linear-gradient(135deg, #7b1fa2, #512da8);
  transform: translateY(2px);
  box-shadow: 0 2px 4px rgba(156, 39, 176, 0.3);
}

/* 设备列表标题区域 */
.device-list-header {
  padding: 15px;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 16px 16px 0 0;
  border-bottom: 1px solid #eee;
  margin-bottom: 0;
  box-sizing: border-box;
  width: 100%;
}

.device-list-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.device-list-subtitle {
  font-size: 14px;
  color: #666;
  margin-top: 5px;
}

.search-tip {
  margin-top: 8px;
  padding: 6px 10px;
  background-color: #e6f7ff;
  border-radius: 4px;
  font-size: 13px;
  color: #1890ff;
  display: inline-block;
}

/* 设备列表 */
.device-list {
  height: calc(100vh - 460px);
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 0 0 16px 16px;
  padding: 15px;
  margin-bottom: 15px;
  box-sizing: border-box;
  width: 100%;
}

.no-device {
  padding: 40px 0;
  text-align: center;
  color: #999;
}

.no-device-icon {
  font-size: 48px;
  margin-bottom: 15px;
}

.no-device-text {
  font-size: 16px;
  margin-bottom: 8px;
}

.no-device-tip {
  font-size: 14px;
  color: #666;
}

.device-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  margin: 10px 0;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.3s ease;
  box-sizing: border-box;
  width: 100%;
}

.device-item:active {
  transform: scale(0.98);
}

.lock-device {
  border-left: 4px solid #9c27b0;
  background-color: rgba(156, 39, 176, 0.05);
}

/* 新增连接状态样式 */
.connecting-device {
  border-left: 4px solid #ff9800;
  background-color: rgba(255, 152, 0, 0.05);
  animation: pulse-border 1.5s infinite;
}

.connected-device {
  border-left: 4px solid #4caf50;
  background-color: rgba(76, 175, 80, 0.05);
  box-shadow: 0 2px 12px rgba(76, 175, 80, 0.25);
}

@keyframes pulse-border {
  0% { border-left-color: rgba(255, 152, 0, 0.5); }
  50% { border-left-color: rgba(255, 152, 0, 1); }
  100% { border-left-color: rgba(255, 152, 0, 0.5); }
}

.device-info {
  flex: 1;
}

.device-name {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
}

.device-id {
  font-size: 12px;
  color: #999;
  margin-bottom: 5px;
  word-break: break-all;
}

.device-rssi {
  font-size: 12px;
  color: #666;
}

.device-type {
  margin-left: 15px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.lock-icon {
  font-size: 24px;
  color: #9c27b0;
}

.device-icon {
  font-size: 24px;
  color: #999;
}

/* 设备状态指示器 */
.device-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 5px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-bottom: 3px;
}

.connecting-dot {
  background-color: #ff9800;
  animation: pulse-dot 1.5s infinite;
}

.connected-dot {
  background-color: #4caf50;
}

@keyframes pulse-dot {
  0% { transform: scale(0.8); opacity: 0.5; }
  50% { transform: scale(1.2); opacity: 1; }
  100% { transform: scale(0.8); opacity: 0.5; }
}

.status-text {
  font-size: 10px;
  color: #666;
}

/* 底部提示 */
.footer-tip {
  padding: 10px;
  text-align: center;
  font-size: 14px;
  color: #666;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 16px;
  margin-bottom: 15px;
}

/* 刷新按钮 */
.refresh-button-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
}

.refresh-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #7b1fa2, #6a1b9a);
  color: white;
  border-radius: 30px;
  padding: 12px 25px;
  font-size: 16px;
  box-shadow: 0 4px 12px rgba(106, 27, 154, 0.4);
  border: none;
  width: 80%;
  position: relative;
  overflow: hidden;
}

.refresh-button::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0));
  border-radius: 30px;
}

.refresh-icon {
  margin-right: 8px;
  font-size: 18px;
  animation: spin 2s infinite linear paused;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.refresh-button:active .refresh-icon {
  animation-play-state: running;
}

.refresh-button:active {
  transform: scale(0.95);
  background: linear-gradient(135deg, #6a1b9a, #4a148c);
  box-shadow: 0 2px 6px rgba(106, 27, 154, 0.3);
}

.refresh-button-hint {
  margin-top: 10px;
  font-size: 12px;
  color: #7e57c2;
  text-align: center;
}

/* 错误提示区域 */
.error-container {
  margin-top: 15px;
  margin-bottom: 10px;
  animation: pulse 2s infinite;
}

.error-message {
  background-color: rgba(255, 152, 0, 0.1);
  border-left: 3px solid #ff9800;
  padding: 10px 15px;
  border-radius: 0 5px 5px 0;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  color: #e65100;
}

.error-icon {
  font-size: 16px;
}

/* 禁用状态的按钮 */
.scan-button[disabled], .refresh-button[disabled] {
  background: linear-gradient(135deg, #9e9e9e, #757575);
  opacity: 0.7;
  cursor: not-allowed;
}

.refresh-button[disabled] .refresh-icon {
  animation: none;
}

.scan-button[disabled]:active, .refresh-button[disabled]:active {
  transform: none;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* 适配深色模式 */
@media (prefers-color-scheme: dark) {
  .status-ok {
    background-color: rgba(76, 175, 80, 0.3);
    color: #81c784;
  }
  
  .status-error {
    background-color: rgba(244, 67, 54, 0.3);
    color: #e57373;
  }
  
  .refresh-button-hint {
    color: #9e9e9e;
  }
}