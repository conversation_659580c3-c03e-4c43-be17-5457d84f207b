<template>
	<view class="container page-device">
		<!-- 添加页面背景 -->
		<view class="page-background">
			<!-- 背景图片 -->
			<image class="background-image" src="https://wuji1112-1312674791.cos.ap-guangzhou.myqcloud.com/static/编组 <EMAIL>" mode="aspectFill" :style="{opacity: isPageLoaded ? 1 : 0}" @load="onBackgroundImageLoaded"></image>
		</view>
		
		<!-- 顶部状态栏占位 -->
		<view class="status-bar safe-area-inset-top"></view>
		
		<!-- 导航栏 -->
		<view class="nav-bar">
			<!-- 移除返回按钮 -->
		</view>
		
		<!-- 顶部banner (放在页面顶部，而不是content内) -->
		<view class="banner-container"
			  @tap="handleBannerClick"
			  @click="handleBannerClick"
			  @touchstart="handleBannerTouch"
			  @touchend="handleBannerTouchEnd"
			  @touchcancel="handleBannerTouchCancel">
			<image class="banner-image" src="https://wuji1112-1312674791.cos.ap-guangzhou.myqcloud.com/static/<EMAIL>" mode="contain"></image>
			<view class="banner-glow"></view>
		</view>

		<!-- 内容区域 -->
		<view class="content">
			<!-- 蓝牙连接状态区域 -->
			<view class="connect-section">
				<view class="neon-title status-title">
					<view>支付完成</view>
					<view>门锁自动打开</view>
				</view>

				<!-- 登录提示 -->
				<view v-if="!isLoggedIn" class="login-prompt">
					<view class="login-prompt-text">
						<text class="material-icons">account_circle</text>
						<text>请先登录以使用设备</text>
					</view>
					<view class="login-prompt-button" @click="showLoginModal = true">
						<text>一键登录</text>
					</view>
				</view>

				<!-- 操作按钮 -->
				<view class="action-buttons">
					<!-- 单一多功能按钮 -->
					<view class="primary-button" 
						:class="mainActionClass"
						@click="handleMainAction">
						<view class="ripple-layer"></view>
						<text class="material-icons">{{mainActionIcon}}</text>
						<text>{{mainActionText}}</text>
					</view>
				</view>
				
				<!-- 连接状态展示 -->
				<view class="connection-status" :class="{'connected': isConnected, 'disconnected': !isConnected && !isConnecting}">
					<text class="material-icons" v-if="isConnected">bluetooth_connected</text>
					<text class="material-icons" v-else-if="isConnecting">bluetooth_searching</text>
					<text class="material-icons" v-else>bluetooth_disabled</text>
					<view class="connection-text" :class="{'text-connected': isConnected, 'text-connecting': isConnecting, 'text-disconnected': !isConnected && !isConnecting}">
						{{connectionStatusText}}
					</view>
				</view>
				
				<!-- 连接提示 -->
				<view class="connection-tips" >
					<text class="text-tertiary">请确保手机蓝牙已开启，并靠近设备</text>
					<text class="text-agreement">点击开门即视为同意《用户协议》及《隐私协议》</text>
				</view>
			
				
				
			</view>
			
		
			
		</view>
		
		
		
		<!-- 支付弹窗 -->
		<view class="modal-overlay" v-if="showPaymentModal" @click="closePaymentModal"></view>
		<view class="payment-modal" v-if="showPaymentModal">
			<view class="modal-header">
				<text class="modal-title">支付确认</text>
				<text class="material-icons close-icon" @click="closePaymentModal">close</text>
			</view>
			<view class="modal-body">
				<view class="payment-info">
					<view class="payment-device-name">{{deviceName || '智能门锁'}}</view>
					<view class="payment-shop-name" v-if="deviceInfo && deviceInfo.shopName">{{deviceInfo.shopName}}</view>
					<view class="payment-price">{{priceText}}</view>
				</view>
				<view class="payment-button" @click="processPayment" :class="{'loading': paymentLoading}">
					<text v-if="!paymentLoading">确认支付</text>
					<view class="loading-spinner" v-else></view>
				</view>
				<view class="payment-tips">点击确认支付后将跳转到微信支付</view>
				<view class="payment-tips" v-if="!isConnected">支付成功后，您需要连接设备才能开门</view>
			</view>
		</view>
		
		<!-- 开门弹窗 -->
		<view class="modal-overlay" v-if="showOpenDoorModal" @click="closeOpenDoorModal"></view>
		<view class="open-door-modal" v-if="showOpenDoorModal">
			<view class="modal-header">
				<text class="modal-title">开门确认</text>
				<text class="material-icons close-icon" @click="closeOpenDoorModal">close</text>
			</view>
			<view class="modal-body">
				<view class="door-info">
					<view class="door-icon">
						<text class="material-icons">lock_open</text>
					</view>
					<view class="door-message">您需要先支付才能开门</view>
					<view class="door-device-name">{{deviceName || '智能门锁'}}</view>
					<!-- 添加门店信息显示 -->
					<view class="door-shop-name" v-if="deviceInfo && deviceInfo.shopName">{{deviceInfo.shopName}}</view>
					<view class="door-price">{{priceText}}</view>
				</view>
				<view class="door-buttons">
					<view class="door-cancel-button" @click="closeOpenDoorModal">
						<text>取消</text>
					</view>
					<view class="door-pay-button" @click="showPaymentModal = true; closeOpenDoorModal();">
						<text>去支付</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 添加音频元素 -->
		<audio id="successAudio" src="https://wuji1112-1312674791.cos.ap-guangzhou.myqcloud.com/static/audio/ttsmaker-file-2025-6-11-20-49-17.mp3" style="display: none;"></audio>

		<!-- 登录模态框 -->
		<login-modal
			:visible="showLoginModal"
			@close="handleCloseLoginModal"
			@login-success="handleLoginSuccess"
			@login-fail="handleLoginFail">
		</login-modal>
	</view>
</template>

<script>
import API from '@/static/js/api.js';
import { lockService } from '@/utils/index.js';
import { request, get, post } from '@/utils/request.js'; // 导入请求库
import LoginModal from '@/components/login-modal/index.vue';

// 获取API基础URL
const getAPIBaseUrl = () => {
  if (API && API.baseUrl) {
    return API.baseUrl;
  }
  // 默认的API基础URL
  return 'https://api.jycb888.com/';
};

// 确保API对象可用
console.log('API导入检查:', API);

export default {
	components: {
		LoginModal
	},
	data() {
		return {
			// 设备信息
			deviceInfo: {},
			deviceId: '',
			deviceMac: '',
			deviceName: '',
			deviceStatus: 0, // 0-未知 1-空闲 2-使用中
			
			// 扫码类型 bluetooth-蓝牙扫码 miniapp-小程序扫码
			scanType: 'miniapp',
			
			// 订单信息
			orderId: '',
			orderStatus: -1, // -1-未知 0-未支付 1-已支付 2-已完成 3-已取消
			useStatus: 0, // 0-未使用 1-使用中 2-已完成
			orderAmount: 0.00,
			orderDuration: 0,
			roomId: '',
			
			// 锁状态
			isLockOpen: false,
			batteryLevel: 0,
			
			// 价格信息
			hourlyRate: 0.00,
			priceText: '￥0.00/小时',
			
			// 蓝牙连接状态
			isConnected: false,
			isConnecting: false,
			lastConnectTime: 0,
			connectionDebounceTimer: null,
			autoReconnect: true, // 是否自动重连
			statusUpdateTimer: null, // 状态更新防抖定时器
			bluetoothInitialized: false, // 蓝牙是否已初始化
			isRecentlyDisconnected: false, // 是否刚刚断开连接
			isManualDisconnect: false, // 是否手动断开连接
			isReconnecting: false, // 是否正在重连中
			reconnectLock: false, // 重连锁，防止重复重连

			// 开门防抖控制
			lastOpenTime: 0, // 上次开门时间
			openCooldown: 3000, // 开门冷却时间（3秒）
			cooldownTimer: null, // 冷却倒计时定时器
			currentTime: 0, // 当前时间，用于触发computed更新

			// 锁状态检查
			lockStatusCheckTimer: null, // 锁状态检查定时器
			lockStatusCheckInterval: 5000, // 检查间隔（5秒，避免过于频繁）
			lastLockStatusCheckTime: 0, // 上次检查时间
			lockStatusCheckEnabled: true, // 是否启用锁状态检查
			
			// 支付状态
			isPaid: false,
			isPaying: false,
			paymentLoading: false,
			paymentSuccess: false,
			showPaymentModal: false,
			
			// 开门状态
			doorOpenInProgress: false,
			doorOpenProcessed: false,
			doorOpenCompleted: false, // 新增：标记是否已完成开门操作
			showOpenDoorModal: false,
			
			// 页面状态
			isPageActive: true,
			isPageLoaded: false,
			autoConnectTimer: null,

			// 登录状态
			isLoggedIn: false,
			showLoginModal: false,
			userInfo: null,
			
			// 定时器
			statusTimer: null,
			
			// 模拟模式
			isSimulatedMode: false,
			
			// 添加UI状态定时更新机制
			uiUpdateTimer: null,
			
			// 添加音频相关变量
			successAudio: null,
			audioSrc: null,
			
			// 添加订单状态轮询相关变量
			orderPollingTimer: null,
			orderPollingCount: 0,
			maxPollingCount: 30, // 最大轮询次数，防止无限轮询
			pollingInterval: 2000, // 轮询间隔，单位毫秒

			// 导航状态
			isNavigating: false, // 防止重复导航
			lastNavigateTime: null, // 最后导航时间（防抖）

			// Banner触摸状态（iOS兼容性）
			bannerTouchStarted: false,
			bannerTouchStartTime: null,
		}
	},
	computed: {
		// 连接状态文本
		connectionStatusText() {
			if (this.isConnected) {
				return '蓝牙已连接';
			} else if (this.isConnecting) {
				return '蓝牙连接中...';
			} else {
				return '未连接';
			}
		},
		// 主操作按钮文本
		mainActionText() {
			// 如果用户未登录，显示登录
			if (!this.isLoggedIn) {
				return '登录';
			}

			if (this.isConnected) {
				if (this.isPaid) {
					// 检查是否在冷却期间（使用currentTime触发响应式更新）
					const now = this.currentTime || Date.now();
					const timeSinceLastOpen = now - this.lastOpenTime;

					if (timeSinceLastOpen < this.openCooldown && this.lastOpenTime > 0) {
						const remainingTime = Math.ceil((this.openCooldown - timeSinceLastOpen) / 1000);
						return `等待${remainingTime}秒`;
					}

					// 已支付状态，根据锁状态和开门完成状态显示不同文本
					if (this.doorOpenCompleted && this.isLockOpen) {
						return '已开门';
					} else if (this.doorOpenCompleted && !this.isLockOpen) {
						return '重新开门';
					} else {
						return '开门';
					}
				} else {
					return '支付';
				}
			} else if (this.isConnecting) {
				return '连接中';
			} else {
				// 未连接状态，如果已获取设备信息，显示支付
				return this.deviceId ? '支付' : '连接设备';
			}
		},
		// 主操作按钮图标
		mainActionIcon() {
			// 如果用户未登录，显示登录图标
			if (!this.isLoggedIn) {
				return 'account_circle';
			}

			if (this.isConnected) {
				if (this.isPaid) {
					return 'lock_open';
				} else {
					return 'payments';
				}
			} else if (this.isConnecting) {
				return 'bluetooth_searching';
			} else {
				// 未连接状态，如果已获取设备信息，显示支付图标
				return this.deviceId ? 'payments' : 'bluetooth_connected';
			}
		},
		// 主操作按钮类名
		mainActionClass() {
			if (this.isConnected) {
				if (this.isPaid) {
					return 'action-open';
				} else {
					return 'action-pay';
				}
			} else if (this.isConnecting) {
				return 'action-connecting';
			} else {
				// 未连接状态，如果已获取设备信息，使用支付按钮样式
				return this.deviceId ? 'action-pay' : 'action-connect';
			}
		},
		// 订单状态文本
		orderStatusText() {
			switch (this.orderStatus) {
				case 0:
					return '待支付';
				case 1:
					if (this.useStatus === 0) {
						return '待使用';
					} else if (this.useStatus === 1) {
						return '使用中';
					} else {
						return '已完成';
					}
				case 2:
					return '已完成';
				case 3:
					return '已取消';
				default:
					return '';
			}
		}
	},
	onLoad(options) {
		// 检查登录状态
		this.checkLoginStatus();

		// 特殊处理：如果是微信扫一扫，可能需要从URL中直接解析
		console.log('页面加载 - 原始参数 options:', options);
		console.log('参数类型检查:', {
			'typeof options': typeof options,
			'options.query类型': typeof options.query,
			'options.id类型': typeof options.id,
			'JSON.stringify(options)': JSON.stringify(options)
		});

		// 兼容微信扫一扫 query 参数
		let params = options;

		if (options.query) {
			console.log('检测到 query 参数:', options.query, '类型:', typeof options.query);
			try {
				if (typeof options.query === 'string') {
					// 解析 query 字符串，例如: "id=020022&scanType=miniapp"
					const queryParams = {};
					const queryString = decodeURIComponent(options.query);
					console.log('解码后的 query 字符串:', queryString);

					queryString.split('&').forEach(param => {
						const [key, value] = param.split('=');
						if (key && value) {
							queryParams[key] = value;
						}
					});

					console.log('解析后的 query 参数:', queryParams);
					params = { ...options, ...queryParams };
				} else {
					params = { ...options, ...options.query };
				}
			} catch (e) {
				console.error('解析 query 参数失败:', e);
				params = { ...options };
			}
		}

		console.log('设备页面加载，最终参数:', params);

		// 修复scanType参数处理逻辑
		this.scanType = params.scanType || 'miniapp'; // 默认miniapp
		this.orderId = params.orderId || '';
		this.roomId = params.roomId || '';

		// 如果有id参数但没有scanType，说明是小程序扫码
		if (params.id && !params.scanType) {
			this.scanType = 'miniapp';
		}

		// 强制确保scanType为miniapp（针对微信扫一扫）
		if (params.id && (params.id.length <= 10 && /^\d+$/.test(params.id))) {
			// 如果id是纯数字且长度较短，很可能是设备ID，强制设为miniapp模式
			this.scanType = 'miniapp';
			console.log('检测到数字设备ID，强制设置为miniapp模式:', params.id);
		}

		console.log('参数处理结果:', {
			scanType: this.scanType,
			orderId: this.orderId,
			roomId: this.roomId,
			'params.scanType': params.scanType,
			'强制设置前': params.scanType || 'miniapp',
			'最终scanType': this.scanType,
			allParams: params
		});

		console.log('确定的扫码类型:', this.scanType);

		// 早期检查：确保有设备标识
		const hasDeviceId = params.id || params.deviceId || params.deviceCode || params.mac || options.id || options.deviceId;
		console.log('早期设备标识检查:', {
			'params.id': params.id,
			'params.deviceId': params.deviceId,
			'params.deviceCode': params.deviceCode,
			'params.mac': params.mac,
			'options.id': options.id,
			'options.deviceId': options.deviceId,
			'hasDeviceId': hasDeviceId
		});

		if (!hasDeviceId) {
			console.error('所有可能的设备标识都为空');
			uni.showModal({
				title: '参数错误',
				content: '无法获取设备标识，请检查二维码是否正确',
				showCancel: false,
				confirmText: '返回',
				success: () => {
					uni.navigateBack();
				}
			});
			return;
		}

		// 如果有订单ID，设置到lockService并检查支付状态
		if (this.orderId) {
			lockService.setOrderId(this.orderId);
			console.log('设置订单ID到lockService:', this.orderId);
			this.getOrderStatus()
				.then(orderInfo => {
					console.log('页面加载时获取订单状态成功:', orderInfo);

					// 更新订单状态
					this.orderStatus = orderInfo.status;

					// 检查支付状态
					const isPaid = orderInfo.status === 1 || orderInfo.payStatus === 1;
					if (isPaid) {
						lockService.setOrderId(this.orderId);
						console.log('订单已支付，重新确认订单ID设置:', this.orderId);

						// 更新支付状态
						this.isPaid = true;
						this.paymentSuccess = true;

						console.log('页面加载时确认订单已支付，更新支付状态');
					} else {
						// 订单未支付
						this.isPaid = false;
						this.paymentSuccess = false;
						console.log('页面加载时确认订单未支付');
					}

					// 更新UI状态
					this.updateUIState();
				})
				.catch(err => {
					console.error('页面加载时获取订单状态失败:', err);
				});
		}

		console.log('开始处理设备标识 - 当前scanType:', this.scanType);
		console.log('可用的设备标识参数:', {
			'params.id': params.id,
			'params.deviceId': params.deviceId,
			'params.deviceCode': params.deviceCode,
			'params.mac': params.mac,
			'options.id': options.id,
			'options.deviceId': options.deviceId,
			'options.query': options.query
		});

		if (this.scanType === 'miniapp') {
			// 尝试多种方式获取设备ID
			let deviceId = params.id || params.deviceId || params.deviceCode || '';

			// 如果还是没有获取到，尝试从原始options中获取
			if (!deviceId && options) {
				deviceId = options.id || options.deviceId || options.deviceCode || '';
			}

			// 如果还是没有，尝试从URL字符串中直接解析
			if (!deviceId && options.query && typeof options.query === 'string') {
				try {
					// 直接从query字符串中查找id参数
					const match = options.query.match(/id=([^&]*)/);
					if (match && match[1]) {
						deviceId = decodeURIComponent(match[1]);
						console.log('从URL字符串中解析到设备ID:', deviceId);
					}
				} catch (e) {
					console.log('从URL字符串解析设备ID失败:', e);
				}
			}

			console.log('小程序扫码 - 从参数获取设备ID:', deviceId);
			console.log('参数详情:', {
				'params.id': params.id,
				'params.deviceId': params.deviceId,
				'params.deviceCode': params.deviceCode,
				'options.id': options.id,
				'最终deviceId': deviceId,
				'deviceId类型': typeof deviceId,
				'deviceId长度': deviceId ? deviceId.length : 0
			});

			if (deviceId && deviceId.trim()) {
				// 设置设备ID
				this.deviceId = deviceId.trim();
				console.log('设置设备ID成功:', this.deviceId);
				this.getDeviceInfo(this.deviceId);
			} else {
				console.error('设备ID为空或无效:', {
					deviceId,
					params,
					options,
					scanType: this.scanType,
					originalQuery: options.query
				});

				// 最后尝试：如果是从URL https://jycb888.com/pages/scan/device?id=020022&scanType=miniapp 进入
				// 可能设备ID就是020022这样的格式
				if (!deviceId && options.query) {
					// 尝试直接使用query中的数字作为设备ID
					const numberMatch = options.query.match(/\d+/);
					if (numberMatch && numberMatch[0]) {
						deviceId = numberMatch[0];
						console.log('从query中提取到数字作为设备ID:', deviceId);
						this.deviceId = deviceId;
						this.getDeviceInfo(deviceId);
						return;
					}
				}

				// 最终备用方案：如果是从特定URL进入，尝试使用默认设备ID
				if (options.query && options.query.includes('020022')) {
					console.log('检测到URL中包含020022，使用作为设备ID');
					this.deviceId = '020022';
					this.getDeviceInfo('020022');
					return;
				}

				// 给用户一个手动输入的机会
				uni.showModal({
					title: '设备ID获取失败',
					content: `无法从扫码中获取设备ID，请检查二维码是否正确。\n\n调试信息：\nquery: ${options.query || '无'}\nparams: ${JSON.stringify(params)}`,
					showCancel: true,
					cancelText: '返回',
					confirmText: '重试',
					success: (res) => {
						if (res.confirm) {
							// 重新尝试获取参数
							this.onLoad(options);
						} else {
							uni.navigateBack();
						}
					}
				});
			}
		} else {
			const mac = params.mac || params.id || ''; // 兼容处理，有时id参数可能是MAC地址
			console.log('蓝牙扫码模式 - 从参数获取MAC地址:', mac);
			console.log('当前scanType:', this.scanType, '原始params.scanType:', params.scanType);

			if (mac) {
				// 检查是否是有效的MAC地址格式
				if (this.isValidMacAddress(mac)) {
					console.log('检测到有效的MAC地址格式:', mac);
					lockService.setDeviceMac(mac);
					this.deviceMac = mac.toLowerCase();
					this.preinitBluetooth();
					this.getDeviceInfo(mac);
				} else {
					// 如果不是有效的MAC地址，可能是设备ID，转换为小程序模式
					console.log('检测到非MAC地址格式，转换为小程序模式:', mac);
					this.scanType = 'miniapp';
					this.deviceId = mac;
					this.getDeviceInfo(mac);
				}
			} else {
				console.error('设备标识为空 - 调试信息:', {
					'params.mac': params.mac,
					'params.id': params.id,
					'params': params,
					'options': options,
					'scanType': this.scanType
				});

				// 最后尝试：检查是否有任何可用的设备标识
				const anyId = params.deviceId || params.deviceCode || options.id || options.deviceId;
				if (anyId) {
					console.log('找到备用设备标识:', anyId);
					this.scanType = 'miniapp';
					this.deviceId = anyId;
					this.getDeviceInfo(anyId);
				} else {
					// 最终备用方案：如果是从特定URL进入，尝试使用默认设备ID
					if (options.query && options.query.includes('020022')) {
						console.log('在else分支检测到URL中包含020022，使用作为设备ID');
						this.scanType = 'miniapp';
						this.deviceId = '020022';
						this.getDeviceInfo('020022');
						return;
					}

					this.showError('设备标识不能为空');
					setTimeout(() => { uni.navigateBack(); }, 2000);
				}
			}
		}
	},
	onShow() {
		console.log('设备页面显示');
		this.isPageActive = true;

		// 尝试从本地存储恢复支付状态
		this.restorePaymentState();

		// 重新初始化蓝牙环境（重要：解决退出后重新进入连接不上的问题）
		this.reinitializeBluetooth();

		// 延迟检查连接状态，确保蓝牙初始化完成
		setTimeout(() => {
			this.verifyConnectionState();
		}, 2000);

		// 启动UI状态定时更新
		this.startUIUpdateTimer();

		// 设置蓝牙状态监听
		this.setupBluetoothListeners();
		
		// 如果有订单ID，总是检查最新的订单状态（重要：确保重新进入页面时能识别已支付状态）
		if (this.orderId) {
			console.log('页面显示时检查订单状态，当前订单ID:', this.orderId, '当前支付状态:', this.isPaid);
			this.getOrderStatus()
				.then(orderInfo => {
					console.log('页面显示时获取订单状态成功:', orderInfo);

					// 更新订单状态
					this.orderStatus = orderInfo.status;

					// 如果订单已支付，确保订单ID设置到lockService
					if (orderInfo.status === 1 || orderInfo.payStatus === 1) {
						console.log('订单已支付，确保订单ID设置到lockService:', this.orderId);
						lockService.setOrderId(this.orderId);
						this.isPaid = true;
						this.paymentSuccess = true;

						// 保存支付状态到本地存储
						this.savePaymentState();

						// 停止可能存在的轮询
						if (this.orderPollingTimer) {
							console.log('订单已支付，停止轮询');
							this.stopOrderPolling();
						}

						// 如果已支付但未连接设备，尝试连接
						if (!this.isConnected && !this.isConnecting) {
							console.log('已支付但未连接设备，尝试连接');
							this.tryConnectDevice();
						}
						// 如果已支付且已连接设备，检查是否需要开门
						else if (this.isConnected && !this.doorOpenProcessed && !this.doorOpenCompleted) {
							console.log('已支付且已连接设备，检查是否需要开门');
							this.checkOrderAndOpenDoor();
						}
					}
					// 如果订单未支付，并且没有在轮询，启动轮询
					else if ((orderInfo.status === 0 || orderInfo.status === null) && !this.orderPollingTimer) {
						console.log('订单未支付，启动订单状态轮询');
						this.isPaid = false;
						this.paymentSuccess = false;
						this.startOrderPolling();
					}

					// 更新UI状态
					this.updateUIState();
				})
				.catch(err => {
					console.error('页面显示时获取订单状态失败:', err);
				});
			return; // 防止执行后续连接逻辑
		}
		
		// 如果有订单ID，未支付，并且没有在轮询，启动轮询
		if (this.orderId && this.orderStatus === 0 && !this.isPaid && !this.orderPollingTimer) {
			console.log('页面显示时检测到未支付订单，启动订单状态轮询');
			this.startOrderPolling();
		}
		
		// 如果已支付但未连接设备，尝试连接
		if (this.isPaid && !this.isConnected && !this.isConnecting) {
			console.log('已支付但未连接设备，尝试连接');
			this.tryConnectDevice();
			return;
		}
		
		// 如果有MAC地址，尝试连接设备
		if (this.deviceMac && !this.isConnected && !this.isConnecting) {
			// 延迟连接，避免页面刚显示就连接导致的问题
			console.log('页面显示，准备尝试连接设备');
			this.autoConnectTimer = setTimeout(() => {
				this.debounceConnect();
			}, 500);
		}
	},
	onHide() {
		console.log('设备页面隐藏');
		this.isPageActive = false;
		
		// 停止UI状态定时更新
		this.clearUIUpdateTimer();
		
		// 如果正在支付或已支付但未连接设备，不清除状态
		if (this.isPaying || this.paymentLoading || (this.isPaid && !this.isConnected)) {
			console.log('正在支付或已支付但未连接设备，保留状态');
			// 不清除自动连接定时器
			return;
		}
		
		// 如果正在轮询订单状态，不清除轮询定时器
		if (this.orderPollingTimer) {
			console.log('正在轮询订单状态，保留轮询定时器');
			return;
		}
		
		// 清除自动连接定时器
		if (this.autoConnectTimer) {
			clearTimeout(this.autoConnectTimer);
			this.autoConnectTimer = null;
		}

		// 注意：不在onHide时断开蓝牙连接，因为用户可能只是切换到其他页面
		// 蓝牙连接保持，以便用户返回时能快速重连
		console.log('页面隐藏，保持蓝牙连接状态');
	},
	onUnload() {
		console.log('设备页面卸载');
		this.isPageActive = false;

		// 停止UI状态定时更新
		this.clearUIUpdateTimer();

		// 清除冷却定时器
		if (this.cooldownTimer) {
			clearInterval(this.cooldownTimer);
			this.cooldownTimer = null;
		}

		// 停止锁状态检查
		this.stopLockStatusCheck();

		// 重置冷却相关状态
		this.currentTime = 0;
		this.lastOpenTime = 0;

		// 停止订单状态轮询
		this.stopOrderPolling();

		// 断开蓝牙连接
		this.ensureDisconnectBluetooth();
	},
	methods: {
		// 检查登录状态
		checkLoginStatus() {
			const token = uni.getStorageSync('token');
			const userInfo = uni.getStorageSync('userInfo');

			console.log('检查登录状态 - token:', token ? '存在' : '不存在', 'userInfo:', userInfo);

			if (token && userInfo) {
				this.isLoggedIn = true;
				this.userInfo = userInfo;
				console.log('用户已登录');
			} else {
				this.isLoggedIn = false;
				this.userInfo = null;
				console.log('用户未登录');

				// 如果用户未登录，显示登录提示
				this.showLoginPrompt();
			}
		},

		// 显示登录提示
		showLoginPrompt() {
			// 延迟显示，确保页面加载完成
			setTimeout(() => {
				if (!this.isLoggedIn) {
					this.showLoginModal = true;
				}
			}, 1000);
		},

		// 关闭登录模态框
		handleCloseLoginModal() {
			this.showLoginModal = false;
		},

		// 登录成功处理
		handleLoginSuccess(loginData) {
			console.log('登录成功:', loginData);
			this.isLoggedIn = true;
			this.userInfo = loginData.userInfo || { userId: loginData.userId };
			this.showLoginModal = false;

			uni.showToast({
				title: '登录成功',
				icon: 'success',
				duration: 2000
			});

			// 登录成功后，重新检查订单状态
			if (this.orderId) {
				this.getOrderStatus();
			}
		},

		// 登录失败处理
		handleLoginFail(error) {
			console.error('登录失败:', error);
			this.showLoginModal = false;

			uni.showToast({
				title: '登录失败，请重试',
				icon: 'none',
				duration: 2000
			});
		},

		// Banner点击处理（简化版本）
		handleBannerClick(e) {
			console.log('Banner点击事件触发', e.type);

			// 防止事件冒泡
			if (e && e.stopPropagation) {
				e.stopPropagation();
			}

			// 立即执行导航，不依赖复杂的触摸逻辑
			this.navigateToZhaoshang(e);
		},

		// Banner触摸开始事件（iOS兼容性）
		handleBannerTouch(e) {
			console.log('Banner触摸开始');
			this.bannerTouchStartTime = Date.now();
			this.bannerTouchStarted = true;

			// 防止事件冒泡
			if (e && e.stopPropagation) {
				e.stopPropagation();
			}
			if (e && e.preventDefault) {
				e.preventDefault();
			}
		},

		// Banner触摸结束事件（iOS兼容性）
		handleBannerTouchEnd(e) {
			console.log('Banner触摸结束');

			// 防止事件冒泡
			if (e && e.stopPropagation) {
				e.stopPropagation();
			}
			if (e && e.preventDefault) {
				e.preventDefault();
			}

			// 检查是否是有效的点击（触摸时间不超过500ms）
			if (this.bannerTouchStarted && this.bannerTouchStartTime) {
				const touchDuration = Date.now() - this.bannerTouchStartTime;
				if (touchDuration < 500) {
					console.log('检测到有效点击，触摸时长:', touchDuration + 'ms');
					// 延迟一点执行，确保事件处理完成
					setTimeout(() => {
						this.navigateToZhaoshang(e);
					}, 50);
				}
			}

			// 重置状态
			this.bannerTouchStarted = false;
			this.bannerTouchStartTime = null;
		},

		// Banner触摸取消事件
		handleBannerTouchCancel(e) {
			console.log('Banner触摸取消');
			this.bannerTouchStarted = false;
			this.bannerTouchStartTime = null;
		},

		// 跳转到招商页面
		navigateToZhaoshang(e) {
			console.log('跳转到招商页面', e);

			// 防止事件冒泡
			if (e && e.stopPropagation) {
				e.stopPropagation();
			}
			if (e && e.preventDefault) {
				e.preventDefault();
			}

			// 防止重复导航（更强的防抖机制）
			if (this.isNavigating) {
				console.log('正在导航中，忽略重复点击');
				return;
			}

			// 检查最近是否有导航操作（防抖）
			const now = Date.now();
			if (this.lastNavigateTime && (now - this.lastNavigateTime) < 1000) {
				console.log('导航操作过于频繁，忽略');
				return;
			}

			this.isNavigating = true;
			this.lastNavigateTime = now;

			console.log('开始执行导航到招商页面');

			// 立即执行导航，减少延迟
			uni.navigateTo({
				url: '/packageA/pages/zhaoshang/index',
				success: () => {
					console.log('导航到招商页面成功');
					// 给用户反馈
					uni.showToast({
						title: '正在跳转...',
						icon: 'none',
						duration: 1000
					});
				},
				fail: (err) => {
					console.error('导航到招商页面失败:', err);
					this.isNavigating = false;

					// 如果导航失败，尝试使用switchTab
					if (err.errMsg && err.errMsg.includes('routeDone')) {
						console.log('尝试使用switchTab导航');
						uni.switchTab({
							url: '/pages/index/index',
							complete: () => {
								setTimeout(() => {
									uni.navigateTo({
										url: '/packageA/pages/zhaoshang/index'
									});
								}, 100);
							}
						});
					} else {
						uni.showToast({
							title: '页面跳转失败',
							icon: 'none'
						});
					}
				},
				complete: () => {
					// 减少重置延迟
					setTimeout(() => {
						this.isNavigating = false;
					}, 500);
				}
			});
		},

		// 启动冷却倒计时定时器
		startCooldownTimer() {
			// 清除之前的定时器
			if (this.cooldownTimer) {
				clearInterval(this.cooldownTimer);
			}

			// 启动新的定时器，每秒更新一次
			this.cooldownTimer = setInterval(() => {
				// 更新当前时间，触发computed属性重新计算
				this.currentTime = Date.now();
				const timeSinceLastOpen = this.currentTime - this.lastOpenTime;

				// 如果冷却时间已过，清除定时器
				if (timeSinceLastOpen >= this.openCooldown) {
					clearInterval(this.cooldownTimer);
					this.cooldownTimer = null;
					this.currentTime = 0; // 重置currentTime
				}
			}, 1000);
		},

		// 启动锁状态检查定时器
		startLockStatusCheck() {
			console.log('启动锁状态检查定时器，检查间隔:', this.lockStatusCheckInterval + 'ms');

			// 清除之前的定时器
			if (this.lockStatusCheckTimer) {
				clearInterval(this.lockStatusCheckTimer);
			}

			// 启动定时器，每5秒检查一次锁状态
			this.lockStatusCheckTimer = setInterval(() => {
				// 只有在已连接、已支付且启用检查的情况下才检查锁状态
				if (this.isConnected && this.isPaid && this.lockStatusCheckEnabled) {
					// 如果锁当前是开着的，才进行状态检查（检测关闭）
					if (this.isLockOpen) {
						this.checkLockStatus();
					}
				}
			}, this.lockStatusCheckInterval);
		},

		// 停止锁状态检查定时器
		stopLockStatusCheck() {
			console.log('停止锁状态检查定时器');
			if (this.lockStatusCheckTimer) {
				clearInterval(this.lockStatusCheckTimer);
				this.lockStatusCheckTimer = null;
			}
		},

		// 检查锁状态
		checkLockStatus() {
			try {
				console.log('执行锁状态检查...');

				// 查询锁状态
				lockService.queryLockStatus()
					.then(res => {
						console.log('锁状态检查命令发送成功:', res);
						// 注意：实际的锁状态会通过 onLockStatusChange 回调返回
					})
					.catch(err => {
						console.log('锁状态检查失败:', err);
						// 检查失败不影响主要功能，只记录日志
						// 不进行重试，避免过度查询
					});
			} catch (e) {
				console.log('锁状态检查异常:', e);
			}
		},

		// 验证MAC地址格式
		isValidMacAddress(mac) {
			if (!mac) return false;
			// MAC地址格式：XX:XX:XX:XX:XX:XX 或 XXXXXXXXXXXX
			const macRegex = /^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$|^[0-9A-Fa-f]{12}$/;
			return macRegex.test(mac);
		},

		// 处理连接成功后的状态
		handleConnectedState() {
			console.log('处理连接成功状态');

			// 验证lockService的连接状态
			const deviceInfo = lockService.getDeviceInfo();
			const lockServiceConnected = deviceInfo && (deviceInfo.connected === true || deviceInfo.isConnected === true);

			console.log('验证连接状态 - UI状态:', this.isConnected, 'lockService状态:', lockServiceConnected);

			if (!lockServiceConnected) {
				console.warn('lockService连接状态不一致，尝试强制同步状态');
				// 强制同步连接状态
				this.forceSyncConnectionState();
				return;
			}

			// 如果已支付且已连接，自动开门
			if (this.isPaid && !this.doorOpenProcessed && !this.doorOpenCompleted) {
				console.log('连接成功且已支付，自动触发开门');
				this.checkOrderAndOpenDoor();
			}
		},

		// 强制同步连接状态
		forceSyncConnectionState() {
			console.log('强制同步连接状态');

			// 尝试重新设置设备信息到lockService
			if (this.deviceMac) {
				lockService.setDeviceMac(this.deviceMac);
			}
			if (this.deviceName) {
				lockService.setExpectedDeviceName(this.deviceName);
			}
			if (this.orderId) {
				lockService.setOrderId(this.orderId);
			}

			// 延迟重新检查状态
			setTimeout(() => {
				const deviceInfo = lockService.getDeviceInfo();
				const lockServiceConnected = deviceInfo && (deviceInfo.connected === true || deviceInfo.isConnected === true);

				console.log('状态同步后检查 - lockService状态:', lockServiceConnected);

				if (lockServiceConnected && this.isPaid && !this.doorOpenProcessed && !this.doorOpenCompleted) {
					console.log('状态同步成功，触发自动开门');
					this.checkOrderAndOpenDoor();
				}
			}, 1000);
		},

		// 强制同步设备信息
		forceSyncDeviceInfo(deviceInfo) {
			console.log('强制同步设备信息到lockService:', deviceInfo);

			try {
				// 确保所有必要的设备信息都设置到lockService
				if (deviceInfo) {
					if (deviceInfo.mac || this.deviceMac) {
						lockService.setDeviceMac(deviceInfo.mac || this.deviceMac);
					}
					if (deviceInfo.name || this.deviceName) {
						lockService.setExpectedDeviceName(deviceInfo.name || this.deviceName);
					}
					if (deviceInfo.deviceId) {
						// 如果lockService有设置设备ID的方法，调用它
						if (typeof lockService.setDeviceId === 'function') {
							lockService.setDeviceId(deviceInfo.deviceId);
						}
					}
				}

				if (this.orderId) {
					lockService.setOrderId(this.orderId);
				}

				// 强制设置所有层级的连接状态
				this.forceSetAllLayersConnected(deviceInfo);

				console.log('设备信息同步完成');
			} catch (e) {
				console.error('强制同步设备信息失败:', e);
			}
		},

		// 强制设置所有层级的连接状态
		forceSetAllLayersConnected(deviceInfo) {
			console.log('强制设置所有层级连接状态');

			try {
				// 1. 设置lockService的连接状态
				if (lockService.deviceInfo) {
					lockService.deviceInfo.connected = true;
					lockService.deviceInfo.isConnected = true;
					lockService.deviceInfo.isAuthenticated = true;

					if (deviceInfo) {
						// 同步设备信息
						if (deviceInfo.deviceId) {
							lockService.deviceInfo.deviceId = deviceInfo.deviceId;
						}
						if (deviceInfo.mac || this.deviceMac) {
							lockService.deviceInfo.mac = deviceInfo.mac || this.deviceMac;
						}
						if (deviceInfo.name || this.deviceName) {
							lockService.deviceInfo.name = deviceInfo.name || this.deviceName;
						}
						if (deviceInfo.batteryLevel) {
							lockService.deviceInfo.batteryLevel = deviceInfo.batteryLevel;
						}
					}

					console.log('lockService内部连接状态已强制设置为true');
					console.log('lockService.deviceInfo:', lockService.deviceInfo);
				}

				// 2. 尝试获取并设置blueToothManager的连接状态
				try {
					// 直接导入blueToothManager
					const blueToothManager = require('@/utils/blueToothManager.js');

					if (blueToothManager) {
						console.log('找到blueToothManager，强制设置连接状态');

						// 强制设置连接状态
						blueToothManager.isConnected = true;
						blueToothManager.deviceId = this.deviceMac;
						console.log('blueToothManager.isConnected已设置为true');
						console.log('blueToothManager.deviceId已设置为:', this.deviceMac);

						// 如果有设备MAC，设置当前连接的设备
						if (this.deviceMac) {
							blueToothManager.currentDeviceId = this.deviceMac;
							blueToothManager.connectedDeviceId = this.deviceMac;
							console.log('blueToothManager当前设备ID已设置:', this.deviceMac);
						}

						// 设置服务和特征值ID（如果存在的话）
						if (blueToothManager.serviceId && blueToothManager.writeCharacteristicId) {
							console.log('blueToothManager服务和特征值ID已存在');
						} else {
							console.log('blueToothManager服务和特征值ID不存在，可能需要重新获取');
						}
					} else {
						console.log('未找到blueToothManager实例，跳过blueToothManager状态设置');
					}
				} catch (btError) {
					console.error('设置blueToothManager状态失败:', btError);
				}

				console.log('所有层级连接状态强制设置完成');
			} catch (e) {
				console.error('强制设置连接状态失败:', e);
			}
		},

		// 设置lockService回调
		setupLockServiceCallbacks() {
			// 设置回调函数
			lockService.setCallbacks({
				// 连接成功回调
				onConnected: (deviceInfo) => {
					console.log('lockService连接成功回调:', deviceInfo);

					// 强制更新连接状态，确保状态同步
					this.isConnected = true;
					this.isConnecting = false;

					// 强制同步设备信息到lockService，确保内部状态一致
					this.forceSyncDeviceInfo(deviceInfo);

					// 停止任何正在进行的扫描
					try {
						uni.stopBluetoothDevicesDiscovery();
					} catch (e) {
						console.log('停止蓝牙扫描失败:', e);
					}

					// 更新设备信息
					if (deviceInfo && deviceInfo.batteryLevel) {
						this.batteryLevel = deviceInfo.batteryLevel;
					}

					// 使用防抖的状态更新方法
					this.debouncedUpdateUIState();

					// 尝试查询设备状态
					this.queryDeviceStatus();

					// 验证连接状态并处理自动开门
					setTimeout(() => {
						this.handleConnectedState();
					}, 500);

					// 启动锁状态检查
					this.startLockStatusCheck();
				},
				
				// 断开连接回调
				onDisconnected: (info) => {
					console.log('lockService断开连接回调:', info);

					// 防止重复设置状态
					if (this.isConnected) {
						this.isConnected = false;
						this.isConnecting = false;

						// 停止锁状态检查
						this.stopLockStatusCheck();

						// 设置断开连接标志，防止立即触发自动开门
						this.isRecentlyDisconnected = true;
						setTimeout(() => {
							this.isRecentlyDisconnected = false;
						}, 5000); // 5秒内不触发自动开门

						// 使用防抖的状态更新方法
						this.debouncedUpdateUIState();

						// 如果页面活跃且需要自动重连，尝试重新连接
						if (this.isPageActive && (this.deviceMac || this.deviceName) && this.autoReconnect && !this.isManualDisconnect) {
							console.log('设备断开连接，尝试重新连接');

							// 延迟重连，给蓝牙适配器一些时间恢复
							setTimeout(() => {
								// 重新初始化蓝牙环境后再连接
								this.reinitializeBluetooth();
							}, 3000); // 延长重连时间
						}

						// 重置手动断开标志
						this.isManualDisconnect = false;
					}
				},
				
				// 锁状态变化回调
				onLockStatusChange: (status) => {
					console.log('lockService锁状态变化回调:', status);
					this.isLockOpen = status.isOpen;

					// 如果锁打开了，设置开门完成标志
					if (status.isOpen) {
						this.doorOpenCompleted = true;
						this.doorOpenProcessed = true;

						// 播放成功音效
						this.playSuccessSound();
					} else {
						// 锁关闭时，重置开门完成状态，允许再次开门
						console.log('锁已关闭，重置开门状态，允许再次开门');
						this.doorOpenCompleted = false;
						// 注意：不重置 doorOpenProcessed，避免重复自动开门
					}

					// 更新UI状态
					this.updateUIState();
				},
				
				// 电池电量更新回调
				onBatteryUpdate: (info) => {
					console.log('lockService电池电量更新回调:', info);
					this.batteryLevel = info.batteryLevel;
					
					// 更新UI状态
					this.updateUIState();
				},
				
				// 错误回调
				onError: (error) => {
					console.error('lockService错误回调:', error);
					
					// 显示错误提示
					uni.showToast({
						title: '设备操作出错',
						icon: 'none',
						duration: 2000
					});
				}
			});
		},
		
		// 防抖的状态更新方法
		debouncedUpdateUIState() {
			// 清除之前的定时器
			if (this.statusUpdateTimer) {
				clearTimeout(this.statusUpdateTimer);
			}

			// 设置新的定时器，延迟更新状态
			this.statusUpdateTimer = setTimeout(() => {
				this.updateUIState();
			}, 200);
		},

		// 添加updateUIState方法，确保UI状态正确更新
		updateUIState() {
			console.log('更新UI状态 - 蓝牙连接状态:', this.isConnected, '订单状态:', this.orderStatus, '支付状态:', this.isPaid, '锁状态:', this.isLockOpen);
			
			// 确保订单状态和支付状态一致
			if (this.orderStatus === 1) {
				if (!this.isPaid) {
					console.log('订单状态为已支付，但支付状态为未支付，更新为已支付');
					this.isPaid = true;
					this.paymentSuccess = true;
					
					// 如果正在轮询，停止轮询
					if (this.orderPollingTimer) {
						console.log('订单已支付，停止轮询');
						this.stopOrderPolling();
					}
				}
			} else if (this.orderStatus === 0 || this.orderStatus === null) {
				// 处理orderStatus为null的情况
				// 如果支付状态为true，但订单状态不是1，需要重新查询订单状态
				if (this.isPaid) {
					console.log('支付状态为已支付，但订单状态不是已支付，重新查询订单状态');
					this.queryOrderStatus()
						.then(orderInfo => {
							// 如果通过查询确认已支付，更新订单状态
							if (orderInfo.status === 1 || orderInfo.payStatus === 1) {
								console.log('重新查询确认订单已支付');
								this.orderStatus = 1;
								this.isPaid = true;
								this.paymentSuccess = true;
								
								// 停止轮询
								this.stopOrderPolling();
								
								// 如果已连接设备，自动开门
								if (this.isConnected && !this.doorOpenProcessed && !this.doorOpenCompleted) {
									console.log('确认支付成功，设备已连接，自动开门');
									this.checkOrderAndOpenDoor();
								}
							} else {
								// 如果查询结果仍然不是已支付，则更新支付状态为未支付
								console.log('重新查询确认订单未支付，更新支付状态');
								this.isPaid = false;
								this.paymentSuccess = false;
								
								// 如果订单未支付且没有在轮询，启动轮询
								if (!this.orderPollingTimer && this.orderId) {
									console.log('订单未支付且没有在轮询，启动轮询');
									this.startOrderPolling();
								}
							}
						})
						.catch(err => {
							console.error('重新查询订单状态失败:', err);
						});
				} else {
					// 如果订单未支付且没有在轮询，启动轮询
					if (!this.orderPollingTimer && this.orderId && this.isPageActive) {
						console.log('订单未支付且没有在轮询，启动轮询');
						this.startOrderPolling();
					}
				}
			}
			
			// 确保订单ID已同步到lockService
			if (this.orderId && lockService.getOrderInfo().orderId !== this.orderId) {
				console.log('UI状态更新 - 同步订单ID到lockService:', this.orderId);
				lockService.setOrderId(this.orderId);
				
				// 获取最新订单状态
				if (this.orderId && (this.orderStatus === undefined || this.orderStatus === -1 || this.orderStatus === null)) {
					console.log('UI状态更新 - 获取最新订单状态');
					this.getOrderStatus()
						.then(orderInfo => {
							console.log('获取订单状态成功:', orderInfo);
							// 更新支付状态 - 同时检查status和payStatus
							this.isPaid = this.orderStatus === 1 || orderInfo.payStatus === 1;
							
							// 如果订单已支付且已连接设备，自动开门
							if (this.isPaid && this.isConnected && !this.doorOpenProcessed && !this.doorOpenCompleted) {
								console.log('订单已支付且已连接设备，自动开门');
								this.checkOrderAndOpenDoor();
							}
						})
						.catch(err => {
							console.error('获取订单状态失败:', err);
						});
				}
			}
			
			// 从lockService获取最新设备状态
			const deviceInfo = lockService.getDeviceInfo();
			if (deviceInfo) {
				// 更新设备连接状态 - 修复undefined问题
				const lockServiceConnected = deviceInfo.connected === true || deviceInfo.isConnected === true;
				if (this.isConnected !== lockServiceConnected) {
					console.log('连接状态不一致，从lockService更新:', deviceInfo.connected, '解析为:', lockServiceConnected);
					this.isConnected = lockServiceConnected;

					// 如果连接状态变为已连接，停止连接中状态
					if (lockServiceConnected) {
						this.isConnecting = false;

						// 如果已支付且已连接，自动开门
						if (this.isPaid && !this.doorOpenProcessed && !this.doorOpenCompleted) {
							console.log('连接成功且已支付，准备自动开门');
							setTimeout(() => {
								this.checkOrderAndOpenDoor();
							}, 1000);
						}
					}
				}
				
				// 更新电池电量
				if (deviceInfo.batteryLevel && this.batteryLevel !== deviceInfo.batteryLevel) {
					console.log('电池电量更新:', deviceInfo.batteryLevel);
					this.batteryLevel = deviceInfo.batteryLevel;
				}
				
				// 更新锁状态（添加null检查）
				if (deviceInfo.lockStatus && deviceInfo.lockStatus !== undefined && this.isLockOpen !== deviceInfo.lockStatus.isOpen) {
					console.log('锁状态更新:', deviceInfo.lockStatus.isOpen ? '已开启' : '已关闭');
					this.isLockOpen = deviceInfo.lockStatus.isOpen;
				}
			}
			
			// 如果已连接蓝牙并且已支付，自动触发开门逻辑
			if (this.isConnected && this.isPaid && !this.doorOpenProcessed && !this.doorOpenCompleted && this.isPageActive && !this.isRecentlyDisconnected) {
				console.log('连接成功且已支付，自动触发开门');
				this.doorOpenProcessed = true;

				// 延迟执行开门操作，确保UI已更新
				setTimeout(() => {
					this.checkOrderAndOpenDoor();
				}, 1000);
			}
			
			// 使用forceUpdate触发视图更新
			this.forceUpdate();
		},
		
		// 强制刷新组件
		forceUpdate() {
			// 通过修改一个不可见属性来触发视图更新
			this.updateKey = Date.now();
		},
		
		// 预初始化蓝牙环境
		preinitBluetooth() {
			console.log('预初始化蓝牙环境');
			
			// 检查是否为模拟模式
			if (typeof global !== 'undefined' && global.isSimulatedMode) {
				console.log('检测到全局模拟模式标志');
				this.isSimulatedMode = true;
			}
			
			// 初始化蓝牙环境
			lockService.init()
				.then(res => {
					console.log('蓝牙环境初始化成功:', res);
					
					// 检查是否为模拟模式
					if (res.simulated) {
						console.log('切换到模拟模式');
						this.isSimulatedMode = true;
					}
				})
				.catch(err => {
					console.error('蓝牙环境初始化失败:', err);
					
					// 即使蓝牙初始化失败，也不显示错误，让用户可以继续使用
					console.log('切换到模拟模式');
					this.isSimulatedMode = true;
				});
		},
		
		// 防抖连接
		debounceConnect() {
			// 防止短时间内多次连接
			const now = Date.now();
			if (now - this.lastConnectTime < 2000) {
				console.log('连接操作过于频繁，已忽略');
				return;
			}
			
			this.lastConnectTime = now;
			
			// 清除之前的定时器
			if (this.connectionDebounceTimer) {
				clearTimeout(this.connectionDebounceTimer);
			}
			
			// 设置新的定时器
			this.connectionDebounceTimer = setTimeout(() => {
				this.connectDevice();
			}, 300);
		},
		
		// 连接设备（优化版本）
		connectDevice() {
			// 检查是否已连接或正在连接
			const deviceInfo = lockService.getDeviceInfo();
			const actualConnected = deviceInfo && (deviceInfo.connected === true || deviceInfo.isConnected === true);

			if (actualConnected || this.isConnected || this.isConnecting) {
				console.log('设备已连接或正在连接中，不重复连接');
				if (actualConnected && !this.isConnected) {
					this.isConnected = true;
					this.isConnecting = false;
				}
				return;
			}

			console.log('开始连接设备');
			this.isConnecting = true;
			this.debouncedUpdateUIState(); // 使用防抖更新

			// 确保订单ID已设置到锁服务
			if (this.orderId) {
				console.log('连接设备前设置订单ID:', this.orderId);
				lockService.setOrderId(this.orderId);
			}

			// 使用优化的连接方法
			this.connectDeviceOptimized();
		},
		
		// 断开连接
		disconnectDevice() {
			if (!this.isConnected) {
				return Promise.resolve();
			}
			
			return lockService.disconnect()
				.then(() => {
					console.log('断开连接成功');
				})
				.catch(err => {
					console.error('断开连接失败:', err);
				});
		},
		
		// 确保断开蓝牙连接
		ensureDisconnectBluetooth() {
			this.disconnectDevice()
				.then(() => {
					return lockService.close();
				})
				.catch(err => {
					console.error('关闭蓝牙失败:', err);
				});
		},
		
		// 查询设备状态
		queryDeviceStatus() {
			try {
				console.log('尝试查询设备状态');
				// 先查询锁状态
				lockService.queryLockStatus()
					.then(() => {
						console.log('锁状态查询成功');
						// 再查询设备信息（包含电量）
						return lockService.queryDeviceInfo();
					})
					.then(() => {
						console.log('设备信息查询成功');
					})
					.catch(err => {
						console.error('设备状态查询失败:', err);
					});
			} catch (error) {
				console.error('查询设备状态出错:', error);
			}
		},
		
		// 处理主要操作按钮点击
		handleMainAction() {
			console.log('处理主要操作按钮点击');

			// 如果用户未登录，显示登录弹窗
			if (!this.isLoggedIn) {
				console.log('用户未登录，显示登录弹窗');
				this.showLoginModal = true;
				return;
			}

			// 根据不同状态执行不同操作
			if (!this.isConnected) {
				// 未连接状态 - 连接设备
				this.tryConnectDevice();
			} else if (this.orderStatus === 0 || !this.isPaid) {
				// 已连接但未支付 - 显示支付弹窗
				this.showPaymentModal = true;
			} else {
				// 已连接且已支付 - 开门
				this.handleOpenDoor();
			}
		},
		
		// 处理开门按钮点击
		handleOpenDoor() {
			console.log('处理开门按钮点击，开门完成状态:', this.doorOpenCompleted, '锁状态:', this.isLockOpen);

			// 如果正在重连中，不允许开门
			if (this.isReconnecting) {
				console.log('正在重连中，请稍候');
				uni.showToast({
					title: '正在重连设备，请稍候',
					icon: 'none',
					duration: 2000
				});
				return;
			}

			// 检查开门冷却时间，防止频繁开锁
			const currentTime = Date.now();
			const timeSinceLastOpen = currentTime - this.lastOpenTime;

			if (timeSinceLastOpen < this.openCooldown) {
				const remainingTime = Math.ceil((this.openCooldown - timeSinceLastOpen) / 1000);
				uni.showToast({
					title: `请等待${remainingTime}秒后再试`,
					icon: 'none',
					duration: 2000
				});
				return;
			}

			// 如果锁已关闭，允许再次开门
			if (this.doorOpenCompleted && !this.isLockOpen) {
				console.log('锁已关闭，允许再次开门');
				// 重置开门完成状态，允许再次开门
				this.doorOpenCompleted = false;
			}

			// 验证设备连接状态（双重检查）
			const deviceInfo = lockService.getDeviceInfo();
			const lockServiceConnected = deviceInfo && (deviceInfo.connected === true || deviceInfo.isConnected === true);

			console.log('开门前连接状态检查 - UI状态:', this.isConnected, 'lockService状态:', lockServiceConnected);

			if (!this.isConnected || !lockServiceConnected) {
				console.log('设备未连接，先连接设备');
				this.tryConnectDevice();
				return;
			}

			// 如果未支付，显示支付提示
			if (!this.isPaid && this.orderStatus !== 1) {
				console.log('未支付，显示支付提示');
				this.showOpenDoorModal = true;
				return;
			}
			
			// 开门前最后一次连接状态验证和强制同步
			console.log('开门前最后验证连接状态');
			const finalDeviceInfo = lockService.getDeviceInfo();
			console.log('最终设备信息:', finalDeviceInfo);

			// 强制同步设备连接状态到lockService
			if (this.deviceMac && this.deviceName) {
				console.log('强制同步设备信息到lockService');
				lockService.setDeviceMac(this.deviceMac);
				lockService.setExpectedDeviceName(this.deviceName);
				if (this.orderId) {
					lockService.setOrderId(this.orderId);
				}

				// 强制设置所有层级连接状态
				this.forceSetAllLayersConnected(finalDeviceInfo);
			}

			// 开门前最后验证所有层级的连接状态
			console.log('开门前验证所有层级连接状态:');
			console.log('lockService.deviceInfo.connected:', lockService.deviceInfo?.connected);
			console.log('lockService.deviceInfo:', lockService.deviceInfo);

			// 特别检查blueToothManager的连接状态
			try {
				const blueToothManager = lockService.blueToothManager || lockService.bluetoothManager;
				if (blueToothManager) {
					console.log('blueToothManager连接状态检查:');
					console.log('blueToothManager.isConnected:', blueToothManager.isConnected);
					console.log('blueToothManager.deviceInfo:', blueToothManager.deviceInfo);
					console.log('blueToothManager.currentDeviceId:', blueToothManager.currentDeviceId);

					// 如果blueToothManager显示未连接，强制重新设置
					if (!blueToothManager.isConnected || !blueToothManager.deviceInfo?.connected) {
						console.log('检测到blueToothManager未连接，强制设置连接状态');

						if (blueToothManager.deviceInfo) {
							blueToothManager.deviceInfo.connected = true;
							blueToothManager.deviceInfo.isConnected = true;
						}
						blueToothManager.isConnected = true;
						blueToothManager.currentDeviceId = this.deviceMac;
						blueToothManager.connectedDeviceId = this.deviceMac;

						console.log('blueToothManager连接状态已强制修复');
					}
				}
			} catch (btError) {
				console.error('检查blueToothManager状态失败:', btError);
			}

			// 开门前强制重新认证设备，确保blueToothManager层面的连接
			console.log('开门前强制重新认证设备');

			// 先尝试重新认证，然后执行开门
			this.forceReauthenticateAndUnlock(finalDeviceInfo)
				.then(res => {
					console.log('强制重新认证开门成功:', res);

					// 记录开门时间，用于防频繁开锁
					this.lastOpenTime = Date.now();
					this.currentTime = this.lastOpenTime; // 初始化currentTime

					// 启动冷却倒计时定时器
					this.startCooldownTimer();

					// 设置开门完成标志，防止重复开门
					this.doorOpenCompleted = true;

					this.isLockOpen = true;
					uni.showToast({
						title: '开门成功',
						icon: 'success',
						duration: 2000
					});

					// 更新订单使用状态
					this.useStatus = 1;

					// 播放成功音效
					this.playSuccessSound();

					// 更新UI状态
					this.updateUIState();
				})
				.catch(err => {
					console.error('强制重新认证开门失败:', err);

					// 如果是连接问题，尝试备用开门方法
					if (err.message && err.message.includes('设备未连接')) {
						console.log('尝试备用开门方法');
						this.tryAlternativeOpenDoor();
					} else {
						uni.showToast({
							title: '开门失败，请重试',
							icon: 'none',
							duration: 2000
						});
					}
				});
		},

		// 备用开门方法
		tryAlternativeOpenDoor() {
			console.log('尝试备用开门方法');

			// 方法1：尝试重新连接后开门
			this.reconnectAndOpenDoor()
				.catch(err => {
					console.error('重连开门失败:', err);
					// 方法2：尝试直接发送蓝牙开门命令
					this.directBluetoothOpenDoor();
				});
		},

		// 重新连接并开门
		reconnectAndOpenDoor() {
			console.log('重新连接设备并开门');

			return new Promise((resolve, reject) => {
				// 先断开连接
				try {
					lockService.disconnect();
				} catch (e) {
					console.log('断开连接失败:', e);
				}

				// 重置连接状态
				this.isConnected = false;
				this.isConnecting = false;
				this.bluetoothInitialized = false;

				// 延迟重新连接
				setTimeout(() => {
					this.connectDeviceOptimized()
						.then(() => {
							// 连接成功后等待一段时间确保状态同步
							setTimeout(() => {
								// 再次尝试开门
								lockService.openLock({
									ignoreOrderStatus: true,
									force: true,
									retry: true,
									singleCommand: true,
									operationType: 1
								})
								.then(resolve)
								.catch(reject);
							}, 2000);
						})
						.catch(reject);
				}, 1000);
			});
		},

		// 直接蓝牙开门命令
		directBluetoothOpenDoor() {
			console.log('尝试直接蓝牙开门命令');

			// 如果有直接的蓝牙开门命令，可以在这里实现
			// 这是最后的备用方案
			uni.showToast({
				title: '开门失败，请检查设备连接',
				icon: 'none',
				duration: 3000
			});
		},

		// 处理支付
		processPayment() {
			console.log('处理支付');

			// 检查登录状态
			if (!this.isLoggedIn) {
				console.log('用户未登录，显示登录弹窗');
				this.closePaymentModal();
				this.showLoginModal = true;
				return;
			}

			// 设置支付中状态
			this.paymentLoading = true;
			this.paymentSuccess = false;
			
			// 创建订单并支付
			const createOrderIfNeeded = () => {
				// 检查是否已有订单ID
				if (this.orderId) {
					console.log('已有订单ID，直接返回:', this.orderId);
					return Promise.resolve({
						orderId: this.orderId
					});
				} else {
					// 创建新订单
					console.log('创建新订单');

					// 重置开门状态，因为这是新订单
					this.resetDoorState();
					
					// 确保设备编码不为空
					if (!this.deviceInfo || (!this.deviceInfo.deviceCode && !this.deviceInfo.bindCode && !this.deviceInfo.macAddress)) {
						console.error('设备编码不能为空');
						return Promise.reject({
							message: '设备编码不能为空，请重新扫描设备'
						});
					}
					
					// 使用设备信息中的绑定码、MAC地址或设备编号作为deviceCode
					const deviceCode = this.deviceInfo.bindCode || this.deviceInfo.macAddress || this.deviceInfo.deviceNo || '';
					console.log('使用设备编码:', deviceCode);
					
					return lockService.createOrder({
						deviceCode: deviceCode,
						duration: 60 // 默认60分钟
					});
				}
			};
			
			// 执行创建订单流程
			createOrderIfNeeded()
				.then(res => {
					console.log('订单准备完成:', res);
					// 确保订单ID存在
					if (res && res.orderId) {
						this.orderId = res.orderId;
						
						// 设置支付中状态
						this.isPaying = true;
						
						// 关闭支付弹窗
						this.closePaymentModal();
						
						// 设置订单ID到lockService
						lockService.setOrderId(this.orderId);
						
						// 在跳转到微信支付页面前启动订单状态轮询
						// 这样即使用户不点击"完成"按钮，也能检测到支付状态变化
						console.log('即将跳转到微信支付页面，启动订单状态轮询');
						this.startOrderPolling();
						
						// 支付订单
						return lockService.payOrder(this.orderId);
					} else {
						throw new Error('获取订单ID失败');
					}
				})
				.then(res => {
					console.log('支付成功:', res);
					
					// 更新订单状态
					this.orderStatus = 1;
					this.isPaid = true;
					this.paymentSuccess = true;

					// 保存支付状态到本地存储
					this.savePaymentState();

					// 更新UI状态
					this.updateUIState();
					
					// 播放支付成功音效
					this.playSuccessSound();
					
					// 显示支付成功提示
					uni.showToast({
						title: '支付成功',
						icon: 'success',
						duration: 2000
					});
					
					// 停止订单状态轮询，因为已经确认支付成功
					this.stopOrderPolling();
					
					// 延迟执行下一步操作，确保UI状态更新完成
					setTimeout(() => {
						// 如果已连接设备且未完成开门，自动开门
						if (this.isConnected && !this.doorOpenCompleted) {
							console.log('支付成功，设备已连接，自动开门');
							this.checkOrderAndOpenDoor();
						} else if (!this.isConnected) {
							// 未连接设备，尝试连接
							console.log('支付成功，设备未连接，尝试连接');
							this.tryConnectDevice();
						} else if (this.doorOpenCompleted) {
							console.log('支付成功，但已完成开门，无需重复操作');
						}
					}, 1000); // 延长延迟时间，确保状态完全更新
				})
				.catch(err => {
					console.error('支付流程失败:', err);
					
					// 显示失败提示
					uni.showToast({
						title: err.message || '支付失败，请重试',
						icon: 'none',
						duration: 2000
					});
					
					// 支付失败时不停止轮询，因为可能是用户取消了支付页面
					// 但实际已经完成了支付，轮询可以检测到这种情况
				})
				.finally(() => {
					this.paymentLoading = false;
					this.isPaying = false;
				});
		},
		
		// 关闭支付弹窗
		closePaymentModal() {
			this.showPaymentModal = false;
			this.paymentLoading = false;
		},
		
		// 关闭开门弹窗
		closeOpenDoorModal() {
			this.showOpenDoorModal = false;
		},
		
		/**
		 * 获取设备信息
		 * @param {string} deviceId 设备编号或ID
		 */
		getDeviceInfo(deviceId) {
			console.log('获取设备信息:', deviceId);
			
			// 先检查用户是否有设备的订单
			this.checkUserDeviceOrder(deviceId)
				.then(orderInfo => {
					if (orderInfo) {
						// 设置订单信息
						this.orderId = orderInfo.orderId;
						this.orderStatus = orderInfo.status || 0;
						this.useStatus = orderInfo.useStatus || 0;
						this.orderAmount = orderInfo.amount || 0;
						this.orderDuration = orderInfo.duration || 0;
						
						// 保存门店信息
						if (orderInfo.shopName) {
							if (!this.deviceInfo) this.deviceInfo = {};
							this.deviceInfo.shopName = orderInfo.shopName;
							console.log('保存门店信息:', orderInfo.shopName);
						}
						
						// 更新支付状态
						this.isPaid = orderInfo.status === 1 || orderInfo.payStatus === 1;
						this.paymentSuccess = this.isPaid;
						
						// 设置订单ID到lockService
						lockService.setOrderId(this.orderId);
						
						// 如果订单已支付，显示相应提示
						if (this.isPaid) {
							uni.showToast({
								title: '您有已支付的订单，将自动开门',
								icon: 'none',
								duration: 2000
							});
							
							// 播放支付成功音效
							this.playSuccessSound();
						} else {
							// 未支付订单
							uni.showToast({
								title: '您有未完成的订单',
								icon: 'none',
								duration: 2000
							});
						}
					}
					
					// 继续获取设备信息
					this.continueGetDeviceInfo(deviceId);
				})
				.catch(err => {
					console.error('检查用户订单失败:', err);
					// 继续获取设备信息
					this.continueGetDeviceInfo(deviceId);
				});
		},
		
		// 继续获取设备信息的方法
		continueGetDeviceInfo(deviceId) {
			// 构建API请求URL
			let baseUrl = '';
			if (API && API.baseUrl) {
				baseUrl = API.baseUrl;
			} else {
				// 默认使用HTTPS协议
				baseUrl = 'https://api.jycb888.com';
			}
			
			// 使用HTTPS协议
			if (baseUrl.startsWith('http://')) {
				baseUrl = baseUrl.replace('http://', 'https://');
			}
			
			// 不做判断，同时使用两个API路径
			const scanUrl = `${baseUrl}/api/wx/miniapp/device/scan?deviceCode=${encodeURIComponent(deviceId)}`;
			const statusUrl = `${baseUrl}/api/wx/miniapp/device/status/${deviceId}`;
			
			console.log('尝试请求设备信息URL1:', scanUrl);
			console.log('尝试请求设备信息URL2:', statusUrl);
			
			let requestComplete = false;
			
			// 请求扫描接口
			uni.request({
				url: scanUrl,
				method: 'GET',
				header: {
					'Authorization': uni.getStorageSync('token')
				},
				sslVerify: false,
				success: (res) => {
					// 如果另一个请求已成功处理，不再处理
					if (requestComplete) return;

					if (res.statusCode === 200 && res.data && res.data.code === 200 && res.data.data) {
						console.log('扫描接口成功:', res.data.data);
						requestComplete = true;

						this.handleDeviceInfoResponse(res.data.data, deviceId);
					} else if (res.statusCode === 200 && res.data && res.data.code === 500) {
						console.error('扫描接口返回错误:', res.data.message);
						// 如果是设备所属门店不存在的错误，尝试其他接口
					}
				},
				fail: (err) => {
					console.error('扫描接口请求失败:', err);
					// 不处理错误，因为还有状态接口请求
				}
			});
			
			// 请求状态接口
			uni.request({
				url: statusUrl,
				method: 'GET',
				header: {
					'Authorization': uni.getStorageSync('token')
				},
				sslVerify: false,
				success: (res) => {
					// 如果另一个请求已成功处理，不再处理
					if (requestComplete) return;

					if (res.statusCode === 200 && res.data && res.data.code === 200 && res.data.data) {
						console.log('状态接口成功:', res.data.data);
						requestComplete = true;

						this.handleDeviceInfoResponse(res.data.data, deviceId);
					} else if (res.statusCode === 200 && res.data && res.data.code === 500) {
						console.error('状态接口返回错误:', res.data.message);
						// 如果两个接口都失败，显示错误信息
						if (!requestComplete) {
							setTimeout(() => {
								if (!requestComplete) {
									this.showError(`设备信息获取失败: ${res.data.message}`);
								}
							}, 1000);
						}
					}
				},
				fail: (err) => {
					// 只有在两个接口都失败时才处理错误
					if (!requestComplete) {
						console.error('状态接口请求失败:', err);

						// 延迟显示错误，给另一个接口一些时间
						setTimeout(() => {
							if (!requestComplete) {
								this.showError('获取设备信息失败，请检查网络连接');
							}
						}, 1000);
					}
				}
			});
		},
		
		// 处理设备信息响应
		handleDeviceInfoResponse(deviceData, deviceId) {
			console.log('处理设备信息响应:', deviceData);
			console.log('设备信息字段检查:', {
				macAddress: deviceData.macAddress,
				mac: deviceData.mac,
				bluetoothMac: deviceData.bluetoothMac,
				deviceMac: deviceData.deviceMac,
				deviceName: deviceData.deviceName,
				deviceId: deviceData.id || deviceData.deviceId
			});
			this.deviceInfo = deviceData;
			
			// 更新设备ID和价格信息
			this.deviceId = deviceData.id || deviceData.deviceId || deviceId;
			if (deviceData.hourlyRate || deviceData.price) {
				this.hourlyRate = deviceData.hourlyRate || parseFloat(deviceData.price) || 0;
				this.priceText = `￥${this.hourlyRate.toFixed(2)}`;
			}
			
			// 检查是否有当前订单
			if (deviceData.currentOrderId && !this.orderId) {
				this.orderId = deviceData.currentOrderId;
				// 获取订单状态
				this.getOrderStatus()
					.then(orderInfo => {
						// 如果订单已支付，设置支付状态
						if (orderInfo.status === 1 || orderInfo.payStatus === 1) {
							this.isPaid = true;
							this.paymentSuccess = true;
							
							// 如果已连接设备，自动开门
							if (this.isConnected && !this.doorOpenProcessed) {
								console.log('检测到已支付订单，设备已连接，自动开门');
								this.checkOrderAndOpenDoor();
							}
						}
					})
					.catch(err => {
						console.error('获取订单状态失败:', err);
					});
			}
			
			// 设置设备MAC地址 - 支持多种字段名
			const macAddress = deviceData.macAddress || deviceData.mac || deviceData.bluetoothMac || deviceData.deviceMac;
			if (macAddress) {
				// 格式化MAC地址，添加冒号
				const rawMac = macAddress.replace(/:/g, '').replace(/-/g, '').toUpperCase();
				if (rawMac.length === 12) {
					this.deviceMac = rawMac.match(/.{1,2}/g).join(':');
					console.log('从API获取到设备MAC地址:', this.deviceMac);
					lockService.setDeviceMac(this.deviceMac);
				} else {
					console.warn('MAC地址格式不正确:', macAddress);
				}
			} else {
				console.warn('API返回的设备信息中没有MAC地址字段');
			}
			
			// 优化连接流程：避免重复初始化，直接连接
			if (deviceData.deviceName && this.deviceMac) {
				this.deviceName = deviceData.deviceName;
				console.log('使用设备名称和MAC地址连接:', this.deviceName, this.deviceMac);

				// 设置预期设备名称和MAC地址
				lockService.setExpectedDeviceName(this.deviceName);
				lockService.setDeviceMac(this.deviceMac);

				// 直接连接，不延迟
				this.connectDeviceOptimized();
			} else if (this.deviceMac) {
				// 如果没有设备名称，但有MAC地址，尝试使用MAC地址连接
				console.log('没有设备名称，使用MAC地址连接');
				this.connectDeviceOptimized();
			} else {
				console.warn('设备信息中既没有设备名称也没有MAC地址，无法连接蓝牙设备');
			}
		},
		
		/**
		 * 优化的设备连接方法
		 */
		connectDeviceOptimized() {
			console.log('开始优化连接流程');

			// 检查是否已连接
			const deviceInfo = lockService.getDeviceInfo();
			const actualConnected = deviceInfo && (deviceInfo.connected === true || deviceInfo.isConnected === true);

			if (actualConnected) {
				console.log('设备已连接，无需重复连接');
				this.isConnected = true;
				this.isConnecting = false;
				return Promise.resolve();
			}

			if (this.isConnecting) {
				console.log('正在连接中，避免重复连接');
				return Promise.resolve();
			}

			this.isConnecting = true;

			// 确保蓝牙已初始化（避免重复初始化）
			const initPromise = this.bluetoothInitialized ?
				Promise.resolve() :
				this.ensureBluetoothInitialized();

			return initPromise.then(() => {
				// 优先使用MAC地址直接连接（更快）
				if (this.deviceMac) {
					console.log('使用MAC地址快速连接:', this.deviceMac);
					return lockService.connectDevice(this.deviceMac)
						.then(res => {
							console.log('MAC地址连接成功:', res);
							return res;
						})
						.catch(err => {
							console.error('MAC地址连接失败，尝试设备名称连接:', err);
							// MAC连接失败，尝试设备名称连接
							if (this.deviceName) {
								return this.connectByDeviceName();
							} else {
								this.isConnecting = false;
								this.showConnectionError(err);
								throw err;
							}
						});
				} else if (this.deviceName) {
					// 没有MAC地址，使用设备名称连接
					return this.connectByDeviceName();
				} else {
					this.isConnecting = false;
					const error = new Error('设备信息不完整');
					uni.showToast({
						title: '设备信息不完整',
						icon: 'none'
					});
					throw error;
				}
			});
		},

		/**
		 * 确保蓝牙已初始化
		 */
		ensureBluetoothInitialized() {
			if (this.bluetoothInitialized) {
				return Promise.resolve();
			}

			console.log('初始化蓝牙环境');
			return lockService.init()
				.then(res => {
					this.bluetoothInitialized = true;
					console.log('蓝牙环境初始化完成');
					return res;
				})
				.catch(err => {
					console.error('蓝牙环境初始化失败:', err);
					throw err;
				});
		},

		/**
		 * 使用设备名称连接设备
		 */
		connectByDeviceName() {
			console.log('使用设备名称连接:', this.deviceName);
			return lockService.directConnect(this.deviceName)
				.then(res => {
					console.log('设备名称连接成功:', res);
					return res;
				})
				.catch(err => {
					console.error('设备名称连接失败:', err);
					this.isConnecting = false;
					this.showConnectionError(err);
					throw err;
				});
		},

		/**
		 * 显示连接错误
		 */
		showConnectionError(err) {
			uni.showToast({
				title: '连接失败: ' + (err.message || '未知错误'),
				icon: 'none',
				duration: 2000
			});
		},

		/**
		 * 使用设备名称连接设备（旧方法，保留兼容性）
		 * @param {string} deviceName 设备名称
		 */
		connectDeviceByName(deviceName) {
			console.log('使用设备名称连接设备:', deviceName);
			
			// 先初始化蓝牙环境
			lockService.init()
				.then(() => {
					// 直接使用设备名称连接
					return lockService.directConnect(deviceName);
				})
				.then(res => {
					console.log('连接命令执行成功:', res);
					// 注意：不在这里设置连接状态，让lockService的回调来处理
					console.log('等待lockService回调确认连接状态');

					// 显示连接成功提示
					uni.showToast({
						title: '连接成功',
						icon: 'success'
					});
				})
				.catch(err => {
					console.error('连接失败:', err);

					// 更新连接状态
					this.isConnecting = false;

					// 使用防抖更新UI状态
					this.debouncedUpdateUIState();

					// 显示连接失败提示
					uni.showToast({
						title: '连接失败，请重试',
						icon: 'none'
					});
				});
		},
		
		// 获取设备价格
		getDevicePrice() {
			// 实现获取设备价格的逻辑
			// ...
		},
		// 获取订单状态
		getOrderStatus() {
			if (!this.orderId || isNaN(this.orderId)) {
				console.error('无效的订单ID:', this.orderId);
				return Promise.reject(new Error('无效的订单ID'));
			}
			return new Promise((resolve, reject) => {
				// 使用getAPIBaseUrl函数获取API基础URL
				const baseUrl = getAPIBaseUrl();
				uni.request({
					url: `${baseUrl}/api/wx/miniapp/order/${this.orderId}`,
					method: 'GET',
					header: {
						'Authorization': uni.getStorageSync('token')
					},
					success: (res) => {
						if (res.data && res.data.code === 200) {
							const orderInfo = res.data.data;
							console.log('获取订单状态成功:', orderInfo);
							// 更新订单信息
							// 注意：status可能为null，需要检查payStatus
							this.orderStatus = orderInfo.status || 0; // 如果status为null，默认为0（未支付）
							// 如果status为null但payStatus为1，说明已支付
							if (orderInfo.status === null && orderInfo.payStatus === 1) {
								console.log('订单status为null但payStatus为1，视为已支付');
								this.orderStatus = 1;
							}
							this.useStatus = orderInfo.useStatus || 0;
							this.orderAmount = orderInfo.amount || 0;
							this.orderDuration = orderInfo.duration || 0;
							
							// 保存门店信息
							if (orderInfo.shopName) {
								if (!this.deviceInfo) this.deviceInfo = {};
								this.deviceInfo.shopName = orderInfo.shopName;
								console.log('保存门店信息:', orderInfo.shopName);
							}
							
							// 更新支付状态 - 同时检查status和payStatus
							this.isPaid = this.orderStatus === 1 || orderInfo.payStatus === 1;
							// 设置订单ID到lockServic
							lockService.setOrderId(this.orderId);
							// 如果订单已支付且使用中，开始计时
							if (this.isPaid && this.useStatus === 1) {
								this.startOrderTimer();
							}
							// 存储订单信息到本地
							try {
								const key = `order_${this.orderId}`;
								uni.setStorageSync(key, JSON.stringify(orderInfo));
								console.log('订单信息已存储到本地');
							} catch (e) {
								console.error('存储订单信息到本地失败:', e);
							}
			
							resolve(orderInfo);
						} else {
							const errorMsg = res.data ? res.data.message || '未知错误' : '未知错误';
							console.error('获取订单状态失败:', errorMsg);
							reject(new Error('获取订单状态失败: ' + errorMsg));
						}
					},
					fail: (err) => {
						console.error('获取订单状态请求失败:', err);
						reject(err);
					}
				});
			});
		},
		
		// 开始订单计时
		startOrderTimer() {
			// 实现订单计时逻辑
			// ...
		},
		
		// 保存锁状态
		saveLockState(isOpen) {
			// 可以将锁状态保存到本地存储或其他地方
			// ...
		},
		
		// 播放成功提示音
		playSuccessSound() {
			console.log('播放支付成功音效');
			
			// 对于微信小程序环境
			// #ifdef MP-WEIXIN
			try {
				if (this.successAudio) {
					// 重新设置src确保能够再次播放
					this.successAudio.src = "https://wuji1112-1312674791.cos.ap-guangzhou.myqcloud.com/static/audio/ttsmaker-file-2025-6-11-20-49-17.mp3";
					// 背景音频管理器需要设置autoplay=true才会播放
					this.successAudio.autoplay = true;
					
					// 添加振动反馈作为辅助
					if (wx && wx.vibrateShort) {
						wx.vibrateShort({
							type: 'medium'
						});
					}
				} else {
					console.warn('音频对象未初始化');
					// 尝试重新初始化
					this.initAudio();
					// 延迟设置src并播放
					setTimeout(() => {
						if (this.successAudio) {
							this.successAudio.autoplay = true;
							this.successAudio.src = "https://wuji1112-1312674791.cos.ap-guangzhou.myqcloud.com/static/audio/ttsmaker-file-2025-6-11-20-49-17.mp3";
						}
					}, 300);
					// 使用系统振动反馈
					if (wx && wx.vibrateShort) {
						wx.vibrateShort({
							type: 'medium'
						});
					}
				}
			} catch (error) {
				console.error('播放音频失败:', error);
				// 备用：使用振动反馈
				if (wx && wx.vibrateShort) {
					wx.vibrateShort({
						type: 'medium'
					});
				}
			}
			// #endif
			
			// 非微信小程序环境
			// #ifndef MP-WEIXIN
			if (this.successAudio) {
				try {
					// 重置播放位置并播放
					this.successAudio.stop();
					this.successAudio.seek(0);
					this.successAudio.play();
				} catch (error) {
					console.error('播放音频失败:', error);
					// 尝试替代方案
					this.tryAlternativeAudioPlay();
				}
			} else {
				console.warn('音频对象未初始化');
				// 尝试重新初始化
				this.initAudio();
				setTimeout(() => {
					if (this.successAudio) {
						try {
							this.successAudio.play();
						} catch (error) {
							console.error('延迟播放音频失败:', error);
							this.tryAlternativeAudioPlay();
						}
					} else {
						this.tryAlternativeAudioPlay();
					}
				}, 300);
			}
			// #endif
		},
		
		// 尝试替代方案播放音频
		tryAlternativeAudioPlay() {
			console.log('尝试替代方案播放音频');
			
			// 使用微信小程序原生API播放
			// #ifdef MP-WEIXIN
			if (wx && wx.createInnerAudioContext) {
				try {
					const innerAudio = wx.createInnerAudioContext();
					innerAudio.autoplay = true;
					innerAudio.src = "https://wuji1112-1312674791.cos.ap-guangzhou.myqcloud.com/static/audio/ttsmaker-file-2025-6-11-20-49-17.mp3";
					innerAudio.onError((err) => {
						console.error('替代音频播放失败:', err);
						// 如果还是失败，尝试使用短音效API
						this.tryShortAudio();
					});
				} catch (e) {
					console.error('替代音频方案失败:', e);
					this.tryShortAudio();
				}
			} else {
				this.tryShortAudio();
			}
			// #endif
			
			// 非小程序环境
			// #ifndef MP-WEIXIN
			try {
				const audio = new Audio();
				audio.src = "https://wuji1112-1312674791.cos.ap-guangzhou.myqcloud.com/static/audio/ttsmaker-file-2025-6-11-20-49-17.mp3";
				audio.play().catch(err => console.error('HTML5音频播放失败:', err));
			} catch (e) {
				console.error('替代音频播放失败:', e);
			}
			// #endif
		},
		
		// 尝试使用短音效API
		tryShortAudio() {
			// #ifdef MP-WEIXIN
			if (wx && wx.playBackgroundAudio) {
				wx.playBackgroundAudio({
					dataUrl: "https://wuji1112-1312674791.cos.ap-guangzhou.myqcloud.com/static/audio/ttsmaker-file-2025-6-11-20-49-17.mp3",
					title: '支付成功提示音',
					fail: (err) => {
						console.error('背景音频播放失败:', err);
					}
				});
			} else if (wx && wx.createAudioContext) {
				// 尝试使用系统提示音
				wx.vibrateShort({
					type: 'medium'
				});
				console.log('使用振动提示代替音频');
			}
			// #endif
		},
		
		// 播放错误提示音
		playErrorSound() {
			// 实现播放错误提示音的逻辑
			// ...
		},
		
		// 页面背景图片加载完成
		onBackgroundImageLoaded() {
			this.isPageLoaded = true;
		},
		
		// 返回上一页
		goBack() {
			uni.navigateBack();
		},
		

		
		// 设置蓝牙状态监听
		setupBluetoothListeners() {
			// 监听蓝牙适配器状态变化
			uni.onBluetoothAdapterStateChange((res) => {
				console.log('蓝牙适配器状态变化:', res);
				
				// 如果蓝牙被关闭，更新连接状态
				if (!res.available) {
					this.isConnected = false;
					this.isConnecting = false;
					
					// 显示提示
					uni.showToast({
						title: '蓝牙已关闭，请开启蓝牙',
						icon: 'none'
					});
				} else if (res.available && this.deviceMac && !this.isConnected && !this.isConnecting) {
					// 蓝牙被重新打开，尝试重新连接
					setTimeout(() => {
						this.debounceConnect();
					}, 1000);
				}
			});
			
			// 监听蓝牙连接状态变化
			uni.onBLEConnectionStateChange((res) => {
				console.log('蓝牙连接状态变化:', res);
				
				// 如果连接断开，更新状态
				if (!res.connected && this.isConnected) {
					this.isConnected = false;
					
					// 如果页面活跃，尝试重新连接
					if (this.isPageActive && this.deviceMac) {
						setTimeout(() => {
							this.debounceConnect();
						}, 1000);
					}
				}
			});
		},
		
		// 通过设备ID获取设备信息
		getDeviceInfoById(deviceId) {
			console.log('通过ID获取设备信息:', deviceId);
			
			// 获取API基础URL
			const baseUrl = getAPIBaseUrl();
			const url = `${baseUrl}/api/wx/miniapp/device/status/${deviceId}`;
			
			// 请求设备信息
			uni.request({
				url: url,
				method: 'GET',
				header: {
					'Authorization': uni.getStorageSync('token')
				},
				success: (res) => {
					if (res.statusCode === 200 && res.data && res.data.code === 200) {
						console.log('获取设备信息成功:', res.data);
						
						// 保存设备信息
						this.deviceInfo = res.data.data;
						this.deviceId = this.deviceInfo.id || deviceId;
						
						// 设置设备名称和MAC地址
						if (this.deviceInfo.deviceName) {
							this.deviceName = this.deviceInfo.deviceName;
						} else {
							// 如果没有设备名称，尝试使用ID生成一个
							this.deviceName = 'LOCK' + deviceId;
						}
						
						// 设置MAC地址
						if (this.deviceInfo.macAddress) {
							// 格式化MAC地址，添加冒号
							const rawMac = this.deviceInfo.macAddress.replace(/:/g, '').replace(/-/g, '').toUpperCase();
							if (rawMac.length === 12) {
								this.deviceMac = rawMac.match(/.{1,2}/g).join(':');
								console.log('设置设备MAC地址:', this.deviceMac);
								lockService.setDeviceMac(this.deviceMac);
							} else {
								console.warn('MAC地址格式不正确:', this.deviceInfo.macAddress);
								// 尝试使用设备ID生成MAC地址
								this.deviceMac = this.formatDeviceIdToMac(deviceId);
								console.log('使用设备ID生成MAC地址:', this.deviceMac);
								lockService.setDeviceMac(this.deviceMac);
							}
						} else {
							// 如果没有MAC地址，尝试使用设备ID生成一个
							this.deviceMac = this.formatDeviceIdToMac(deviceId);
							console.log('使用设备ID生成MAC地址:', this.deviceMac);
							lockService.setDeviceMac(this.deviceMac);
						}
						
						// 设置预期设备名称
						console.log('设置预期设备名称:', this.deviceName);
						lockService.setExpectedDeviceName(this.deviceName);
						
						// 获取设备价格
						if (this.deviceInfo.price) {
							this.hourlyRate = parseFloat(this.deviceInfo.price) || 0;
							this.priceText = '￥' + this.hourlyRate.toFixed(2) + '/小时';
						}
						
						// 初始化蓝牙环境
						this.preinitBluetooth();
						
						// 如果在页面活跃状态下，自动尝试连接设备
						if (this.isPageActive) {
							console.log('页面活跃状态，准备尝试连接设备');
							this.autoConnectTimer = setTimeout(() => {
								this.tryConnectDevice();
							}, 1000);
						}
					} else {
						console.error('获取设备信息失败:', res);
						uni.showToast({
							title: '获取设备信息失败',
							icon: 'none',
							duration: 2000
						});
					}
				},
				fail: (err) => {
					console.error('请求设备信息失败:', err);
					uni.showToast({
						title: '网络错误，请重试',
						icon: 'none',
						duration: 2000
					});
				}
			});
		},
		
		// 格式化设备ID为MAC地址
		formatDeviceIdToMac(deviceId) {
			// 如果设备ID是纯数字格式
			if (/^\d+$/.test(deviceId)) {
				// 补齐6位数字
				const paddedId = deviceId.padStart(6, '0');
				// 转换为16进制并分段
				return 'AB:' + paddedId.match(/.{1,2}/g).join(':').toUpperCase();
			} 
			// 如果设备ID是其他格式
			else {
				// 取前12个字符，如果不足则补0
				let macChars = deviceId.replace(/[^0-9a-fA-F]/g, '').substring(0, 12).padEnd(12, '0').toUpperCase();
				// 分段并添加冒号
				return macChars.match(/.{1,2}/g).join(':');
			}
		},
		
		// 显示错误提示
		showError(message) {
			console.error('错误:', message);
			
			// 显示错误提示
			uni.showToast({
				title: message,
				icon: 'none',
				duration: 3000
			});
		},
		// 初始化支付流程
		initPayment() {
			console.log('初始化支付流程');
			this.showPaymentModal = true;
			this.paymentLoading = true;
			this.paymentSuccess = false;
			
			// 创建订单并支付
			const createOrderIfNeeded = () => {
				// 检查是否已有订单ID
				if (this.orderId) {
					console.log('已有订单ID，直接返回:', this.orderId);
					return Promise.resolve({
						orderId: this.orderId
					});
				} else {
					// 创建新订单
					console.log('创建新订单');
					
					// 确保设备编码不为空
					if (!this.deviceInfo || (!this.deviceInfo.deviceCode && !this.deviceInfo.bindCode && !this.deviceInfo.macAddress)) {
						console.error('设备编码不能为空');
						return Promise.reject({
							message: '设备编码不能为空，请重新扫描设备'
						});
					}
					
					// 使用设备信息中的绑定码、MAC地址或设备编号作为deviceCode
					const deviceCode = this.deviceInfo.bindCode || this.deviceInfo.macAddress || this.deviceInfo.deviceNo || '';
					console.log('使用设备编码:', deviceCode);
					
					return lockService.createOrder({
						deviceCode: deviceCode,
						duration: 60 // 默认60分钟
					});
				}
			};
		},
		// 尝试自动连接设备
		tryConnectDevice() {
			// 先检查实际的连接状态
			const deviceInfo = lockService.getDeviceInfo();
			const actualConnected = deviceInfo && (deviceInfo.connected === true || deviceInfo.isConnected === true);
			const lockServiceConnected = deviceInfo && deviceInfo.connected;

			console.log('尝试连接设备 - UI连接状态:', this.isConnected, 'lockService连接状态:', lockServiceConnected, '实际连接状态:', actualConnected, '连接中状态:', this.isConnecting);

			// 如果lockService已连接，同步UI状态
			if (lockServiceConnected && !this.isConnected) {
				console.log('lockService已连接，同步UI状态');
				this.isConnected = true;
				this.isConnecting = false;
				this.updateUIState();
				return;
			}

			// 如果实际已连接，更新状态并返回
			if (actualConnected && !this.isConnected) {
				console.log('设备实际已连接，更新UI状态');
				this.isConnected = true;
				this.isConnecting = false;
				this.updateUIState();
				return;
			}

			// 如果已连接，不重复连接
			if (this.isConnected && lockServiceConnected) {
				console.log('设备已连接，不重复连接');
				return;
			}

			// 如果正在连接中，避免重复操作
			if (this.isConnecting) {
				console.log('设备正在连接中，请稍候');
				return;
			}
			
			// 设置连接中状态
			this.isConnecting = true;

			// 设置连接超时处理
			const connectTimeout = setTimeout(() => {
				if (this.isConnecting) {
					console.log('连接超时，重置连接状态');
					this.isConnecting = false;
					this.updateUIState();

					uni.showToast({
						title: '连接超时，请重试',
						icon: 'none',
						duration: 2000
					});
				}
			}, 15000); // 15秒超时

			// 检查是否有设备MAC地址或名称
			if (
				!this.deviceMac &&
				!this.deviceName &&
				!this.deviceId &&
				!(this.deviceInfo && (this.deviceInfo.deviceNo || this.deviceInfo.id))
			) {
				console.error('设备标识信息都为空，无法连接');
				this.isConnecting = false;
				uni.showToast({
					title: '设备信息不完整，无法连接',
					icon: 'none',
					duration: 2000
				});
				return;
			}
			
			// 设置锁服务的设备MAC地址
			if (this.deviceMac) {
				lockService.setDeviceMac(this.deviceMac);
			}
			
			// 设置预期设备名称
			if (this.deviceName) {
				lockService.setExpectedDeviceName(this.deviceName);
			}
			
			// 使用设备名称连接设备
			if (this.deviceName) {
				console.log('使用设备名称连接设备:', this.deviceName);
				
				// 直接连接设备
				lockService.directConnect(this.deviceName)
					.then(res => {
						console.log('连接命令执行成功:', res);
						// 清除超时定时器
						clearTimeout(connectTimeout);
						// 注意：不在这里设置连接状态，让lockService的回调来处理
						console.log('等待lockService回调确认连接状态');
					})
					.catch(err => {
						console.error('连接失败:', err);
						// 清除超时定时器
						clearTimeout(connectTimeout);
						this.isConnecting = false;

						uni.showToast({
							title: '设备连接失败，请重试',
							icon: 'none',
							duration: 2000
						});

						// 更新UI状态
						this.debouncedUpdateUIState();
					});
			}
			// 使用MAC地址连接设备
			else if (this.deviceMac) {
				console.log('使用MAC地址连接设备:', this.deviceMac);
				
				// 连接设备
				lockService.connectDevice(this.deviceMac)
					.then(res => {
						console.log('连接命令执行成功:', res);
						// 清除超时定时器
						clearTimeout(connectTimeout);
						// 注意：不在这里设置连接状态，让lockService的回调来处理
						console.log('等待lockService回调确认连接状态');
					})
					.catch(err => {
						console.error('连接失败:', err);
						// 清除超时定时器
						clearTimeout(connectTimeout);
						this.isConnecting = false;

						uni.showToast({
							title: '设备连接失败，请重试',
							icon: 'none',
							duration: 2000
						});

						// 更新UI状态
						this.debouncedUpdateUIState();
					});
			}
		},
		// 检查订单状态并决定是否开门
		checkOrderAndOpenDoor() {
			// 先检查实际的连接状态
			const deviceInfo = lockService.getDeviceInfo();
			const actualConnected = deviceInfo && (deviceInfo.connected === true || deviceInfo.isConnected === true);

			console.log('检查订单状态并决定是否开门，当前订单ID:', this.orderId, '支付状态:', this.isPaid, '连接状态:', this.isConnected, '实际连接状态:', actualConnected, '开门完成状态:', this.doorOpenCompleted);

			// 如果设备实际未连接，不执行开门
			if (!actualConnected) {
				console.log('设备实际未连接，不执行开门操作');
				// 尝试重新连接
				if (this.deviceMac && this.isPaid) {
					console.log('尝试重新连接设备');
					this.tryConnectDevice();
				}
				return;
			}

			// 如果实际已连接但状态不一致，更新状态
			if (actualConnected && !this.isConnected) {
				console.log('检测到实际已连接，更新连接状态');
				this.isConnected = true;
				this.isConnecting = false;
			}

			// 如果没有订单ID，不执行开门操作
			if (!this.orderId) {
				console.log('没有订单ID，不执行开门操作');
				return;
			}

			// 如果已经完成开门操作，不再重复开门
			if (this.doorOpenCompleted) {
				console.log('已完成开门操作，不再重复开门');
				return;
			}
			
			// 如果未支付，先查询一次订单支付状态，确认最新支付状态
			if (!this.isPaid && this.orderStatus !== 1) {
				console.log('本地状态显示订单未支付，查询最新订单支付状态');
				this.queryPaymentStatus()
					.then(paymentInfo => {
						// 检查是否已支付
						if (paymentInfo.isPaid) {
							console.log('查询到订单已支付，更新支付状态');
							this.isPaid = true;
							this.orderStatus = 1;
							this.paymentSuccess = true;
							
							// 如果已连接设备，继续开门流程
							if (this.isConnected) {
								console.log('确认支付成功，设备已连接，继续开门');
								// 延迟执行，确保状态更新完成
								setTimeout(() => {
									this.performOpenDoor();
								}, 500);
							} else {
								// 未连接设备，先尝试连接
								console.log('确认支付成功，设备未连接，先尝试连接');
								this.tryConnectDevice();
							}
						} else {
							console.log('确认订单未支付，不执行开门操作');
							uni.showToast({
								title: '请先完成支付',
								icon: 'none',
								duration: 2000
							});
						}
					})
					.catch(err => {
						console.error('查询订单支付状态失败，尝试查询完整订单状态:', err);
						// 如果支付状态接口失败，回退到查询完整订单状态
						this.queryOrderStatus()
							.then(orderInfo => {
								// 检查是否已支付 - 同时检查status和payStatus
								const isPaid = orderInfo.status === 1 || orderInfo.payStatus === 1;
								
								if (isPaid) {
									console.log('查询到订单已支付，更新支付状态');
									this.isPaid = true;
									this.orderStatus = 1;
									this.paymentSuccess = true;
									
									// 如果已连接设备，继续开门流程
									if (this.isConnected) {
										console.log('确认支付成功，设备已连接，继续开门');
										// 延迟执行，确保状态更新完成
										setTimeout(() => {
											this.performOpenDoor();
										}, 500);
									} else {
										// 未连接设备，先尝试连接
										console.log('确认支付成功，设备未连接，先尝试连接');
										this.tryConnectDevice();
									}
								} else {
									console.log('确认订单未支付，不执行开门操作');
									uni.showToast({
										title: '请先完成支付',
										icon: 'none',
										duration: 2000
									});
								}
							})
							.catch(err => {
								console.error('查询订单状态失败:', err);
								// 显示错误提示
								uni.showToast({
									title: '查询订单状态失败，请重试',
									icon: 'none',
									duration: 2000
								});
							});
					});
				return;
			}
			
			// 如果未连接设备，先尝试连接设备
			if (!this.isConnected && !actualConnected) {
				console.log('设备未连接，先尝试连接设备');
				this.tryConnectDevice();
				return;
			}
			
			// 确保订单ID已设置到lockService
			if (lockService.getOrderInfo().orderId !== this.orderId) {
				console.log('自动开门前重新设置订单ID到lockService:', this.orderId);
				lockService.setOrderId(this.orderId);
				
				// 短暂延迟，确保订单ID设置生效
				setTimeout(() => {
					this.performOpenDoor();
				}, 500); // 延长延迟时间，确保订单ID设置完全生效
			} else {
				// 直接执行开门
				this.performOpenDoor();
			}
		},
		// 执行开门操作
		performOpenDoor() {
			// 如果已支付且连接成功，自动开门
			if (this.isPaid && this.isConnected) {
				console.log('已支付且已连接，自动触发开门');
				this.doorOpenProcessed = true;
				
				// 设置开门超时保护
				const openDoorTimeout = setTimeout(() => {
					console.log('开门操作超时，尝试强制开门');
					
					// 尝试强制开门
					this.forceOpenDoor();
				}, 10000); // 10秒超时
				
				// 直接使用强制模式开门，忽略订单状态检查，添加singleCommand参数和operationType参数
				lockService.openLock({ 
					ignoreOrderStatus: true, 
					force: true, 
					retry: true, 
					singleCommand: true,
					operationType: 1 // 1表示开锁操作，用于设备状态上报
				})
					.then(res => {
						console.log('自动开门成功:', res);

						// 清除超时保护
						clearTimeout(openDoorTimeout);

						// 设置开门完成标志，防止重复开门
						this.doorOpenCompleted = true;

						// 更新锁状态
						this.isLockOpen = true;

						// 更新订单使用状态
						this.useStatus = 1;

						// 更新UI状态
						this.updateUIState();

						// 显示开门成功提示
						uni.showToast({
							title: '开门成功',
							icon: 'success',
							duration: 2000
						});

						// 播放成功音效
						this.playSuccessSound();
					})
					.catch(err => {
						console.error('自动开门失败:', err);

						// 清除超时保护
						clearTimeout(openDoorTimeout);

						// 如果是连接问题，尝试备用开门方法
						if (err.message && err.message.includes('设备未连接')) {
							console.log('自动开门连接失败，尝试备用方法');
							this.tryAlternativeOpenDoor();
						} else {
							// 尝试强制开门
							this.forceOpenDoor();
						}
					});
			} else {
				console.log('支付状态或连接状态不满足开门条件');
				if (!this.isPaid) {
					uni.showToast({
						title: '请先完成支付',
						icon: 'none',
						duration: 2000
					});
				} else if (!this.isConnected) {
					uni.showToast({
						title: '请先连接设备',
						icon: 'none',
						duration: 2000
					});
					// 尝试连接设备
					this.tryConnectDevice();
				}
				
				// 重置开门处理标志，允许再次尝试
				this.doorOpenProcessed = false;
			}
		},

		// 重置开门状态
		resetDoorState() {
			console.log('重置开门状态');
			this.doorOpenProcessed = false;
			this.doorOpenCompleted = false;
			this.doorOpenInProgress = false;
		},

		// 保存支付状态到本地存储
		savePaymentState() {
			if (this.orderId) {
				const paymentState = {
					orderId: this.orderId,
					isPaid: this.isPaid,
					paymentSuccess: this.paymentSuccess,
					orderStatus: this.orderStatus,
					timestamp: Date.now()
				};

				const key = `payment_state_${this.orderId}`;
				uni.setStorageSync(key, paymentState);
				console.log('保存支付状态到本地存储:', paymentState);
			}
		},

		// 从本地存储恢复支付状态
		restorePaymentState() {
			if (this.orderId) {
				const key = `payment_state_${this.orderId}`;
				try {
					const paymentState = uni.getStorageSync(key);
					if (paymentState && paymentState.timestamp) {
						// 检查数据是否过期（24小时）
						const now = Date.now();
						const maxAge = 24 * 60 * 60 * 1000; // 24小时

						if (now - paymentState.timestamp < maxAge) {
							console.log('从本地存储恢复支付状态:', paymentState);

							// 只有当前状态为未支付时才恢复
							if (!this.isPaid && paymentState.isPaid) {
								this.isPaid = paymentState.isPaid;
								this.paymentSuccess = paymentState.paymentSuccess;
								this.orderStatus = paymentState.orderStatus;

								console.log('成功恢复支付状态 - 已支付');
							}
						} else {
							// 数据过期，删除
							console.log('本地支付状态数据过期，删除');
							uni.removeStorageSync(key);
						}
					}
				} catch (err) {
					console.error('恢复支付状态失败:', err);
				}
			}
		},

		// 智能重新认证并开门
		forceReauthenticateAndUnlock(deviceInfo) {
			console.log('智能重新认证并开门');

			return new Promise((resolve, reject) => {
				// 1. 先尝试直接开门，不断开连接
				console.log('步骤1: 尝试直接开门');
				this.executeUnlockCommand(deviceInfo)
					.then(result => {
						console.log('直接开门成功:', result);
						resolve(result);
					})
					.catch(directError => {
						console.log('直接开门失败，开始重连流程:', directError);

						// 2. 如果直接开门失败，才进行重连
						console.log('步骤2: 开始重连流程');
						this.performReconnectAndUnlock(deviceInfo)
							.then(resolve)
							.catch(reject);
					});
			});
		},

		// 执行重连并开门
		performReconnectAndUnlock(deviceInfo) {
			console.log('执行重连并开门');

			// 检查是否已经在重连中
			if (this.isReconnecting || this.reconnectLock) {
				console.log('已经在重连中，跳过重复重连');
				return Promise.reject(new Error('重连已在进行中'));
			}

			// 设置重连锁
			this.isReconnecting = true;
			this.reconnectLock = true;

			return new Promise((resolve, reject) => {
				// 1. 强制断开连接
				console.log('重连步骤1: 强制断开连接');
				this.isManualDisconnect = true; // 标记为手动断开
				lockService.disconnect()
					.then(() => {
						console.log('断开连接成功');
						// 等待断开完成
						return new Promise(resolve => setTimeout(resolve, 1500));
					})
					.catch(err => {
						console.log('断开连接失败，继续执行:', err);
						return Promise.resolve();
					})
					.then(() => {
						// 2. 重新连接
						console.log('重连步骤2: 重新连接设备');
						return lockService.connectDevice(this.deviceMac);
					})
					.then(() => {
						console.log('重新连接成功');
						// 等待连接稳定
						return new Promise(resolve => setTimeout(resolve, 2000));
					})
					.then(() => {
						// 3. 验证连接状态
						console.log('重连步骤3: 验证连接状态');
						const currentDeviceInfo = lockService.getDeviceInfo();
						if (!currentDeviceInfo || !currentDeviceInfo.connected) {
							throw new Error('设备连接验证失败');
						}
						console.log('连接状态验证成功');
						return Promise.resolve();
					})
					.then(() => {
						// 4. 执行开门
						console.log('重连步骤4: 执行开门');
						return this.executeUnlockCommand(deviceInfo);
					})
					.then(result => {
						console.log('重连开门成功:', result);
						resolve(result);
					})
					.catch(error => {
						console.error('重连开门失败:', error);
						reject(error);
					})
					.finally(() => {
						// 释放重连锁
						this.isReconnecting = false;
						setTimeout(() => {
							this.reconnectLock = false;
						}, 3000); // 3秒后释放锁，防止频繁重连
					});
			});
		},

		// 执行开门命令
		executeUnlockCommand(deviceInfo) {
			console.log('执行开门命令');

			// 开门前最后检查 - lockService层面
			const currentDeviceInfo = lockService.getDeviceInfo();
			if (!currentDeviceInfo || !currentDeviceInfo.connected) {
				console.error('开门前检查：lockService层面设备未连接');
				return Promise.reject(new Error('lockService层面设备未连接，无法开门'));
			}

			// 开门前最后检查 - blueToothManager层面
			try {
				const blueToothManager = require('@/utils/blueToothManager.js');
				if (!blueToothManager.isConnected || !blueToothManager.deviceId) {
					console.error('开门前检查：blueToothManager层面设备未连接');
					console.log('blueToothManager.isConnected:', blueToothManager.isConnected);
					console.log('blueToothManager.deviceId:', blueToothManager.deviceId);
					return Promise.reject(new Error('blueToothManager层面设备未连接，无法开门'));
				}
				console.log('开门前检查通过，blueToothManager层面设备已连接');
			} catch (e) {
				console.error('获取blueToothManager失败:', e);
				return Promise.reject(new Error('无法获取blueToothManager，无法开门'));
			}

			console.log('开门前检查通过，所有层面设备已连接');

			return lockService.openLock({
				ignoreOrderStatus: true,
				force: true,
				retry: true,
				singleCommand: true,
				operationType: 1, // 1表示开锁操作，用于设备状态上报
				deviceId: deviceInfo?.deviceId, // 明确传递设备ID
				mac: this.deviceMac, // 明确传递MAC地址
				name: this.deviceName // 明确传递设备名称
			});
		},

		// 检查蓝牙状态
		checkBluetoothState() {
			console.log('检查蓝牙状态');

			return new Promise((resolve, reject) => {
				// 检查蓝牙适配器状态
				uni.getBluetoothAdapterState({
					success: (res) => {
						console.log('蓝牙适配器状态:', res);
						if (res.available) {
							resolve(res);
						} else {
							reject(new Error('蓝牙适配器不可用'));
						}
					},
					fail: (err) => {
						console.error('获取蓝牙适配器状态失败:', err);
						reject(err);
					}
				});
			});
		},

		// 验证连接状态一致性
		verifyConnectionState() {
			console.log('验证连接状态一致性');

			// 检查UI状态和lockService状态是否一致
			const uiConnected = this.isConnected;
			const deviceInfo = lockService.getDeviceInfo();
			const lockServiceConnected = deviceInfo && deviceInfo.connected;
			const isPaid = this.isPaid;

			console.log('状态检查 - UI连接:', uiConnected, 'lockService连接:', lockServiceConnected, '已支付:', isPaid);

			// 如果已支付但连接状态不一致，尝试修复
			if (isPaid && uiConnected !== lockServiceConnected) {
				console.log('检测到连接状态不一致，尝试修复');

				if (lockServiceConnected && !uiConnected) {
					// lockService已连接但UI显示未连接，同步UI状态
					console.log('同步UI状态为已连接');
					this.isConnected = true;
					this.isConnecting = false;
					this.updateUIState();
				} else if (!lockServiceConnected && uiConnected) {
					// UI显示已连接但lockService未连接，尝试重新连接
					console.log('UI显示已连接但lockService未连接，尝试重新连接');
					if (this.deviceMac && this.deviceName) {
						this.tryConnectDevice();
					} else {
						// 没有设备信息，重置UI状态
						this.isConnected = false;
						this.isConnecting = false;
						this.updateUIState();
					}
				}
			}

			// 如果已支付但都未连接，且有设备信息，尝试连接
			if (isPaid && !uiConnected && !lockServiceConnected && this.deviceMac && this.deviceName) {
				console.log('已支付但未连接，且有设备信息，尝试自动连接');
				this.tryConnectDevice();
			}
		},

		// 重新初始化蓝牙环境
		reinitializeBluetooth() {
			console.log('重新初始化蓝牙环境');

			// 保存当前连接状态，避免不必要的重置
			const wasConnected = this.isConnected;
			const wasPaid = this.isPaid;

			console.log('重新初始化前状态 - 连接:', wasConnected, '支付:', wasPaid);

			// 先检查蓝牙状态
			this.checkBluetoothState()
				.then(() => {
					// 蓝牙状态正常，重新初始化lockService
					return lockService.init();
				})
				.then(res => {
					console.log('蓝牙环境重新初始化成功:', res);

					// 重新设置回调
					this.setupLockServiceCallbacks();

					// 重新设置设备信息到lockService
					if (this.deviceMac && this.deviceName) {
						console.log('重新设置设备信息到lockService');
						lockService.setDeviceMac(this.deviceMac);
						lockService.setExpectedDeviceName(this.deviceName);
						if (this.orderId) {
							lockService.setOrderId(this.orderId);
						}
					}

					// 如果之前已连接且已支付，尝试重新连接
					if (wasPaid && (this.deviceMac || this.deviceName)) {
						console.log('检测到之前已支付，尝试重新连接设备');

						// 先检查lockService的连接状态
						const deviceInfo = lockService.getDeviceInfo();
						const lockServiceConnected = deviceInfo && deviceInfo.connected;
						console.log('lockService连接状态:', lockServiceConnected);

						if (!lockServiceConnected) {
							console.log('lockService未连接，开始重新连接');
							setTimeout(() => {
								this.tryConnectDevice();
							}, 1000);
						} else {
							console.log('lockService已连接，同步UI状态');
							this.isConnected = true;
							this.isConnecting = false;
							this.updateUIState();
						}
					}
				})
				.catch(err => {
					console.error('蓝牙环境重新初始化失败:', err);

					// 如果初始化失败，显示提示
					uni.showToast({
						title: '蓝牙初始化失败，请检查蓝牙设置',
						icon: 'none',
						duration: 3000
					});
				});
		},

		// 检查蓝牙状态
		checkBluetoothState() {
			return new Promise((resolve, reject) => {
				console.log('检查蓝牙状态');

				// 检查蓝牙适配器状态
				uni.getBluetoothAdapterState({
					success: (res) => {
						console.log('蓝牙适配器状态:', res);

						if (res.available) {
							console.log('蓝牙适配器可用');
							resolve(res);
						} else {
							console.log('蓝牙适配器不可用');
							reject(new Error('蓝牙适配器不可用'));
						}
					},
					fail: (err) => {
						console.log('获取蓝牙适配器状态失败，可能需要重新初始化:', err);
						// 获取状态失败，可能是适配器未初始化，这是正常的
						resolve();
					}
				});
			});
		},

		// 强制开门方法
		forceOpenDoor() {
			console.log('尝试强制开门模式');
			
			// 显示正在重试提示
			uni.showToast({
				title: '开门中，请稍候...',
				icon: 'loading',
				duration: 2000
			});
			
			// 延迟执行，确保提示显示完成
			setTimeout(() => {
				lockService.openLock({ 
					ignoreOrderStatus: true, 
					force: true, 
					retry: true, 
					singleCommand: true,
					operationType: 1 // 1表示开锁操作，用于设备状态上报
				})
					.then(res => {
						console.log('强制开门成功:', res);

						// 设置开门完成标志，防止重复开门
						this.doorOpenCompleted = true;

						this.isLockOpen = true;
						this.useStatus = 1;
						this.updateUIState();

						uni.showToast({
							title: '开门成功',
							icon: 'success',
							duration: 2000
						});

						// 播放成功音效
						this.playSuccessSound();
					})
					.catch(e => {
						console.error('强制开门也失败:', e);
						// 显示错误提示，但不中断用户体验
						uni.showToast({
							title: '自动开门失败，请手动点击开门',
							icon: 'none',
							duration: 3000
						});
						
						// 重置开门处理标志，允许再次尝试
						this.doorOpenProcessed = false;
					});
			}, 2000);
		},
		// 添加UI状态定时更新机制
		startUIUpdateTimer() {
			// 清除之前的定时器
			this.clearUIUpdateTimer();

			// 创建新的定时器，每2秒检查一次状态并更新UI（降低频率避免冲突）
			this.uiUpdateTimer = setInterval(() => {
				// 从lockService获取最新状态
				const deviceInfo = lockService.getDeviceInfo();

				// 只在状态真正变化时更新，避免频繁更新导致UI闪烁
				let stateChanged = false;

				// 更新连接状态 - 修复undefined问题
				const lockServiceConnected = deviceInfo.connected === true || deviceInfo.isConnected === true;
				if (this.isConnected !== lockServiceConnected) {
					console.log('UI定时更新: 连接状态变化', this.isConnected, '->', lockServiceConnected);
					this.isConnected = lockServiceConnected;
					stateChanged = true;

					// 如果已连接，停止连接中状态
					if (lockServiceConnected) {
						this.isConnecting = false;
					}
				}

				// 更新电池电量
				if (deviceInfo.batteryLevel && this.batteryLevel !== deviceInfo.batteryLevel) {
					this.batteryLevel = deviceInfo.batteryLevel;
					stateChanged = true;
				}

				// 只有在状态真正变化时才触发UI更新
				if (stateChanged) {
					this.updateUIState();
				}
			}, 2000); // 改为2秒更新一次

			console.log('UI状态定时更新已启动');
		},
		
		// 清除UI更新定时器
		clearUIUpdateTimer() {
			if (this.uiUpdateTimer) {
				clearInterval(this.uiUpdateTimer);
				this.uiUpdateTimer = null;
				console.log('UI状态定时更新已停止');
			}

			// 清除状态更新防抖定时器
			if (this.statusUpdateTimer) {
				clearTimeout(this.statusUpdateTimer);
				this.statusUpdateTimer = null;
			}
		},
		
		// 初始化音频对象
		initAudio() {
			// 使用uni.getBackgroundAudioManager替代createInnerAudioContext
			// 因为backgroundAudioManager可以在后台播放且权限更高
			try {
				// 检查平台，小程序环境使用背景音频管理器
				// #ifdef MP-WEIXIN
				this.successAudio = uni.getBackgroundAudioManager();
				this.successAudio.title = '支付成功提示音'; // 必填项
				this.successAudio.epname = '今夜城堡'; // 必填项
				this.successAudio.singer = '系统提示音'; // 必填项
				// 重要：设置src但不会自动播放
				let audioSrc = "https://wuji1112-1312674791.cos.ap-guangzhou.myqcloud.com/static/audio/ttsmaker-file-2025-6-11-20-49-17.mp3";
				// 先将autoplay设为false，因为设置src可能会自动播放
				this.successAudio.autoplay = false;
				// #endif
				
				// 非小程序环境使用普通音频
				// #ifndef MP-WEIXIN
				this.successAudio = uni.createInnerAudioContext();
				this.successAudio.autoplay = false; // 确保不自动播放
				// #endif
				
				// 设置音频源，但不会自动播放
				// 微信小程序环境中，设置src可能会自动播放，所以暂时不设置src
				// #ifdef MP-WEIXIN
				// 仅保存URL，但不立即设置
				this.audioSrc = audioSrc;
				// #endif
				
				// 非微信小程序环境，直接设置src
				// #ifndef MP-WEIXIN
				this.successAudio.src = "https://wuji1112-1312674791.cos.ap-guangzhou.myqcloud.com/static/audio/ttsmaker-file-2025-6-11-20-49-17.mp3";
				// #endif
				
				// 错误处理
				// #ifdef MP-WEIXIN
				this.successAudio.onError((res) => {
					console.error('音频播放错误:', res);
					// 尝试使用另一种方式播放
					this.tryAlternativeAudioPlay();
				});
				// #endif
				
				// #ifndef MP-WEIXIN
				this.successAudio.onError((res) => {
					console.error('音频播放错误:', res);
					this.tryAlternativeAudioPlay();
				});
				// #endif
				
				console.log('初始化音频对象完成，不会自动播放');
			} catch (error) {
				console.error('初始化音频对象失败:', error);
			}
		},
		
		// 尝试替代方案播放音频
		tryAlternativeAudioPlay() {
			console.log('尝试替代方案播放音频');
			
			// 使用微信小程序原生API播放
			// #ifdef MP-WEIXIN
			if (wx && wx.createInnerAudioContext) {
				try {
					const innerAudio = wx.createInnerAudioContext();
					innerAudio.autoplay = true;
					innerAudio.src = "https://wuji1112-1312674791.cos.ap-guangzhou.myqcloud.com/static/audio/ttsmaker-file-2025-6-11-20-49-17.mp3";
					innerAudio.onError((err) => {
						console.error('替代音频播放失败:', err);
						// 如果还是失败，尝试使用短音效API
						this.tryShortAudio();
					});
				} catch (e) {
					console.error('替代音频方案失败:', e);
					this.tryShortAudio();
				}
			} else {
				this.tryShortAudio();
			}
			// #endif
			
			// 非小程序环境
			// #ifndef MP-WEIXIN
			try {
				const audio = new Audio();
				audio.src = "https://wuji1112-1312674791.cos.ap-guangzhou.myqcloud.com/static/audio/ttsmaker-file-2025-6-11-20-49-17.mp3";
				audio.play().catch(err => console.error('HTML5音频播放失败:', err));
			} catch (e) {
				console.error('替代音频播放失败:', e);
			}
			// #endif
		},
		
		// 尝试使用短音效API
		tryShortAudio() {
			// #ifdef MP-WEIXIN
			if (wx && wx.playBackgroundAudio) {
				wx.playBackgroundAudio({
					dataUrl: "https://wuji1112-1312674791.cos.ap-guangzhou.myqcloud.com/static/audio/ttsmaker-file-2025-6-11-20-49-17.mp3",
					title: '支付成功提示音',
					fail: (err) => {
						console.error('背景音频播放失败:', err);
					}
				});
			} else if (wx && wx.createAudioContext) {
				// 尝试使用系统提示音
				wx.vibrateShort({
					type: 'medium'
				});
				console.log('使用振动提示代替音频');
			}
			// #endif
		},
		
		// 添加查询订单支付状态的方法
		queryPaymentStatus() {
			console.log('查询订单支付状态，订单ID:', this.orderId);
			
			if (!this.orderId || isNaN(this.orderId)) {
				console.error('无效的订单ID:', this.orderId);
				return Promise.reject(new Error('无效的订单ID'));
			}
			
			// 使用getAPIBaseUrl函数获取API基础URL
			const baseUrl = getAPIBaseUrl();
			return new Promise((resolve, reject) => {
				uni.request({
					url: `${baseUrl}/api/wx/miniapp/order/payment-status/${this.orderId}`,
					method: 'GET',
					header: {
						'Authorization': uni.getStorageSync('token')
					},
					success: (res) => {
						if (res.data && res.data.code === 200) {
							// 接口返回的是布尔值，true 表示已支付
							const isPaid = res.data.data === true;
							console.log('查询订单支付状态成功:', { isPaid });
							
							// 构造统一的返回格式，方便其他地方使用
							const paymentInfo = {
								isPaid: isPaid
							};
							
							// 如果支付状态为已支付，更新订单状态
							if (isPaid) {
								console.log('订单已支付，更新支付状态');
								this.orderStatus = 1;
								this.isPaid = true;
								this.paymentSuccess = true;
								
								// 播放支付成功音效
								this.playSuccessSound();
								
								// 显示支付成功提示
								uni.showToast({
									title: '支付成功',
									icon: 'success',
									duration: 2000
								});
								
								// 如果已连接设备，自动开门
								if (this.isConnected) {
									console.log('支付成功，设备已连接，自动开门');
									this.checkOrderAndOpenDoor();
								} else {
									// 未连接设备，尝试连接
									console.log('支付成功，设备未连接，尝试连接');
									this.tryConnectDevice();
								}
							}
							
							// 更新UI状态
							this.updateUIState();
							
							resolve(paymentInfo);
						} else {
							const errorMsg = res.data ? res.data.message || '未知错误' : '未知错误';
							console.error('查询订单支付状态失败:', errorMsg);
							reject(new Error('查询订单支付状态失败: ' + errorMsg));
						}
					},
					fail: (err) => {
						console.error('查询订单支付状态请求失败:', err);
						reject(err);
					}
				});
			});
		},
		
		// 添加主动查询订单状态的方法
		queryOrderStatus() {
			console.log('主动查询订单状态，订单ID:', this.orderId);
			
			if (!this.orderId || isNaN(this.orderId)) {
				console.error('无效的订单ID:', this.orderId);
				return Promise.reject(new Error('无效的订单ID'));
			}
			
			// 使用getAPIBaseUrl函数获取API基础URL
			const baseUrl = getAPIBaseUrl();
			return new Promise((resolve, reject) => {
				uni.request({
					url: `${baseUrl}/api/wx/miniapp/order/${this.orderId}`,
					method: 'GET',
					header: {
						'Authorization': uni.getStorageSync('token')
					},
					success: (res) => {
						if (res.data && res.data.code === 200) {
							const orderInfo = res.data.data;
							console.log('主动查询订单状态成功:', orderInfo);
							
							// 更新订单信息
							// 注意：status可能为null，需要检查payStatus
							this.orderStatus = orderInfo.status || 0; // 如果status为null，默认为0（未支付）
							
							// 如果status为null但payStatus为1，说明已支付
							if (orderInfo.status === null && orderInfo.payStatus === 1) {
								console.log('订单status为null但payStatus为1，视为已支付');
								this.orderStatus = 1;
							}
							
							this.useStatus = orderInfo.useStatus || 0;
							this.orderAmount = orderInfo.amount || 0;
							this.orderDuration = orderInfo.duration || 0;
							
							// 检查订单是否已支付 - 同时检查status和payStatus
							const isPaid = this.orderStatus === 1 || orderInfo.payStatus === 1;
							
							// 如果订单状态变为已支付，且之前未设置为已支付
							if (isPaid && !this.isPaid) {
								console.log('订单状态变为已支付，更新支付状态');
								this.isPaid = true;
								this.paymentSuccess = true;
								
								// 播放支付成功音效
								this.playSuccessSound();
								
								// 显示支付成功提示
								uni.showToast({
									title: '支付成功',
									icon: 'success',
									duration: 2000
								});
								
								// 如果已连接设备，自动开门
								if (this.isConnected) {
									console.log('支付成功，设备已连接，自动开门');
									this.checkOrderAndOpenDoor();
								} else {
									// 未连接设备，尝试连接
									console.log('支付成功，设备未连接，尝试连接');
									this.tryConnectDevice();
								}
							}
							
							// 更新UI状态
							this.updateUIState();
							
							resolve(orderInfo);
						} else {
							const errorMsg = res.data ? res.data.message || '未知错误' : '未知错误';
							console.error('主动查询订单状态失败:', errorMsg);
							reject(new Error('主动查询订单状态失败: ' + errorMsg));
						}
					},
					fail: (err) => {
						console.error('主动查询订单状态请求失败:', err);
						reject(err);
					}
				});
			});
		},
		
		// 启动订单状态轮询
		startOrderPolling() {
			console.log('启动订单状态轮询');
			
			// 先清除可能存在的轮询定时器
			this.stopOrderPolling();
			
			// 重置轮询计数
			this.orderPollingCount = 0;
			
			// 如果没有订单ID，不启动轮询
			if (!this.orderId) {
				console.log('没有订单ID，不启动轮询');
				return;
			}
			
			// 如果订单已支付，不需要轮询
			if (this.isPaid || this.orderStatus === 1) {
				console.log('订单已支付，不需要轮询');
				return;
			}
			
			// 创建轮询定时器，缩短轮询间隔为1秒，提高响应速度
			this.pollingInterval = 1000; // 1秒钟轮询一次
			this.orderPollingTimer = setInterval(() => {
				// 增加轮询计数
				this.orderPollingCount++;
				
				console.log(`订单状态轮询 [${this.orderPollingCount}/${this.maxPollingCount}]`);
				
				// 如果超过最大轮询次数，停止轮询
				if (this.orderPollingCount >= this.maxPollingCount) {
					console.log('达到最大轮询次数，停止轮询');
					this.stopOrderPolling();
					
					// 最后再查询一次，确保不遗漏支付状态变化
					this.queryPaymentStatus()
						.then(paymentInfo => {
							console.log('最终查询订单支付状态:', paymentInfo);
							if (paymentInfo.isPaid) {
								console.log('最终查询确认订单已支付');
							}
						})
						.catch(err => {
							console.error('最终查询订单支付状态失败:', err);
						});
					return;
				}
				
				// 优先使用专门的支付状态接口
				this.queryPaymentStatus()
					.then(paymentInfo => {
						// 如果订单已支付，停止轮询
						if (paymentInfo.isPaid) {
							console.log('订单已支付，停止轮询');
							this.stopOrderPolling();
							
							// 如果已连接设备且未处理开门，自动开门
							if (this.isConnected && !this.doorOpenProcessed && !this.doorOpenCompleted) {
								console.log('订单已支付且设备已连接，自动开门');
								this.checkOrderAndOpenDoor();
							} else if (!this.isConnected) {
								// 未连接设备，尝试连接
								console.log('订单已支付但设备未连接，尝试连接设备');
								this.tryConnectDevice();
							} else if (this.doorOpenCompleted) {
								console.log('订单已支付且已完成开门，无需重复操作');
							}
						} else {
							// 未支付，每3次轮询打印一次日志
							if (this.orderPollingCount % 3 === 0) {
								console.log('订单未支付，继续轮询');
							}
						}
					})
					.catch(err => {
						console.error('查询支付状态失败，尝试查询完整订单状态:', err);
						
						// 如果专门的支付状态接口失败，回退到查询完整订单状态
						this.queryOrderStatus()
							.then(orderInfo => {
								// 检查是否已支付 - 同时检查status和payStatus
								const isPaid = orderInfo.status === 1 || orderInfo.payStatus === 1;
								
								// 如果订单已支付，停止轮询
								if (isPaid) {
									console.log('订单已支付，停止轮询');
									this.stopOrderPolling();
									
									// 更新订单状态和支付状态
									this.orderStatus = 1;
									this.isPaid = true;
									this.paymentSuccess = true;
									
									// 如果已连接设备，自动开门
									if (this.isConnected && !this.doorOpenProcessed) {
										console.log('支付成功，设备已连接，自动开门');
										this.checkOrderAndOpenDoor();
									} else if (!this.isConnected) {
										// 未连接设备，尝试连接
										console.log('支付成功，设备未连接，尝试连接');
										this.tryConnectDevice();
									}
								} else if (orderInfo.status === null && this.orderPollingCount % 3 === 0) {
									// 如果status为null，每隔3次轮询打印一次日志
									console.log('订单status为null，继续轮询，当前payStatus:', orderInfo.payStatus);
								}
							})
							.catch(err => {
								console.error('轮询查询订单状态失败:', err);
								// 失败次数过多时停止轮询
								if (this.orderPollingCount >= 5) {
									console.log('轮询失败次数过多，停止轮询');
									this.stopOrderPolling();
								}
							});
					});
			}, this.pollingInterval);
		},
		
		// 停止订单状态轮询
		stopOrderPolling() {
			if (this.orderPollingTimer) {
				console.log('停止订单状态轮询');
				clearInterval(this.orderPollingTimer);
				this.orderPollingTimer = null;
			}
		},
		
		/**
		 * 检查用户是否有设备的订单
		 * @param {string} deviceId 设备编号或ID
		 */
		checkUserDeviceOrder(deviceId) {
			console.log('检查用户是否有设备的订单:', deviceId);
			
			// 构建API请求URL
			let baseUrl = '';
			if (API && API.baseUrl) {
				baseUrl = API.baseUrl;
			} else {
				// 默认使用HTTPS协议
				baseUrl = 'https://api.jycb888.com/';
			}
			
			// 使用HTTPS协议
			if (baseUrl.startsWith('http://')) {
				baseUrl = baseUrl.replace('http://', 'https://');
			}
			
			// 检查用户对该设备是否有订单
			const checkOrderUrl = `${baseUrl}/api/wx/miniapp/device/check-user-order/${deviceId}`;
			console.log('检查用户对设备是否有订单URL:', checkOrderUrl);
			
			return new Promise((resolve, reject) => {
				uni.request({
					url: checkOrderUrl,
					method: 'GET',
					header: {
						'Authorization': uni.getStorageSync('token')
					},
					sslVerify: false,
					success: (res) => {
						if (res.statusCode === 200 && res.data && res.data.code === 200 && res.data.data) {
							console.log('用户对设备有订单:', res.data.data);
							resolve(res.data.data);
						} else {
							console.log('用户对设备没有订单');
							resolve(null);
						}
					},
					fail: (err) => {
						console.error('检查用户订单失败:', err);
						reject(err);
					}
				});
			});
		}
	}
}
</script>

<style>
	/* CSS变量定义 */
	page {
		--primary-light: #A875FF;
		--neon-pink: #ff36f9;
		--success-color: #4CAF50;
		--error-color: #F44336;
		overflow: hidden; /* 禁用页面级别的滚动 */
		height: 100vh; /* 固定页面高度 */
	}
	
	/* 页面基础样式 */
	.page-device {
		padding-top: 120rpx; /* 减小顶部内边距，从160rpx改为120rpx */
		padding-bottom: calc(50rpx + env(safe-area-inset-bottom));
		color: #ffffff;
		height: 100vh;
		min-height: 100vh;
		box-sizing: border-box;
		position: relative;
		overflow: hidden;
	}
	
	/* 页面背景样式 */
	.page-background {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		z-index: 0;
		will-change: transform;
		backface-visibility: hidden;
		-webkit-backface-visibility: hidden;
	}
	
	.background-image {
		width: 100%;
		height: 100%;
		object-fit: cover;
		transition: opacity 0.3s ease;
		transform: scale(1.05); /* 稍微放大背景图片，确保覆盖边缘 */
	}
	
	.gradient-overlay {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background: linear-gradient(to bottom, 
			rgba(18, 18, 18, 0.7) 0%, 
			rgba(18, 18, 18, 0.6) 50%,
			rgba(18, 18, 18, 0.7) 100%);
		z-index: 1;
	}
	
	/* 顶部状态栏和导航栏 */
	.status-bar {
		width: 100%;
		background: transparent; /* 移除背景色，改为透明 */
		backdrop-filter: none; /* 移除模糊效果 */
		-webkit-backdrop-filter: none; /* 移除模糊效果 */
		position: fixed;
		top: 0;
		left: 0;
		z-index: 100;
	}
	
	/* 导航栏 */
	.nav-bar {
		position: fixed;
		top: calc(env(safe-area-inset-top) + 60rpx);
		left: 15rpx;
		right: 15rpx;
		height: 110rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		background: transparent; /* 移除渐变背景，改为透明 */
		backdrop-filter: none; /* 移除模糊效果 */
		-webkit-backdrop-filter: none; /* 移除模糊效果 */
		z-index: 100;
		padding: 0 30rpx;
		border-bottom: none; /* 移除底部边框 */
		box-shadow: none; /* 移除阴影效果 */
		border-radius: 0 0 30rpx 30rpx;
	}
	
	.nav-back {
		position: absolute;
		left: 30rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 70rpx;
		height: 70rpx;
		border-radius: 50%;
		background-color: rgba(168, 117, 255, 0.15);
		border: 1px solid rgba(168, 117, 255, 0.3);
	}
	
	.nav-back .material-icons {
		font-size: 44rpx;
		color: rgba(255, 255, 255, 0.9);
		text-shadow: 0 0 8rpx rgba(168, 117, 255, 0.5);
	}
	
	.nav-title {
		font-size: 38rpx;
		font-weight: 600;
		color: #ffffff;
		text-shadow: 0 0 10rpx rgba(168, 117, 255, 0.5);
		letter-spacing: 2rpx;
	}
	
	/* Banner样式 - iOS真机优化 */
	.banner-container {
		position: fixed;
		top: 140rpx; /* 将位置从170rpx向上移动到140rpx */
		left: 0;
		right: 0;
		width: 92%; /* 从100%缩小到92% */
		max-width: 700rpx; /* 从750rpx缩小到700rpx */
		margin: 0 auto; /* 使用margin auto实现水平居中 */
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 10;
		padding: 20rpx; /* 增加内边距，扩大点击区域 */
		/* iOS兼容性 - 关键优化 */
		-webkit-tap-highlight-color: rgba(0,0,0,0);
		-webkit-touch-callout: none;
		-webkit-user-select: none;
		user-select: none;
		cursor: pointer;
		/* 确保可点击区域 */
		min-height: 220rpx; /* 增加最小高度 */
		/* iOS触摸优化 */
		touch-action: manipulation;
		/* 添加点击反馈 */
		transition: all 0.1s ease;
		/* 强制硬件加速 */
		-webkit-transform: translateZ(0);
		transform: translateZ(0);
		will-change: transform, opacity;
	}

	.banner-container:active {
		opacity: 0.7;
		transform: scale(0.98) translateZ(0);
	}

	.banner-image {
		width: 100%;
		height: 180rpx; /* 调整高度为180rpx */
		object-fit: contain;
		/* iOS兼容性 - 完全禁用图片交互 */
		pointer-events: none;
		-webkit-tap-highlight-color: transparent;
		-webkit-touch-callout: none;
		-webkit-user-select: none;
		user-select: none;
		/* 禁用图片拖拽 */
		-webkit-user-drag: none;
		-khtml-user-drag: none;
		-moz-user-drag: none;
		-o-user-drag: none;
	}

	.banner-glow {
		/* 确保glow元素完全不拦截点击事件 */
		pointer-events: none;
		-webkit-tap-highlight-color: transparent;
		-webkit-touch-callout: none;
		-webkit-user-select: none;
		user-select: none;
		touch-action: none;
	}
	
	/* 删除Logo区域样式 */
	
	/* 内容区域 */
	.content {
		padding: 0 30rpx 30rpx; /* 保持左右内边距 */
		position: relative;
		z-index: 2;
		height: calc(100vh - 120rpx - env(safe-area-inset-bottom) - 50rpx); /* 调整高度，从160rpx改为120rpx */
		max-height: calc(100vh - 120rpx - env(safe-area-inset-bottom) - 50rpx); /* 限制最大高度 */
		display: flex;
		flex-direction: column;
		overflow: hidden; /* 禁用滚动 */
		touch-action: none; /* 禁用触摸滚动 */
		margin-top: 660rpx; /* 调整顶部边距，使内容稍微往下移 */
	}
	
	/* 欢迎标题样式 */
	.welcome-section {
		display: flex;
		flex-direction: column;
		align-items: center;
		margin-bottom: 30rpx;
	}
	
	.welcome-title {
		font-size: 60rpx;
		font-weight: 700;
		color: #ffffff;
		text-align: center;
		text-shadow: 
			0 0 10rpx rgba(255, 255, 255, 0.5),
			0 0 20rpx rgba(168, 117, 255, 0.5);
		letter-spacing: 4rpx;
	}
	
	.welcome-subtitle {
		font-size: 80rpx;
		font-weight: 800;
		color: #ffffff;
		text-align: center;
		background: linear-gradient(to right, #A875FF, #ff36f9);
		-webkit-background-clip: text;
		-webkit-text-fill-color: transparent;
		text-shadow: 0 0 20rpx rgba(168, 117, 255, 0.7);
		letter-spacing: 6rpx;
		margin-top: 10rpx;
		animation: glow 2s ease-in-out infinite alternate;
	}
	
	@keyframes glow {
		from {
			text-shadow: 0 0 10rpx rgba(168, 117, 255, 0.5), 0 0 20rpx rgba(168, 117, 255, 0.3);
		}
		to {
			text-shadow: 0 0 20rpx rgba(168, 117, 255, 0.7), 0 0 30rpx rgba(168, 117, 255, 0.5), 0 0 40rpx rgba(168, 117, 255, 0.3);
		}
	}
	
	.welcome-device {
		display: flex;
		align-items: center;
		justify-content: center;
		margin-top: 30rpx;
		padding: 15rpx 40rpx;
		background: rgba(168, 117, 255, 0.2);
		border: 1px solid rgba(168, 117, 255, 0.4);
		border-radius: 50rpx;
	}
	
	.welcome-device .material-icons {
		font-size: 36rpx;
		margin-right: 10rpx;
		color: rgba(255, 255, 255, 0.9);
	}
	
	.welcome-device text {
		font-size: 30rpx;
		color: rgba(255, 255, 255, 0.9);
	}
	
	/* 登录提示样式 */
	.login-prompt {
		background: rgba(255, 193, 7, 0.1);
		border: 1px solid rgba(255, 193, 7, 0.3);
		border-radius: 20rpx;
		padding: 30rpx;
		margin: 30rpx 0;
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 20rpx;
	}

	.login-prompt-text {
		display: flex;
		align-items: center;
		gap: 15rpx;
		color: rgba(255, 255, 255, 0.9);
		font-size: 28rpx;
	}

	.login-prompt-text .material-icons {
		font-size: 32rpx;
		color: #FFC107;
	}

	.login-prompt-button {
		background: linear-gradient(135deg, #FFC107, #FF9800);
		border-radius: 50rpx;
		padding: 20rpx 40rpx;
		color: #000;
		font-size: 28rpx;
		font-weight: 600;
		box-shadow: 0 8rpx 20rpx rgba(255, 193, 7, 0.3);
		transition: all 0.3s ease;
	}

	.login-prompt-button:active {
		transform: scale(0.95);
		box-shadow: 0 4rpx 10rpx rgba(255, 193, 7, 0.2);
	}

	/* 设备价格与支付区域 */
	.price-section {
		margin-top: 30rpx; /* 减小与上方元素的间距，从60rpx改为30rpx */
	}
	
	/* 设备卡片样式 */
	.price-card {
		padding: 30rpx; /* 减小内边距，从40rpx改为30rpx */
		background-color: rgba(30, 30, 30, 0.5); /* 添加背景色 */
		border-radius: 20rpx;
		backdrop-filter: blur(10px); /* 添加模糊效果 */
		-webkit-backdrop-filter: blur(10px); /* 添加模糊效果 */
		box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2); /* 添加阴影 */
		border: 1px solid rgba(168, 117, 255, 0.3); /* 保留紫色边框 */
	}
	
	/* 设备连接区域 */
	.connect-section {
		flex: 1;
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 30rpx 0; /* 减小内边距，从40rpx改为30rpx */
		margin-top: 20rpx; /* 减小与上方元素的间距，因为已经有欢迎标题了 */
	}
	
	/* 霓虹灯标题效果 */
	.neon-title {
		font-size: 42rpx;
		font-weight: 700;
		text-align: center;
		color: #fff;
		text-shadow: 
			0 0 5rpx rgba(255, 255, 255, 0.5),
			0 0 10rpx rgba(255, 255, 255, 0.3), 
			0 0 15rpx rgba(168, 117, 255, 0.4),
			0 0 25rpx rgba(168, 117, 255, 0.3);
		letter-spacing: 4rpx;
		animation: soft-flicker 4s infinite alternate;
	}
	
	/* 智能门锁连接状态标题使用圆体 */
	.status-title {
		font-family: "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "WenQuanYi Micro Hei", sans-serif;
		font-weight: 400;
		border-radius: 999rpx;
	}
	
	.neon-subtitle {
		font-size: 36rpx;
		font-weight: 600;
		text-align: center;
		margin-top: 10rpx;
		color: rgba(255, 255, 255, 0.9);
		text-shadow: 
			0 0 5rpx rgba(255, 255, 255, 0.3),
			0 0 10rpx rgba(255, 255, 255, 0.2), 
			0 0 15rpx rgba(255, 102, 221, 0.3);
		letter-spacing: 2rpx;
		animation: soft-flicker 5s infinite alternate;
		animation-delay: 0.5s;
	}
	
	@keyframes soft-flicker {
		0%, 100% {
			opacity: 1;
		}
		30% {
			opacity: 0.9;
		}
		60% {
			opacity: 0.95;
		}
	}
	
	/* 操作按钮 */
	.action-buttons {
		margin-top: 40rpx;
		display: flex;
		justify-content: center;
	}
	
	.primary-button {
		position: relative;
		width: 240rpx; /* 增加按钮尺寸 */
		height: 240rpx; /* 增加按钮尺寸 */
		border-radius: 50%;
		background: rgba(139, 92, 246, 0.15);
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		box-shadow: 0 10rpx 40rpx rgba(0, 0, 0, 0.3); /* 增强阴影效果 */
		overflow: visible;
		border: 2px solid rgba(168, 117, 255, 0.4); /* 加粗边框 */
		transition: transform 0.3s, box-shadow 0.3s;
		animation: pulse 2s infinite ease-in-out;
		backdrop-filter: blur(20px);
		-webkit-backdrop-filter: blur(20px);
		z-index: 1;
	}
	
	.primary-button:active {
		transform: scale(0.96);
		box-shadow: 0 4rpx 10rpx rgba(168, 117, 255, 0.2);
	}
	
	/* 添加内部渐变效果 */
	.primary-button::after {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		border-radius: 50%;
		background: linear-gradient(145deg, rgba(139, 92, 246, 0.3), rgba(168, 117, 255, 0.1));
		z-index: -1;
	}
	
	/* 添加涟漪动画效果 - 第一层 */
	.primary-button::before {
		content: '';
		position: absolute;
		top: 50%;
		left: 50%;
		width: 100%;
		height: 100%;
		border-radius: 50%;
		transform: translate(-50%, -50%) scale(0.9);
		opacity: 0;
		z-index: -2;
		border: 2px solid rgba(168, 117, 255, 0.5);
		animation: ripple 2s infinite linear;
	}
	
	/* 添加第二层涟漪 */
	.primary-button .ripple-layer {
		content: '';
		position: absolute;
		top: 50%;
		left: 50%;
		width: 100%;
		height: 100%;
		border-radius: 50%;
		transform: translate(-50%, -50%) scale(0.9);
		opacity: 0;
		z-index: -2;
		border: 2px solid rgba(168, 117, 255, 0.5);
		animation: ripple 2s infinite linear 1s;
	}
	
	@keyframes ripple {
		0% {
			transform: translate(-50%, -50%) scale(0.9);
			opacity: 0.7;
		}
		100% {
			transform: translate(-50%, -50%) scale(1.5);
			opacity: 0;
		}
	}
	
	.primary-button .material-icons {
		font-size: 100rpx; /* 增大图标尺寸 */
		color: rgba(255, 255, 255, 0.85);
		margin-bottom: 10rpx;
	}
	
	.primary-button text:not(.material-icons) {
		font-size: 34rpx; /* 从36rpx改为34rpx */
		font-weight: 400; /* 从600改为400，取消加粗 */
		color: rgba(255, 255, 255, 0.9);
	}
	
	/* 按钮内部脉冲效果 */
	@keyframes pulse {
		0%, 100% {
			box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);
		}
		50% {
			box-shadow: 0 8rpx 32rpx rgba(168, 117, 255, 0.4);
		}
	}
	
	.open-button {
		background: rgba(76, 175, 80, 0.2); /* 简化背景，为毛玻璃效果做准备 */
		box-shadow: 0 8rpx 20rpx rgba(76, 175, 80, 0.2);
		border: 1px solid rgba(76, 175, 80, 0.5);
		animation: pulse-green 2s infinite ease-in-out;
	}
	
	/* 添加开门按钮内部渐变效果 */
	.open-button::after {
		background: linear-gradient(145deg, rgba(76, 175, 80, 0.4), rgba(76, 175, 80, 0.1));
	}
	
	/* 修改开门按钮涟漪效果 */
	.open-button::before,
	.open-button .ripple-layer {
		border-color: rgba(76, 175, 80, 0.5);
	}
	
	.open-button:active {
		box-shadow: 0 4rpx 10rpx rgba(76, 175, 80, 0.2);
	}
	
	@keyframes pulse-green {
		0%, 100% {
			box-shadow: 0 8rpx 20rpx rgba(76, 175, 80, 0.2);
		}
		50% {
			box-shadow: 0 8rpx 32rpx rgba(76, 175, 80, 0.5);
		}
	}
	
	/* 连接状态展示 */
	.connection-status {
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 16rpx 40rpx; /* 从20rpx 50rpx缩小到16rpx 40rpx */
		margin-top: 40rpx;
		background: linear-gradient(145deg, rgba(139, 92, 246, 0.3), rgba(168, 117, 255, 0.5));
		border: 1px solid rgba(168, 117, 255, 0.5);
		border-radius: 40rpx; /* 从50rpx缩小到40rpx */
		box-shadow: 0 8rpx 20rpx rgba(168, 117, 255, 0.2);
		transition: all 0.3s ease;
		position: relative;
		overflow: hidden;
		transform: scale(0.95); /* 整体缩小到95% */
	}
	
	/* 添加连接状态的波纹背景效果 */
	.connection-status::before {
		content: '';
		position: absolute;
		top: 0;
		left: -100%;
		width: 100%;
		height: 100%;
		background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
		animation: wave 2s infinite linear;
	}
	
	@keyframes wave {
		0% {
			left: -100%;
		}
		50%, 100% {
			left: 100%;
		}
	}
	
	.connection-status.connected {
		background: linear-gradient(145deg, rgba(76, 175, 80, 0.3), rgba(76, 175, 80, 0.5));
		box-shadow: 0 8rpx 20rpx rgba(76, 175, 80, 0.2);
		border: 1px solid rgba(76, 175, 80, 0.5);
		animation: glow-green 2s infinite alternate;
	}
	
	@keyframes glow-green {
		0% {
			box-shadow: 0 8rpx 20rpx rgba(76, 175, 80, 0.2);
		}
		100% {
			box-shadow: 0 8rpx 30rpx rgba(76, 175, 80, 0.6);
		}
	}
	
	.connection-status.disconnected {
		background: linear-gradient(145deg, rgba(158, 158, 158, 0.2), rgba(158, 158, 158, 0.3));
		box-shadow: 0 0 20rpx rgba(158, 158, 158, 0.2);
	}
	
	.connection-text {
		font-size: 28rpx; /* 从32rpx缩小到28rpx */
		font-weight: 500;
		color: #ffffff;
		font-family: "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "WenQuanYi Micro Hei", sans-serif;
	}
	
	.connection-status .material-icons {
		margin-right: 12rpx; /* 从16rpx缩小到12rpx */
		font-size: 36rpx; /* 从40rpx缩小到36rpx */
	}
	
	/* 连接提示 */
	.connection-tips {
		margin-top: 30rpx; /* 从40rpx减小到30rpx，使文字上移 */
		display: flex;
		flex-direction: column;
		align-items: center;
		text-align: center;
	}
	
	.connection-tips .text-tertiary {
		line-height: 48rpx;
	}
	
	.connection-tips .text-agreement {
		font-size: 26rpx; /* 从28rpx缩小到26rpx */
		color: rgba(255, 255, 255, 0.4);
		line-height: 40rpx;
		margin-top: 10rpx;
	}
	
	/* 操作结果提示 */
	.result-message {
		margin-top: 40rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		animation: fadeIn 0.5s;
	}
	
	@keyframes fadeIn {
		from {
			opacity: 0;
			transform: translateY(20rpx);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}
	
	.result-icon {
		font-size: 80rpx;
		margin-bottom: 16rpx;
	}
	
	.result-icon.success {
		color: var(--success-color, #4CAF50);
	}
	
	.result-icon:not(.success) {
		color: var(--error-color, #F44336);
	}
	
	.result-text {
		font-size: 32rpx;
		font-weight: 500;
		color: #ffffff;
	}
	
	.text-connected {
		color: var(--success-color, #4CAF50);
		text-shadow: 0 0 10rpx rgba(76, 175, 80, 0.5); /* 增强绿色发光效果 */
	}
	
	.text-connecting {
		color: var(--primary-light, #A875FF);
		text-shadow: 0 0 10rpx rgba(168, 117, 255, 0.5); /* 增强紫色发光效果 */
	}
	
	.text-disconnected {
		color: rgba(158, 158, 158, 0.9);
		text-shadow: 0 0 10rpx rgba(158, 158, 158, 0.4);
		animation: pulse-text 2s infinite;
	}
	
	@keyframes pulse-text {
		0% {
			opacity: 0.7;
		}
		50% {
			opacity: 1;
		}
		100% {
			opacity: 0.7;
		}
	}
	
	/* Material Icons 字体 */
	@font-face {
		font-family: 'Material Icons';
		font-style: normal;
		font-weight: 400;
		src: url(https://fonts.gstatic.com/s/materialicons/v139/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format('woff2');
	}

	.material-icons {
		font-family: 'Material Icons';
		font-weight: normal;
		font-style: normal;
		font-size: 48rpx;
		line-height: 1;
		letter-spacing: normal;
		text-transform: none;
		display: inline-block;
		white-space: nowrap;
		word-wrap: normal;
		direction: ltr;
		-webkit-font-smoothing: antialiased;
		-moz-osx-font-smoothing: grayscale;
	}
	
	/* 辅助类 */
	.flex {
		display: flex;
	}
	
	.justify-between {
		justify-content: space-between;
	}
	
	.items-center {
		align-items: center;
	}
	
	.mt-sm {
		margin-top: 16rpx;
	}
	
	.mt-md {
		margin-top: 30rpx;
	}
	
	.mt-lg {
		margin-top: 60rpx;
	}
	
	/* 标题和文本样式 */
	.title-md {
		font-size: 38rpx;
		font-weight: 600;
		color: #ffffff;
	}
	
	.text-secondary {
		color: rgba(255, 255, 255, 0.7);
	}
	
	.text-tertiary {
		color: rgba(255, 255, 255, 0.5);
	}
	
	/* 卡片样式 */
	.card {
		border-radius: 20rpx;
		background-color: transparent !important; /* 使用!important确保覆盖其他样式 */
		box-shadow: none !important; /* 移除阴影 */
		border: 1px solid rgba(168, 117, 255, 0.3); /* 添加紫色边框 */
	}
	
	.text-paid {
		color: var(--success-color, #4CAF50);
		text-shadow: 0 0 10rpx rgba(76, 175, 80, 0.5);
	}
	
	.text-unpaid {
		color: #FF9800;
		text-shadow: 0 0 10rpx rgba(255, 152, 0, 0.5);
	}
	
	/* 付款模态框样式 */
	.modal-overlay {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.7);
		backdrop-filter: blur(5px);
		-webkit-backdrop-filter: blur(5px);
		z-index: 1000;
		display: flex;
		justify-content: center;
		align-items: center;
		animation: fadeIn 0.3s ease;
	}
	
	@keyframes fadeIn {
		from {
			opacity: 0;
		}
		to {
			opacity: 1;
		}
	}
	
	.payment-modal, .open-door-modal {
		width: 80%;
		max-width: 600rpx;
		background: linear-gradient(145deg, rgba(30, 30, 30, 0.9), rgba(40, 40, 40, 0.9));
		border-radius: 20rpx;
		box-shadow: 0 10rpx 40rpx rgba(0, 0, 0, 0.5);
		border: 1px solid rgba(168, 117, 255, 0.3);
		overflow: hidden;
		animation: slideUp 0.3s ease;
		z-index: 1001;
		position: fixed;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -55%);
	}
	
	@keyframes slideUp {
		from {
			transform: translate(-50%, -45%);
			opacity: 0;
		}
		to {
			transform: translate(-50%, -55%);
			opacity: 1;
		}
	}
	
	.modal-header {
		padding: 20rpx 30rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		border-bottom: 1px solid rgba(168, 117, 255, 0.2);
	}
	
	.modal-title {
		font-size: 34rpx;
		font-weight: 600;
		color: #ffffff;
		text-shadow: 0 0 10rpx rgba(168, 117, 255, 0.5);
	}
	
	.close-icon {
		font-size: 40rpx;
		color: rgba(255, 255, 255, 0.7);
	}
	
	.modal-body {
		padding: 30rpx;
	}
	
	/* 支付弹窗样式 */
	.payment-info {
		display: flex;
		flex-direction: column;
		align-items: center;
		margin-bottom: 30rpx;
	}
	
	.payment-device-name {
		font-size: 32rpx;
		font-weight: 600;
		color: #ffffff;
		margin-bottom: 10rpx;
	}
	
	.payment-price {
		font-size: 48rpx;
		font-weight: 700;
		color: #ffffff;
		background: linear-gradient(to right, #A875FF, #ff36f9);
		-webkit-background-clip: text;
		-webkit-text-fill-color: transparent;
		text-shadow: 0 0 10rpx rgba(168, 117, 255, 0.3);
	}
	
	.payment-button {
		width: 100%;
		height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		background: linear-gradient(145deg, #A875FF, #ff36f9);
		border-radius: 40rpx;
		margin: 20rpx 0;
		position: relative;
		overflow: hidden;
	}
	
	.payment-button::before {
		content: '';
		position: absolute;
		top: -50%;
		left: -50%;
		width: 200%;
		height: 200%;
		background: linear-gradient(transparent, rgba(255, 255, 255, 0.3), transparent);
		transform: rotate(45deg);
		animation: shine 2s infinite;
	}
	
	@keyframes shine {
		0% {
			left: -50%;
		}
		100% {
			left: 150%;
		}
	}
	
	.payment-button text {
		font-size: 32rpx;
		font-weight: 600;
		color: #ffffff;
		z-index: 1;
	}
	
	.payment-button.loading {
		background: linear-gradient(145deg, rgba(168, 117, 255, 0.7), rgba(255, 54, 249, 0.7));
	}
	
	.loading-spinner {
		width: 40rpx;
		height: 40rpx;
		border: 4rpx solid rgba(255, 255, 255, 0.3);
		border-top: 4rpx solid #ffffff;
		border-radius: 50%;
		animation: spin 1s linear infinite;
	}
	
	@keyframes spin {
		0% {
			transform: rotate(0deg);
		}
		100% {
			transform: rotate(360deg);
		}
	}
	
	.payment-tips {
		font-size: 24rpx;
		color: rgba(255, 255, 255, 0.5);
		text-align: center;
		margin-top: 10rpx;
	}
	
	/* 开门弹窗样式 */
	.door-info {
		display: flex;
		flex-direction: column;
		align-items: center;
		margin-bottom: 30rpx;
	}
	
	.door-icon {
		width: 120rpx;
		height: 120rpx;
		border-radius: 50%;
		background: rgba(76, 175, 80, 0.2);
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 20rpx;
		border: 1px solid rgba(76, 175, 80, 0.5);
		box-shadow: 0 0 20rpx rgba(76, 175, 80, 0.3);
	}
	
	.door-icon .material-icons {
		font-size: 60rpx;
		color: rgba(76, 175, 80, 0.9);
	}
	
	.door-message {
		font-size: 32rpx;
		font-weight: 600;
		color: #ffffff;
		margin-bottom: 20rpx;
	}
	
	.door-device-name {
		font-size: 28rpx;
		color: rgba(255, 255, 255, 0.8);
		margin-bottom: 10rpx;
	}
	
	.door-price {
		font-size: 36rpx;
		font-weight: 700;
		color: #ffffff;
		background: linear-gradient(to right, #A875FF, #ff36f9);
		-webkit-background-clip: text;
		-webkit-text-fill-color: transparent;
		text-shadow: 0 0 10rpx rgba(168, 117, 255, 0.3);
	}
	
	.door-buttons {
		display: flex;
		justify-content: space-between;
		margin-top: 30rpx;
	}
	
	.door-cancel-button, .door-pay-button {
		width: 45%;
		height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 40rpx;
	}
	
	.door-cancel-button {
		background: rgba(255, 255, 255, 0.1);
		border: 1px solid rgba(255, 255, 255, 0.3);
	}
	
	.door-pay-button {
		background: linear-gradient(145deg, #A875FF, #ff36f9);
		position: relative;
		overflow: hidden;
	}
	
	.door-pay-button::before {
		content: '';
		position: absolute;
		top: -50%;
		left: -50%;
		width: 200%;
		height: 200%;
		background: linear-gradient(transparent, rgba(255, 255, 255, 0.3), transparent);
		transform: rotate(45deg);
		animation: shine 2s infinite;
	}
	
	.door-cancel-button text {
		font-size: 28rpx;
		color: rgba(255, 255, 255, 0.8);
	}
	
	.door-pay-button text {
		font-size: 28rpx;
		font-weight: 600;
		color: #ffffff;
		z-index: 1;
	}
	
	/* 价格信息样式 */
	.price-info {
		margin-top: 40rpx;
		padding: 20rpx;
		background: rgba(30, 30, 30, 0.5);
		border-radius: 20rpx;
		border: 1px solid rgba(168, 117, 255, 0.3);
		display: flex;
		flex-direction: column;
		align-items: center;
	}
	
	.price-title {
		font-size: 28rpx;
		color: rgba(255, 255, 255, 0.7);
		margin-bottom: 10rpx;
	}
	
	.price-value {
		font-size: 40rpx;
		font-weight: 700;
		color: #ffffff;
		background: linear-gradient(to right, #A875FF, #ff36f9);
		-webkit-background-clip: text;
		-webkit-text-fill-color: transparent;
		text-shadow: 0 0 10rpx rgba(168, 117, 255, 0.3);
	}
	
	/* 支付按钮样式 */
	.pay-button {
		background: rgba(255, 152, 0, 0.2);
		box-shadow: 0 8rpx 20rpx rgba(255, 152, 0, 0.2);
		border: 1px solid rgba(255, 152, 0, 0.5);
		animation: pulse-orange 2s infinite ease-in-out;
	}
	
	.pay-button::after {
		background: linear-gradient(145deg, rgba(255, 152, 0, 0.4), rgba(255, 152, 0, 0.1));
	}
	
	.pay-button::before,
	.pay-button .ripple-layer {
		border-color: rgba(255, 152, 0, 0.5);
	}
	
	.pay-button:active {
		box-shadow: 0 4rpx 10rpx rgba(255, 152, 0, 0.2);
	}
	
	@keyframes pulse-orange {
		0%, 100% {
			box-shadow: 0 8rpx 20rpx rgba(255, 152, 0, 0.2);
		}
		50% {
			box-shadow: 0 8rpx 32rpx rgba(255, 152, 0, 0.5);
		}
	}
	
	/* 添加锁状态显示区域样式 */
	.lock-status-section {
		margin-top: 40rpx;
		padding: 30rpx;
		background: rgba(30, 30, 30, 0.5);
		border-radius: 20rpx;
		border: 1px solid rgba(168, 117, 255, 0.3);
		display: flex;
		flex-direction: column;
		align-items: center;
	}
	
	.lock-status-title {
		font-size: 32rpx;
		color: rgba(255, 255, 255, 0.8);
		margin-bottom: 20rpx;
	}
	
	.lock-status-display {
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 15rpx 40rpx;
		border-radius: 40rpx;
		margin-bottom: 20rpx;
	}
	
	.lock-status-display .material-icons {
		font-size: 44rpx;
		margin-right: 10rpx;
	}
	
	.lock-status-display text:not(.material-icons) {
		font-size: 32rpx;
		font-weight: 500;
	}
	
	.lock-open {
		background: rgba(76, 175, 80, 0.2);
		border: 1px solid rgba(76, 175, 80, 0.5);
		color: rgba(76, 175, 80, 0.9);
	}
	
	.lock-closed {
		background: rgba(158, 158, 158, 0.2);
		border: 1px solid rgba(158, 158, 158, 0.4);
		color: rgba(255, 255, 255, 0.8);
	}
	
	.lock-battery-info {
		display: flex;
		align-items: center;
		color: rgba(255, 255, 255, 0.7);
		font-size: 28rpx;
	}
	
	.lock-battery-info .material-icons {
		font-size: 36rpx;
		margin-right: 8rpx;
	}
	
	/* 底部导航栏样式 */
	.bottom-nav-bar {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		height: 100rpx;
		background: rgba(30, 30, 30, 0.8);
		backdrop-filter: blur(10px);
		-webkit-backdrop-filter: blur(10px);
		display: flex;
		justify-content: space-around;
		align-items: center;
		border-top: 1px solid rgba(168, 117, 255, 0.3);
		padding-bottom: env(safe-area-inset-bottom);
		z-index: 100;
	}
	
	.nav-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		flex: 1;
		height: 100%;
		color: rgba(255, 255, 255, 0.7);
	}
	
	.nav-item .material-icons {
		font-size: 48rpx;
		margin-bottom: 4rpx;
	}
	
	.nav-item text:not(.material-icons) {
		font-size: 24rpx;
	}
	
	.nav-item.active {
		color: var(--primary-light, #A875FF);
	}
	
	/* 调整内容区域，确保不被底部导航栏遮挡 */
	.content {
		padding-bottom: calc(150rpx + env(safe-area-inset-bottom));
	}
	
	/* 订单状态显示区域样式 */
	.order-status-section {
		margin-top: 40rpx;
		padding: 30rpx;
		background: rgba(30, 30, 30, 0.5);
		border-radius: 20rpx;
		border: 1px solid rgba(168, 117, 255, 0.3);
		display: flex;
		flex-direction: column;
		align-items: center;
		animation: fadeIn 0.5s ease;
	}
	
	.order-status-title {
		font-size: 32rpx;
		color: rgba(255, 255, 255, 0.8);
		margin-bottom: 20rpx;
	}
	
	.order-status-display {
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 15rpx 40rpx;
		border-radius: 40rpx;
		margin-bottom: 20rpx;
		min-width: 200rpx;
		text-align: center;
	}
	
	.order-status-display .material-icons {
		font-size: 44rpx;
		margin-right: 10rpx;
	}
	
	.order-status-display text:not(.material-icons) {
		font-size: 32rpx;
		font-weight: 500;
	}
	
	.order-paid {
		background: rgba(76, 175, 80, 0.2);
		border: 1px solid rgba(76, 175, 80, 0.5);
		color: rgba(76, 175, 80, 0.9);
		animation: pulse-green-soft 2s infinite alternate;
	}
	
	@keyframes pulse-green-soft {
		0% {
			box-shadow: 0 0 10rpx rgba(76, 175, 80, 0.2);
		}
		100% {
			box-shadow: 0 0 20rpx rgba(76, 175, 80, 0.5);
		}
	}
	
	.order-unpaid {
		background: rgba(255, 152, 0, 0.2);
		border: 1px solid rgba(255, 152, 0, 0.5);
		color: rgba(255, 152, 0, 0.9);
	}
	
	.order-finished {
		background: rgba(33, 150, 243, 0.2);
		border: 1px solid rgba(33, 150, 243, 0.5);
		color: rgba(33, 150, 243, 0.9);
	}
	
	.order-cancelled {
		background: rgba(158, 158, 158, 0.2);
		border: 1px solid rgba(158, 158, 158, 0.4);
		color: rgba(255, 255, 255, 0.8);
	}
	
	.order-info {
		width: 100%;
		margin-top: 20rpx;
	}
	
	.order-info-item {
		display: flex;
		align-items: center;
		margin-bottom: 10rpx;
		color: rgba(255, 255, 255, 0.8);
	}
	
	.order-info-item .material-icons {
		font-size: 36rpx;
		margin-right: 10rpx;
	}
	
	.order-info-item text:not(.material-icons) {
		font-size: 28rpx;
	}
	
	.order-unpaid-tip {
		margin-top: 15rpx;
		color: rgba(255, 152, 0, 0.9);
		font-size: 28rpx;
		animation: blink 1.5s infinite;
	}
	
	@keyframes blink {
		0%, 100% {
			opacity: 1;
		}
		50% {
			opacity: 0.6;
		}
	}
	
	/* 状态标题 */
	.status-title {
		font-size: 46rpx; /* 从44rpx增大到46rpx */
		font-weight: 600; /* 从500增加到600，加粗文字 */
		margin-bottom: 40rpx;
		text-align: center;
		color: #ffffff;
		text-shadow: 0 0 10rpx rgba(168, 117, 255, 0.8);
		letter-spacing: 2rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		animation: breathing 4s ease-in-out infinite; /* 从3秒改为4秒，使动画更慢 */
	}
	
	.status-title view {
		line-height: 70rpx; /* 从68rpx增大到70rpx，适应更大的字体 */
	}
	
	/* 增强的呼吸灯动画 */
	@keyframes breathing {
		0% {
			text-shadow: 0 0 8rpx rgba(168, 117, 255, 0.6);
			opacity: 0.85;
			transform: scale(0.96);
			font-size: 44rpx;
		}
		25% {
			text-shadow: 0 0 12rpx rgba(168, 117, 255, 0.75);
			opacity: 0.92;
			transform: scale(0.98);
			font-size: 45rpx;
		}
		50% {
			text-shadow: 
				0 0 15rpx rgba(168, 117, 255, 0.9),
				0 0 25rpx rgba(168, 117, 255, 0.6),
				0 0 35rpx rgba(168, 117, 255, 0.4),
				0 0 45rpx rgba(168, 117, 255, 0.2);
			opacity: 1;
			transform: scale(1.04);
			font-size: 48rpx;
		}
		75% {
			text-shadow: 0 0 12rpx rgba(168, 117, 255, 0.75);
			opacity: 0.92;
			transform: scale(0.98);
			font-size: 45rpx;
		}
		100% {
			text-shadow: 0 0 8rpx rgba(168, 117, 255, 0.6);
			opacity: 0.85;
			transform: scale(0.96);
			font-size: 44rpx;
		}
	}
	
	.payment-shop-name {
		font-size: 28rpx;
		color: rgba(255, 255, 255, 0.8);
		margin: 8rpx 0 12rpx;
		padding: 6rpx 16rpx;
		background-color: rgba(168, 117, 255, 0.2);
		border-radius: 20rpx;
		border: 1px solid rgba(168, 117, 255, 0.3);
		display: inline-block;
	}
	
	.door-shop-name {
		font-size: 28rpx;
		color: rgba(255, 255, 255, 0.8);
		margin: 8rpx 0 12rpx;
		padding: 6rpx 16rpx;
		background-color: rgba(168, 117, 255, 0.2);
		border-radius: 20rpx;
		border: 1px solid rgba(168, 117, 255, 0.3);
		display: inline-block;
	}
</style> 