/**
 * 今夜城堡用户端API服务
 * 封装了与后端交互的各种API接口
 */

// API基础配置
const API_CONFIG = {
  // 基础URL，实际开发中需替换为真实的API地址
  baseUrl: 'https://api.jycb888.com',
  // API版本
  // 请求超时时间(ms)
  timeout: 30000,
  // 请求头
  headers: {
    'Content-Type': 'application/json'
  }
};

/**
 * 确保URL格式正确，避免双斜杠问题
 * @param {String} baseUrl 基础URL
 * @param {String} path 路径
 * @returns {String} 正确格式的完整URL
 */
function buildUrl(baseUrl, path) {
  // 移除baseUrl结尾的斜杠
  const base = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;
  // 确保path以斜杠开头
  const apiPath = path.startsWith('/') ? path : `/${path}`;
  return `${base}${apiPath}`;
}

/**
 * 预处理请求数据，将空字符串转换为null
 * 避免后端解析数值字段时出错
 * @param {Object|Array} data 请求数据
 * @returns {Object|Array} 处理后的数据
 */
function preprocessRequestData(data) {
  if (!data) return data;
  
  if (Array.isArray(data)) {    // 处理数组
    return data.map(item => preprocessRequestData(item));
  } else if (typeof data === 'object' && data !== null) {
    // 处理对象
    const result = {};
    for (const key in data) {
      if (data[key] === '') {
        // 将空字符串转为null
        result[key] = null;
      } else if (typeof data[key] === 'object' && data[key] !== null) {
        // 递归处理嵌套对象或数组
        result[key] = preprocessRequestData(data[key]);
      } else {
        // 其他值保持不变
        result[key] = data[key];
      }
    }
    return result;
  }
  
  // 原始类型直接返回
  return data;
}

/**
 * 统一请求方法
 * @param {Object} options 请求配置
 * @returns {Promise} Promise对象
 */
function request(options) {
  return new Promise((resolve, reject) => {
    // 获取存储的token
    const token = uni.getStorageSync('token');
    
    // 构建完整请求地址
    const url = options.url.startsWith('http') 
      ? options.url 
      : buildUrl(API_CONFIG.baseUrl, options.url);
    
    // 构建请求头
    const header = {
      ...API_CONFIG.headers,
      ...options.header
    };
    
    // 如果有token，添加到请求头
    if (token) {
      header['Authorization'] = `${token}`;
    }
    
    // 预处理请求数据，避免空字符串导致后端解析错误
    const processedData = options.data ? preprocessRequestData(options.data) : options.data;
    
    // 发起请求
    uni.request({
      url: url,
      data: processedData,
      method: options.method || 'GET',
      header: header,
      timeout: options.timeout || API_CONFIG.timeout,
      success: (res) => {
        // 请求成功，但需要判断业务状态码
        if (res.statusCode >= 200 && res.statusCode < 300) {
          // 业务状态码判断
          if (res.data && (res.data.code === 0 || res.data.code === 200 || res.data.success === true)) {
            resolve(res.data);
          } else {
            // 业务错误
            const errorMsg = (res.data && res.data.message) || '请求失败';
            console.error('API业务错误:', errorMsg, res.data);
            
            // 处理特定错误码
            if (res.data && res.data.code === 401) {
              // token失效，清除登录状态
              uni.removeStorageSync('token');
              uni.removeStorageSync('userInfo');
              
              // 获取当前页面路径
              const pages = getCurrentPages();
              const currentPage = pages[pages.length - 1];
              const currentPath = currentPage ? currentPage.route : '';
              
              // 避免重复提示和重复跳转
              const lastLoginTipTime = uni.getStorageSync('lastLoginTipTime');
              const now = Date.now();
              
              // 如果距离上次提示超过3秒，或者是首次提示
              if (!lastLoginTipTime || (now - lastLoginTipTime > 3000)) {
                // 记录本次提示时间
                uni.setStorageSync('lastLoginTipTime', now);
                
                // 显示登录提示
                uni.showToast({
                  title: '请先登录后再操作',
                  icon: 'none',
                  duration: 2000
                });
                
                // 如果当前不在首页或登录页，则跳转到首页
                if (currentPath && !currentPath.includes('index/index') && !currentPath.includes('login')) {
                  setTimeout(() => {
                    uni.reLaunch({
                      url: '/pages/index/index'
                    });
                  }, 1000);
                }
              }
            }
            
            reject({
              code: res.data.code,
              message: errorMsg,
              data: res.data
            });
          }
        } else {
          // HTTP错误
          console.error('HTTP错误:', res.statusCode, res);
          reject({
            code: res.statusCode,
            message: `HTTP错误: ${res.statusCode}`,
            data: res.data
          });
        }
      },
      fail: (err) => {
        // 请求失败
        console.error('请求失败:', err);
        
        // 网络错误处理
        let errorMsg = '网络请求失败，请检查网络连接';
        if (err.errMsg && err.errMsg.includes('timeout')) {
          errorMsg = '请求超时，请稍后重试';
        }
        
        reject({
          code: -1,
          message: errorMsg,
          error: err
        });
      }
    });
  });
}

/**
 * 文件上传方法
 * @param {Object} options 上传配置
 * @returns {Promise} Promise对象
 */
function uploadFile(options) {
  return new Promise((resolve, reject) => {
    // 获取存储的token
    const token = uni.getStorageSync('token');
    
    // 构建完整请求地址
    const url = options.url.startsWith('http') 
      ? options.url 
      : buildUrl(API_CONFIG.baseUrl, options.url);
    
    // 构建请求头
    const header = {
      ...options.header
    };
    
    // 如果有token，添加到请求头
    if (token) {
      header['Authorization'] = `${token}`;
    }
    
    // 处理表单数据中的空字符串
    const processedFormData = options.formData ? preprocessRequestData(options.formData) : options.formData;
    
    // 发起上传请求
    uni.uploadFile({
      url: url,
      filePath: options.filePath,
      name: options.name || 'file',
      formData: processedFormData || {},
      header: header,
      success: (res) => {
        // 上传成功，但需要判断业务状态码
        if (res.statusCode >= 200 && res.statusCode < 300) {
          // 解析返回的JSON字符串
          let data;
          try {
            data = JSON.parse(res.data);
          } catch (e) {
            data = res.data;
          }
          
          // 业务状态码判断
          if (data && (data.code === 0 || data.code === 200 || data.success === true)) {
            resolve(data);
          } else {
            // 业务错误
            const errorMsg = (data && data.message) || '上传失败';
            console.error('上传业务错误:', errorMsg, data);
            
            reject({
              code: data.code,
              message: errorMsg,
              data: data
            });
          }
        } else {
          // HTTP错误
          console.error('上传HTTP错误:', res.statusCode, res);
          reject({
            code: res.statusCode,
            message: `上传HTTP错误: ${res.statusCode}`,
            data: res.data
          });
        }
      },
      fail: (err) => {
        // 上传失败
        console.error('上传失败:', err);
        
        reject({
          code: -1,
          message: '文件上传失败，请重试',
          error: err
        });
      }
    });
  });
}

/**
 * 今夜城堡API服务类
 */
const API = {
  // 暴露baseUrl供其他模块使用
  baseUrl: API_CONFIG.baseUrl,

  /**
   * 用户认证相关API
   */
  user: {
    /**
     * 微信登录
     * @param {String} code 微信小程序登录code
     * @param {String} avatarUrl 用户头像URL(可选)
     * @param {String} nickname 用户昵称(可选)
     * @param {String} phoneCode 手机号凭证(可选)
     * @returns {Promise} Promise对象
     */
    login(code, avatarUrl, nickname, phoneCode) {
      return request({
        url: '/api/wx/miniapp/login',
        method: 'POST',
        data: { 
          code, 
          avatar: avatarUrl, 
          nickname,
          phoneCode
        }
      });
    },
    
    /**
     * 获取用户信息
     * @returns {Promise} Promise对象
     */
    getInfo() {
      return request({
        url: '/api/wx/miniapp/user/info',
        method: 'GET'
      });
    },

    /**
     * 获取用户历史订单
     * @param {Number} status 订单状态（0-全部，1-进行中，2-已完成，3-已取消）
     * @param {Number} page 页码
     * @param {Number} pageSize 每页数量
     * @returns {Promise} Promise对象
     */
    getOrders(status, page = 1, pageSize = 10) {
      return request({
        url: '/api/wx/miniapp/user/consumptions',
        method: 'GET',
        data: { page, size: pageSize }
      });
    },

    /**
     * 获取用户历史消费记录
     * @param {Number} page 页码
     * @param {Number} pageSize 每页数量
     * @returns {Promise} Promise对象
     */
    getConsumptions(page = 1, pageSize = 10) {
      return request({
        url: '/api/wx/miniapp/user/consumptions',
        method: 'GET',
        data: { page, size: pageSize }
      });
    }
  },
  
  /**
   * 设备连接与控制相关API
   */
  device: {
    /**
     * 获取设备订单信息
     * @param {String} mac 设备MAC地址
     * @returns {Promise} Promise对象
     */
    getOrderInfo(mac) {
      return request({
        url: `/api/wx/miniapp/device/status/${mac}`,
        method: 'GET'
      });
    },
    
    /**
     * 扫描设备二维码并自动创建订单
     * @param {String} deviceCode 设备二维码或MAC地址
     * @returns {Promise} Promise对象
     */
    scanAndPay(deviceCode) {
      return request({
        url: `/api/wx/miniapp/device/scan-and-pay?deviceCode=${encodeURIComponent(deviceCode)}`,
        method: 'GET'
      });
    },
    
    /**
     * 上传设备状态
     * @param {Object} data 设备状态数据
     * @returns {Promise} Promise对象
     */
    uploadStatus(data) {
      return request({
        url: '/api/wx/miniapp/device/status',
        method: 'POST',
        data
      });
    },
    
    /**
     * 校验设备绑定状态
     * @param {String} mac 设备MAC地址
     * @returns {Promise} Promise对象
     */
    checkBindStatus(mac) {
      return request({
        url: '/api/wx/miniapp/device/check',
        method: 'GET',
        data: { deviceCode: mac }
      });
    },
    
    /**
     * 获取设备使用价格
     * @param {String} deviceId 设备ID
     * @returns {Promise} Promise对象
     */
    getDevicePrice(deviceId) {
      return request({
        url: `/api/wx/miniapp/device/status/${deviceId}`,
        method: 'GET'
      });
    },
    
    /**
     * 校验设备价格
     * @param {String} deviceId 设备ID
     * @param {Number} price 价格
     * @returns {Promise} Promise对象
     */
    verifyDevicePrice(deviceId, price) {
      return request({
        url: `/api/wx/miniapp/device/price/verify`,
        method: 'POST',
        data: {
          deviceId,
          price
        }
      });
    },

    /**
     * 开锁使用设备
     * @param {Object} data 开锁数据，包含orderId、deviceId、deviceCode等
     * @returns {Promise} Promise对象
     */
    unlock(data) {
      return request({
        url: '/api/wx/miniapp/device/unlock',
        method: 'POST',
        data: data
      });
    },

    /**
     * 结束使用设备
     * @param {String} orderId 订单ID
     * @returns {Promise} Promise对象
     */
    endUse(orderId) {
      return request({
        url: `/api/wx/miniapp/device/end-use/${orderId}`,
        method: 'POST'
      });
    }
  },
  
  /**
   * 订单相关API
   */
  order: {
    /**
     * 获取订单列表
     * @param {Number} status 订单状态：0-全部 1-进行中 2-已完成
     * @param {Number} page 页码
     * @param {Number} pageSize 每页数量
     * @returns {Promise} Promise对象
     */
    getList(status = 0, page = 1, pageSize = 10) {
      // 构建请求参数
      const params = { 
        page, 
        size: pageSize 
      };
      
      // 只有当status不为0时才添加到参数中
      if (status !== 0) {
        params.status = status;
      }
      
      return request({
        url: '/api/wx/miniapp/order/list',
        method: 'GET',
        data: params
      });
    },
    
    /**
     * 获取订单总数
     * @returns {Promise} Promise对象
     */
    getOrderCount() {
      return request({
        url: '/api/wx/miniapp/order/count',
        method: 'GET'
      });
    },
    
    /**
     * 获取订单基本信息
     * @param {String} orderId 订单ID
     * @returns {Promise} Promise对象
     */
    getDetail(orderId) {
      return request({
        url: `/api/wx/miniapp/order/${orderId}`,
        method: 'GET'
      });
    },
    
    /**
     * 获取订单详细信息
     * @param {String} orderId 订单ID
     * @returns {Promise} Promise对象
     */
    getOrderDetail(orderId) {
      return request({
        url: `/api/wx/miniapp/order/${orderId}`,
        method: 'GET'
      });
    },
    
    /**
     * 创建新订单
     * @param {Object} orderData 订单数据，包含storeId、roomId、deviceMac
     * @returns {Promise} Promise对象
     */
    create(orderData) {
      return request({
        url: '/api/wx/miniapp/order/create',
        method: 'POST',
        data: orderData
      });
    },
    
    /**
     * 支付订单
     * @param {String} orderId 订单ID
     * @param {String} paymentMethod 支付方式，默认为wechat
     * @returns {Promise} Promise对象
     */
    payment(orderId, paymentMethod = 'wechat') {
      return request({
        url: `/api/wx/miniapp/order/pay/${orderId}`,
        method: 'POST',
        data: { paymentMethod }
      });
    },
    
    /**
     * 完成订单
     * @param {String} orderId 订单ID
     * @returns {Promise} 完成结果
     */
    finishOrder(orderId) {
      return request({
        url: `/api/wx/miniapp/order/finish/${orderId}`,
        method: 'POST'
      });
    },
    
    /**
     * 获取订单支付状态
     * @param {String} orderId 订单ID
     * @returns {Promise} 支付状态
     */
    getPaymentStatus(orderId) {
      return request({
        url: `/api/wx/miniapp/order/payment-status/${orderId}`,
        method: 'GET'
      });
    },
    
    /**
     * 关闭支付订单
     * @param {String} orderId 订单ID
     * @returns {Promise} 关闭结果
     */
    closePayment(orderId) {
      return request({
        url: `/api/wx/miniapp/pay/close/${orderId}`,
        method: 'POST'
      });
    },
    
    /**
     * 申请退款
     * @param {String} orderId 订单ID
     * @param {Number} refundAmount 退款金额
     * @param {String} refundReason 退款原因
     * @returns {Promise} 退款申请结果
     */
    refund(orderId, refundAmount, refundReason) {
      return request({
        url: `/api/wx/miniapp/pay/refund/${orderId}`,
        method: 'POST',
        data: {
          refundAmount,
          refundReason
        }
      });
    },
    
    /**
     * 查询订单详情
     * @param {String} orderId 订单ID
     * @returns {Promise} 订单详情
     */
    getOrderDetail(orderId) {
      return request({
        url: `/api/wx/miniapp/order/detail/${orderId}`,
        method: 'GET'
      });
    }
  },
  
  /**
   * 设备异常上报相关API
   */
  report: {
    /**
     * 提交设备异常报告
     * @param {Object} reportData 包含orderId、issueType、content、images等字段
     * @returns {Promise} Promise对象
     */
    submit(reportData) {
      return request({
        url: '/api/feedback/submit',
        method: 'POST',
        data: reportData
      });
    },
    
    /**
     * 获取异常报告状态
     * @param {String} reportId 报告ID
     * @returns {Promise} Promise对象
     */
    getStatus(reportId) {
      return request({
        url: `/api/feedback/${reportId}`,
        method: 'GET'
      });
    },
    
    /**
     * 上传异常报告图片
     * @param {String} filePath 本地图片路径
     * @returns {Promise} Promise对象
     */
    uploadImage(filePath) {
      return uploadFile({
        url: '/api/upload/image',
        filePath: filePath,
        name: 'file',
        formData: {
          type: 'feedback'
        }
      });
    }
  },
  
  /**
   * 客服与帮助相关API
   */
  help: {
    /**
     * 获取客服联系方式
     * @returns {Promise} Promise对象
     */
    getServiceContact() {
      return request({
        url: '/api/wx/miniapp/contact/service',
        method: 'GET'
      });
    },
    
    /**
     * 获取常见问题列表
     * @returns {Promise} Promise对象
     */
    getFAQ() {
      return request({
        url: '/api/wx/miniapp/help/faq',
        method: 'GET'
      });
    }
  },
  
  /**
   * 文件上传API
   */
  upload: {
    /**
     * 通用文件上传
     * @param {String} filePath 本地文件路径
     * @param {String} type 文件类型，如'image'、'report'等
     * @returns {Promise} Promise对象
     */
    file(filePath, type = 'image') {
      return uploadFile({
        url: '/api/wx/miniapp/upload/file',
        filePath: filePath,
        name: 'file',
        formData: {
          type: type
        }
      });
    }
  },
  
  /**
   * 招商合作API
   */
  partner: {
    /**
     * 提交招商合作信息
     * @param {Object} partnerData 招商合作信息，包含name和phone
     * @returns {Promise} Promise对象
     */
    submit(partnerData) {
      return request({
        url: '/api/wx/miniapp/partner/submit',
        method: 'POST',
        data: partnerData
      });
    },
    
    /**
     * 获取招商合作政策
     * @returns {Promise} Promise对象
     */
    getPolicy() {
      return request({
        url: '/api/wx/miniapp/partner/policy',
        method: 'GET'
      });
    }
  }
};

export default API; 