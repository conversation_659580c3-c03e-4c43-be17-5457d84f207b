{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/scan/payment-result.vue?445b", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/scan/payment-result.vue?7871", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/scan/payment-result.vue?b9fa", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/scan/payment-result.vue?2836", "uni-app:///pages/scan/payment-result.vue", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/scan/payment-result.vue?c8a1", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/scan/payment-result.vue?3ab7"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "success", "orderId", "mac", "errorMsg", "onLoad", "console", "methods", "goBack", "uni", "delta", "fail", "goHome", "url", "retryPayment"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,sBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA2H;AAC3H;AACkE;AACL;AACa;;;AAG1E;AACgM;AAChM,gBAAgB,uMAAU;AAC1B,EAAE,oFAAM;AACR,EAAE,yFAAM;AACR,EAAE,kGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,6FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA4vB,CAAgB,0vBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCiEhxB;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;;IAEA;IACA;IACA;IACA;IACA;EACA;EACAC;IACA;IACAC;MAAA;MACAC;QACAC;QACAC;UACA;QACA;MACA;IACA;IAEA;IACAC;MACAH;QACAI;MACA;IACA;IAEA;IACAC;MACA;QACA;QACAL;UACAI;QACA;MACA;QACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AClHA;AAAA;AAAA;AAAA;AAAykC,CAAgB,miCAAG,EAAC,C;;;;;;;;;;;ACA7lC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/scan/payment-result.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/scan/payment-result.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./payment-result.vue?vue&type=template&id=dd3b14a6&\"\nvar renderjs\nimport script from \"./payment-result.vue?vue&type=script&lang=js&\"\nexport * from \"./payment-result.vue?vue&type=script&lang=js&\"\nimport style0 from \"./payment-result.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/scan/payment-result.vue\"\nexport default component.exports", "export * from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./payment-result.vue?vue&type=template&id=dd3b14a6&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./payment-result.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./payment-result.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container page-payment-result\">\r\n\t\t<!-- 页面背景 -->\r\n\t\t<view class=\"page-background\">\r\n\t\t\t<!-- 背景图片 -->\r\n\t\t\t<image class=\"background-image\" src=\"https://wuji1112-1312674791.cos.ap-guangzhou.myqcloud.com/static/shouye3.png\" mode=\"aspectFill\"></image>\r\n\t\t\t\r\n\t\t\t<!-- 页面渐变背景 -->\r\n\t\t\t<view class=\"gradient-overlay\"></view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 顶部状态栏占位 -->\r\n\t\t<view class=\"status-bar safe-area-inset-top\"></view>\r\n\t\t\r\n\t\t<!-- 顶部导航栏 -->\r\n\t\t<view class=\"nav-bar\">\r\n\t\t\t<view class=\"nav-back\" @click=\"goBack\">\r\n\t\t\t\t<text class=\"material-icons\">arrow_back</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"nav-title\">支付结果</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 结果内容 -->\r\n\t\t<view class=\"result-content\">\r\n\t\t\t<!-- 结果图标 -->\r\n\t\t\t<view class=\"result-icon\" :class=\"{'success': success}\">\r\n\t\t\t\t<text class=\"material-icons\">{{ success ? 'check_circle' : 'error' }}</text>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 结果标题 -->\r\n\t\t\t<view class=\"result-title\">\r\n\t\t\t\t{{ success ? '支付成功' : '支付失败' }}\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 结果信息 -->\r\n\t\t\t<view class=\"result-info\">\r\n\t\t\t\t{{ errorMsg || (success ? '您的订单已支付成功' : '支付处理失败，请重试') }}\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 订单信息 -->\r\n\t\t\t<view class=\"order-info\" v-if=\"orderId\">\r\n\t\t\t\t<view class=\"info-item\">\r\n\t\t\t\t\t<text class=\"info-label\">订单号：</text>\r\n\t\t\t\t\t<text class=\"info-value\">{{ orderId }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"info-item\" v-if=\"mac\">\r\n\t\t\t\t\t<text class=\"info-label\">设备MAC：</text>\r\n\t\t\t\t\t<text class=\"info-value\">{{ mac }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 操作按钮 -->\r\n\t\t\t<view class=\"action-buttons\">\r\n\t\t\t\t<view class=\"primary-button\" @click=\"goHome\">\r\n\t\t\t\t\t<text>返回首页</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"primary-button retry-button\" @click=\"retryPayment\" v-if=\"!success\">\r\n\t\t\t\t\t<text>重新支付</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tsuccess: false,\r\n\t\t\torderId: '',\r\n\t\t\tmac: '',\r\n\t\t\terrorMsg: '',\r\n\t\t}\r\n\t},\r\n\tonLoad(options) {\r\n\t\tconsole.log('支付结果页面参数:', options);\r\n\t\t\r\n\t\t// 解析参数\r\n\t\tthis.success = options.success === 'true';\r\n\t\tthis.orderId = options.orderId || '';\r\n\t\tthis.mac = options.mac || '';\r\n\t\tthis.errorMsg = options.errorMsg ? decodeURIComponent(options.errorMsg) : '';\r\n\t},\r\n\tmethods: {\r\n\t\t// 返回上一页\r\n\t\tgoBack() {\r\n\t\t\tuni.navigateBack({\r\n\t\t\t\tdelta: 1,\r\n\t\t\t\tfail: () => {\r\n\t\t\t\t\tthis.goHome();\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 返回首页\r\n\t\tgoHome() {\r\n\t\t\tuni.switchTab({\r\n\t\t\t\turl: '/pages/index/index'\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 重新支付\r\n\t\tretryPayment() {\r\n\t\t\tif (this.mac) {\r\n\t\t\t\t// 跳转到设备页面重新支付\r\n\t\t\t\tuni.redirectTo({\r\n\t\t\t\t\turl: `/pages/scan/device?mac=${this.mac}&orderId=${this.orderId}`\r\n\t\t\t\t});\r\n\t\t\t} else {\r\n\t\t\t\t// 返回上一页\r\n\t\t\t\tthis.goBack();\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style>\r\n\t/* CSS变量定义 */\r\n\tpage {\r\n\t\t--primary-light: #A875FF;\r\n\t\t--neon-pink: #ff36f9;\r\n\t\t--success-color: #4CAF50;\r\n\t\t--error-color: #F44336;\r\n\t}\r\n\t\r\n\t/* 页面基础样式 */\r\n\t.page-payment-result {\r\n\t\tpadding-top: 180rpx;\r\n\t\tpadding-bottom: calc(170rpx + env(safe-area-inset-bottom));\r\n\t\tcolor: #ffffff;\r\n\t\theight: 100vh;\r\n\t\tmin-height: 100vh;\r\n\t\tbox-sizing: border-box;\r\n\t\tposition: relative;\r\n\t\toverflow: hidden;\r\n\t}\r\n\t\r\n\t/* 页面背景样式 */\r\n\t.page-background {\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tz-index: 0;\r\n\t}\r\n\t\r\n\t/* 背景图片样式 */\r\n\t.background-image {\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tz-index: 0;\r\n\t\tobject-fit: cover;\r\n\t}\r\n\t\r\n\t.gradient-overlay {\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tbackground: linear-gradient(to bottom, \r\n\t\t\trgba(18, 18, 18, 0.7) 0%, \r\n\t\t\trgba(18, 18, 18, 0.6) 50%,\r\n\t\t\trgba(18, 18, 18, 0.7) 100%);\r\n\t\tz-index: 1;\r\n\t}\r\n\t\r\n\t/* 顶部状态栏和导航栏 */\r\n\t.status-bar {\r\n\t\twidth: 100%;\r\n\t\tbackground: transparent;\r\n\t\tbackdrop-filter: none;\r\n\t\t-webkit-backdrop-filter: none;\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tz-index: 100;\r\n\t}\r\n\t\r\n\t/* 导航栏 */\r\n\t.nav-bar {\r\n\t\tposition: fixed;\r\n\t\ttop: calc(env(safe-area-inset-top) + 60rpx);\r\n\t\tleft: 15rpx;\r\n\t\tright: 15rpx;\r\n\t\theight: 110rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tbackground: transparent;\r\n\t\tbackdrop-filter: none;\r\n\t\t-webkit-backdrop-filter: none;\r\n\t\tz-index: 100;\r\n\t\tpadding: 0 30rpx;\r\n\t\tborder-bottom: none;\r\n\t\tbox-shadow: none;\r\n\t\tborder-radius: 0 0 30rpx 30rpx;\r\n\t}\r\n\t\r\n\t.nav-back {\r\n\t\tposition: absolute;\r\n\t\tleft: 30rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\twidth: 70rpx;\r\n\t\theight: 70rpx;\r\n\t\tborder-radius: 50%;\r\n\t\tbackground-color: rgba(168, 117, 255, 0.15);\r\n\t\tborder: 1px solid rgba(168, 117, 255, 0.3);\r\n\t}\r\n\t\r\n\t.nav-back .material-icons {\r\n\t\tfont-size: 44rpx;\r\n\t\tcolor: rgba(255, 255, 255, 0.9);\r\n\t\ttext-shadow: 0 0 8rpx rgba(168, 117, 255, 0.5);\r\n\t}\r\n\t\r\n\t.nav-title {\r\n\t\tfont-size: 38rpx;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #ffffff;\r\n\t\ttext-shadow: 0 0 10rpx rgba(168, 117, 255, 0.5);\r\n\t\tletter-spacing: 2rpx;\r\n\t}\r\n\t\r\n\t/* 结果内容区域 */\r\n\t.result-content {\r\n\t\tposition: relative;\r\n\t\tz-index: 2;\r\n\t\tpadding: 0 40rpx;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tmargin-top: 60rpx;\r\n\t}\r\n\t\r\n\t/* 结果图标 */\r\n\t.result-icon {\r\n\t\twidth: 160rpx;\r\n\t\theight: 160rpx;\r\n\t\tborder-radius: 50%;\r\n\t\tbackground-color: rgba(244, 67, 54, 0.15);\r\n\t\tborder: 1px solid rgba(244, 67, 54, 0.3);\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tmargin-bottom: 40rpx;\r\n\t\tbox-shadow: 0 0 30rpx rgba(244, 67, 54, 0.3);\r\n\t}\r\n\t\r\n\t.result-icon.success {\r\n\t\tbackground-color: rgba(76, 175, 80, 0.15);\r\n\t\tborder: 1px solid rgba(76, 175, 80, 0.3);\r\n\t\tbox-shadow: 0 0 30rpx rgba(76, 175, 80, 0.3);\r\n\t}\r\n\t\r\n\t.result-icon .material-icons {\r\n\t\tfont-size: 100rpx;\r\n\t\tcolor: var(--error-color);\r\n\t}\r\n\t\r\n\t.result-icon.success .material-icons {\r\n\t\tcolor: var(--success-color);\r\n\t}\r\n\t\r\n\t/* 结果标题 */\r\n\t.result-title {\r\n\t\tfont-size: 48rpx;\r\n\t\tfont-weight: 700;\r\n\t\tcolor: #ffffff;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\ttext-shadow: 0 0 10rpx rgba(168, 117, 255, 0.5);\r\n\t}\r\n\t\r\n\t/* 结果信息 */\r\n\t.result-info {\r\n\t\tfont-size: 32rpx;\r\n\t\tcolor: rgba(255, 255, 255, 0.8);\r\n\t\ttext-align: center;\r\n\t\tmargin-bottom: 40rpx;\r\n\t\tmax-width: 600rpx;\r\n\t\tline-height: 1.5;\r\n\t}\r\n\t\r\n\t/* 订单信息 */\r\n\t.order-info {\r\n\t\twidth: 100%;\r\n\t\tbackground-color: rgba(30, 30, 30, 0.5);\r\n\t\tborder-radius: 20rpx;\r\n\t\tpadding: 30rpx;\r\n\t\tmargin-bottom: 60rpx;\r\n\t\tborder: 1px solid rgba(168, 117, 255, 0.2);\r\n\t}\r\n\t\r\n\t.info-item {\r\n\t\tdisplay: flex;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\t\r\n\t.info-item:last-child {\r\n\t\tmargin-bottom: 0;\r\n\t}\r\n\t\r\n\t.info-label {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: rgba(255, 255, 255, 0.6);\r\n\t\twidth: 160rpx;\r\n\t}\r\n\t\r\n\t.info-value {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: rgba(255, 255, 255, 0.9);\r\n\t\tflex: 1;\r\n\t\tword-break: break-all;\r\n\t}\r\n\t\r\n\t/* 操作按钮 */\r\n\t.action-buttons {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\twidth: 100%;\r\n\t\tmargin-top: 20rpx;\r\n\t}\r\n\t\r\n\t.primary-button {\r\n\t\tflex: 1;\r\n\t\theight: 90rpx;\r\n\t\tbackground: linear-gradient(145deg, rgba(168, 117, 255, 0.7), rgba(168, 117, 255, 0.9));\r\n\t\tborder-radius: 45rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tbox-shadow: 0 8rpx 20rpx rgba(168, 117, 255, 0.3);\r\n\t\tmargin: 0 20rpx;\r\n\t}\r\n\t\r\n\t.primary-button text {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #ffffff;\r\n\t}\r\n\t\r\n\t.retry-button {\r\n\t\tbackground: linear-gradient(145deg, rgba(255, 152, 0, 0.7), rgba(255, 152, 0, 0.9));\r\n\t\tbox-shadow: 0 8rpx 20rpx rgba(255, 152, 0, 0.3);\r\n\t}\r\n\t\r\n\t/* Material Icons 字体 */\r\n\t@font-face {\r\n\t\tfont-family: 'Material Icons';\r\n\t\tfont-style: normal;\r\n\t\tfont-weight: 400;\r\n\t\tsrc: url(https://fonts.gstatic.com/s/materialicons/v139/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format('woff2');\r\n\t}\r\n\t\r\n\t.material-icons {\r\n\t\tfont-family: 'Material Icons';\r\n\t\tfont-weight: normal;\r\n\t\tfont-style: normal;\r\n\t\tfont-size: 48rpx;\r\n\t\tline-height: 1;\r\n\t\tletter-spacing: normal;\r\n\t\ttext-transform: none;\r\n\t\tdisplay: inline-block;\r\n\t\twhite-space: nowrap;\r\n\t\tword-wrap: normal;\r\n\t\tdirection: ltr;\r\n\t\t-webkit-font-smoothing: antialiased;\r\n\t\t-moz-osx-font-smoothing: grayscale;\r\n\t}\r\n</style> ", "import mod from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./payment-result.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./payment-result.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754165306717\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}