










































/* 引入通用样式和图标 */
/* Material Icons 字体 - 使用本地字符映射代替在线字体 */
.material-icons {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-smoothing: antialiased;
}
/* 图标尺寸 */
.material-icons.md-18 { font-size: 18px;
}
.material-icons.md-24 { font-size: 24px;
}
.material-icons.md-36 { font-size: 36px;
}
.material-icons.md-48 { font-size: 48px;
}
/* 图标颜色 */
.material-icons.md-dark { color: rgba(0, 0, 0, 0.54);
}
.material-icons.md-dark.md-inactive { color: rgba(0, 0, 0, 0.26);
}
.material-icons.md-light { color: rgba(255, 255, 255, 1);
}
.material-icons.md-light.md-inactive { color: rgba(255, 255, 255, 0.3);
}
/* 自定义颜色 */
.material-icons.primary { color: var(--primary, #8B5CF6);
}
.material-icons.primary-light { color: var(--primary-light, #A875FF);
}
.material-icons.neon-green { color: var(--neon-green, #39D353);
}
.material-icons.neon-pink { color: var(--neon-pink, #FF69B4);
}
.material-icons.neon-yellow { color: var(--neon-yellow, #FFD700);
}
.material-icons.neon-blue { color: var(--neon-blue, #00BFFF);
}
/*每个页面公共css */
page {
	background-color: #000000;
	color: #FFFFFF;
	font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
	height: 100%;
	overflow-x: hidden;
	box-sizing: border-box;
}
/* iOS安全区域适配 */
.safe-area-inset-bottom {
	padding-bottom: constant(safe-area-inset-bottom); /* iOS 11.0 */
	padding-bottom: env(safe-area-inset-bottom); /* iOS 11.2+ */
}
.safe-area-inset-top {
	height: 25px;
	padding-top: constant(safe-area-inset-top); /* iOS 11.0 */
	padding-top: env(safe-area-inset-top); /* iOS 11.2+ */
}
.container {
	width: 100%;
	min-height: 100vh;
	display: flex;
	flex-direction: column;
	box-sizing: border-box;
	position: relative;
	padding-bottom: calc(170rpx + env(safe-area-inset-bottom)); /* 修改底部内边距适配新的导航栏高度 */
}
/* 自定义颜色变量 */
:root {
	--primary: #8B5CF6;
	--primary-light: #A875FF;
	--neon-pink: #ff36f9;
	--text-primary: #FFFFFF;
	--text-secondary: rgba(255, 255, 255, 0.7);
	--text-tertiary: rgba(255, 255, 255, 0.5);
}
/* 通用文本颜色 */
.text-primary {
	color: #FFFFFF;
}
.text-secondary {
	color: rgba(255, 255, 255, 0.7);
}
.text-tertiary {
	color: rgba(255, 255, 255, 0.5);
}
/* 通用边距 */
.mt-xs {
	margin-top: 10rpx;
}
.mt-sm {
	margin-top: 20rpx;
}
.mt-md {
	margin-top: 30rpx;
}
.mt-lg {
	margin-top: 40rpx;
}
.mb-xs {
	margin-bottom: 10rpx;
}
.mb-sm {
	margin-bottom: 20rpx;
}
.mb-md {
	margin-bottom: 30rpx;
}
.mb-lg {
	margin-bottom: 40rpx;
}
/* 通用flex布局 */
.flex {
	display: flex;
}
.flex-col {
	display: flex;
	flex-direction: column;
}
.justify-between {
	justify-content: space-between;
}
.justify-center {
	justify-content: center;
}
.items-center {
	align-items: center;
}
.flex-1 {
	flex: 1;
}
.text-center {
	text-align: center;
}

