<template>
	<view class="container page-contact">
		<!-- 页面背景 -->
		<view class="page-background">
			<!-- 页面背景波浪装饰 -->
			<view class="wave-decoration">
				<view class="wave wave1"></view>
				<view class="wave wave2"></view>
				<view class="wave wave3"></view>
			</view>
			
			<!-- 页面渐变背景 -->
			<view class="gradient-overlay"></view>
		</view>
		
		<!-- 导航栏 -->
		<view class="navbar">
			<view class="navbar-left" @click="goBack">
				<text class="material-icons md-24 text-primary">arrow_back</text>
			</view>
			<view class="navbar-title">联系客服</view>
			<view class="navbar-right"></view>
		</view>
		
		<!-- 页面内容 -->
		<view class="content">
			<!-- 在线客服 -->
			<view class="section card-container">
				<view class="section-title">
					<view class="icon-wrapper neon-blue-bg">
						<text class="material-icons neon-blue">support_agent</text>
					</view>
					<text class="title-md">在线客服</text>
				</view>
				<view class="card contact-card">
					<view class="contact-item">
						<text class="text-secondary">工作时间</text>
						<text class="text-primary">{{ (contactInfo.onlineService && contactInfo.onlineService.workTime) || '每日 10:00 - 22:00' }}</text>
					</view>
					<view class="contact-item mt-md">
						<text class="text-secondary">平均响应时间</text>
						<text class="text-primary">{{ (contactInfo.onlineService && contactInfo.onlineService.responseTime) || '5分钟内' }}</text>
					</view>
					<button class="btn btn-primary mt-lg" @click="contactOnline">立即咨询</button>
				</view>
			</view>
			
			<!-- 电话客服 -->
			<view class="section card-container mt-lg">
				<view class="section-title">
					<view class="icon-wrapper neon-green-bg">
						<text class="material-icons neon-green">phone</text>
					</view>
					<text class="title-md">电话客服</text>
				</view>
				<view class="card contact-card">
					<view class="contact-item">
						<text class="text-secondary">客服热线</text>
						<text class="text-primary">{{ (contactInfo.phoneService && contactInfo.phoneService.phoneNumber) || '************' }}</text>
					</view>
					<view class="contact-item mt-md">
						<text class="text-secondary">工作时间</text>
						<text class="text-primary">{{ (contactInfo.phoneService && contactInfo.phoneService.workTime) || '每日 9:00 - 21:00' }}</text>
					</view>
					<button class="btn btn-outline mt-lg" @click="callPhone">拨打电话</button>
				</view>
			</view>
			
			<!-- 常见问题 -->
			<view class="section card-container mt-lg">
				<view class="section-title">
					<view class="icon-wrapper neon-yellow-bg">
						<text class="material-icons neon-yellow">help_outline</text>
					</view>
					<text class="title-md">常见问题</text>
				</view>
				
				<view class="faq-list">
					<view class="card faq-item" v-for="(item, index) in faqs" :key="index" @click="toggleFaq(index)">
						<view class="faq-header flex justify-between items-center">
							<text class="faq-title">{{item.question}}</text>
							<text class="material-icons md-24 text-tertiary">
								{{item.expanded ? 'keyboard_arrow_up' : 'keyboard_arrow_down'}}
							</text>
						</view>
						<view class="faq-content" v-if="item.expanded">
							<view class="divider mt-sm mb-sm"></view>
							<text class="text-secondary">{{item.answer}}</text>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 底部空白区域，确保内容不被遮挡 -->
			<view class="bottom-space"></view>
		</view>
		
		<!-- 加载状态 -->
		<view class="loading-container" v-if="loading">
			<view class="loading-spinner"></view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				loading: true,
				contactInfo: {
					onlineService: {
						workTime: '每日 10:00 - 22:00',
						responseTime: '5分钟内'
					},
					phoneService: {
						phoneNumber: '************',
						workTime: '每日 9:00 - 21:00'
					}
				},
				faqs: []
			}
		},
		onLoad() {
			this.getContactInfo();
			this.getFaqs();
		},
		methods: {
			goBack() {
				uni.navigateBack();
			},
			// 获取客服联系信息
			getContactInfo() {
				this.$api.help.getServiceContact()
					.then(res => {
						if (res.data) {
							this.contactInfo = res.data;
						}
					})
					.catch(err => {
						console.error('获取客服联系信息失败:', err);
						uni.showToast({
							title: '获取客服信息失败',
							icon: 'none'
						});
					})
					.finally(() => {
						this.loading = false;
					});
			},
			// 获取常见问题
			getFaqs() {
				this.$api.help.getFAQ()
					.then(res => {
						if (res.data && res.data.length) {
							this.faqs = res.data.map(item => ({
								...item,
								expanded: false
							}));
						} else {
							// 使用默认FAQ数据
							this.faqs = [
								{
									question: '如何预订今夜城堡的服务？',
									answer: '您可以通过我们的小程序直接预订，或者前往门店现场预订。建议提前预约以确保有空位。',
									expanded: false
								},
								{
									question: '如何取消或修改已预订的服务？',
									answer: '您可以在"我的订单"中找到相应订单，点击"取消预订"按钮。如需修改，请先取消原订单再重新预订，或联系客服协助处理。',
									expanded: false
								},
								{
									question: '忘记带充电器怎么办？',
									answer: '别担心，我们的每个房间都配备了各种型号的充电器，包括Type-C、Lightning和其他常见接口，可免费使用。',
									expanded: false
								},
								{
									question: '如何上报设备异常问题？',
									answer: '您可以在"我的"页面中找到"设备异常上报"功能，填写相关信息并提交。我们会尽快处理并联系您。',
									expanded: false
								},
								{
									question: '会员有哪些特权？',
									answer: '会员可享受折扣价格、优先预订、积分奖励、生日特惠等多项特权。详情可在"我的"-"会员权益"中查看。',
									expanded: false
								}
							];
						}
					})
					.catch(err => {
						console.error('获取常见问题失败:', err);
						// 使用默认FAQ数据
						this.faqs = [
							{
								question: '如何预订今夜城堡的服务？',
								answer: '您可以通过我们的小程序直接预订，或者前往门店现场预订。建议提前预约以确保有空位。',
								expanded: false
							},
							{
								question: '如何取消或修改已预订的服务？',
								answer: '您可以在"我的订单"中找到相应订单，点击"取消预订"按钮。如需修改，请先取消原订单再重新预订，或联系客服协助处理。',
								expanded: false
							},
							{
								question: '忘记带充电器怎么办？',
								answer: '别担心，我们的每个房间都配备了各种型号的充电器，包括Type-C、Lightning和其他常见接口，可免费使用。',
								expanded: false
							},
							{
								question: '如何上报设备异常问题？',
								answer: '您可以在"我的"页面中找到"设备异常上报"功能，填写相关信息并提交。我们会尽快处理并联系您。',
								expanded: false
							},
							{
								question: '会员有哪些特权？',
								answer: '会员可享受折扣价格、优先预订、积分奖励、生日特惠等多项特权。详情可在"我的"-"会员权益"中查看。',
								expanded: false
							}
						];
					});
			},
			contactOnline() {
				uni.showToast({
					title: '正在连接客服...',
					icon: 'none'
				});
				
				// 模拟连接客服
				setTimeout(() => {
					uni.showModal({
						title: '客服提示',
						content: '当前客服繁忙，预计等待时间3分钟，是否继续等待？',
						confirmText: '继续等待',
						cancelText: '稍后再试',
						success: function(res) {
							if (res.confirm) {
								uni.showToast({
									title: '已加入等待队列',
									icon: 'none'
								});
							}
						}
					});
				}, 1000);
			},
			callPhone() {
				const phoneNumber = (this.contactInfo.phoneService && this.contactInfo.phoneService.phoneNumber) || '4008889999';
				uni.makePhoneCall({
					phoneNumber: phoneNumber,
					success: () => {
						console.log('拨打电话成功');
					},
					fail: (err) => {
						console.error('拨打电话失败', err);
						uni.showToast({
							title: '拨打电话失败',
							icon: 'none'
						});
					}
				});
			},
			toggleFaq(index) {
				this.faqs.forEach((item, i) => {
					if (i === index) {
						item.expanded = !item.expanded;
					} else {
						item.expanded = false;
					}
				});
			}
		}
	}
</script>

<style>
	/* 页面基础样式 */
	.page-contact {
		color: #ffffff;
		height: 100vh;
		min-height: 100vh;
		box-sizing: border-box;
		position: relative;
		overflow: hidden;
		display: flex;
		flex-direction: column;
	}
	
	/* 页面背景样式 */
	.page-background {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		z-index: 0;
		background-color: #121212;
	}
	
	.gradient-overlay {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background: linear-gradient(to bottom, 
			rgba(18, 18, 18, 0) 0%, 
			rgba(18, 18, 18, 0.3) 20%, 
			rgba(18, 18, 18, 0.7) 50%,
			rgba(18, 18, 18, 0.9) 80%,
			#121212 100%);
		z-index: 1;
		pointer-events: none;
	}
	
	/* 波浪装饰 */
	.wave-decoration {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		overflow: hidden;
		z-index: 0;
	}
	
	.wave {
		position: absolute;
		width: 250%;
		height: 120%;
		top: -80%;
		left: -75%;
		border-radius: 43%;
		background: transparent;
	}
	
	.wave1 {
		background: linear-gradient(135deg, rgba(168, 117, 255, 0.3) 0%, rgba(139, 92, 246, 0.25) 100%);
		animation: wave 25s linear infinite;
		z-index: 3;
	}
	
	.wave2 {
		background: linear-gradient(135deg, rgba(105, 30, 255, 0.25) 0%, rgba(96, 56, 176, 0.2) 100%);
		animation: wave 22s linear infinite;
		animation-delay: -5s;
		z-index: 2;
	}
	
	.wave3 {
		background: linear-gradient(135deg, rgba(147, 51, 234, 0.25) 0%, rgba(79, 70, 229, 0.15) 100%);
		animation: wave 18s linear infinite;
		animation-delay: -8s;
		z-index: 1;
	}
	
	@keyframes wave {
		0% {
			transform: rotate(0deg);
		}
		100% {
			transform: rotate(360deg);
		}
	}
	
	/* 顶部导航样式 */
	.navbar {
		height: 90rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 30rpx;
		margin-top: var(--status-bar-height);
		position: relative;
		z-index: 100;
	}
	
	.navbar-title {
		font-size: 34rpx;
		font-weight: 600;
	}
	
	.navbar-left, .navbar-right {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	/* 内容区域 */
	.content {
		flex: 1;
		padding: 30rpx 30rpx;
		position: relative;
		z-index: 5;
		width: 100%;
		box-sizing: border-box;
		overflow: auto;
	}
	
	/* 卡片容器 */
	.card-container {
		margin-bottom: 40rpx;
		width: 100%;
	}
	
	.section-title {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
	}
	
	.icon-wrapper {
		width: 60rpx;
		height: 60rpx;
		border-radius: 50%;
		display: flex;
		justify-content: center;
		align-items: center;
		margin-right: 16rpx;
		transition: transform 0.3s ease;
	}
	
	.section-title:active .icon-wrapper {
		transform: rotate(15deg);
	}
	
	.neon-blue-bg {
		background-color: rgba(0, 233, 255, 0.15);
		box-shadow: 0 0 15rpx rgba(0, 233, 255, 0.3);
	}
	
	.neon-green-bg {
		background-color: rgba(0, 255, 133, 0.15);
		box-shadow: 0 0 15rpx rgba(0, 255, 133, 0.3);
	}
	
	.neon-yellow-bg {
		background-color: rgba(255, 222, 89, 0.15);
		box-shadow: 0 0 15rpx rgba(255, 222, 89, 0.3);
	}
	
	/* 卡片样式 */
	.card {
		border-radius: 20rpx;
		background-color: rgba(255, 255, 255, 0.08);
		backdrop-filter: blur(5px);
		-webkit-backdrop-filter: blur(5px);
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);
		border: 1px solid rgba(255, 255, 255, 0.05);
		transition: transform 0.3s ease, box-shadow 0.3s ease;
		width: 100%;
		box-sizing: border-box;
		margin-left: auto;
		margin-right: auto;
	}
	
	.contact-card {
		padding: 30rpx;
		background: linear-gradient(to bottom, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
	}
	
	.contact-item {
		display: flex;
		flex-direction: column;
	}
	
	.contact-item .text-secondary {
		margin-bottom: 8rpx;
	}
	
	.contact-item .text-primary {
		font-size: 32rpx;
		font-weight: 500;
	}
	
	/* FAQ列表 */
	.faq-list {
		display: flex;
		flex-direction: column;
		gap: 20rpx;
		width: 100%;
	}
	
	.faq-item {
		padding: 24rpx;
		width: 100%;
		position: relative;
		overflow: hidden;
	}
	
	.faq-item:after {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background: linear-gradient(to right, rgba(168, 117, 255, 0.05), transparent);
		opacity: 0;
		transition: opacity 0.3s ease;
		pointer-events: none;
	}
	
	.faq-item:active {
		transform: scale(0.99);
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.2);
	}
	
	.faq-item:active:after {
		opacity: 1;
	}
	
	.faq-header {
		width: 100%;
	}
	
	.faq-title {
		font-size: 30rpx;
		font-weight: 500;
		color: #FFFFFF;
	}
	
	.faq-content {
		margin-top: 10rpx;
		animation: fadeIn 0.3s ease;
	}
	
	@keyframes fadeIn {
		from { opacity: 0; transform: translateY(-10rpx); }
		to { opacity: 1; transform: translateY(0); }
	}
	
	.divider {
		height: 1px;
		background-color: rgba(255, 255, 255, 0.1);
	}
	
	/* 按钮样式 */
	.btn {
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 15rpx;
		padding: 25rpx;
		font-size: 30rpx;
		font-weight: 600;
		transition: all 0.3s ease;
	}
	
	.btn-primary {
		background: linear-gradient(135deg, rgba(168, 117, 255, 0.8), rgba(139, 92, 246, 0.9));
		color: #FFFFFF;
		box-shadow: 0 4rpx 20rpx rgba(168, 117, 255, 0.4);
		border: none;
	}
	
	.btn-primary:active {
		transform: scale(0.98);
		box-shadow: 0 2rpx 10rpx rgba(168, 117, 255, 0.3);
	}
	
	.btn-outline {
		background-color: transparent;
		border: 1px solid rgba(168, 117, 255, 0.4);
		color: var(--primary-light, #A875FF);
	}
	
	.btn-outline:active {
		background-color: rgba(168, 117, 255, 0.1);
		transform: scale(0.98);
	}
	
	/* 底部空间 */
	.bottom-space {
		height: 60rpx;
	}
	
	/* Material Icons 字体 */
	@font-face {
		font-family: 'Material Icons';
		font-style: normal;
		font-weight: 400;
		src: url(https://fonts.gstatic.com/s/materialicons/v139/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format('woff2');
	}

	.material-icons {
		font-family: 'Material Icons';
		font-weight: normal;
		font-style: normal;
		font-size: 24rpx;
		line-height: 1;
		letter-spacing: normal;
		text-transform: none;
		display: inline-block;
		white-space: nowrap;
		word-wrap: normal;
		direction: ltr;
		-webkit-font-smoothing: antialiased;
		-moz-osx-font-smoothing: grayscale;
	}
	
	.material-icons.md-24 {
		font-size: 48rpx;
	}
	
	.material-icons.md-48 {
		font-size: 96rpx;
	}
	
	.material-icons.text-primary {
		color: var(--text-primary, #FFFFFF);
	}
	
	.material-icons.text-tertiary {
		color: var(--text-tertiary, rgba(255, 255, 255, 0.5));
	}
	
	/* 彩色图标 */
	.material-icons.neon-blue {
		color: var(--neon-blue, #00E9FF);
		text-shadow: 0 0 10rpx rgba(0, 233, 255, 0.5);
		animation: glow-blue 3s infinite alternate;
	}
	
	.material-icons.neon-green {
		color: var(--neon-green, #00FF85);
		text-shadow: 0 0 10rpx rgba(0, 255, 133, 0.5);
		animation: glow-green 3s infinite alternate;
	}
	
	.material-icons.neon-yellow {
		color: var(--neon-yellow, #FFDE59);
		text-shadow: 0 0 10rpx rgba(255, 222, 89, 0.5);
		animation: glow-yellow 3s infinite alternate;
	}
	
	@keyframes glow-blue {
		from { text-shadow: 0 0 5rpx rgba(0, 233, 255, 0.3); }
		to { text-shadow: 0 0 15rpx rgba(0, 233, 255, 0.6); }
	}
	
	@keyframes glow-green {
		from { text-shadow: 0 0 5rpx rgba(0, 255, 133, 0.3); }
		to { text-shadow: 0 0 15rpx rgba(0, 255, 133, 0.6); }
	}
	
	@keyframes glow-yellow {
		from { text-shadow: 0 0 5rpx rgba(255, 222, 89, 0.3); }
		to { text-shadow: 0 0 15rpx rgba(255, 222, 89, 0.6); }
	}
	
	/* 辅助类 */
	.flex {
		display: flex;
	}
	
	.justify-between {
		justify-content: space-between;
	}
	
	.items-center {
		align-items: center;
	}
	
	.text-center {
		text-align: center;
	}
	
	.mt-sm {
		margin-top: 16rpx;
	}
	
	.mb-sm {
		margin-bottom: 16rpx;
	}
	
	.mt-md {
		margin-top: 30rpx;
	}
	
	.mt-lg {
		margin-top: 60rpx;
	}
	
	/* 文本样式 */
	.title-md {
		font-size: 36rpx;
		font-weight: 600;
		color: #ffffff;
		text-shadow: 0 0 10rpx rgba(255, 255, 255, 0.2);
	}
	
	.text-secondary {
		color: rgba(255, 255, 255, 0.7);
		font-size: 28rpx;
	}
	
	.text-tertiary {
		color: rgba(255, 255, 255, 0.5);
		font-size: 26rpx;
	}
	
	.text-primary {
		color: #FFFFFF;
		font-size: 28rpx;
	}
</style> 