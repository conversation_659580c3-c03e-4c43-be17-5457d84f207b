<view class="{{['tab-bar','data-v-47b4a9d0',(hasBottomSafeArea)?'safe-area-inset-bottom':'',(isAndroid)?'android-tab-bar':'']}}" style="{{(tabBarStyle)}}"><view class="tab-bar-border data-v-47b4a9d0"></view><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['switchTab',['$0'],[[['list','',index,'pagePath']]]]]]]}}" class="{{['tab-bar-item','data-v-47b4a9d0',(item.m0)?'active':'',(isAndroid)?'android-tab-item':'']}}" bindtap="__e"><text class="{{['material-icons','data-v-47b4a9d0',(isAndroid)?'android-icon':'']}}">{{item.$orig.icon}}</text><text class="{{['tab-text','data-v-47b4a9d0',(isAndroid)?'android-text':'']}}">{{item.$orig.text}}</text></view></block></view>