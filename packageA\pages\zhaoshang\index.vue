<template>
	<view class="container page-zhaoshang" :class="{'ios-device': isIOS}">
		<!-- 页面背景 -->
		<view class="page-background">
			<!-- 背景图片 -->
			<image class="background-image" src="https://wuji1112-1312674791.cos.ap-guangzhou.myqcloud.com/static/编组 <EMAIL>" mode="aspectFill"></image>
			
			<!-- 页面渐变背景 - 保留磨砂效果 -->
			<view class="gradient-overlay"></view>
		</view>
		
		<!-- 顶部状态栏占位 -->
		<view class="status-bar" :style="{'background-color': 'rgba(84, 51, 224, 0.8)'}"></view>
		
		<!-- 内容区域 -->
		<scroll-view class="content-wrapper" scroll-y="true" :style="{'padding-top': isIOS ? '0' : 'calc(env(safe-area-inset-top))'}">
			<!-- 顶部图片，从页面顶部开始 -->
			<image class="top-image" src="https://wuji1112-1312674791.cos.ap-guangzhou.myqcloud.com/static/zhaoshang/<EMAIL>" mode="widthFix"></image>
			
			<!-- 表单区域 -->
			<view class="form-container">
				<view class="form-title">填写信息，领取限时优惠政策!</view>
				
				<view class="form-item">
					<view class="label-container">
						<text class="form-label">联系人</text>
						<text class="required">*</text>
					</view>
					<input class="form-input" type="text" v-model="formData.name" placeholder="请输入您的姓名" placeholder-class="placeholder-style" />
				</view>
				
				<view class="form-item">
					<view class="label-container">
						<text class="form-label">联系方式</text>
						<text class="required">*</text>
					</view>
					<input class="form-input" type="number" v-model="formData.phone" maxlength="11" placeholder="请输入您的手机号" placeholder-class="placeholder-style" />
				</view>
				
				<view class="submit-button" @click="submitForm">
					<text class="submit-text">立即提交</text>
				</view>
			</view>
			
			<!-- 底部图片 -->
			<image class="bottom-image" src="https://wuji1112-1312674791.cos.ap-guangzhou.myqcloud.com/static/zhaoshang/<EMAIL>" mode="widthFix"></image>
		</scroll-view>
		
		<!-- 返回按钮 - 悬浮在左上角，与安全区域保持距离 -->
		<view class="floating-back" @click="goBack" :style="{'top': isIOS ? '90rpx' : 'calc(env(safe-area-inset-top) + 20rpx)'}">
			<text class="material-icons">arrow_back</text>
		</view>
	</view>
</template>

<script>
	import API from '../../../static/js/api.js';
	
	export default {
		data() {
			return {
				formData: {
					name: '',
					phone: ''
				},
				isIOS: false
			}
		},
		onLoad() {
			// 检测平台
			uni.getSystemInfo({
				success: (res) => {
					this.isIOS = res.platform.toLowerCase() === 'ios';
					
					// 设置状态栏样式，改为金色系
					uni.setNavigationBarColor({
						frontColor: '#ffffff',
						backgroundColor: '#e09a1b'
					});
				}
			});
		},
		methods: {
			// 返回上一页
			goBack() {
				uni.navigateBack({
					delta: 1
				});
			},
			
			// 提交表单
			submitForm() {
				// 表单验证
				if (!this.formData.name.trim()) {
					uni.showToast({
						title: '请输入联系人姓名',
						icon: 'none'
					});
					return;
				}
				
				if (!this.formData.phone || !/^1[3-9]\d{9}$/.test(this.formData.phone)) {
					uni.showToast({
						title: '请输入正确的手机号',
						icon: 'none'
					});
					return;
				}
				
				// 显示加载中
				uni.showLoading({
					title: '提交中...'
				});
				
				// 调用API提交表单数据
				API.partner.submit(this.formData)
					.then(res => {
						uni.hideLoading();
						
						// 显示提交成功
						uni.showToast({
							title: '提交成功',
							icon: 'success',
							duration: 2000,
							success: () => {
								// 清空表单
								this.formData.name = '';
								this.formData.phone = '';
							}
						});
					})
					.catch(err => {
						uni.hideLoading();
						
						// 显示错误信息
						uni.showToast({
							title: err.message || '提交失败，请稍后重试',
							icon: 'none',
							duration: 2000
						});
					});
			}
		}
	}
</script>

<style>
	/* CSS变量定义 */
	page {
		--primary-light: #A875FF;
		--neon-pink: #ff36f9;
		--form-purple: #5433e0;
		--border-color: rgba(165, 166, 185, 0.51);
		--text-color: #333333;
		--placeholder-color: rgba(153, 153, 153, 0.6);
	}
	
	/* 页面基础样式 */
	.page-zhaoshang {
		width: 100%;
		height: 100vh; /* 使用固定高度 */
		box-sizing: border-box;
		position: relative;
		margin: 0;
		padding: 0;
	}
	
	/* 页面背景样式 */
	.page-background {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		z-index: 0;
	}
	
	/* 背景图片样式 */
	.background-image {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		z-index: 0;
		object-fit: cover;
	}
	
	.gradient-overlay {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background: linear-gradient(to bottom, 
			rgba(224, 168, 51, 0.7) 0%, 
			rgba(230, 149, 15, 0.7) 50%,
			rgba(240, 155, 25, 0.7) 100%);
		backdrop-filter: blur(5px);
		-webkit-backdrop-filter: blur(5px);
		z-index: 1;
		pointer-events: none;
	}
	
	/* 顶部状态栏占位 */
	.status-bar {
		width: 100%;
		height: var(--status-bar-height);
		position: fixed;
		top: 0;
		z-index: 100;
		background-color: rgba(224, 168, 51, 0.8); /* 金色系，与页面风格一致 */
	}
	
	.ios-device .status-bar {
		height: 44px; /* 增加iOS设备的状态栏高度，为灵动岛留出空间 */
	}
	
	/* 内容包装器 */
	.content-wrapper {
		position: relative;
		z-index: 2;
		width: 100%;
		height: 100vh; /* 设置高度 */
		box-sizing: border-box;
	}
	
	/* 顶部图片样式 */
	.top-image {
		width: 100%;
		display: block;
		flex-shrink: 0;
		margin-top: -10rpx; /* 添加负边距使图片上移 */
		/* 移除色相旋转滤镜，使用原始颜色 */
	}
	
	/* iOS设备特定样式 */
	.ios-device .top-image {
		margin-top: 60rpx; /* 将负边距改为正边距，使图片向下移动避免被灵动岛挡住 */
		margin-bottom: 0;
	}
	
	/* 表单容器 */
	.form-container {
		width: 100%;
		padding: 30rpx 40rpx;
		background: rgba(255, 255, 255, 1); /* 纯白色背景 */
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
		box-sizing: border-box;
		margin-bottom: 0;
	}
	
	/* 表单标题 */
	.form-title {
		font-size: 40rpx;
		color: #5433e0; /* 从金色系改回紫色系 */
		font-weight: bold;
		text-align: center;
		margin: 30rpx 0 40rpx;
	}
	
	/* 表单项容器 */
	.form-item {
		margin-bottom: 40rpx;
		width: 100%;
	}
	
	/* 标签容器 */
	.label-container {
		display: flex;
		flex-direction: row;
		align-items: center;
		margin-bottom: 10rpx;
	}
	
	/* 必填星号 */
	.required {
		color: #ba4d67;
		font-size: 21rpx;
		margin-left: 5rpx;
	}
	
	.form-label {
		font-size: 24rpx;
		font-weight: normal;
		color: #444950;
	}
	
	.form-input {
		width: 100%;
		height: 78rpx;
		background: #ffffff;
		border: 1px solid rgba(165, 166, 185, 0.51);
		border-radius: 10rpx;
		padding: 0 30rpx;
		font-size: 28rpx;
		color: #333333;
		box-sizing: border-box;
	}
	
	/* iOS设备特定样式 - 输入框 */
	.ios-device .form-input {
		height: 70rpx; /* iOS设备上更小的高度 */
	}
	
	.placeholder-style {
		color: rgba(153, 153, 153, 0.6);
	}
	
	/* 提交按钮 */
	.submit-button {
		width: 100%;
		height: 80rpx;
		background: linear-gradient(135deg, #6a4dff, #9b4dff); /* 从金色系改回紫色渐变 */
		border-radius: 40rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-top: 60rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 8rpx 16rpx rgba(155, 77, 255, 0.3); /* 调整阴影颜色为紫色系 */
	}
	
	.submit-text {
		font-size: 32rpx;
		color: #ffffff;
		font-weight: 500;
	}
	
	/* 底部图片样式 */
	.bottom-image {
		width: 100%;
		display: block;
	}
	
	/* 返回按钮 */
	.floating-back {
		position: fixed;
		left: 20rpx;
		width: 70rpx;
		height: 70rpx;
		border-radius: 50%;
		background-color: rgba(255, 197, 84, 0.15); /* 调整为金色系 */
		border: 1px solid rgba(255, 197, 84, 0.3); /* 调整为金色系 */
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 10;
	}
	
	.floating-back .material-icons {
		font-size: 48rpx;
		color: #ffffff;
	}
	
	/* Material Icons 字体 */
	@font-face {
		font-family: 'Material Icons';
		font-style: normal;
		font-weight: 400;
		src: url(https://fonts.gstatic.com/s/materialicons/v139/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format('woff2');
	}

	.material-icons {
		font-family: 'Material Icons';
		font-weight: normal;
		font-style: normal;
		font-size: 48rpx;
		line-height: 1;
		letter-spacing: normal;
		text-transform: none;
		display: inline-block;
		white-space: nowrap;
		word-wrap: normal;
		direction: ltr;
		-webkit-font-smoothing: antialiased;
		-moz-osx-font-smoothing: grayscale;
	}
</style> 