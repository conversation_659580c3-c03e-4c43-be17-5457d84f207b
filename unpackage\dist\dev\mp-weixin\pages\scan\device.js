(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pages/scan/device"],{

/***/ 101:
/*!***************************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/main.js?{"page":"pages%2Fscan%2Fdevice"} ***!
  \***************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _device = _interopRequireDefault(__webpack_require__(/*! ./pages/scan/device.vue */ 102));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_device.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 102:
/*!********************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/scan/device.vue ***!
  \********************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _device_vue_vue_type_template_id_2c2b0e3f___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./device.vue?vue&type=template&id=2c2b0e3f& */ 103);
/* harmony import */ var _device_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./device.vue?vue&type=script&lang=js& */ 105);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _device_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _device_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _device_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./device.vue?vue&type=style&index=0&lang=css& */ 107);
/* harmony import */ var _HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 36);

var renderjs





/* normalize component */

var component = Object(_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _device_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _device_vue_vue_type_template_id_2c2b0e3f___WEBPACK_IMPORTED_MODULE_0__["render"],
  _device_vue_vue_type_template_id_2c2b0e3f___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  null,
  null,
  false,
  _device_vue_vue_type_template_id_2c2b0e3f___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pages/scan/device.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 103:
/*!***************************************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/scan/device.vue?vue&type=template&id=2c2b0e3f& ***!
  \***************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_device_vue_vue_type_template_id_2c2b0e3f___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./device.vue?vue&type=template&id=2c2b0e3f& */ 104);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_device_vue_vue_type_template_id_2c2b0e3f___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_device_vue_vue_type_template_id_2c2b0e3f___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_device_vue_vue_type_template_id_2c2b0e3f___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_device_vue_vue_type_template_id_2c2b0e3f___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 104:
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/scan/device.vue?vue&type=template&id=2c2b0e3f& ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  if (!_vm._isMounted) {
    _vm.e0 = function ($event) {
      _vm.showLoginModal = true
    }
    _vm.e1 = function ($event) {
      _vm.showPaymentModal = true
      _vm.closeOpenDoorModal()
    }
  }
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 105:
/*!*********************************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/scan/device.vue?vue&type=script&lang=js& ***!
  \*********************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_device_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./device.vue?vue&type=script&lang=js& */ 106);
/* harmony import */ var _HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_device_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_device_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_device_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_device_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_device_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 106:
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/scan/device.vue?vue&type=script&lang=js& ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni, global, wx) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ 11));
var _slicedToArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/slicedToArray */ 5));
var _typeof2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/typeof */ 13));
var _api = _interopRequireDefault(__webpack_require__(/*! @/static/js/api.js */ 32));
var _index = __webpack_require__(/*! @/utils/index.js */ 39);
var _request = __webpack_require__(/*! @/utils/request.js */ 42);
var _methods;
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { (0, _defineProperty2.default)(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
var LoginModal = function LoginModal() {
  Promise.all(/*! require.ensure | components/login-modal/index */[__webpack_require__.e("common/vendor"), __webpack_require__.e("components/login-modal/index")]).then((function () {
    return resolve(__webpack_require__(/*! @/components/login-modal/index.vue */ 137));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
// 获取API基础URL
var getAPIBaseUrl = function getAPIBaseUrl() {
  if (_api.default && _api.default.baseUrl) {
    return _api.default.baseUrl;
  }
  // 默认的API基础URL
  return 'https://api.jycb888.com/';
};

// 确保API对象可用
console.log('API导入检查:', _api.default);
var _default = {
  components: {
    LoginModal: LoginModal
  },
  data: function data() {
    return {
      // 设备信息
      deviceInfo: {},
      deviceId: '',
      deviceMac: '',
      deviceName: '',
      deviceStatus: 0,
      // 0-未知 1-空闲 2-使用中

      // 扫码类型 bluetooth-蓝牙扫码 miniapp-小程序扫码
      scanType: 'miniapp',
      // 订单信息
      orderId: '',
      orderStatus: -1,
      // -1-未知 0-未支付 1-已支付 2-已完成 3-已取消
      useStatus: 0,
      // 0-未使用 1-使用中 2-已完成
      orderAmount: 0.00,
      orderDuration: 0,
      roomId: '',
      // 锁状态
      isLockOpen: false,
      batteryLevel: 0,
      // 价格信息
      hourlyRate: 0.00,
      priceText: '￥0.00/小时',
      // 蓝牙连接状态
      isConnected: false,
      isConnecting: false,
      lastConnectTime: 0,
      connectionDebounceTimer: null,
      autoReconnect: true,
      // 是否自动重连
      statusUpdateTimer: null,
      // 状态更新防抖定时器
      bluetoothInitialized: false,
      // 蓝牙是否已初始化
      isRecentlyDisconnected: false,
      // 是否刚刚断开连接
      isManualDisconnect: false,
      // 是否手动断开连接
      isReconnecting: false,
      // 是否正在重连中
      reconnectLock: false,
      // 重连锁，防止重复重连

      // 开门防抖控制
      lastOpenTime: 0,
      // 上次开门时间
      openCooldown: 3000,
      // 开门冷却时间（3秒）
      cooldownTimer: null,
      // 冷却倒计时定时器
      currentTime: 0,
      // 当前时间，用于触发computed更新

      // 锁状态检查
      lockStatusCheckTimer: null,
      // 锁状态检查定时器
      lockStatusCheckInterval: 5000,
      // 检查间隔（5秒，避免过于频繁）
      lastLockStatusCheckTime: 0,
      // 上次检查时间
      lockStatusCheckEnabled: true,
      // 是否启用锁状态检查

      // 支付状态
      isPaid: false,
      isPaying: false,
      paymentLoading: false,
      paymentSuccess: false,
      showPaymentModal: false,
      // 开门状态
      doorOpenInProgress: false,
      doorOpenProcessed: false,
      doorOpenCompleted: false,
      // 新增：标记是否已完成开门操作
      showOpenDoorModal: false,
      // 页面状态
      isPageActive: true,
      isPageLoaded: false,
      autoConnectTimer: null,
      // 登录状态
      isLoggedIn: false,
      showLoginModal: false,
      userInfo: null,
      // 定时器
      statusTimer: null,
      // 模拟模式
      isSimulatedMode: false,
      // 添加UI状态定时更新机制
      uiUpdateTimer: null,
      // 添加音频相关变量
      successAudio: null,
      audioSrc: null,
      // 添加订单状态轮询相关变量
      orderPollingTimer: null,
      orderPollingCount: 0,
      maxPollingCount: 30,
      // 最大轮询次数，防止无限轮询
      pollingInterval: 2000,
      // 轮询间隔，单位毫秒

      // 导航状态
      isNavigating: false,
      // 防止重复导航
      lastNavigateTime: null,
      // 最后导航时间（防抖）

      // Banner触摸状态（iOS兼容性）
      bannerTouchStarted: false,
      bannerTouchStartTime: null
    };
  },
  computed: {
    // 连接状态文本
    connectionStatusText: function connectionStatusText() {
      if (this.isConnected) {
        return '蓝牙已连接';
      } else if (this.isConnecting) {
        return '蓝牙连接中...';
      } else {
        return '未连接';
      }
    },
    // 主操作按钮文本
    mainActionText: function mainActionText() {
      // 如果用户未登录，显示登录
      if (!this.isLoggedIn) {
        return '登录';
      }
      if (this.isConnected) {
        if (this.isPaid) {
          // 检查是否在冷却期间（使用currentTime触发响应式更新）
          var now = this.currentTime || Date.now();
          var timeSinceLastOpen = now - this.lastOpenTime;
          if (timeSinceLastOpen < this.openCooldown && this.lastOpenTime > 0) {
            var remainingTime = Math.ceil((this.openCooldown - timeSinceLastOpen) / 1000);
            return "\u7B49\u5F85".concat(remainingTime, "\u79D2");
          }

          // 已支付状态，根据锁状态和开门完成状态显示不同文本
          if (this.doorOpenCompleted && this.isLockOpen) {
            return '已开门';
          } else if (this.doorOpenCompleted && !this.isLockOpen) {
            return '重新开门';
          } else {
            return '开门';
          }
        } else {
          return '支付';
        }
      } else if (this.isConnecting) {
        return '连接中';
      } else {
        // 未连接状态，如果已获取设备信息，显示支付
        return this.deviceId ? '支付' : '连接设备';
      }
    },
    // 主操作按钮图标
    mainActionIcon: function mainActionIcon() {
      // 如果用户未登录，显示登录图标
      if (!this.isLoggedIn) {
        return 'account_circle';
      }
      if (this.isConnected) {
        if (this.isPaid) {
          return 'lock_open';
        } else {
          return 'payments';
        }
      } else if (this.isConnecting) {
        return 'bluetooth_searching';
      } else {
        // 未连接状态，如果已获取设备信息，显示支付图标
        return this.deviceId ? 'payments' : 'bluetooth_connected';
      }
    },
    // 主操作按钮类名
    mainActionClass: function mainActionClass() {
      if (this.isConnected) {
        if (this.isPaid) {
          return 'action-open';
        } else {
          return 'action-pay';
        }
      } else if (this.isConnecting) {
        return 'action-connecting';
      } else {
        // 未连接状态，如果已获取设备信息，使用支付按钮样式
        return this.deviceId ? 'action-pay' : 'action-connect';
      }
    },
    // 订单状态文本
    orderStatusText: function orderStatusText() {
      switch (this.orderStatus) {
        case 0:
          return '待支付';
        case 1:
          if (this.useStatus === 0) {
            return '待使用';
          } else if (this.useStatus === 1) {
            return '使用中';
          } else {
            return '已完成';
          }
        case 2:
          return '已完成';
        case 3:
          return '已取消';
        default:
          return '';
      }
    }
  },
  onLoad: function onLoad(options) {
    var _this = this;
    // 检查登录状态
    this.checkLoginStatus();

    // 特殊处理：如果是微信扫一扫，可能需要从URL中直接解析
    console.log('页面加载 - 原始参数 options:', options);
    console.log('参数类型检查:', {
      'typeof options': (0, _typeof2.default)(options),
      'options.query类型': (0, _typeof2.default)(options.query),
      'options.id类型': (0, _typeof2.default)(options.id),
      'JSON.stringify(options)': JSON.stringify(options)
    });

    // 兼容微信扫一扫 query 参数
    var params = options;
    if (options.query) {
      console.log('检测到 query 参数:', options.query, '类型:', (0, _typeof2.default)(options.query));
      try {
        if (typeof options.query === 'string') {
          // 解析 query 字符串，例如: "id=020022&scanType=miniapp"
          var queryParams = {};
          var queryString = decodeURIComponent(options.query);
          console.log('解码后的 query 字符串:', queryString);
          queryString.split('&').forEach(function (param) {
            var _param$split = param.split('='),
              _param$split2 = (0, _slicedToArray2.default)(_param$split, 2),
              key = _param$split2[0],
              value = _param$split2[1];
            if (key && value) {
              queryParams[key] = value;
            }
          });
          console.log('解析后的 query 参数:', queryParams);
          params = _objectSpread(_objectSpread({}, options), queryParams);
        } else {
          params = _objectSpread(_objectSpread({}, options), options.query);
        }
      } catch (e) {
        console.error('解析 query 参数失败:', e);
        params = _objectSpread({}, options);
      }
    }
    console.log('设备页面加载，最终参数:', params);

    // 修复scanType参数处理逻辑
    this.scanType = params.scanType || 'miniapp'; // 默认miniapp
    this.orderId = params.orderId || '';
    this.roomId = params.roomId || '';

    // 如果有id参数但没有scanType，说明是小程序扫码
    if (params.id && !params.scanType) {
      this.scanType = 'miniapp';
    }

    // 强制确保scanType为miniapp（针对微信扫一扫）
    if (params.id && params.id.length <= 10 && /^\d+$/.test(params.id)) {
      // 如果id是纯数字且长度较短，很可能是设备ID，强制设为miniapp模式
      this.scanType = 'miniapp';
      console.log('检测到数字设备ID，强制设置为miniapp模式:', params.id);
    }
    console.log('参数处理结果:', {
      scanType: this.scanType,
      orderId: this.orderId,
      roomId: this.roomId,
      'params.scanType': params.scanType,
      '强制设置前': params.scanType || 'miniapp',
      '最终scanType': this.scanType,
      allParams: params
    });
    console.log('确定的扫码类型:', this.scanType);

    // 早期检查：确保有设备标识
    var hasDeviceId = params.id || params.deviceId || params.deviceCode || params.mac || options.id || options.deviceId;
    console.log('早期设备标识检查:', {
      'params.id': params.id,
      'params.deviceId': params.deviceId,
      'params.deviceCode': params.deviceCode,
      'params.mac': params.mac,
      'options.id': options.id,
      'options.deviceId': options.deviceId,
      'hasDeviceId': hasDeviceId
    });
    if (!hasDeviceId) {
      console.error('所有可能的设备标识都为空');
      uni.showModal({
        title: '参数错误',
        content: '无法获取设备标识，请检查二维码是否正确',
        showCancel: false,
        confirmText: '返回',
        success: function success() {
          uni.navigateBack();
        }
      });
      return;
    }

    // 如果有订单ID，设置到lockService并检查支付状态
    if (this.orderId) {
      _index.lockService.setOrderId(this.orderId);
      console.log('设置订单ID到lockService:', this.orderId);
      this.getOrderStatus().then(function (orderInfo) {
        console.log('页面加载时获取订单状态成功:', orderInfo);

        // 更新订单状态
        _this.orderStatus = orderInfo.status;

        // 检查支付状态
        var isPaid = orderInfo.status === 1 || orderInfo.payStatus === 1;
        if (isPaid) {
          _index.lockService.setOrderId(_this.orderId);
          console.log('订单已支付，重新确认订单ID设置:', _this.orderId);

          // 更新支付状态
          _this.isPaid = true;
          _this.paymentSuccess = true;
          console.log('页面加载时确认订单已支付，更新支付状态');
        } else {
          // 订单未支付
          _this.isPaid = false;
          _this.paymentSuccess = false;
          console.log('页面加载时确认订单未支付');
        }

        // 更新UI状态
        _this.updateUIState();
      }).catch(function (err) {
        console.error('页面加载时获取订单状态失败:', err);
      });
    }
    console.log('开始处理设备标识 - 当前scanType:', this.scanType);
    console.log('可用的设备标识参数:', {
      'params.id': params.id,
      'params.deviceId': params.deviceId,
      'params.deviceCode': params.deviceCode,
      'params.mac': params.mac,
      'options.id': options.id,
      'options.deviceId': options.deviceId,
      'options.query': options.query
    });
    if (this.scanType === 'miniapp') {
      // 尝试多种方式获取设备ID
      var deviceId = params.id || params.deviceId || params.deviceCode || '';

      // 如果还是没有获取到，尝试从原始options中获取
      if (!deviceId && options) {
        deviceId = options.id || options.deviceId || options.deviceCode || '';
      }

      // 如果还是没有，尝试从URL字符串中直接解析
      if (!deviceId && options.query && typeof options.query === 'string') {
        try {
          // 直接从query字符串中查找id参数
          var match = options.query.match(/id=([^&]*)/);
          if (match && match[1]) {
            deviceId = decodeURIComponent(match[1]);
            console.log('从URL字符串中解析到设备ID:', deviceId);
          }
        } catch (e) {
          console.log('从URL字符串解析设备ID失败:', e);
        }
      }
      console.log('小程序扫码 - 从参数获取设备ID:', deviceId);
      console.log('参数详情:', {
        'params.id': params.id,
        'params.deviceId': params.deviceId,
        'params.deviceCode': params.deviceCode,
        'options.id': options.id,
        '最终deviceId': deviceId,
        'deviceId类型': (0, _typeof2.default)(deviceId),
        'deviceId长度': deviceId ? deviceId.length : 0
      });
      if (deviceId && deviceId.trim()) {
        // 设置设备ID
        this.deviceId = deviceId.trim();
        console.log('设置设备ID成功:', this.deviceId);
        this.getDeviceInfo(this.deviceId);
      } else {
        console.error('设备ID为空或无效:', {
          deviceId: deviceId,
          params: params,
          options: options,
          scanType: this.scanType,
          originalQuery: options.query
        });

        // 最后尝试：如果是从URL https://jycb888.com/pages/scan/device?id=020022&scanType=miniapp 进入
        // 可能设备ID就是020022这样的格式
        if (!deviceId && options.query) {
          // 尝试直接使用query中的数字作为设备ID
          var numberMatch = options.query.match(/\d+/);
          if (numberMatch && numberMatch[0]) {
            deviceId = numberMatch[0];
            console.log('从query中提取到数字作为设备ID:', deviceId);
            this.deviceId = deviceId;
            this.getDeviceInfo(deviceId);
            return;
          }
        }

        // 最终备用方案：如果是从特定URL进入，尝试使用默认设备ID
        if (options.query && options.query.includes('020022')) {
          console.log('检测到URL中包含020022，使用作为设备ID');
          this.deviceId = '020022';
          this.getDeviceInfo('020022');
          return;
        }

        // 给用户一个手动输入的机会
        uni.showModal({
          title: '设备ID获取失败',
          content: "\u65E0\u6CD5\u4ECE\u626B\u7801\u4E2D\u83B7\u53D6\u8BBE\u5907ID\uFF0C\u8BF7\u68C0\u67E5\u4E8C\u7EF4\u7801\u662F\u5426\u6B63\u786E\u3002\n\n\u8C03\u8BD5\u4FE1\u606F\uFF1A\nquery: ".concat(options.query || '无', "\nparams: ").concat(JSON.stringify(params)),
          showCancel: true,
          cancelText: '返回',
          confirmText: '重试',
          success: function success(res) {
            if (res.confirm) {
              // 重新尝试获取参数
              _this.onLoad(options);
            } else {
              uni.navigateBack();
            }
          }
        });
      }
    } else {
      var mac = params.mac || params.id || ''; // 兼容处理，有时id参数可能是MAC地址
      console.log('蓝牙扫码模式 - 从参数获取MAC地址:', mac);
      console.log('当前scanType:', this.scanType, '原始params.scanType:', params.scanType);
      if (mac) {
        // 检查是否是有效的MAC地址格式
        if (this.isValidMacAddress(mac)) {
          console.log('检测到有效的MAC地址格式:', mac);
          _index.lockService.setDeviceMac(mac);
          this.deviceMac = mac.toLowerCase();
          this.preinitBluetooth();
          this.getDeviceInfo(mac);
        } else {
          // 如果不是有效的MAC地址，可能是设备ID，转换为小程序模式
          console.log('检测到非MAC地址格式，转换为小程序模式:', mac);
          this.scanType = 'miniapp';
          this.deviceId = mac;
          this.getDeviceInfo(mac);
        }
      } else {
        console.error('设备标识为空 - 调试信息:', {
          'params.mac': params.mac,
          'params.id': params.id,
          'params': params,
          'options': options,
          'scanType': this.scanType
        });

        // 最后尝试：检查是否有任何可用的设备标识
        var anyId = params.deviceId || params.deviceCode || options.id || options.deviceId;
        if (anyId) {
          console.log('找到备用设备标识:', anyId);
          this.scanType = 'miniapp';
          this.deviceId = anyId;
          this.getDeviceInfo(anyId);
        } else {
          // 最终备用方案：如果是从特定URL进入，尝试使用默认设备ID
          if (options.query && options.query.includes('020022')) {
            console.log('在else分支检测到URL中包含020022，使用作为设备ID');
            this.scanType = 'miniapp';
            this.deviceId = '020022';
            this.getDeviceInfo('020022');
            return;
          }
          this.showError('设备标识不能为空');
          setTimeout(function () {
            uni.navigateBack();
          }, 2000);
        }
      }
    }
  },
  onShow: function onShow() {
    var _this2 = this;
    console.log('设备页面显示');
    this.isPageActive = true;

    // 尝试从本地存储恢复支付状态
    this.restorePaymentState();

    // 重新初始化蓝牙环境（重要：解决退出后重新进入连接不上的问题）
    this.reinitializeBluetooth();

    // 延迟检查连接状态，确保蓝牙初始化完成
    setTimeout(function () {
      _this2.verifyConnectionState();
    }, 2000);

    // 启动UI状态定时更新
    this.startUIUpdateTimer();

    // 设置蓝牙状态监听
    this.setupBluetoothListeners();

    // 如果有订单ID，总是检查最新的订单状态（重要：确保重新进入页面时能识别已支付状态）
    if (this.orderId) {
      console.log('页面显示时检查订单状态，当前订单ID:', this.orderId, '当前支付状态:', this.isPaid);
      this.getOrderStatus().then(function (orderInfo) {
        console.log('页面显示时获取订单状态成功:', orderInfo);

        // 更新订单状态
        _this2.orderStatus = orderInfo.status;

        // 如果订单已支付，确保订单ID设置到lockService
        if (orderInfo.status === 1 || orderInfo.payStatus === 1) {
          console.log('订单已支付，确保订单ID设置到lockService:', _this2.orderId);
          _index.lockService.setOrderId(_this2.orderId);
          _this2.isPaid = true;
          _this2.paymentSuccess = true;

          // 保存支付状态到本地存储
          _this2.savePaymentState();

          // 停止可能存在的轮询
          if (_this2.orderPollingTimer) {
            console.log('订单已支付，停止轮询');
            _this2.stopOrderPolling();
          }

          // 如果已支付但未连接设备，尝试连接
          if (!_this2.isConnected && !_this2.isConnecting) {
            console.log('已支付但未连接设备，尝试连接');
            _this2.tryConnectDevice();
          }
          // 如果已支付且已连接设备，检查是否需要开门
          else if (_this2.isConnected && !_this2.doorOpenProcessed && !_this2.doorOpenCompleted) {
            console.log('已支付且已连接设备，检查是否需要开门');
            _this2.checkOrderAndOpenDoor();
          }
        }
        // 如果订单未支付，并且没有在轮询，启动轮询
        else if ((orderInfo.status === 0 || orderInfo.status === null) && !_this2.orderPollingTimer) {
          console.log('订单未支付，启动订单状态轮询');
          _this2.isPaid = false;
          _this2.paymentSuccess = false;
          _this2.startOrderPolling();
        }

        // 更新UI状态
        _this2.updateUIState();
      }).catch(function (err) {
        console.error('页面显示时获取订单状态失败:', err);
      });
      return; // 防止执行后续连接逻辑
    }

    // 如果有订单ID，未支付，并且没有在轮询，启动轮询
    if (this.orderId && this.orderStatus === 0 && !this.isPaid && !this.orderPollingTimer) {
      console.log('页面显示时检测到未支付订单，启动订单状态轮询');
      this.startOrderPolling();
    }

    // 如果已支付但未连接设备，尝试连接
    if (this.isPaid && !this.isConnected && !this.isConnecting) {
      console.log('已支付但未连接设备，尝试连接');
      this.tryConnectDevice();
      return;
    }

    // 如果有MAC地址，尝试连接设备
    if (this.deviceMac && !this.isConnected && !this.isConnecting) {
      // 延迟连接，避免页面刚显示就连接导致的问题
      console.log('页面显示，准备尝试连接设备');
      this.autoConnectTimer = setTimeout(function () {
        _this2.debounceConnect();
      }, 500);
    }
  },
  onHide: function onHide() {
    console.log('设备页面隐藏');
    this.isPageActive = false;

    // 停止UI状态定时更新
    this.clearUIUpdateTimer();

    // 如果正在支付或已支付但未连接设备，不清除状态
    if (this.isPaying || this.paymentLoading || this.isPaid && !this.isConnected) {
      console.log('正在支付或已支付但未连接设备，保留状态');
      // 不清除自动连接定时器
      return;
    }

    // 如果正在轮询订单状态，不清除轮询定时器
    if (this.orderPollingTimer) {
      console.log('正在轮询订单状态，保留轮询定时器');
      return;
    }

    // 清除自动连接定时器
    if (this.autoConnectTimer) {
      clearTimeout(this.autoConnectTimer);
      this.autoConnectTimer = null;
    }

    // 注意：不在onHide时断开蓝牙连接，因为用户可能只是切换到其他页面
    // 蓝牙连接保持，以便用户返回时能快速重连
    console.log('页面隐藏，保持蓝牙连接状态');
  },
  onUnload: function onUnload() {
    console.log('设备页面卸载');
    this.isPageActive = false;

    // 停止UI状态定时更新
    this.clearUIUpdateTimer();

    // 清除冷却定时器
    if (this.cooldownTimer) {
      clearInterval(this.cooldownTimer);
      this.cooldownTimer = null;
    }

    // 停止锁状态检查
    this.stopLockStatusCheck();

    // 重置冷却相关状态
    this.currentTime = 0;
    this.lastOpenTime = 0;

    // 停止订单状态轮询
    this.stopOrderPolling();

    // 断开蓝牙连接
    this.ensureDisconnectBluetooth();
  },
  methods: (_methods = {
    // 检查登录状态
    checkLoginStatus: function checkLoginStatus() {
      var token = uni.getStorageSync('token');
      var userInfo = uni.getStorageSync('userInfo');
      console.log('检查登录状态 - token:', token ? '存在' : '不存在', 'userInfo:', userInfo);
      if (token && userInfo) {
        this.isLoggedIn = true;
        this.userInfo = userInfo;
        console.log('用户已登录');
      } else {
        this.isLoggedIn = false;
        this.userInfo = null;
        console.log('用户未登录');

        // 如果用户未登录，显示登录提示
        this.showLoginPrompt();
      }
    },
    // 显示登录提示
    showLoginPrompt: function showLoginPrompt() {
      var _this3 = this;
      // 延迟显示，确保页面加载完成
      setTimeout(function () {
        if (!_this3.isLoggedIn) {
          _this3.showLoginModal = true;
        }
      }, 1000);
    },
    // 关闭登录模态框
    handleCloseLoginModal: function handleCloseLoginModal() {
      this.showLoginModal = false;
    },
    // 登录成功处理
    handleLoginSuccess: function handleLoginSuccess(loginData) {
      console.log('登录成功:', loginData);
      this.isLoggedIn = true;
      this.userInfo = loginData.userInfo || {
        userId: loginData.userId
      };
      this.showLoginModal = false;
      uni.showToast({
        title: '登录成功',
        icon: 'success',
        duration: 2000
      });

      // 登录成功后，重新检查订单状态
      if (this.orderId) {
        this.getOrderStatus();
      }
    },
    // 登录失败处理
    handleLoginFail: function handleLoginFail(error) {
      console.error('登录失败:', error);
      this.showLoginModal = false;
      uni.showToast({
        title: '登录失败，请重试',
        icon: 'none',
        duration: 2000
      });
    },
    // Banner点击处理（简化版本）
    handleBannerClick: function handleBannerClick(e) {
      console.log('Banner点击事件触发', e.type);

      // 防止事件冒泡
      if (e && e.stopPropagation) {
        e.stopPropagation();
      }

      // 立即执行导航，不依赖复杂的触摸逻辑
      this.navigateToZhaoshang(e);
    },
    // Banner触摸开始事件（iOS兼容性）
    handleBannerTouch: function handleBannerTouch(e) {
      console.log('Banner触摸开始');
      this.bannerTouchStartTime = Date.now();
      this.bannerTouchStarted = true;

      // 防止事件冒泡
      if (e && e.stopPropagation) {
        e.stopPropagation();
      }
      if (e && e.preventDefault) {
        e.preventDefault();
      }
    },
    // Banner触摸结束事件（iOS兼容性）
    handleBannerTouchEnd: function handleBannerTouchEnd(e) {
      var _this4 = this;
      console.log('Banner触摸结束');

      // 防止事件冒泡
      if (e && e.stopPropagation) {
        e.stopPropagation();
      }
      if (e && e.preventDefault) {
        e.preventDefault();
      }

      // 检查是否是有效的点击（触摸时间不超过500ms）
      if (this.bannerTouchStarted && this.bannerTouchStartTime) {
        var touchDuration = Date.now() - this.bannerTouchStartTime;
        if (touchDuration < 500) {
          console.log('检测到有效点击，触摸时长:', touchDuration + 'ms');
          // 延迟一点执行，确保事件处理完成
          setTimeout(function () {
            _this4.navigateToZhaoshang(e);
          }, 50);
        }
      }

      // 重置状态
      this.bannerTouchStarted = false;
      this.bannerTouchStartTime = null;
    },
    // Banner触摸取消事件
    handleBannerTouchCancel: function handleBannerTouchCancel(e) {
      console.log('Banner触摸取消');
      this.bannerTouchStarted = false;
      this.bannerTouchStartTime = null;
    },
    // 跳转到招商页面
    navigateToZhaoshang: function navigateToZhaoshang(e) {
      var _this5 = this;
      console.log('跳转到招商页面', e);

      // 防止事件冒泡
      if (e && e.stopPropagation) {
        e.stopPropagation();
      }
      if (e && e.preventDefault) {
        e.preventDefault();
      }

      // 防止重复导航（更强的防抖机制）
      if (this.isNavigating) {
        console.log('正在导航中，忽略重复点击');
        return;
      }

      // 检查最近是否有导航操作（防抖）
      var now = Date.now();
      if (this.lastNavigateTime && now - this.lastNavigateTime < 1000) {
        console.log('导航操作过于频繁，忽略');
        return;
      }
      this.isNavigating = true;
      this.lastNavigateTime = now;
      console.log('开始执行导航到招商页面');

      // 立即执行导航，减少延迟
      uni.navigateTo({
        url: '/packageA/pages/zhaoshang/index',
        success: function success() {
          console.log('导航到招商页面成功');
          // 给用户反馈
          uni.showToast({
            title: '正在跳转...',
            icon: 'none',
            duration: 1000
          });
        },
        fail: function fail(err) {
          console.error('导航到招商页面失败:', err);
          _this5.isNavigating = false;

          // 如果导航失败，尝试使用switchTab
          if (err.errMsg && err.errMsg.includes('routeDone')) {
            console.log('尝试使用switchTab导航');
            uni.switchTab({
              url: '/pages/index/index',
              complete: function complete() {
                setTimeout(function () {
                  uni.navigateTo({
                    url: '/packageA/pages/zhaoshang/index'
                  });
                }, 100);
              }
            });
          } else {
            uni.showToast({
              title: '页面跳转失败',
              icon: 'none'
            });
          }
        },
        complete: function complete() {
          // 减少重置延迟
          setTimeout(function () {
            _this5.isNavigating = false;
          }, 500);
        }
      });
    },
    // 启动冷却倒计时定时器
    startCooldownTimer: function startCooldownTimer() {
      var _this6 = this;
      // 清除之前的定时器
      if (this.cooldownTimer) {
        clearInterval(this.cooldownTimer);
      }

      // 启动新的定时器，每秒更新一次
      this.cooldownTimer = setInterval(function () {
        // 更新当前时间，触发computed属性重新计算
        _this6.currentTime = Date.now();
        var timeSinceLastOpen = _this6.currentTime - _this6.lastOpenTime;

        // 如果冷却时间已过，清除定时器
        if (timeSinceLastOpen >= _this6.openCooldown) {
          clearInterval(_this6.cooldownTimer);
          _this6.cooldownTimer = null;
          _this6.currentTime = 0; // 重置currentTime
        }
      }, 1000);
    },
    // 启动锁状态检查定时器
    startLockStatusCheck: function startLockStatusCheck() {
      var _this7 = this;
      console.log('启动锁状态检查定时器，检查间隔:', this.lockStatusCheckInterval + 'ms');

      // 清除之前的定时器
      if (this.lockStatusCheckTimer) {
        clearInterval(this.lockStatusCheckTimer);
      }

      // 启动定时器，每5秒检查一次锁状态
      this.lockStatusCheckTimer = setInterval(function () {
        // 只有在已连接、已支付且启用检查的情况下才检查锁状态
        if (_this7.isConnected && _this7.isPaid && _this7.lockStatusCheckEnabled) {
          // 如果锁当前是开着的，才进行状态检查（检测关闭）
          if (_this7.isLockOpen) {
            _this7.checkLockStatus();
          }
        }
      }, this.lockStatusCheckInterval);
    },
    // 停止锁状态检查定时器
    stopLockStatusCheck: function stopLockStatusCheck() {
      console.log('停止锁状态检查定时器');
      if (this.lockStatusCheckTimer) {
        clearInterval(this.lockStatusCheckTimer);
        this.lockStatusCheckTimer = null;
      }
    },
    // 检查锁状态
    checkLockStatus: function checkLockStatus() {
      try {
        console.log('执行锁状态检查...');

        // 查询锁状态
        _index.lockService.queryLockStatus().then(function (res) {
          console.log('锁状态检查命令发送成功:', res);
          // 注意：实际的锁状态会通过 onLockStatusChange 回调返回
        }).catch(function (err) {
          console.log('锁状态检查失败:', err);
          // 检查失败不影响主要功能，只记录日志
          // 不进行重试，避免过度查询
        });
      } catch (e) {
        console.log('锁状态检查异常:', e);
      }
    },
    // 验证MAC地址格式
    isValidMacAddress: function isValidMacAddress(mac) {
      if (!mac) return false;
      // MAC地址格式：XX:XX:XX:XX:XX:XX 或 XXXXXXXXXXXX
      var macRegex = /^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$|^[0-9A-Fa-f]{12}$/;
      return macRegex.test(mac);
    },
    // 处理连接成功后的状态
    handleConnectedState: function handleConnectedState() {
      console.log('处理连接成功状态');

      // 验证lockService的连接状态
      var deviceInfo = _index.lockService.getDeviceInfo();
      var lockServiceConnected = deviceInfo && (deviceInfo.connected === true || deviceInfo.isConnected === true);
      console.log('验证连接状态 - UI状态:', this.isConnected, 'lockService状态:', lockServiceConnected);
      if (!lockServiceConnected) {
        console.warn('lockService连接状态不一致，尝试强制同步状态');
        // 强制同步连接状态
        this.forceSyncConnectionState();
        return;
      }

      // 如果已支付且已连接，自动开门
      if (this.isPaid && !this.doorOpenProcessed && !this.doorOpenCompleted) {
        console.log('连接成功且已支付，自动触发开门');
        this.checkOrderAndOpenDoor();
      }
    },
    // 强制同步连接状态
    forceSyncConnectionState: function forceSyncConnectionState() {
      var _this8 = this;
      console.log('强制同步连接状态');

      // 尝试重新设置设备信息到lockService
      if (this.deviceMac) {
        _index.lockService.setDeviceMac(this.deviceMac);
      }
      if (this.deviceName) {
        _index.lockService.setExpectedDeviceName(this.deviceName);
      }
      if (this.orderId) {
        _index.lockService.setOrderId(this.orderId);
      }

      // 延迟重新检查状态
      setTimeout(function () {
        var deviceInfo = _index.lockService.getDeviceInfo();
        var lockServiceConnected = deviceInfo && (deviceInfo.connected === true || deviceInfo.isConnected === true);
        console.log('状态同步后检查 - lockService状态:', lockServiceConnected);
        if (lockServiceConnected && _this8.isPaid && !_this8.doorOpenProcessed && !_this8.doorOpenCompleted) {
          console.log('状态同步成功，触发自动开门');
          _this8.checkOrderAndOpenDoor();
        }
      }, 1000);
    },
    // 强制同步设备信息
    forceSyncDeviceInfo: function forceSyncDeviceInfo(deviceInfo) {
      console.log('强制同步设备信息到lockService:', deviceInfo);
      try {
        // 确保所有必要的设备信息都设置到lockService
        if (deviceInfo) {
          if (deviceInfo.mac || this.deviceMac) {
            _index.lockService.setDeviceMac(deviceInfo.mac || this.deviceMac);
          }
          if (deviceInfo.name || this.deviceName) {
            _index.lockService.setExpectedDeviceName(deviceInfo.name || this.deviceName);
          }
          if (deviceInfo.deviceId) {
            // 如果lockService有设置设备ID的方法，调用它
            if (typeof _index.lockService.setDeviceId === 'function') {
              _index.lockService.setDeviceId(deviceInfo.deviceId);
            }
          }
        }
        if (this.orderId) {
          _index.lockService.setOrderId(this.orderId);
        }

        // 强制设置所有层级的连接状态
        this.forceSetAllLayersConnected(deviceInfo);
        console.log('设备信息同步完成');
      } catch (e) {
        console.error('强制同步设备信息失败:', e);
      }
    },
    // 强制设置所有层级的连接状态
    forceSetAllLayersConnected: function forceSetAllLayersConnected(deviceInfo) {
      console.log('强制设置所有层级连接状态');
      try {
        // 1. 设置lockService的连接状态
        if (_index.lockService.deviceInfo) {
          _index.lockService.deviceInfo.connected = true;
          _index.lockService.deviceInfo.isConnected = true;
          _index.lockService.deviceInfo.isAuthenticated = true;
          if (deviceInfo) {
            // 同步设备信息
            if (deviceInfo.deviceId) {
              _index.lockService.deviceInfo.deviceId = deviceInfo.deviceId;
            }
            if (deviceInfo.mac || this.deviceMac) {
              _index.lockService.deviceInfo.mac = deviceInfo.mac || this.deviceMac;
            }
            if (deviceInfo.name || this.deviceName) {
              _index.lockService.deviceInfo.name = deviceInfo.name || this.deviceName;
            }
            if (deviceInfo.batteryLevel) {
              _index.lockService.deviceInfo.batteryLevel = deviceInfo.batteryLevel;
            }
          }
          console.log('lockService内部连接状态已强制设置为true');
          console.log('lockService.deviceInfo:', _index.lockService.deviceInfo);
        }

        // 2. 尝试获取并设置blueToothManager的连接状态
        try {
          // 直接导入blueToothManager
          var blueToothManager = __webpack_require__(/*! @/utils/blueToothManager.js */ 40);
          if (blueToothManager) {
            console.log('找到blueToothManager，强制设置连接状态');

            // 强制设置连接状态
            blueToothManager.isConnected = true;
            blueToothManager.deviceId = this.deviceMac;
            console.log('blueToothManager.isConnected已设置为true');
            console.log('blueToothManager.deviceId已设置为:', this.deviceMac);

            // 如果有设备MAC，设置当前连接的设备
            if (this.deviceMac) {
              blueToothManager.currentDeviceId = this.deviceMac;
              blueToothManager.connectedDeviceId = this.deviceMac;
              console.log('blueToothManager当前设备ID已设置:', this.deviceMac);
            }

            // 设置服务和特征值ID（如果存在的话）
            if (blueToothManager.serviceId && blueToothManager.writeCharacteristicId) {
              console.log('blueToothManager服务和特征值ID已存在');
            } else {
              console.log('blueToothManager服务和特征值ID不存在，可能需要重新获取');
            }
          } else {
            console.log('未找到blueToothManager实例，跳过blueToothManager状态设置');
          }
        } catch (btError) {
          console.error('设置blueToothManager状态失败:', btError);
        }
        console.log('所有层级连接状态强制设置完成');
      } catch (e) {
        console.error('强制设置连接状态失败:', e);
      }
    },
    // 设置lockService回调
    setupLockServiceCallbacks: function setupLockServiceCallbacks() {
      var _this9 = this;
      // 设置回调函数
      _index.lockService.setCallbacks({
        // 连接成功回调
        onConnected: function onConnected(deviceInfo) {
          console.log('lockService连接成功回调:', deviceInfo);

          // 强制更新连接状态，确保状态同步
          _this9.isConnected = true;
          _this9.isConnecting = false;

          // 强制同步设备信息到lockService，确保内部状态一致
          _this9.forceSyncDeviceInfo(deviceInfo);

          // 停止任何正在进行的扫描
          try {
            uni.stopBluetoothDevicesDiscovery();
          } catch (e) {
            console.log('停止蓝牙扫描失败:', e);
          }

          // 更新设备信息
          if (deviceInfo && deviceInfo.batteryLevel) {
            _this9.batteryLevel = deviceInfo.batteryLevel;
          }

          // 使用防抖的状态更新方法
          _this9.debouncedUpdateUIState();

          // 尝试查询设备状态
          _this9.queryDeviceStatus();

          // 验证连接状态并处理自动开门
          setTimeout(function () {
            _this9.handleConnectedState();
          }, 500);

          // 启动锁状态检查
          _this9.startLockStatusCheck();
        },
        // 断开连接回调
        onDisconnected: function onDisconnected(info) {
          console.log('lockService断开连接回调:', info);

          // 防止重复设置状态
          if (_this9.isConnected) {
            _this9.isConnected = false;
            _this9.isConnecting = false;

            // 停止锁状态检查
            _this9.stopLockStatusCheck();

            // 设置断开连接标志，防止立即触发自动开门
            _this9.isRecentlyDisconnected = true;
            setTimeout(function () {
              _this9.isRecentlyDisconnected = false;
            }, 5000); // 5秒内不触发自动开门

            // 使用防抖的状态更新方法
            _this9.debouncedUpdateUIState();

            // 如果页面活跃且需要自动重连，尝试重新连接
            if (_this9.isPageActive && (_this9.deviceMac || _this9.deviceName) && _this9.autoReconnect && !_this9.isManualDisconnect) {
              console.log('设备断开连接，尝试重新连接');

              // 延迟重连，给蓝牙适配器一些时间恢复
              setTimeout(function () {
                // 重新初始化蓝牙环境后再连接
                _this9.reinitializeBluetooth();
              }, 3000); // 延长重连时间
            }

            // 重置手动断开标志
            _this9.isManualDisconnect = false;
          }
        },
        // 锁状态变化回调
        onLockStatusChange: function onLockStatusChange(status) {
          console.log('lockService锁状态变化回调:', status);
          _this9.isLockOpen = status.isOpen;

          // 如果锁打开了，设置开门完成标志
          if (status.isOpen) {
            _this9.doorOpenCompleted = true;
            _this9.doorOpenProcessed = true;

            // 播放成功音效
            _this9.playSuccessSound();
          } else {
            // 锁关闭时，重置开门完成状态，允许再次开门
            console.log('锁已关闭，重置开门状态，允许再次开门');
            _this9.doorOpenCompleted = false;
            // 注意：不重置 doorOpenProcessed，避免重复自动开门
          }

          // 更新UI状态
          _this9.updateUIState();
        },
        // 电池电量更新回调
        onBatteryUpdate: function onBatteryUpdate(info) {
          console.log('lockService电池电量更新回调:', info);
          _this9.batteryLevel = info.batteryLevel;

          // 更新UI状态
          _this9.updateUIState();
        },
        // 错误回调
        onError: function onError(error) {
          console.error('lockService错误回调:', error);

          // 显示错误提示
          uni.showToast({
            title: '设备操作出错',
            icon: 'none',
            duration: 2000
          });
        }
      });
    },
    // 防抖的状态更新方法
    debouncedUpdateUIState: function debouncedUpdateUIState() {
      var _this10 = this;
      // 清除之前的定时器
      if (this.statusUpdateTimer) {
        clearTimeout(this.statusUpdateTimer);
      }

      // 设置新的定时器，延迟更新状态
      this.statusUpdateTimer = setTimeout(function () {
        _this10.updateUIState();
      }, 200);
    },
    // 添加updateUIState方法，确保UI状态正确更新
    updateUIState: function updateUIState() {
      var _this11 = this;
      console.log('更新UI状态 - 蓝牙连接状态:', this.isConnected, '订单状态:', this.orderStatus, '支付状态:', this.isPaid, '锁状态:', this.isLockOpen);

      // 确保订单状态和支付状态一致
      if (this.orderStatus === 1) {
        if (!this.isPaid) {
          console.log('订单状态为已支付，但支付状态为未支付，更新为已支付');
          this.isPaid = true;
          this.paymentSuccess = true;

          // 如果正在轮询，停止轮询
          if (this.orderPollingTimer) {
            console.log('订单已支付，停止轮询');
            this.stopOrderPolling();
          }
        }
      } else if (this.orderStatus === 0 || this.orderStatus === null) {
        // 处理orderStatus为null的情况
        // 如果支付状态为true，但订单状态不是1，需要重新查询订单状态
        if (this.isPaid) {
          console.log('支付状态为已支付，但订单状态不是已支付，重新查询订单状态');
          this.queryOrderStatus().then(function (orderInfo) {
            // 如果通过查询确认已支付，更新订单状态
            if (orderInfo.status === 1 || orderInfo.payStatus === 1) {
              console.log('重新查询确认订单已支付');
              _this11.orderStatus = 1;
              _this11.isPaid = true;
              _this11.paymentSuccess = true;

              // 停止轮询
              _this11.stopOrderPolling();

              // 如果已连接设备，自动开门
              if (_this11.isConnected && !_this11.doorOpenProcessed && !_this11.doorOpenCompleted) {
                console.log('确认支付成功，设备已连接，自动开门');
                _this11.checkOrderAndOpenDoor();
              }
            } else {
              // 如果查询结果仍然不是已支付，则更新支付状态为未支付
              console.log('重新查询确认订单未支付，更新支付状态');
              _this11.isPaid = false;
              _this11.paymentSuccess = false;

              // 如果订单未支付且没有在轮询，启动轮询
              if (!_this11.orderPollingTimer && _this11.orderId) {
                console.log('订单未支付且没有在轮询，启动轮询');
                _this11.startOrderPolling();
              }
            }
          }).catch(function (err) {
            console.error('重新查询订单状态失败:', err);
          });
        } else {
          // 如果订单未支付且没有在轮询，启动轮询
          if (!this.orderPollingTimer && this.orderId && this.isPageActive) {
            console.log('订单未支付且没有在轮询，启动轮询');
            this.startOrderPolling();
          }
        }
      }

      // 确保订单ID已同步到lockService
      if (this.orderId && _index.lockService.getOrderInfo().orderId !== this.orderId) {
        console.log('UI状态更新 - 同步订单ID到lockService:', this.orderId);
        _index.lockService.setOrderId(this.orderId);

        // 获取最新订单状态
        if (this.orderId && (this.orderStatus === undefined || this.orderStatus === -1 || this.orderStatus === null)) {
          console.log('UI状态更新 - 获取最新订单状态');
          this.getOrderStatus().then(function (orderInfo) {
            console.log('获取订单状态成功:', orderInfo);
            // 更新支付状态 - 同时检查status和payStatus
            _this11.isPaid = _this11.orderStatus === 1 || orderInfo.payStatus === 1;

            // 如果订单已支付且已连接设备，自动开门
            if (_this11.isPaid && _this11.isConnected && !_this11.doorOpenProcessed && !_this11.doorOpenCompleted) {
              console.log('订单已支付且已连接设备，自动开门');
              _this11.checkOrderAndOpenDoor();
            }
          }).catch(function (err) {
            console.error('获取订单状态失败:', err);
          });
        }
      }

      // 从lockService获取最新设备状态
      var deviceInfo = _index.lockService.getDeviceInfo();
      if (deviceInfo) {
        // 更新设备连接状态 - 修复undefined问题
        var lockServiceConnected = deviceInfo.connected === true || deviceInfo.isConnected === true;
        if (this.isConnected !== lockServiceConnected) {
          console.log('连接状态不一致，从lockService更新:', deviceInfo.connected, '解析为:', lockServiceConnected);
          this.isConnected = lockServiceConnected;

          // 如果连接状态变为已连接，停止连接中状态
          if (lockServiceConnected) {
            this.isConnecting = false;

            // 如果已支付且已连接，自动开门
            if (this.isPaid && !this.doorOpenProcessed && !this.doorOpenCompleted) {
              console.log('连接成功且已支付，准备自动开门');
              setTimeout(function () {
                _this11.checkOrderAndOpenDoor();
              }, 1000);
            }
          }
        }

        // 更新电池电量
        if (deviceInfo.batteryLevel && this.batteryLevel !== deviceInfo.batteryLevel) {
          console.log('电池电量更新:', deviceInfo.batteryLevel);
          this.batteryLevel = deviceInfo.batteryLevel;
        }

        // 更新锁状态（添加null检查）
        if (deviceInfo.lockStatus && deviceInfo.lockStatus !== undefined && this.isLockOpen !== deviceInfo.lockStatus.isOpen) {
          console.log('锁状态更新:', deviceInfo.lockStatus.isOpen ? '已开启' : '已关闭');
          this.isLockOpen = deviceInfo.lockStatus.isOpen;
        }
      }

      // 如果已连接蓝牙并且已支付，自动触发开门逻辑
      if (this.isConnected && this.isPaid && !this.doorOpenProcessed && !this.doorOpenCompleted && this.isPageActive && !this.isRecentlyDisconnected) {
        console.log('连接成功且已支付，自动触发开门');
        this.doorOpenProcessed = true;

        // 延迟执行开门操作，确保UI已更新
        setTimeout(function () {
          _this11.checkOrderAndOpenDoor();
        }, 1000);
      }

      // 使用forceUpdate触发视图更新
      this.forceUpdate();
    },
    // 强制刷新组件
    forceUpdate: function forceUpdate() {
      // 通过修改一个不可见属性来触发视图更新
      this.updateKey = Date.now();
    },
    // 预初始化蓝牙环境
    preinitBluetooth: function preinitBluetooth() {
      var _this12 = this;
      console.log('预初始化蓝牙环境');

      // 检查是否为模拟模式
      if (typeof global !== 'undefined' && global.isSimulatedMode) {
        console.log('检测到全局模拟模式标志');
        this.isSimulatedMode = true;
      }

      // 初始化蓝牙环境
      _index.lockService.init().then(function (res) {
        console.log('蓝牙环境初始化成功:', res);

        // 检查是否为模拟模式
        if (res.simulated) {
          console.log('切换到模拟模式');
          _this12.isSimulatedMode = true;
        }
      }).catch(function (err) {
        console.error('蓝牙环境初始化失败:', err);

        // 即使蓝牙初始化失败，也不显示错误，让用户可以继续使用
        console.log('切换到模拟模式');
        _this12.isSimulatedMode = true;
      });
    },
    // 防抖连接
    debounceConnect: function debounceConnect() {
      var _this13 = this;
      // 防止短时间内多次连接
      var now = Date.now();
      if (now - this.lastConnectTime < 2000) {
        console.log('连接操作过于频繁，已忽略');
        return;
      }
      this.lastConnectTime = now;

      // 清除之前的定时器
      if (this.connectionDebounceTimer) {
        clearTimeout(this.connectionDebounceTimer);
      }

      // 设置新的定时器
      this.connectionDebounceTimer = setTimeout(function () {
        _this13.connectDevice();
      }, 300);
    },
    // 连接设备（优化版本）
    connectDevice: function connectDevice() {
      // 检查是否已连接或正在连接
      var deviceInfo = _index.lockService.getDeviceInfo();
      var actualConnected = deviceInfo && (deviceInfo.connected === true || deviceInfo.isConnected === true);
      if (actualConnected || this.isConnected || this.isConnecting) {
        console.log('设备已连接或正在连接中，不重复连接');
        if (actualConnected && !this.isConnected) {
          this.isConnected = true;
          this.isConnecting = false;
        }
        return;
      }
      console.log('开始连接设备');
      this.isConnecting = true;
      this.debouncedUpdateUIState(); // 使用防抖更新

      // 确保订单ID已设置到锁服务
      if (this.orderId) {
        console.log('连接设备前设置订单ID:', this.orderId);
        _index.lockService.setOrderId(this.orderId);
      }

      // 使用优化的连接方法
      this.connectDeviceOptimized();
    },
    // 断开连接
    disconnectDevice: function disconnectDevice() {
      if (!this.isConnected) {
        return Promise.resolve();
      }
      return _index.lockService.disconnect().then(function () {
        console.log('断开连接成功');
      }).catch(function (err) {
        console.error('断开连接失败:', err);
      });
    },
    // 确保断开蓝牙连接
    ensureDisconnectBluetooth: function ensureDisconnectBluetooth() {
      this.disconnectDevice().then(function () {
        return _index.lockService.close();
      }).catch(function (err) {
        console.error('关闭蓝牙失败:', err);
      });
    },
    // 查询设备状态
    queryDeviceStatus: function queryDeviceStatus() {
      try {
        console.log('尝试查询设备状态');
        // 先查询锁状态
        _index.lockService.queryLockStatus().then(function () {
          console.log('锁状态查询成功');
          // 再查询设备信息（包含电量）
          return _index.lockService.queryDeviceInfo();
        }).then(function () {
          console.log('设备信息查询成功');
        }).catch(function (err) {
          console.error('设备状态查询失败:', err);
        });
      } catch (error) {
        console.error('查询设备状态出错:', error);
      }
    },
    // 处理主要操作按钮点击
    handleMainAction: function handleMainAction() {
      console.log('处理主要操作按钮点击');

      // 如果用户未登录，显示登录弹窗
      if (!this.isLoggedIn) {
        console.log('用户未登录，显示登录弹窗');
        this.showLoginModal = true;
        return;
      }

      // 根据不同状态执行不同操作
      if (!this.isConnected) {
        // 未连接状态 - 连接设备
        this.tryConnectDevice();
      } else if (this.orderStatus === 0 || !this.isPaid) {
        // 已连接但未支付 - 显示支付弹窗
        this.showPaymentModal = true;
      } else {
        // 已连接且已支付 - 开门
        this.handleOpenDoor();
      }
    },
    // 处理开门按钮点击
    handleOpenDoor: function handleOpenDoor() {
      var _lockService$deviceIn,
        _this14 = this;
      console.log('处理开门按钮点击，开门完成状态:', this.doorOpenCompleted, '锁状态:', this.isLockOpen);

      // 如果正在重连中，不允许开门
      if (this.isReconnecting) {
        console.log('正在重连中，请稍候');
        uni.showToast({
          title: '正在重连设备，请稍候',
          icon: 'none',
          duration: 2000
        });
        return;
      }

      // 检查开门冷却时间，防止频繁开锁
      var currentTime = Date.now();
      var timeSinceLastOpen = currentTime - this.lastOpenTime;
      if (timeSinceLastOpen < this.openCooldown) {
        var remainingTime = Math.ceil((this.openCooldown - timeSinceLastOpen) / 1000);
        uni.showToast({
          title: "\u8BF7\u7B49\u5F85".concat(remainingTime, "\u79D2\u540E\u518D\u8BD5"),
          icon: 'none',
          duration: 2000
        });
        return;
      }

      // 如果锁已关闭，允许再次开门
      if (this.doorOpenCompleted && !this.isLockOpen) {
        console.log('锁已关闭，允许再次开门');
        // 重置开门完成状态，允许再次开门
        this.doorOpenCompleted = false;
      }

      // 验证设备连接状态（双重检查）
      var deviceInfo = _index.lockService.getDeviceInfo();
      var lockServiceConnected = deviceInfo && (deviceInfo.connected === true || deviceInfo.isConnected === true);
      console.log('开门前连接状态检查 - UI状态:', this.isConnected, 'lockService状态:', lockServiceConnected);
      if (!this.isConnected || !lockServiceConnected) {
        console.log('设备未连接，先连接设备');
        this.tryConnectDevice();
        return;
      }

      // 如果未支付，显示支付提示
      if (!this.isPaid && this.orderStatus !== 1) {
        console.log('未支付，显示支付提示');
        this.showOpenDoorModal = true;
        return;
      }

      // 开门前最后一次连接状态验证和强制同步
      console.log('开门前最后验证连接状态');
      var finalDeviceInfo = _index.lockService.getDeviceInfo();
      console.log('最终设备信息:', finalDeviceInfo);

      // 强制同步设备连接状态到lockService
      if (this.deviceMac && this.deviceName) {
        console.log('强制同步设备信息到lockService');
        _index.lockService.setDeviceMac(this.deviceMac);
        _index.lockService.setExpectedDeviceName(this.deviceName);
        if (this.orderId) {
          _index.lockService.setOrderId(this.orderId);
        }

        // 强制设置所有层级连接状态
        this.forceSetAllLayersConnected(finalDeviceInfo);
      }

      // 开门前最后验证所有层级的连接状态
      console.log('开门前验证所有层级连接状态:');
      console.log('lockService.deviceInfo.connected:', (_lockService$deviceIn = _index.lockService.deviceInfo) === null || _lockService$deviceIn === void 0 ? void 0 : _lockService$deviceIn.connected);
      console.log('lockService.deviceInfo:', _index.lockService.deviceInfo);

      // 特别检查blueToothManager的连接状态
      try {
        var blueToothManager = _index.lockService.blueToothManager || _index.lockService.bluetoothManager;
        if (blueToothManager) {
          var _blueToothManager$dev;
          console.log('blueToothManager连接状态检查:');
          console.log('blueToothManager.isConnected:', blueToothManager.isConnected);
          console.log('blueToothManager.deviceInfo:', blueToothManager.deviceInfo);
          console.log('blueToothManager.currentDeviceId:', blueToothManager.currentDeviceId);

          // 如果blueToothManager显示未连接，强制重新设置
          if (!blueToothManager.isConnected || !((_blueToothManager$dev = blueToothManager.deviceInfo) !== null && _blueToothManager$dev !== void 0 && _blueToothManager$dev.connected)) {
            console.log('检测到blueToothManager未连接，强制设置连接状态');
            if (blueToothManager.deviceInfo) {
              blueToothManager.deviceInfo.connected = true;
              blueToothManager.deviceInfo.isConnected = true;
            }
            blueToothManager.isConnected = true;
            blueToothManager.currentDeviceId = this.deviceMac;
            blueToothManager.connectedDeviceId = this.deviceMac;
            console.log('blueToothManager连接状态已强制修复');
          }
        }
      } catch (btError) {
        console.error('检查blueToothManager状态失败:', btError);
      }

      // 开门前强制重新认证设备，确保blueToothManager层面的连接
      console.log('开门前强制重新认证设备');

      // 先尝试重新认证，然后执行开门
      this.forceReauthenticateAndUnlock(finalDeviceInfo).then(function (res) {
        console.log('强制重新认证开门成功:', res);

        // 记录开门时间，用于防频繁开锁
        _this14.lastOpenTime = Date.now();
        _this14.currentTime = _this14.lastOpenTime; // 初始化currentTime

        // 启动冷却倒计时定时器
        _this14.startCooldownTimer();

        // 设置开门完成标志，防止重复开门
        _this14.doorOpenCompleted = true;
        _this14.isLockOpen = true;
        uni.showToast({
          title: '开门成功',
          icon: 'success',
          duration: 2000
        });

        // 更新订单使用状态
        _this14.useStatus = 1;

        // 播放成功音效
        _this14.playSuccessSound();

        // 更新UI状态
        _this14.updateUIState();
      }).catch(function (err) {
        console.error('强制重新认证开门失败:', err);

        // 如果是连接问题，尝试备用开门方法
        if (err.message && err.message.includes('设备未连接')) {
          console.log('尝试备用开门方法');
          _this14.tryAlternativeOpenDoor();
        } else {
          uni.showToast({
            title: '开门失败，请重试',
            icon: 'none',
            duration: 2000
          });
        }
      });
    },
    // 备用开门方法
    tryAlternativeOpenDoor: function tryAlternativeOpenDoor() {
      var _this15 = this;
      console.log('尝试备用开门方法');

      // 方法1：尝试重新连接后开门
      this.reconnectAndOpenDoor().catch(function (err) {
        console.error('重连开门失败:', err);
        // 方法2：尝试直接发送蓝牙开门命令
        _this15.directBluetoothOpenDoor();
      });
    },
    // 重新连接并开门
    reconnectAndOpenDoor: function reconnectAndOpenDoor() {
      var _this16 = this;
      console.log('重新连接设备并开门');
      return new Promise(function (resolve, reject) {
        // 先断开连接
        try {
          _index.lockService.disconnect();
        } catch (e) {
          console.log('断开连接失败:', e);
        }

        // 重置连接状态
        _this16.isConnected = false;
        _this16.isConnecting = false;
        _this16.bluetoothInitialized = false;

        // 延迟重新连接
        setTimeout(function () {
          _this16.connectDeviceOptimized().then(function () {
            // 连接成功后等待一段时间确保状态同步
            setTimeout(function () {
              // 再次尝试开门
              _index.lockService.openLock({
                ignoreOrderStatus: true,
                force: true,
                retry: true,
                singleCommand: true,
                operationType: 1
              }).then(resolve).catch(reject);
            }, 2000);
          }).catch(reject);
        }, 1000);
      });
    },
    // 直接蓝牙开门命令
    directBluetoothOpenDoor: function directBluetoothOpenDoor() {
      console.log('尝试直接蓝牙开门命令');

      // 如果有直接的蓝牙开门命令，可以在这里实现
      // 这是最后的备用方案
      uni.showToast({
        title: '开门失败，请检查设备连接',
        icon: 'none',
        duration: 3000
      });
    },
    // 处理支付
    processPayment: function processPayment() {
      var _this17 = this;
      console.log('处理支付');

      // 检查登录状态
      if (!this.isLoggedIn) {
        console.log('用户未登录，显示登录弹窗');
        this.closePaymentModal();
        this.showLoginModal = true;
        return;
      }

      // 设置支付中状态
      this.paymentLoading = true;
      this.paymentSuccess = false;

      // 创建订单并支付
      var createOrderIfNeeded = function createOrderIfNeeded() {
        // 检查是否已有订单ID
        if (_this17.orderId) {
          console.log('已有订单ID，直接返回:', _this17.orderId);
          return Promise.resolve({
            orderId: _this17.orderId
          });
        } else {
          // 创建新订单
          console.log('创建新订单');

          // 重置开门状态，因为这是新订单
          _this17.resetDoorState();

          // 确保设备编码不为空
          if (!_this17.deviceInfo || !_this17.deviceInfo.deviceCode && !_this17.deviceInfo.bindCode && !_this17.deviceInfo.macAddress) {
            console.error('设备编码不能为空');
            return Promise.reject({
              message: '设备编码不能为空，请重新扫描设备'
            });
          }

          // 使用设备信息中的绑定码、MAC地址或设备编号作为deviceCode
          var deviceCode = _this17.deviceInfo.bindCode || _this17.deviceInfo.macAddress || _this17.deviceInfo.deviceNo || '';
          console.log('使用设备编码:', deviceCode);
          return _index.lockService.createOrder({
            deviceCode: deviceCode,
            duration: 60 // 默认60分钟
          });
        }
      };

      // 执行创建订单流程
      createOrderIfNeeded().then(function (res) {
        console.log('订单准备完成:', res);
        // 确保订单ID存在
        if (res && res.orderId) {
          _this17.orderId = res.orderId;

          // 设置支付中状态
          _this17.isPaying = true;

          // 关闭支付弹窗
          _this17.closePaymentModal();

          // 设置订单ID到lockService
          _index.lockService.setOrderId(_this17.orderId);

          // 在跳转到微信支付页面前启动订单状态轮询
          // 这样即使用户不点击"完成"按钮，也能检测到支付状态变化
          console.log('即将跳转到微信支付页面，启动订单状态轮询');
          _this17.startOrderPolling();

          // 支付订单
          return _index.lockService.payOrder(_this17.orderId);
        } else {
          throw new Error('获取订单ID失败');
        }
      }).then(function (res) {
        console.log('支付成功:', res);

        // 更新订单状态
        _this17.orderStatus = 1;
        _this17.isPaid = true;
        _this17.paymentSuccess = true;

        // 保存支付状态到本地存储
        _this17.savePaymentState();

        // 更新UI状态
        _this17.updateUIState();

        // 播放支付成功音效
        _this17.playSuccessSound();

        // 显示支付成功提示
        uni.showToast({
          title: '支付成功',
          icon: 'success',
          duration: 2000
        });

        // 停止订单状态轮询，因为已经确认支付成功
        _this17.stopOrderPolling();

        // 延迟执行下一步操作，确保UI状态更新完成
        setTimeout(function () {
          // 如果已连接设备且未完成开门，自动开门
          if (_this17.isConnected && !_this17.doorOpenCompleted) {
            console.log('支付成功，设备已连接，自动开门');
            _this17.checkOrderAndOpenDoor();
          } else if (!_this17.isConnected) {
            // 未连接设备，尝试连接
            console.log('支付成功，设备未连接，尝试连接');
            _this17.tryConnectDevice();
          } else if (_this17.doorOpenCompleted) {
            console.log('支付成功，但已完成开门，无需重复操作');
          }
        }, 1000); // 延长延迟时间，确保状态完全更新
      }).catch(function (err) {
        console.error('支付流程失败:', err);

        // 显示失败提示
        uni.showToast({
          title: err.message || '支付失败，请重试',
          icon: 'none',
          duration: 2000
        });

        // 支付失败时不停止轮询，因为可能是用户取消了支付页面
        // 但实际已经完成了支付，轮询可以检测到这种情况
      }).finally(function () {
        _this17.paymentLoading = false;
        _this17.isPaying = false;
      });
    },
    // 关闭支付弹窗
    closePaymentModal: function closePaymentModal() {
      this.showPaymentModal = false;
      this.paymentLoading = false;
    },
    // 关闭开门弹窗
    closeOpenDoorModal: function closeOpenDoorModal() {
      this.showOpenDoorModal = false;
    },
    /**
     * 获取设备信息
     * @param {string} deviceId 设备编号或ID
     */
    getDeviceInfo: function getDeviceInfo(deviceId) {
      var _this18 = this;
      console.log('获取设备信息:', deviceId);

      // 先检查用户是否有设备的订单
      this.checkUserDeviceOrder(deviceId).then(function (orderInfo) {
        if (orderInfo) {
          // 设置订单信息
          _this18.orderId = orderInfo.orderId;
          _this18.orderStatus = orderInfo.status || 0;
          _this18.useStatus = orderInfo.useStatus || 0;
          _this18.orderAmount = orderInfo.amount || 0;
          _this18.orderDuration = orderInfo.duration || 0;

          // 保存门店信息
          if (orderInfo.shopName) {
            if (!_this18.deviceInfo) _this18.deviceInfo = {};
            _this18.deviceInfo.shopName = orderInfo.shopName;
            console.log('保存门店信息:', orderInfo.shopName);
          }

          // 更新支付状态
          _this18.isPaid = orderInfo.status === 1 || orderInfo.payStatus === 1;
          _this18.paymentSuccess = _this18.isPaid;

          // 设置订单ID到lockService
          _index.lockService.setOrderId(_this18.orderId);

          // 如果订单已支付，显示相应提示
          if (_this18.isPaid) {
            uni.showToast({
              title: '您有已支付的订单，将自动开门',
              icon: 'none',
              duration: 2000
            });

            // 播放支付成功音效
            _this18.playSuccessSound();
          } else {
            // 未支付订单
            uni.showToast({
              title: '您有未完成的订单',
              icon: 'none',
              duration: 2000
            });
          }
        }

        // 继续获取设备信息
        _this18.continueGetDeviceInfo(deviceId);
      }).catch(function (err) {
        console.error('检查用户订单失败:', err);
        // 继续获取设备信息
        _this18.continueGetDeviceInfo(deviceId);
      });
    },
    // 继续获取设备信息的方法
    continueGetDeviceInfo: function continueGetDeviceInfo(deviceId) {
      var _this19 = this;
      // 构建API请求URL
      var baseUrl = '';
      if (_api.default && _api.default.baseUrl) {
        baseUrl = _api.default.baseUrl;
      } else {
        // 默认使用HTTPS协议
        baseUrl = 'https://api.jycb888.com';
      }

      // 使用HTTPS协议
      if (baseUrl.startsWith('http://')) {
        baseUrl = baseUrl.replace('http://', 'https://');
      }

      // 不做判断，同时使用两个API路径
      var scanUrl = "".concat(baseUrl, "/api/wx/miniapp/device/scan?deviceCode=").concat(encodeURIComponent(deviceId));
      var statusUrl = "".concat(baseUrl, "/api/wx/miniapp/device/status/").concat(deviceId);
      console.log('尝试请求设备信息URL1:', scanUrl);
      console.log('尝试请求设备信息URL2:', statusUrl);
      var requestComplete = false;

      // 请求扫描接口
      uni.request({
        url: scanUrl,
        method: 'GET',
        header: {
          'Authorization': uni.getStorageSync('token')
        },
        sslVerify: false,
        success: function success(res) {
          // 如果另一个请求已成功处理，不再处理
          if (requestComplete) return;
          if (res.statusCode === 200 && res.data && res.data.code === 200 && res.data.data) {
            console.log('扫描接口成功:', res.data.data);
            requestComplete = true;
            _this19.handleDeviceInfoResponse(res.data.data, deviceId);
          } else if (res.statusCode === 200 && res.data && res.data.code === 500) {
            console.error('扫描接口返回错误:', res.data.message);
            // 如果是设备所属门店不存在的错误，尝试其他接口
          }
        },

        fail: function fail(err) {
          console.error('扫描接口请求失败:', err);
          // 不处理错误，因为还有状态接口请求
        }
      });

      // 请求状态接口
      uni.request({
        url: statusUrl,
        method: 'GET',
        header: {
          'Authorization': uni.getStorageSync('token')
        },
        sslVerify: false,
        success: function success(res) {
          // 如果另一个请求已成功处理，不再处理
          if (requestComplete) return;
          if (res.statusCode === 200 && res.data && res.data.code === 200 && res.data.data) {
            console.log('状态接口成功:', res.data.data);
            requestComplete = true;
            _this19.handleDeviceInfoResponse(res.data.data, deviceId);
          } else if (res.statusCode === 200 && res.data && res.data.code === 500) {
            console.error('状态接口返回错误:', res.data.message);
            // 如果两个接口都失败，显示错误信息
            if (!requestComplete) {
              setTimeout(function () {
                if (!requestComplete) {
                  _this19.showError("\u8BBE\u5907\u4FE1\u606F\u83B7\u53D6\u5931\u8D25: ".concat(res.data.message));
                }
              }, 1000);
            }
          }
        },
        fail: function fail(err) {
          // 只有在两个接口都失败时才处理错误
          if (!requestComplete) {
            console.error('状态接口请求失败:', err);

            // 延迟显示错误，给另一个接口一些时间
            setTimeout(function () {
              if (!requestComplete) {
                _this19.showError('获取设备信息失败，请检查网络连接');
              }
            }, 1000);
          }
        }
      });
    },
    // 处理设备信息响应
    handleDeviceInfoResponse: function handleDeviceInfoResponse(deviceData, deviceId) {
      var _this20 = this;
      console.log('处理设备信息响应:', deviceData);
      console.log('设备信息字段检查:', {
        macAddress: deviceData.macAddress,
        mac: deviceData.mac,
        bluetoothMac: deviceData.bluetoothMac,
        deviceMac: deviceData.deviceMac,
        deviceName: deviceData.deviceName,
        deviceId: deviceData.id || deviceData.deviceId
      });
      this.deviceInfo = deviceData;

      // 更新设备ID和价格信息
      this.deviceId = deviceData.id || deviceData.deviceId || deviceId;
      if (deviceData.hourlyRate || deviceData.price) {
        this.hourlyRate = deviceData.hourlyRate || parseFloat(deviceData.price) || 0;
        this.priceText = "\uFFE5".concat(this.hourlyRate.toFixed(2));
      }

      // 检查是否有当前订单
      if (deviceData.currentOrderId && !this.orderId) {
        this.orderId = deviceData.currentOrderId;
        // 获取订单状态
        this.getOrderStatus().then(function (orderInfo) {
          // 如果订单已支付，设置支付状态
          if (orderInfo.status === 1 || orderInfo.payStatus === 1) {
            _this20.isPaid = true;
            _this20.paymentSuccess = true;

            // 如果已连接设备，自动开门
            if (_this20.isConnected && !_this20.doorOpenProcessed) {
              console.log('检测到已支付订单，设备已连接，自动开门');
              _this20.checkOrderAndOpenDoor();
            }
          }
        }).catch(function (err) {
          console.error('获取订单状态失败:', err);
        });
      }

      // 设置设备MAC地址 - 支持多种字段名
      var macAddress = deviceData.macAddress || deviceData.mac || deviceData.bluetoothMac || deviceData.deviceMac;
      if (macAddress) {
        // 格式化MAC地址，添加冒号
        var rawMac = macAddress.replace(/:/g, '').replace(/-/g, '').toUpperCase();
        if (rawMac.length === 12) {
          this.deviceMac = rawMac.match(/.{1,2}/g).join(':');
          console.log('从API获取到设备MAC地址:', this.deviceMac);
          _index.lockService.setDeviceMac(this.deviceMac);
        } else {
          console.warn('MAC地址格式不正确:', macAddress);
        }
      } else {
        console.warn('API返回的设备信息中没有MAC地址字段');
      }

      // 优化连接流程：避免重复初始化，直接连接
      if (deviceData.deviceName && this.deviceMac) {
        this.deviceName = deviceData.deviceName;
        console.log('使用设备名称和MAC地址连接:', this.deviceName, this.deviceMac);

        // 设置预期设备名称和MAC地址
        _index.lockService.setExpectedDeviceName(this.deviceName);
        _index.lockService.setDeviceMac(this.deviceMac);

        // 直接连接，不延迟
        this.connectDeviceOptimized();
      } else if (this.deviceMac) {
        // 如果没有设备名称，但有MAC地址，尝试使用MAC地址连接
        console.log('没有设备名称，使用MAC地址连接');
        this.connectDeviceOptimized();
      } else {
        console.warn('设备信息中既没有设备名称也没有MAC地址，无法连接蓝牙设备');
      }
    },
    /**
     * 优化的设备连接方法
     */
    connectDeviceOptimized: function connectDeviceOptimized() {
      var _this21 = this;
      console.log('开始优化连接流程');

      // 检查是否已连接
      var deviceInfo = _index.lockService.getDeviceInfo();
      var actualConnected = deviceInfo && (deviceInfo.connected === true || deviceInfo.isConnected === true);
      if (actualConnected) {
        console.log('设备已连接，无需重复连接');
        this.isConnected = true;
        this.isConnecting = false;
        return Promise.resolve();
      }
      if (this.isConnecting) {
        console.log('正在连接中，避免重复连接');
        return Promise.resolve();
      }
      this.isConnecting = true;

      // 确保蓝牙已初始化（避免重复初始化）
      var initPromise = this.bluetoothInitialized ? Promise.resolve() : this.ensureBluetoothInitialized();
      return initPromise.then(function () {
        // 优先使用MAC地址直接连接（更快）
        if (_this21.deviceMac) {
          console.log('使用MAC地址快速连接:', _this21.deviceMac);
          return _index.lockService.connectDevice(_this21.deviceMac).then(function (res) {
            console.log('MAC地址连接成功:', res);
            return res;
          }).catch(function (err) {
            console.error('MAC地址连接失败，尝试设备名称连接:', err);
            // MAC连接失败，尝试设备名称连接
            if (_this21.deviceName) {
              return _this21.connectByDeviceName();
            } else {
              _this21.isConnecting = false;
              _this21.showConnectionError(err);
              throw err;
            }
          });
        } else if (_this21.deviceName) {
          // 没有MAC地址，使用设备名称连接
          return _this21.connectByDeviceName();
        } else {
          _this21.isConnecting = false;
          var error = new Error('设备信息不完整');
          uni.showToast({
            title: '设备信息不完整',
            icon: 'none'
          });
          throw error;
        }
      });
    },
    /**
     * 确保蓝牙已初始化
     */
    ensureBluetoothInitialized: function ensureBluetoothInitialized() {
      var _this22 = this;
      if (this.bluetoothInitialized) {
        return Promise.resolve();
      }
      console.log('初始化蓝牙环境');
      return _index.lockService.init().then(function (res) {
        _this22.bluetoothInitialized = true;
        console.log('蓝牙环境初始化完成');
        return res;
      }).catch(function (err) {
        console.error('蓝牙环境初始化失败:', err);
        throw err;
      });
    },
    /**
     * 使用设备名称连接设备
     */
    connectByDeviceName: function connectByDeviceName() {
      var _this23 = this;
      console.log('使用设备名称连接:', this.deviceName);
      return _index.lockService.directConnect(this.deviceName).then(function (res) {
        console.log('设备名称连接成功:', res);
        return res;
      }).catch(function (err) {
        console.error('设备名称连接失败:', err);
        _this23.isConnecting = false;
        _this23.showConnectionError(err);
        throw err;
      });
    },
    /**
     * 显示连接错误
     */
    showConnectionError: function showConnectionError(err) {
      uni.showToast({
        title: '连接失败: ' + (err.message || '未知错误'),
        icon: 'none',
        duration: 2000
      });
    },
    /**
     * 使用设备名称连接设备（旧方法，保留兼容性）
     * @param {string} deviceName 设备名称
     */
    connectDeviceByName: function connectDeviceByName(deviceName) {
      var _this24 = this;
      console.log('使用设备名称连接设备:', deviceName);

      // 先初始化蓝牙环境
      _index.lockService.init().then(function () {
        // 直接使用设备名称连接
        return _index.lockService.directConnect(deviceName);
      }).then(function (res) {
        console.log('连接命令执行成功:', res);
        // 注意：不在这里设置连接状态，让lockService的回调来处理
        console.log('等待lockService回调确认连接状态');

        // 显示连接成功提示
        uni.showToast({
          title: '连接成功',
          icon: 'success'
        });
      }).catch(function (err) {
        console.error('连接失败:', err);

        // 更新连接状态
        _this24.isConnecting = false;

        // 使用防抖更新UI状态
        _this24.debouncedUpdateUIState();

        // 显示连接失败提示
        uni.showToast({
          title: '连接失败，请重试',
          icon: 'none'
        });
      });
    },
    // 获取设备价格
    getDevicePrice: function getDevicePrice() {
      // 实现获取设备价格的逻辑
      // ...
    },
    // 获取订单状态
    getOrderStatus: function getOrderStatus() {
      var _this25 = this;
      if (!this.orderId || isNaN(this.orderId)) {
        console.error('无效的订单ID:', this.orderId);
        return Promise.reject(new Error('无效的订单ID'));
      }
      return new Promise(function (resolve, reject) {
        // 使用getAPIBaseUrl函数获取API基础URL
        var baseUrl = getAPIBaseUrl();
        uni.request({
          url: "".concat(baseUrl, "/api/wx/miniapp/order/").concat(_this25.orderId),
          method: 'GET',
          header: {
            'Authorization': uni.getStorageSync('token')
          },
          success: function success(res) {
            if (res.data && res.data.code === 200) {
              var orderInfo = res.data.data;
              console.log('获取订单状态成功:', orderInfo);
              // 更新订单信息
              // 注意：status可能为null，需要检查payStatus
              _this25.orderStatus = orderInfo.status || 0; // 如果status为null，默认为0（未支付）
              // 如果status为null但payStatus为1，说明已支付
              if (orderInfo.status === null && orderInfo.payStatus === 1) {
                console.log('订单status为null但payStatus为1，视为已支付');
                _this25.orderStatus = 1;
              }
              _this25.useStatus = orderInfo.useStatus || 0;
              _this25.orderAmount = orderInfo.amount || 0;
              _this25.orderDuration = orderInfo.duration || 0;

              // 保存门店信息
              if (orderInfo.shopName) {
                if (!_this25.deviceInfo) _this25.deviceInfo = {};
                _this25.deviceInfo.shopName = orderInfo.shopName;
                console.log('保存门店信息:', orderInfo.shopName);
              }

              // 更新支付状态 - 同时检查status和payStatus
              _this25.isPaid = _this25.orderStatus === 1 || orderInfo.payStatus === 1;
              // 设置订单ID到lockServic
              _index.lockService.setOrderId(_this25.orderId);
              // 如果订单已支付且使用中，开始计时
              if (_this25.isPaid && _this25.useStatus === 1) {
                _this25.startOrderTimer();
              }
              // 存储订单信息到本地
              try {
                var key = "order_".concat(_this25.orderId);
                uni.setStorageSync(key, JSON.stringify(orderInfo));
                console.log('订单信息已存储到本地');
              } catch (e) {
                console.error('存储订单信息到本地失败:', e);
              }
              resolve(orderInfo);
            } else {
              var errorMsg = res.data ? res.data.message || '未知错误' : '未知错误';
              console.error('获取订单状态失败:', errorMsg);
              reject(new Error('获取订单状态失败: ' + errorMsg));
            }
          },
          fail: function fail(err) {
            console.error('获取订单状态请求失败:', err);
            reject(err);
          }
        });
      });
    },
    // 开始订单计时
    startOrderTimer: function startOrderTimer() {
      // 实现订单计时逻辑
      // ...
    },
    // 保存锁状态
    saveLockState: function saveLockState(isOpen) {
      // 可以将锁状态保存到本地存储或其他地方
      // ...
    },
    // 播放成功提示音
    playSuccessSound: function playSuccessSound() {
      var _this26 = this;
      console.log('播放支付成功音效');

      // 对于微信小程序环境

      try {
        if (this.successAudio) {
          // 重新设置src确保能够再次播放
          this.successAudio.src = "https://wuji1112-1312674791.cos.ap-guangzhou.myqcloud.com/static/audio/ttsmaker-file-2025-6-11-20-49-17.mp3";
          // 背景音频管理器需要设置autoplay=true才会播放
          this.successAudio.autoplay = true;

          // 添加振动反馈作为辅助
          if (wx && wx.vibrateShort) {
            wx.vibrateShort({
              type: 'medium'
            });
          }
        } else {
          console.warn('音频对象未初始化');
          // 尝试重新初始化
          this.initAudio();
          // 延迟设置src并播放
          setTimeout(function () {
            if (_this26.successAudio) {
              _this26.successAudio.autoplay = true;
              _this26.successAudio.src = "https://wuji1112-1312674791.cos.ap-guangzhou.myqcloud.com/static/audio/ttsmaker-file-2025-6-11-20-49-17.mp3";
            }
          }, 300);
          // 使用系统振动反馈
          if (wx && wx.vibrateShort) {
            wx.vibrateShort({
              type: 'medium'
            });
          }
        }
      } catch (error) {
        console.error('播放音频失败:', error);
        // 备用：使用振动反馈
        if (wx && wx.vibrateShort) {
          wx.vibrateShort({
            type: 'medium'
          });
        }
      }

      // 非微信小程序环境
    },
    // 尝试替代方案播放音频
    tryAlternativeAudioPlay: function tryAlternativeAudioPlay() {
      var _this27 = this;
      console.log('尝试替代方案播放音频');

      // 使用微信小程序原生API播放

      if (wx && wx.createInnerAudioContext) {
        try {
          var innerAudio = wx.createInnerAudioContext();
          innerAudio.autoplay = true;
          innerAudio.src = "https://wuji1112-1312674791.cos.ap-guangzhou.myqcloud.com/static/audio/ttsmaker-file-2025-6-11-20-49-17.mp3";
          innerAudio.onError(function (err) {
            console.error('替代音频播放失败:', err);
            // 如果还是失败，尝试使用短音效API
            _this27.tryShortAudio();
          });
        } catch (e) {
          console.error('替代音频方案失败:', e);
          this.tryShortAudio();
        }
      } else {
        this.tryShortAudio();
      }

      // 非小程序环境
    },
    // 尝试使用短音效API
    tryShortAudio: function tryShortAudio() {
      if (wx && wx.playBackgroundAudio) {
        wx.playBackgroundAudio({
          dataUrl: "https://wuji1112-1312674791.cos.ap-guangzhou.myqcloud.com/static/audio/ttsmaker-file-2025-6-11-20-49-17.mp3",
          title: '支付成功提示音',
          fail: function fail(err) {
            console.error('背景音频播放失败:', err);
          }
        });
      } else if (wx && wx.createAudioContext) {
        // 尝试使用系统提示音
        wx.vibrateShort({
          type: 'medium'
        });
        console.log('使用振动提示代替音频');
      }
    },
    // 播放错误提示音
    playErrorSound: function playErrorSound() {
      // 实现播放错误提示音的逻辑
      // ...
    },
    // 页面背景图片加载完成
    onBackgroundImageLoaded: function onBackgroundImageLoaded() {
      this.isPageLoaded = true;
    },
    // 返回上一页
    goBack: function goBack() {
      uni.navigateBack();
    },
    // 设置蓝牙状态监听
    setupBluetoothListeners: function setupBluetoothListeners() {
      var _this28 = this;
      // 监听蓝牙适配器状态变化
      uni.onBluetoothAdapterStateChange(function (res) {
        console.log('蓝牙适配器状态变化:', res);

        // 如果蓝牙被关闭，更新连接状态
        if (!res.available) {
          _this28.isConnected = false;
          _this28.isConnecting = false;

          // 显示提示
          uni.showToast({
            title: '蓝牙已关闭，请开启蓝牙',
            icon: 'none'
          });
        } else if (res.available && _this28.deviceMac && !_this28.isConnected && !_this28.isConnecting) {
          // 蓝牙被重新打开，尝试重新连接
          setTimeout(function () {
            _this28.debounceConnect();
          }, 1000);
        }
      });

      // 监听蓝牙连接状态变化
      uni.onBLEConnectionStateChange(function (res) {
        console.log('蓝牙连接状态变化:', res);

        // 如果连接断开，更新状态
        if (!res.connected && _this28.isConnected) {
          _this28.isConnected = false;

          // 如果页面活跃，尝试重新连接
          if (_this28.isPageActive && _this28.deviceMac) {
            setTimeout(function () {
              _this28.debounceConnect();
            }, 1000);
          }
        }
      });
    },
    // 通过设备ID获取设备信息
    getDeviceInfoById: function getDeviceInfoById(deviceId) {
      var _this29 = this;
      console.log('通过ID获取设备信息:', deviceId);

      // 获取API基础URL
      var baseUrl = getAPIBaseUrl();
      var url = "".concat(baseUrl, "/api/wx/miniapp/device/status/").concat(deviceId);

      // 请求设备信息
      uni.request({
        url: url,
        method: 'GET',
        header: {
          'Authorization': uni.getStorageSync('token')
        },
        success: function success(res) {
          if (res.statusCode === 200 && res.data && res.data.code === 200) {
            console.log('获取设备信息成功:', res.data);

            // 保存设备信息
            _this29.deviceInfo = res.data.data;
            _this29.deviceId = _this29.deviceInfo.id || deviceId;

            // 设置设备名称和MAC地址
            if (_this29.deviceInfo.deviceName) {
              _this29.deviceName = _this29.deviceInfo.deviceName;
            } else {
              // 如果没有设备名称，尝试使用ID生成一个
              _this29.deviceName = 'LOCK' + deviceId;
            }

            // 设置MAC地址
            if (_this29.deviceInfo.macAddress) {
              // 格式化MAC地址，添加冒号
              var rawMac = _this29.deviceInfo.macAddress.replace(/:/g, '').replace(/-/g, '').toUpperCase();
              if (rawMac.length === 12) {
                _this29.deviceMac = rawMac.match(/.{1,2}/g).join(':');
                console.log('设置设备MAC地址:', _this29.deviceMac);
                _index.lockService.setDeviceMac(_this29.deviceMac);
              } else {
                console.warn('MAC地址格式不正确:', _this29.deviceInfo.macAddress);
                // 尝试使用设备ID生成MAC地址
                _this29.deviceMac = _this29.formatDeviceIdToMac(deviceId);
                console.log('使用设备ID生成MAC地址:', _this29.deviceMac);
                _index.lockService.setDeviceMac(_this29.deviceMac);
              }
            } else {
              // 如果没有MAC地址，尝试使用设备ID生成一个
              _this29.deviceMac = _this29.formatDeviceIdToMac(deviceId);
              console.log('使用设备ID生成MAC地址:', _this29.deviceMac);
              _index.lockService.setDeviceMac(_this29.deviceMac);
            }

            // 设置预期设备名称
            console.log('设置预期设备名称:', _this29.deviceName);
            _index.lockService.setExpectedDeviceName(_this29.deviceName);

            // 获取设备价格
            if (_this29.deviceInfo.price) {
              _this29.hourlyRate = parseFloat(_this29.deviceInfo.price) || 0;
              _this29.priceText = '￥' + _this29.hourlyRate.toFixed(2) + '/小时';
            }

            // 初始化蓝牙环境
            _this29.preinitBluetooth();

            // 如果在页面活跃状态下，自动尝试连接设备
            if (_this29.isPageActive) {
              console.log('页面活跃状态，准备尝试连接设备');
              _this29.autoConnectTimer = setTimeout(function () {
                _this29.tryConnectDevice();
              }, 1000);
            }
          } else {
            console.error('获取设备信息失败:', res);
            uni.showToast({
              title: '获取设备信息失败',
              icon: 'none',
              duration: 2000
            });
          }
        },
        fail: function fail(err) {
          console.error('请求设备信息失败:', err);
          uni.showToast({
            title: '网络错误，请重试',
            icon: 'none',
            duration: 2000
          });
        }
      });
    },
    // 格式化设备ID为MAC地址
    formatDeviceIdToMac: function formatDeviceIdToMac(deviceId) {
      // 如果设备ID是纯数字格式
      if (/^\d+$/.test(deviceId)) {
        // 补齐6位数字
        var paddedId = deviceId.padStart(6, '0');
        // 转换为16进制并分段
        return 'AB:' + paddedId.match(/.{1,2}/g).join(':').toUpperCase();
      }
      // 如果设备ID是其他格式
      else {
        // 取前12个字符，如果不足则补0
        var macChars = deviceId.replace(/[^0-9a-fA-F]/g, '').substring(0, 12).padEnd(12, '0').toUpperCase();
        // 分段并添加冒号
        return macChars.match(/.{1,2}/g).join(':');
      }
    },
    // 显示错误提示
    showError: function showError(message) {
      console.error('错误:', message);

      // 显示错误提示
      uni.showToast({
        title: message,
        icon: 'none',
        duration: 3000
      });
    },
    // 初始化支付流程
    initPayment: function initPayment() {
      var _this30 = this;
      console.log('初始化支付流程');
      this.showPaymentModal = true;
      this.paymentLoading = true;
      this.paymentSuccess = false;

      // 创建订单并支付
      var createOrderIfNeeded = function createOrderIfNeeded() {
        // 检查是否已有订单ID
        if (_this30.orderId) {
          console.log('已有订单ID，直接返回:', _this30.orderId);
          return Promise.resolve({
            orderId: _this30.orderId
          });
        } else {
          // 创建新订单
          console.log('创建新订单');

          // 确保设备编码不为空
          if (!_this30.deviceInfo || !_this30.deviceInfo.deviceCode && !_this30.deviceInfo.bindCode && !_this30.deviceInfo.macAddress) {
            console.error('设备编码不能为空');
            return Promise.reject({
              message: '设备编码不能为空，请重新扫描设备'
            });
          }

          // 使用设备信息中的绑定码、MAC地址或设备编号作为deviceCode
          var deviceCode = _this30.deviceInfo.bindCode || _this30.deviceInfo.macAddress || _this30.deviceInfo.deviceNo || '';
          console.log('使用设备编码:', deviceCode);
          return _index.lockService.createOrder({
            deviceCode: deviceCode,
            duration: 60 // 默认60分钟
          });
        }
      };
    },
    // 尝试自动连接设备
    tryConnectDevice: function tryConnectDevice() {
      var _this31 = this;
      // 先检查实际的连接状态
      var deviceInfo = _index.lockService.getDeviceInfo();
      var actualConnected = deviceInfo && (deviceInfo.connected === true || deviceInfo.isConnected === true);
      var lockServiceConnected = deviceInfo && deviceInfo.connected;
      console.log('尝试连接设备 - UI连接状态:', this.isConnected, 'lockService连接状态:', lockServiceConnected, '实际连接状态:', actualConnected, '连接中状态:', this.isConnecting);

      // 如果lockService已连接，同步UI状态
      if (lockServiceConnected && !this.isConnected) {
        console.log('lockService已连接，同步UI状态');
        this.isConnected = true;
        this.isConnecting = false;
        this.updateUIState();
        return;
      }

      // 如果实际已连接，更新状态并返回
      if (actualConnected && !this.isConnected) {
        console.log('设备实际已连接，更新UI状态');
        this.isConnected = true;
        this.isConnecting = false;
        this.updateUIState();
        return;
      }

      // 如果已连接，不重复连接
      if (this.isConnected && lockServiceConnected) {
        console.log('设备已连接，不重复连接');
        return;
      }

      // 如果正在连接中，避免重复操作
      if (this.isConnecting) {
        console.log('设备正在连接中，请稍候');
        return;
      }

      // 设置连接中状态
      this.isConnecting = true;

      // 设置连接超时处理
      var connectTimeout = setTimeout(function () {
        if (_this31.isConnecting) {
          console.log('连接超时，重置连接状态');
          _this31.isConnecting = false;
          _this31.updateUIState();
          uni.showToast({
            title: '连接超时，请重试',
            icon: 'none',
            duration: 2000
          });
        }
      }, 15000); // 15秒超时

      // 检查是否有设备MAC地址或名称
      if (!this.deviceMac && !this.deviceName && !this.deviceId && !(this.deviceInfo && (this.deviceInfo.deviceNo || this.deviceInfo.id))) {
        console.error('设备标识信息都为空，无法连接');
        this.isConnecting = false;
        uni.showToast({
          title: '设备信息不完整，无法连接',
          icon: 'none',
          duration: 2000
        });
        return;
      }

      // 设置锁服务的设备MAC地址
      if (this.deviceMac) {
        _index.lockService.setDeviceMac(this.deviceMac);
      }

      // 设置预期设备名称
      if (this.deviceName) {
        _index.lockService.setExpectedDeviceName(this.deviceName);
      }

      // 使用设备名称连接设备
      if (this.deviceName) {
        console.log('使用设备名称连接设备:', this.deviceName);

        // 直接连接设备
        _index.lockService.directConnect(this.deviceName).then(function (res) {
          console.log('连接命令执行成功:', res);
          // 清除超时定时器
          clearTimeout(connectTimeout);
          // 注意：不在这里设置连接状态，让lockService的回调来处理
          console.log('等待lockService回调确认连接状态');
        }).catch(function (err) {
          console.error('连接失败:', err);
          // 清除超时定时器
          clearTimeout(connectTimeout);
          _this31.isConnecting = false;
          uni.showToast({
            title: '设备连接失败，请重试',
            icon: 'none',
            duration: 2000
          });

          // 更新UI状态
          _this31.debouncedUpdateUIState();
        });
      }
      // 使用MAC地址连接设备
      else if (this.deviceMac) {
        console.log('使用MAC地址连接设备:', this.deviceMac);

        // 连接设备
        _index.lockService.connectDevice(this.deviceMac).then(function (res) {
          console.log('连接命令执行成功:', res);
          // 清除超时定时器
          clearTimeout(connectTimeout);
          // 注意：不在这里设置连接状态，让lockService的回调来处理
          console.log('等待lockService回调确认连接状态');
        }).catch(function (err) {
          console.error('连接失败:', err);
          // 清除超时定时器
          clearTimeout(connectTimeout);
          _this31.isConnecting = false;
          uni.showToast({
            title: '设备连接失败，请重试',
            icon: 'none',
            duration: 2000
          });

          // 更新UI状态
          _this31.debouncedUpdateUIState();
        });
      }
    },
    // 检查订单状态并决定是否开门
    checkOrderAndOpenDoor: function checkOrderAndOpenDoor() {
      var _this32 = this;
      // 先检查实际的连接状态
      var deviceInfo = _index.lockService.getDeviceInfo();
      var actualConnected = deviceInfo && (deviceInfo.connected === true || deviceInfo.isConnected === true);
      console.log('检查订单状态并决定是否开门，当前订单ID:', this.orderId, '支付状态:', this.isPaid, '连接状态:', this.isConnected, '实际连接状态:', actualConnected, '开门完成状态:', this.doorOpenCompleted);

      // 如果设备实际未连接，不执行开门
      if (!actualConnected) {
        console.log('设备实际未连接，不执行开门操作');
        // 尝试重新连接
        if (this.deviceMac && this.isPaid) {
          console.log('尝试重新连接设备');
          this.tryConnectDevice();
        }
        return;
      }

      // 如果实际已连接但状态不一致，更新状态
      if (actualConnected && !this.isConnected) {
        console.log('检测到实际已连接，更新连接状态');
        this.isConnected = true;
        this.isConnecting = false;
      }

      // 如果没有订单ID，不执行开门操作
      if (!this.orderId) {
        console.log('没有订单ID，不执行开门操作');
        return;
      }

      // 如果已经完成开门操作，不再重复开门
      if (this.doorOpenCompleted) {
        console.log('已完成开门操作，不再重复开门');
        return;
      }

      // 如果未支付，先查询一次订单支付状态，确认最新支付状态
      if (!this.isPaid && this.orderStatus !== 1) {
        console.log('本地状态显示订单未支付，查询最新订单支付状态');
        this.queryPaymentStatus().then(function (paymentInfo) {
          // 检查是否已支付
          if (paymentInfo.isPaid) {
            console.log('查询到订单已支付，更新支付状态');
            _this32.isPaid = true;
            _this32.orderStatus = 1;
            _this32.paymentSuccess = true;

            // 如果已连接设备，继续开门流程
            if (_this32.isConnected) {
              console.log('确认支付成功，设备已连接，继续开门');
              // 延迟执行，确保状态更新完成
              setTimeout(function () {
                _this32.performOpenDoor();
              }, 500);
            } else {
              // 未连接设备，先尝试连接
              console.log('确认支付成功，设备未连接，先尝试连接');
              _this32.tryConnectDevice();
            }
          } else {
            console.log('确认订单未支付，不执行开门操作');
            uni.showToast({
              title: '请先完成支付',
              icon: 'none',
              duration: 2000
            });
          }
        }).catch(function (err) {
          console.error('查询订单支付状态失败，尝试查询完整订单状态:', err);
          // 如果支付状态接口失败，回退到查询完整订单状态
          _this32.queryOrderStatus().then(function (orderInfo) {
            // 检查是否已支付 - 同时检查status和payStatus
            var isPaid = orderInfo.status === 1 || orderInfo.payStatus === 1;
            if (isPaid) {
              console.log('查询到订单已支付，更新支付状态');
              _this32.isPaid = true;
              _this32.orderStatus = 1;
              _this32.paymentSuccess = true;

              // 如果已连接设备，继续开门流程
              if (_this32.isConnected) {
                console.log('确认支付成功，设备已连接，继续开门');
                // 延迟执行，确保状态更新完成
                setTimeout(function () {
                  _this32.performOpenDoor();
                }, 500);
              } else {
                // 未连接设备，先尝试连接
                console.log('确认支付成功，设备未连接，先尝试连接');
                _this32.tryConnectDevice();
              }
            } else {
              console.log('确认订单未支付，不执行开门操作');
              uni.showToast({
                title: '请先完成支付',
                icon: 'none',
                duration: 2000
              });
            }
          }).catch(function (err) {
            console.error('查询订单状态失败:', err);
            // 显示错误提示
            uni.showToast({
              title: '查询订单状态失败，请重试',
              icon: 'none',
              duration: 2000
            });
          });
        });
        return;
      }

      // 如果未连接设备，先尝试连接设备
      if (!this.isConnected && !actualConnected) {
        console.log('设备未连接，先尝试连接设备');
        this.tryConnectDevice();
        return;
      }

      // 确保订单ID已设置到lockService
      if (_index.lockService.getOrderInfo().orderId !== this.orderId) {
        console.log('自动开门前重新设置订单ID到lockService:', this.orderId);
        _index.lockService.setOrderId(this.orderId);

        // 短暂延迟，确保订单ID设置生效
        setTimeout(function () {
          _this32.performOpenDoor();
        }, 500); // 延长延迟时间，确保订单ID设置完全生效
      } else {
        // 直接执行开门
        this.performOpenDoor();
      }
    },
    // 执行开门操作
    performOpenDoor: function performOpenDoor() {
      var _this33 = this;
      // 如果已支付且连接成功，自动开门
      if (this.isPaid && this.isConnected) {
        console.log('已支付且已连接，自动触发开门');
        this.doorOpenProcessed = true;

        // 设置开门超时保护
        var openDoorTimeout = setTimeout(function () {
          console.log('开门操作超时，尝试强制开门');

          // 尝试强制开门
          _this33.forceOpenDoor();
        }, 10000); // 10秒超时

        // 直接使用强制模式开门，忽略订单状态检查，添加singleCommand参数和operationType参数
        _index.lockService.openLock({
          ignoreOrderStatus: true,
          force: true,
          retry: true,
          singleCommand: true,
          operationType: 1 // 1表示开锁操作，用于设备状态上报
        }).then(function (res) {
          console.log('自动开门成功:', res);

          // 清除超时保护
          clearTimeout(openDoorTimeout);

          // 设置开门完成标志，防止重复开门
          _this33.doorOpenCompleted = true;

          // 更新锁状态
          _this33.isLockOpen = true;

          // 更新订单使用状态
          _this33.useStatus = 1;

          // 更新UI状态
          _this33.updateUIState();

          // 显示开门成功提示
          uni.showToast({
            title: '开门成功',
            icon: 'success',
            duration: 2000
          });

          // 播放成功音效
          _this33.playSuccessSound();
        }).catch(function (err) {
          console.error('自动开门失败:', err);

          // 清除超时保护
          clearTimeout(openDoorTimeout);

          // 如果是连接问题，尝试备用开门方法
          if (err.message && err.message.includes('设备未连接')) {
            console.log('自动开门连接失败，尝试备用方法');
            _this33.tryAlternativeOpenDoor();
          } else {
            // 尝试强制开门
            _this33.forceOpenDoor();
          }
        });
      } else {
        console.log('支付状态或连接状态不满足开门条件');
        if (!this.isPaid) {
          uni.showToast({
            title: '请先完成支付',
            icon: 'none',
            duration: 2000
          });
        } else if (!this.isConnected) {
          uni.showToast({
            title: '请先连接设备',
            icon: 'none',
            duration: 2000
          });
          // 尝试连接设备
          this.tryConnectDevice();
        }

        // 重置开门处理标志，允许再次尝试
        this.doorOpenProcessed = false;
      }
    },
    // 重置开门状态
    resetDoorState: function resetDoorState() {
      console.log('重置开门状态');
      this.doorOpenProcessed = false;
      this.doorOpenCompleted = false;
      this.doorOpenInProgress = false;
    },
    // 保存支付状态到本地存储
    savePaymentState: function savePaymentState() {
      if (this.orderId) {
        var paymentState = {
          orderId: this.orderId,
          isPaid: this.isPaid,
          paymentSuccess: this.paymentSuccess,
          orderStatus: this.orderStatus,
          timestamp: Date.now()
        };
        var key = "payment_state_".concat(this.orderId);
        uni.setStorageSync(key, paymentState);
        console.log('保存支付状态到本地存储:', paymentState);
      }
    },
    // 从本地存储恢复支付状态
    restorePaymentState: function restorePaymentState() {
      if (this.orderId) {
        var key = "payment_state_".concat(this.orderId);
        try {
          var paymentState = uni.getStorageSync(key);
          if (paymentState && paymentState.timestamp) {
            // 检查数据是否过期（24小时）
            var now = Date.now();
            var maxAge = 24 * 60 * 60 * 1000; // 24小时

            if (now - paymentState.timestamp < maxAge) {
              console.log('从本地存储恢复支付状态:', paymentState);

              // 只有当前状态为未支付时才恢复
              if (!this.isPaid && paymentState.isPaid) {
                this.isPaid = paymentState.isPaid;
                this.paymentSuccess = paymentState.paymentSuccess;
                this.orderStatus = paymentState.orderStatus;
                console.log('成功恢复支付状态 - 已支付');
              }
            } else {
              // 数据过期，删除
              console.log('本地支付状态数据过期，删除');
              uni.removeStorageSync(key);
            }
          }
        } catch (err) {
          console.error('恢复支付状态失败:', err);
        }
      }
    },
    // 智能重新认证并开门
    forceReauthenticateAndUnlock: function forceReauthenticateAndUnlock(deviceInfo) {
      var _this34 = this;
      console.log('智能重新认证并开门');
      return new Promise(function (resolve, reject) {
        // 1. 先尝试直接开门，不断开连接
        console.log('步骤1: 尝试直接开门');
        _this34.executeUnlockCommand(deviceInfo).then(function (result) {
          console.log('直接开门成功:', result);
          resolve(result);
        }).catch(function (directError) {
          console.log('直接开门失败，开始重连流程:', directError);

          // 2. 如果直接开门失败，才进行重连
          console.log('步骤2: 开始重连流程');
          _this34.performReconnectAndUnlock(deviceInfo).then(resolve).catch(reject);
        });
      });
    },
    // 执行重连并开门
    performReconnectAndUnlock: function performReconnectAndUnlock(deviceInfo) {
      var _this35 = this;
      console.log('执行重连并开门');

      // 检查是否已经在重连中
      if (this.isReconnecting || this.reconnectLock) {
        console.log('已经在重连中，跳过重复重连');
        return Promise.reject(new Error('重连已在进行中'));
      }

      // 设置重连锁
      this.isReconnecting = true;
      this.reconnectLock = true;
      return new Promise(function (resolve, reject) {
        // 1. 强制断开连接
        console.log('重连步骤1: 强制断开连接');
        _this35.isManualDisconnect = true; // 标记为手动断开
        _index.lockService.disconnect().then(function () {
          console.log('断开连接成功');
          // 等待断开完成
          return new Promise(function (resolve) {
            return setTimeout(resolve, 1500);
          });
        }).catch(function (err) {
          console.log('断开连接失败，继续执行:', err);
          return Promise.resolve();
        }).then(function () {
          // 2. 重新连接
          console.log('重连步骤2: 重新连接设备');
          return _index.lockService.connectDevice(_this35.deviceMac);
        }).then(function () {
          console.log('重新连接成功');
          // 等待连接稳定
          return new Promise(function (resolve) {
            return setTimeout(resolve, 2000);
          });
        }).then(function () {
          // 3. 验证连接状态
          console.log('重连步骤3: 验证连接状态');
          var currentDeviceInfo = _index.lockService.getDeviceInfo();
          if (!currentDeviceInfo || !currentDeviceInfo.connected) {
            throw new Error('设备连接验证失败');
          }
          console.log('连接状态验证成功');
          return Promise.resolve();
        }).then(function () {
          // 4. 执行开门
          console.log('重连步骤4: 执行开门');
          return _this35.executeUnlockCommand(deviceInfo);
        }).then(function (result) {
          console.log('重连开门成功:', result);
          resolve(result);
        }).catch(function (error) {
          console.error('重连开门失败:', error);
          reject(error);
        }).finally(function () {
          // 释放重连锁
          _this35.isReconnecting = false;
          setTimeout(function () {
            _this35.reconnectLock = false;
          }, 3000); // 3秒后释放锁，防止频繁重连
        });
      });
    },
    // 执行开门命令
    executeUnlockCommand: function executeUnlockCommand(deviceInfo) {
      console.log('执行开门命令');

      // 开门前最后检查 - lockService层面
      var currentDeviceInfo = _index.lockService.getDeviceInfo();
      if (!currentDeviceInfo || !currentDeviceInfo.connected) {
        console.error('开门前检查：lockService层面设备未连接');
        return Promise.reject(new Error('lockService层面设备未连接，无法开门'));
      }

      // 开门前最后检查 - blueToothManager层面
      try {
        var blueToothManager = __webpack_require__(/*! @/utils/blueToothManager.js */ 40);
        if (!blueToothManager.isConnected || !blueToothManager.deviceId) {
          console.error('开门前检查：blueToothManager层面设备未连接');
          console.log('blueToothManager.isConnected:', blueToothManager.isConnected);
          console.log('blueToothManager.deviceId:', blueToothManager.deviceId);
          return Promise.reject(new Error('blueToothManager层面设备未连接，无法开门'));
        }
        console.log('开门前检查通过，blueToothManager层面设备已连接');
      } catch (e) {
        console.error('获取blueToothManager失败:', e);
        return Promise.reject(new Error('无法获取blueToothManager，无法开门'));
      }
      console.log('开门前检查通过，所有层面设备已连接');
      return _index.lockService.openLock({
        ignoreOrderStatus: true,
        force: true,
        retry: true,
        singleCommand: true,
        operationType: 1,
        // 1表示开锁操作，用于设备状态上报
        deviceId: deviceInfo === null || deviceInfo === void 0 ? void 0 : deviceInfo.deviceId,
        // 明确传递设备ID
        mac: this.deviceMac,
        // 明确传递MAC地址
        name: this.deviceName // 明确传递设备名称
      });
    },
    // 检查蓝牙状态
    checkBluetoothState: function checkBluetoothState() {
      console.log('检查蓝牙状态');
      return new Promise(function (resolve, reject) {
        // 检查蓝牙适配器状态
        uni.getBluetoothAdapterState({
          success: function success(res) {
            console.log('蓝牙适配器状态:', res);
            if (res.available) {
              resolve(res);
            } else {
              reject(new Error('蓝牙适配器不可用'));
            }
          },
          fail: function fail(err) {
            console.error('获取蓝牙适配器状态失败:', err);
            reject(err);
          }
        });
      });
    },
    // 验证连接状态一致性
    verifyConnectionState: function verifyConnectionState() {
      console.log('验证连接状态一致性');

      // 检查UI状态和lockService状态是否一致
      var uiConnected = this.isConnected;
      var deviceInfo = _index.lockService.getDeviceInfo();
      var lockServiceConnected = deviceInfo && deviceInfo.connected;
      var isPaid = this.isPaid;
      console.log('状态检查 - UI连接:', uiConnected, 'lockService连接:', lockServiceConnected, '已支付:', isPaid);

      // 如果已支付但连接状态不一致，尝试修复
      if (isPaid && uiConnected !== lockServiceConnected) {
        console.log('检测到连接状态不一致，尝试修复');
        if (lockServiceConnected && !uiConnected) {
          // lockService已连接但UI显示未连接，同步UI状态
          console.log('同步UI状态为已连接');
          this.isConnected = true;
          this.isConnecting = false;
          this.updateUIState();
        } else if (!lockServiceConnected && uiConnected) {
          // UI显示已连接但lockService未连接，尝试重新连接
          console.log('UI显示已连接但lockService未连接，尝试重新连接');
          if (this.deviceMac && this.deviceName) {
            this.tryConnectDevice();
          } else {
            // 没有设备信息，重置UI状态
            this.isConnected = false;
            this.isConnecting = false;
            this.updateUIState();
          }
        }
      }

      // 如果已支付但都未连接，且有设备信息，尝试连接
      if (isPaid && !uiConnected && !lockServiceConnected && this.deviceMac && this.deviceName) {
        console.log('已支付但未连接，且有设备信息，尝试自动连接');
        this.tryConnectDevice();
      }
    },
    // 重新初始化蓝牙环境
    reinitializeBluetooth: function reinitializeBluetooth() {
      var _this36 = this;
      console.log('重新初始化蓝牙环境');

      // 保存当前连接状态，避免不必要的重置
      var wasConnected = this.isConnected;
      var wasPaid = this.isPaid;
      console.log('重新初始化前状态 - 连接:', wasConnected, '支付:', wasPaid);

      // 先检查蓝牙状态
      this.checkBluetoothState().then(function () {
        // 蓝牙状态正常，重新初始化lockService
        return _index.lockService.init();
      }).then(function (res) {
        console.log('蓝牙环境重新初始化成功:', res);

        // 重新设置回调
        _this36.setupLockServiceCallbacks();

        // 重新设置设备信息到lockService
        if (_this36.deviceMac && _this36.deviceName) {
          console.log('重新设置设备信息到lockService');
          _index.lockService.setDeviceMac(_this36.deviceMac);
          _index.lockService.setExpectedDeviceName(_this36.deviceName);
          if (_this36.orderId) {
            _index.lockService.setOrderId(_this36.orderId);
          }
        }

        // 如果之前已连接且已支付，尝试重新连接
        if (wasPaid && (_this36.deviceMac || _this36.deviceName)) {
          console.log('检测到之前已支付，尝试重新连接设备');

          // 先检查lockService的连接状态
          var deviceInfo = _index.lockService.getDeviceInfo();
          var lockServiceConnected = deviceInfo && deviceInfo.connected;
          console.log('lockService连接状态:', lockServiceConnected);
          if (!lockServiceConnected) {
            console.log('lockService未连接，开始重新连接');
            setTimeout(function () {
              _this36.tryConnectDevice();
            }, 1000);
          } else {
            console.log('lockService已连接，同步UI状态');
            _this36.isConnected = true;
            _this36.isConnecting = false;
            _this36.updateUIState();
          }
        }
      }).catch(function (err) {
        console.error('蓝牙环境重新初始化失败:', err);

        // 如果初始化失败，显示提示
        uni.showToast({
          title: '蓝牙初始化失败，请检查蓝牙设置',
          icon: 'none',
          duration: 3000
        });
      });
    }
  }, (0, _defineProperty2.default)(_methods, "checkBluetoothState", function checkBluetoothState() {
    return new Promise(function (resolve, reject) {
      console.log('检查蓝牙状态');

      // 检查蓝牙适配器状态
      uni.getBluetoothAdapterState({
        success: function success(res) {
          console.log('蓝牙适配器状态:', res);
          if (res.available) {
            console.log('蓝牙适配器可用');
            resolve(res);
          } else {
            console.log('蓝牙适配器不可用');
            reject(new Error('蓝牙适配器不可用'));
          }
        },
        fail: function fail(err) {
          console.log('获取蓝牙适配器状态失败，可能需要重新初始化:', err);
          // 获取状态失败，可能是适配器未初始化，这是正常的
          resolve();
        }
      });
    });
  }), (0, _defineProperty2.default)(_methods, "forceOpenDoor", function forceOpenDoor() {
    var _this37 = this;
    console.log('尝试强制开门模式');

    // 显示正在重试提示
    uni.showToast({
      title: '开门中，请稍候...',
      icon: 'loading',
      duration: 2000
    });

    // 延迟执行，确保提示显示完成
    setTimeout(function () {
      _index.lockService.openLock({
        ignoreOrderStatus: true,
        force: true,
        retry: true,
        singleCommand: true,
        operationType: 1 // 1表示开锁操作，用于设备状态上报
      }).then(function (res) {
        console.log('强制开门成功:', res);

        // 设置开门完成标志，防止重复开门
        _this37.doorOpenCompleted = true;
        _this37.isLockOpen = true;
        _this37.useStatus = 1;
        _this37.updateUIState();
        uni.showToast({
          title: '开门成功',
          icon: 'success',
          duration: 2000
        });

        // 播放成功音效
        _this37.playSuccessSound();
      }).catch(function (e) {
        console.error('强制开门也失败:', e);
        // 显示错误提示，但不中断用户体验
        uni.showToast({
          title: '自动开门失败，请手动点击开门',
          icon: 'none',
          duration: 3000
        });

        // 重置开门处理标志，允许再次尝试
        _this37.doorOpenProcessed = false;
      });
    }, 2000);
  }), (0, _defineProperty2.default)(_methods, "startUIUpdateTimer", function startUIUpdateTimer() {
    var _this38 = this;
    // 清除之前的定时器
    this.clearUIUpdateTimer();

    // 创建新的定时器，每2秒检查一次状态并更新UI（降低频率避免冲突）
    this.uiUpdateTimer = setInterval(function () {
      // 从lockService获取最新状态
      var deviceInfo = _index.lockService.getDeviceInfo();

      // 只在状态真正变化时更新，避免频繁更新导致UI闪烁
      var stateChanged = false;

      // 更新连接状态 - 修复undefined问题
      var lockServiceConnected = deviceInfo.connected === true || deviceInfo.isConnected === true;
      if (_this38.isConnected !== lockServiceConnected) {
        console.log('UI定时更新: 连接状态变化', _this38.isConnected, '->', lockServiceConnected);
        _this38.isConnected = lockServiceConnected;
        stateChanged = true;

        // 如果已连接，停止连接中状态
        if (lockServiceConnected) {
          _this38.isConnecting = false;
        }
      }

      // 更新电池电量
      if (deviceInfo.batteryLevel && _this38.batteryLevel !== deviceInfo.batteryLevel) {
        _this38.batteryLevel = deviceInfo.batteryLevel;
        stateChanged = true;
      }

      // 只有在状态真正变化时才触发UI更新
      if (stateChanged) {
        _this38.updateUIState();
      }
    }, 2000); // 改为2秒更新一次

    console.log('UI状态定时更新已启动');
  }), (0, _defineProperty2.default)(_methods, "clearUIUpdateTimer", function clearUIUpdateTimer() {
    if (this.uiUpdateTimer) {
      clearInterval(this.uiUpdateTimer);
      this.uiUpdateTimer = null;
      console.log('UI状态定时更新已停止');
    }

    // 清除状态更新防抖定时器
    if (this.statusUpdateTimer) {
      clearTimeout(this.statusUpdateTimer);
      this.statusUpdateTimer = null;
    }
  }), (0, _defineProperty2.default)(_methods, "initAudio", function initAudio() {
    var _this39 = this;
    // 使用uni.getBackgroundAudioManager替代createInnerAudioContext
    // 因为backgroundAudioManager可以在后台播放且权限更高
    try {
      // 检查平台，小程序环境使用背景音频管理器

      this.successAudio = uni.getBackgroundAudioManager();
      this.successAudio.title = '支付成功提示音'; // 必填项
      this.successAudio.epname = '今夜城堡'; // 必填项
      this.successAudio.singer = '系统提示音'; // 必填项
      // 重要：设置src但不会自动播放
      var audioSrc = "https://wuji1112-1312674791.cos.ap-guangzhou.myqcloud.com/static/audio/ttsmaker-file-2025-6-11-20-49-17.mp3";
      // 先将autoplay设为false，因为设置src可能会自动播放
      this.successAudio.autoplay = false;

      // 非小程序环境使用普通音频

      // 设置音频源，但不会自动播放
      // 微信小程序环境中，设置src可能会自动播放，所以暂时不设置src

      // 仅保存URL，但不立即设置
      this.audioSrc = audioSrc;

      // 非微信小程序环境，直接设置src

      // 错误处理

      this.successAudio.onError(function (res) {
        console.error('音频播放错误:', res);
        // 尝试使用另一种方式播放
        _this39.tryAlternativeAudioPlay();
      });
      console.log('初始化音频对象完成，不会自动播放');
    } catch (error) {
      console.error('初始化音频对象失败:', error);
    }
  }), (0, _defineProperty2.default)(_methods, "tryAlternativeAudioPlay", function tryAlternativeAudioPlay() {
    var _this40 = this;
    console.log('尝试替代方案播放音频');

    // 使用微信小程序原生API播放

    if (wx && wx.createInnerAudioContext) {
      try {
        var innerAudio = wx.createInnerAudioContext();
        innerAudio.autoplay = true;
        innerAudio.src = "https://wuji1112-1312674791.cos.ap-guangzhou.myqcloud.com/static/audio/ttsmaker-file-2025-6-11-20-49-17.mp3";
        innerAudio.onError(function (err) {
          console.error('替代音频播放失败:', err);
          // 如果还是失败，尝试使用短音效API
          _this40.tryShortAudio();
        });
      } catch (e) {
        console.error('替代音频方案失败:', e);
        this.tryShortAudio();
      }
    } else {
      this.tryShortAudio();
    }

    // 非小程序环境
  }), (0, _defineProperty2.default)(_methods, "tryShortAudio", function tryShortAudio() {
    if (wx && wx.playBackgroundAudio) {
      wx.playBackgroundAudio({
        dataUrl: "https://wuji1112-1312674791.cos.ap-guangzhou.myqcloud.com/static/audio/ttsmaker-file-2025-6-11-20-49-17.mp3",
        title: '支付成功提示音',
        fail: function fail(err) {
          console.error('背景音频播放失败:', err);
        }
      });
    } else if (wx && wx.createAudioContext) {
      // 尝试使用系统提示音
      wx.vibrateShort({
        type: 'medium'
      });
      console.log('使用振动提示代替音频');
    }
  }), (0, _defineProperty2.default)(_methods, "queryPaymentStatus", function queryPaymentStatus() {
    var _this41 = this;
    console.log('查询订单支付状态，订单ID:', this.orderId);
    if (!this.orderId || isNaN(this.orderId)) {
      console.error('无效的订单ID:', this.orderId);
      return Promise.reject(new Error('无效的订单ID'));
    }

    // 使用getAPIBaseUrl函数获取API基础URL
    var baseUrl = getAPIBaseUrl();
    return new Promise(function (resolve, reject) {
      uni.request({
        url: "".concat(baseUrl, "/api/wx/miniapp/order/payment-status/").concat(_this41.orderId),
        method: 'GET',
        header: {
          'Authorization': uni.getStorageSync('token')
        },
        success: function success(res) {
          if (res.data && res.data.code === 200) {
            // 接口返回的是布尔值，true 表示已支付
            var isPaid = res.data.data === true;
            console.log('查询订单支付状态成功:', {
              isPaid: isPaid
            });

            // 构造统一的返回格式，方便其他地方使用
            var paymentInfo = {
              isPaid: isPaid
            };

            // 如果支付状态为已支付，更新订单状态
            if (isPaid) {
              console.log('订单已支付，更新支付状态');
              _this41.orderStatus = 1;
              _this41.isPaid = true;
              _this41.paymentSuccess = true;

              // 播放支付成功音效
              _this41.playSuccessSound();

              // 显示支付成功提示
              uni.showToast({
                title: '支付成功',
                icon: 'success',
                duration: 2000
              });

              // 如果已连接设备，自动开门
              if (_this41.isConnected) {
                console.log('支付成功，设备已连接，自动开门');
                _this41.checkOrderAndOpenDoor();
              } else {
                // 未连接设备，尝试连接
                console.log('支付成功，设备未连接，尝试连接');
                _this41.tryConnectDevice();
              }
            }

            // 更新UI状态
            _this41.updateUIState();
            resolve(paymentInfo);
          } else {
            var errorMsg = res.data ? res.data.message || '未知错误' : '未知错误';
            console.error('查询订单支付状态失败:', errorMsg);
            reject(new Error('查询订单支付状态失败: ' + errorMsg));
          }
        },
        fail: function fail(err) {
          console.error('查询订单支付状态请求失败:', err);
          reject(err);
        }
      });
    });
  }), (0, _defineProperty2.default)(_methods, "queryOrderStatus", function queryOrderStatus() {
    var _this42 = this;
    console.log('主动查询订单状态，订单ID:', this.orderId);
    if (!this.orderId || isNaN(this.orderId)) {
      console.error('无效的订单ID:', this.orderId);
      return Promise.reject(new Error('无效的订单ID'));
    }

    // 使用getAPIBaseUrl函数获取API基础URL
    var baseUrl = getAPIBaseUrl();
    return new Promise(function (resolve, reject) {
      uni.request({
        url: "".concat(baseUrl, "/api/wx/miniapp/order/").concat(_this42.orderId),
        method: 'GET',
        header: {
          'Authorization': uni.getStorageSync('token')
        },
        success: function success(res) {
          if (res.data && res.data.code === 200) {
            var orderInfo = res.data.data;
            console.log('主动查询订单状态成功:', orderInfo);

            // 更新订单信息
            // 注意：status可能为null，需要检查payStatus
            _this42.orderStatus = orderInfo.status || 0; // 如果status为null，默认为0（未支付）

            // 如果status为null但payStatus为1，说明已支付
            if (orderInfo.status === null && orderInfo.payStatus === 1) {
              console.log('订单status为null但payStatus为1，视为已支付');
              _this42.orderStatus = 1;
            }
            _this42.useStatus = orderInfo.useStatus || 0;
            _this42.orderAmount = orderInfo.amount || 0;
            _this42.orderDuration = orderInfo.duration || 0;

            // 检查订单是否已支付 - 同时检查status和payStatus
            var isPaid = _this42.orderStatus === 1 || orderInfo.payStatus === 1;

            // 如果订单状态变为已支付，且之前未设置为已支付
            if (isPaid && !_this42.isPaid) {
              console.log('订单状态变为已支付，更新支付状态');
              _this42.isPaid = true;
              _this42.paymentSuccess = true;

              // 播放支付成功音效
              _this42.playSuccessSound();

              // 显示支付成功提示
              uni.showToast({
                title: '支付成功',
                icon: 'success',
                duration: 2000
              });

              // 如果已连接设备，自动开门
              if (_this42.isConnected) {
                console.log('支付成功，设备已连接，自动开门');
                _this42.checkOrderAndOpenDoor();
              } else {
                // 未连接设备，尝试连接
                console.log('支付成功，设备未连接，尝试连接');
                _this42.tryConnectDevice();
              }
            }

            // 更新UI状态
            _this42.updateUIState();
            resolve(orderInfo);
          } else {
            var errorMsg = res.data ? res.data.message || '未知错误' : '未知错误';
            console.error('主动查询订单状态失败:', errorMsg);
            reject(new Error('主动查询订单状态失败: ' + errorMsg));
          }
        },
        fail: function fail(err) {
          console.error('主动查询订单状态请求失败:', err);
          reject(err);
        }
      });
    });
  }), (0, _defineProperty2.default)(_methods, "startOrderPolling", function startOrderPolling() {
    var _this43 = this;
    console.log('启动订单状态轮询');

    // 先清除可能存在的轮询定时器
    this.stopOrderPolling();

    // 重置轮询计数
    this.orderPollingCount = 0;

    // 如果没有订单ID，不启动轮询
    if (!this.orderId) {
      console.log('没有订单ID，不启动轮询');
      return;
    }

    // 如果订单已支付，不需要轮询
    if (this.isPaid || this.orderStatus === 1) {
      console.log('订单已支付，不需要轮询');
      return;
    }

    // 创建轮询定时器，缩短轮询间隔为1秒，提高响应速度
    this.pollingInterval = 1000; // 1秒钟轮询一次
    this.orderPollingTimer = setInterval(function () {
      // 增加轮询计数
      _this43.orderPollingCount++;
      console.log("\u8BA2\u5355\u72B6\u6001\u8F6E\u8BE2 [".concat(_this43.orderPollingCount, "/").concat(_this43.maxPollingCount, "]"));

      // 如果超过最大轮询次数，停止轮询
      if (_this43.orderPollingCount >= _this43.maxPollingCount) {
        console.log('达到最大轮询次数，停止轮询');
        _this43.stopOrderPolling();

        // 最后再查询一次，确保不遗漏支付状态变化
        _this43.queryPaymentStatus().then(function (paymentInfo) {
          console.log('最终查询订单支付状态:', paymentInfo);
          if (paymentInfo.isPaid) {
            console.log('最终查询确认订单已支付');
          }
        }).catch(function (err) {
          console.error('最终查询订单支付状态失败:', err);
        });
        return;
      }

      // 优先使用专门的支付状态接口
      _this43.queryPaymentStatus().then(function (paymentInfo) {
        // 如果订单已支付，停止轮询
        if (paymentInfo.isPaid) {
          console.log('订单已支付，停止轮询');
          _this43.stopOrderPolling();

          // 如果已连接设备且未处理开门，自动开门
          if (_this43.isConnected && !_this43.doorOpenProcessed && !_this43.doorOpenCompleted) {
            console.log('订单已支付且设备已连接，自动开门');
            _this43.checkOrderAndOpenDoor();
          } else if (!_this43.isConnected) {
            // 未连接设备，尝试连接
            console.log('订单已支付但设备未连接，尝试连接设备');
            _this43.tryConnectDevice();
          } else if (_this43.doorOpenCompleted) {
            console.log('订单已支付且已完成开门，无需重复操作');
          }
        } else {
          // 未支付，每3次轮询打印一次日志
          if (_this43.orderPollingCount % 3 === 0) {
            console.log('订单未支付，继续轮询');
          }
        }
      }).catch(function (err) {
        console.error('查询支付状态失败，尝试查询完整订单状态:', err);

        // 如果专门的支付状态接口失败，回退到查询完整订单状态
        _this43.queryOrderStatus().then(function (orderInfo) {
          // 检查是否已支付 - 同时检查status和payStatus
          var isPaid = orderInfo.status === 1 || orderInfo.payStatus === 1;

          // 如果订单已支付，停止轮询
          if (isPaid) {
            console.log('订单已支付，停止轮询');
            _this43.stopOrderPolling();

            // 更新订单状态和支付状态
            _this43.orderStatus = 1;
            _this43.isPaid = true;
            _this43.paymentSuccess = true;

            // 如果已连接设备，自动开门
            if (_this43.isConnected && !_this43.doorOpenProcessed) {
              console.log('支付成功，设备已连接，自动开门');
              _this43.checkOrderAndOpenDoor();
            } else if (!_this43.isConnected) {
              // 未连接设备，尝试连接
              console.log('支付成功，设备未连接，尝试连接');
              _this43.tryConnectDevice();
            }
          } else if (orderInfo.status === null && _this43.orderPollingCount % 3 === 0) {
            // 如果status为null，每隔3次轮询打印一次日志
            console.log('订单status为null，继续轮询，当前payStatus:', orderInfo.payStatus);
          }
        }).catch(function (err) {
          console.error('轮询查询订单状态失败:', err);
          // 失败次数过多时停止轮询
          if (_this43.orderPollingCount >= 5) {
            console.log('轮询失败次数过多，停止轮询');
            _this43.stopOrderPolling();
          }
        });
      });
    }, this.pollingInterval);
  }), (0, _defineProperty2.default)(_methods, "stopOrderPolling", function stopOrderPolling() {
    if (this.orderPollingTimer) {
      console.log('停止订单状态轮询');
      clearInterval(this.orderPollingTimer);
      this.orderPollingTimer = null;
    }
  }), (0, _defineProperty2.default)(_methods, "checkUserDeviceOrder", function checkUserDeviceOrder(deviceId) {
    console.log('检查用户是否有设备的订单:', deviceId);

    // 构建API请求URL
    var baseUrl = '';
    if (_api.default && _api.default.baseUrl) {
      baseUrl = _api.default.baseUrl;
    } else {
      // 默认使用HTTPS协议
      baseUrl = 'https://api.jycb888.com/';
    }

    // 使用HTTPS协议
    if (baseUrl.startsWith('http://')) {
      baseUrl = baseUrl.replace('http://', 'https://');
    }

    // 检查用户对该设备是否有订单
    var checkOrderUrl = "".concat(baseUrl, "/api/wx/miniapp/device/check-user-order/").concat(deviceId);
    console.log('检查用户对设备是否有订单URL:', checkOrderUrl);
    return new Promise(function (resolve, reject) {
      uni.request({
        url: checkOrderUrl,
        method: 'GET',
        header: {
          'Authorization': uni.getStorageSync('token')
        },
        sslVerify: false,
        success: function success(res) {
          if (res.statusCode === 200 && res.data && res.data.code === 200 && res.data.data) {
            console.log('用户对设备有订单:', res.data.data);
            resolve(res.data.data);
          } else {
            console.log('用户对设备没有订单');
            resolve(null);
          }
        },
        fail: function fail(err) {
          console.error('检查用户订单失败:', err);
          reject(err);
        }
      });
    });
  }), _methods)
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"], __webpack_require__(/*! ./../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/webpack/buildin/global.js */ 3), __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"]))

/***/ }),

/***/ 107:
/*!*****************************************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/scan/device.vue?vue&type=style&index=0&lang=css& ***!
  \*****************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_device_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--6-oneOf-1-2!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src??ref--6-oneOf-1-3!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./device.vue?vue&type=style&index=0&lang=css& */ 108);
/* harmony import */ var _HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_device_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_device_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_device_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_device_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_4_65_2025051206_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_device_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 108:
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--6-oneOf-1-2!./node_modules/postcss-loader/src??ref--6-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/scan/device.vue?vue&type=style&index=0&lang=css& ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[101,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/scan/device.js.map