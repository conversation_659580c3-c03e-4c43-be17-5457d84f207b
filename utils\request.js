/**
 * 网络请求工具类
 * 封装uni.request，提供统一的请求接口
 */

// 基础URL
const BASE_URL = 'https://api.jycb888.com';

// 请求超时时间（毫秒）
const TIMEOUT = 30000;

/**
 * 发起网络请求
 * @param {Object} options 请求选项
 * @returns {Promise} 请求结果
 */
export function request(options) {
  return new Promise((resolve, reject) => {
    // 获取token
    const token = uni.getStorageSync('token');
    
    // 构建请求头
    const header = {
      'Content-Type': 'application/json',
      ...options.header
    };
    
    // 如果有token，添加到请求头
    if (token) {
      header['Authorization'] = token;
    }
    
    // 完整URL
    const url = /^(http|https):\/\//.test(options.url) 
      ? options.url 
      : BASE_URL + options.url;
    
    // 发起请求
    uni.request({
      url: url,
      method: options.method || 'GET',
      data: options.data,
      header: header,
      timeout: options.timeout || TIMEOUT,
      success: (res) => {
        // 请求成功
        const { statusCode, data } = res;
        
        if (statusCode >= 200 && statusCode < 300) {
          // HTTP状态码正常
          
          // 检查业务状态码
          if (data.code === 200) {
            // 业务成功
            resolve(data);
          } else if (data.code === 401) {
            // 未登录或登录已过期
            _handleUnauthorized();
            reject(new Error(data.message || '登录已过期，请重新登录'));
          } else {
            // 业务失败
            reject(new Error(data.message || '请求失败'));
          }
        } else if (statusCode === 401) {
          // 未授权
          _handleUnauthorized();
          reject(new Error('登录已过期，请重新登录'));
        } else {
          // HTTP状态码异常
          reject(new Error(`请求失败，状态码: ${statusCode}`));
        }
      },
      fail: (err) => {
        // 请求失败
        console.error('请求失败:', err);
        
        // 网络错误处理
        if (err.errMsg.includes('request:fail')) {
          reject(new Error('网络连接失败，请检查网络设置'));
        } else {
          reject(new Error(err.errMsg || '请求失败'));
        }
      }
    });
  });
}

/**
 * 处理未授权情况
 * @private
 */
function _handleUnauthorized() {
  // 清除token
  uni.removeStorageSync('token');
  
  // 显示提示
  uni.showToast({
    title: '登录已过期，请重新登录',
    icon: 'none',
    duration: 2000
  });
  
  // 延迟跳转到登录页
  setTimeout(() => {
    uni.navigateTo({
      url: '/pages/login/login'
    });
  }, 1500);
}

/**
 * GET请求
 * @param {string} url 请求地址
 * @param {Object} data 请求参数
 * @param {Object} options 其他选项
 * @returns {Promise} 请求结果
 */
export function get(url, data = {}, options = {}) {
  return request({
    url,
    method: 'GET',
    data,
    ...options
  });
}

/**
 * POST请求
 * @param {string} url 请求地址
 * @param {Object} data 请求参数
 * @param {Object} options 其他选项
 * @returns {Promise} 请求结果
 */
export function post(url, data = {}, options = {}) {
  return request({
    url,
    method: 'POST',
    data,
    ...options
  });
}

/**
 * PUT请求
 * @param {string} url 请求地址
 * @param {Object} data 请求参数
 * @param {Object} options 其他选项
 * @returns {Promise} 请求结果
 */
export function put(url, data = {}, options = {}) {
  return request({
    url,
    method: 'PUT',
    data,
    ...options
  });
}

/**
 * DELETE请求
 * @param {string} url 请求地址
 * @param {Object} data 请求参数
 * @param {Object} options 其他选项
 * @returns {Promise} 请求结果
 */
export function del(url, data = {}, options = {}) {
  return request({
    url,
    method: 'DELETE',
    data,
    ...options
  });
}

// 导出默认对象
export default {
  request,
  get,
  post,
  put,
  del
}; 