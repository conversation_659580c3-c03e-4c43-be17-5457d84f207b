<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高翔の拔头发大挑战</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Microsoft YaHei', sans-serif;
            background-color: #f0f0f0;
            overflow-x: hidden;
            text-align: center;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        h1 {
            font-size: 2.5rem;
            color: #ff4081;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
            animation: bounce 1s infinite alternate;
        }
        
        .game-container {
            position: relative;
            width: 300px;
            height: 400px;
            margin: 20px auto;
            background-color: #fff;
            border-radius: 20px;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
            overflow: hidden;
        }
        
        .head {
            position: relative;
            width: 200px;
            height: 250px;
            margin: 30px auto 0;
            background-color: #ffdbac;
            border-radius: 50% 50% 45% 45%;
            overflow: hidden;
        }
        
        .face {
            position: relative;
            width: 100%;
            height: 100%;
        }
        
        .eyes-container {
            position: absolute;
            top: 30%;
            width: 100%;
            display: flex;
            justify-content: space-around;
            transition: all 0.3s ease;
        }
        
        .eye {
            width: 40px;
            height: 40px;
            background-color: white;
            border-radius: 50%;
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;
            border: 2px solid #000;
        }
        
        .pupil {
            width: 20px;
            height: 20px;
            background-color: black;
            border-radius: 50%;
            position: relative;
            transition: all 0.3s ease;
        }
        
        .crazy-eye .pupil {
            width: 15px;
            height: 15px;
            transform: translate(5px, -5px);
        }
        
        .cross-eye .pupil:before,
        .cross-eye .pupil:after {
            content: '';
            position: absolute;
            background-color: white;
            width: 20px;
            height: 3px;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(45deg);
        }
        
        .cross-eye .pupil:after {
            transform: translate(-50%, -50%) rotate(-45deg);
        }
        
        .dizzy-eye .pupil:before {
            content: '';
            position: absolute;
            width: 20px;
            height: 20px;
            border: 3px solid white;
            border-radius: 50%;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            animation: spin 1s linear infinite;
        }
        
        .tiny-eye .pupil {
            transform: scale(0.5);
        }
        
        .wide-eye .pupil {
            transform: scale(1.5);
        }
        
        .mouth {
            position: absolute;
            bottom: 25%;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 40px;
            background-color: #ff6b6b;
            border-radius: 0 0 40px 40px;
            transition: all 0.3s ease;
            overflow: hidden;
        }
        
        .mouth.screaming {
            height: 60px;
            background-color: #000;
        }
        
        .teeth {
            position: absolute;
            top: 0;
            width: 100%;
            height: 15px;
            background-color: white;
            display: flex;
            justify-content: space-around;
        }
        
        .tooth {
            width: 10px;
            height: 100%;
            background-color: white;
            border-radius: 0 0 5px 5px;
        }
        
        .tongue {
            position: absolute;
            bottom: 5px;
            left: 50%;
            transform: translateX(-50%);
            width: 40px;
            height: 20px;
            background-color: #ff4d4d;
            border-radius: 20px 20px 0 0;
        }
        
        .hair-container {
            position: absolute;
            top: -20px;
            left: 0;
            width: 100%;
            height: 100px;
        }
        
        .hair {
            position: absolute;
            width: 10px;
            height: 80px;
            background-color: #333;
            border-radius: 5px;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .hair:hover {
            transform: scale(1.1);
        }
        
        .hair.pulled {
            animation: pull-hair 0.5s forwards;
        }
        
        @keyframes pull-hair {
            0% { transform: translateY(0); }
            50% { transform: translateY(-20px); }
            100% { transform: translateY(-100px); opacity: 0; }
        }
        
        .score-container {
            margin: 20px auto;
            font-size: 1.5rem;
            font-weight: bold;
            color: #333;
        }
        
        .btn {
            padding: 12px 24px;
            font-size: 1.2rem;
            background-color: #4caf50;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
            transition: transform 0.2s, background-color 0.3s;
        }
        
        .btn:hover {
            transform: scale(1.1);
            background-color: #45a049;
        }
        
        .sweat {
            position: absolute;
            width: 10px;
            height: 15px;
            background-color: rgba(100, 200, 255, 0.7);
            border-radius: 50%;
            opacity: 0;
        }
        
        .sweat.active {
            animation: drop 1s linear forwards;
        }
        
        @keyframes drop {
            0% { transform: translateY(0); opacity: 0.7; }
            100% { transform: translateY(50px); opacity: 0; }
        }
        
        .pain-meter {
            width: 80%;
            height: 20px;
            background-color: #ddd;
            margin: 10px auto;
            border-radius: 10px;
            overflow: hidden;
        }
        
        .pain-level {
            height: 100%;
            width: 0%;
            background: linear-gradient(to right, #4caf50, #ffeb3b, #f44336);
            transition: width 0.3s ease;
        }
        
        .game-over {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            font-size: 2rem;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.5s;
            z-index: 100;
        }
        
        .game-over.active {
            opacity: 1;
            pointer-events: all;
        }
        
        .bald-head {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border-radius: 50% 50% 45% 45%;
            background: linear-gradient(to bottom, #ffdbac, #ffcb8e);
            opacity: 0;
            transition: opacity 0.5s;
        }
        
        .bald-head.active {
            opacity: 1;
        }
        
        .eyebrow {
            position: absolute;
            width: 40px;
            height: 8px;
            background-color: #333;
            border-radius: 4px;
            transition: all 0.3s ease;
        }
        
        .eyebrow.left {
            top: 22%;
            left: 25%;
            transform: rotate(-10deg);
        }
        
        .eyebrow.right {
            top: 22%;
            right: 25%;
            transform: rotate(10deg);
        }
        
        .eyebrow.angry {
            height: 10px;
        }
        
        .eyebrow.left.angry {
            transform: rotate(-30deg) translateY(-5px);
        }
        
        .eyebrow.right.angry {
            transform: rotate(30deg) translateY(-5px);
        }
        
        @keyframes bounce {
            0% { transform: translateY(0); }
            100% { transform: translateY(-10px); }
        }
        
        .sound-toggle {
            position: absolute;
            top: 10px;
            right: 10px;
            font-size: 24px;
            cursor: pointer;
            z-index: 10;
        }
        
        .expression {
            position: absolute;
            bottom: 10px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 1.2rem;
            color: #ff4081;
            font-weight: bold;
            opacity: 0;
            transition: opacity 0.3s;
        }
        
        .expression.active {
            opacity: 1;
            animation: fade-out 2s forwards;
        }
        
        @keyframes fade-out {
            0% { opacity: 1; transform: translateX(-50%) translateY(0); }
            100% { opacity: 0; transform: translateX(-50%) translateY(-20px); }
        }
        
        @keyframes spin {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>高翔の拔头发大挑战</h1>
        
        <div class="game-container">
            <div class="sound-toggle" id="soundToggle">🔊</div>
            
            <div class="head">
                <div class="bald-head" id="baldHead"></div>
                <div class="face">
                    <div class="eyebrow left" id="leftEyebrow"></div>
                    <div class="eyebrow right" id="rightEyebrow"></div>
                    <div class="eyes-container" id="eyesContainer">
                        <div class="eye" id="leftEye">
                            <div class="pupil" id="leftPupil"></div>
                        </div>
                        <div class="eye" id="rightEye">
                            <div class="pupil" id="rightPupil"></div>
                        </div>
                    </div>
                    <div class="mouth" id="mouth">
                        <div class="teeth">
                            <div class="tooth"></div>
                            <div class="tooth"></div>
                            <div class="tooth"></div>
                            <div class="tooth"></div>
                            <div class="tooth"></div>
                        </div>
                        <div class="tongue"></div>
                    </div>
                </div>
                <div class="hair-container" id="hairContainer"></div>
            </div>
            
            <div class="pain-meter">
                <div class="pain-level" id="painLevel"></div>
            </div>
            
            <div class="score-container">
                拔掉: <span id="score">0</span> 根头发
            </div>
            
            <div class="expression" id="expression"></div>
            
            <div class="game-over" id="gameOver">
                <div>游戏结束!</div>
                <div>高翔已经秃了!</div>
                <button class="btn" id="restartBtn">再来一次</button>
            </div>
        </div>
        
        <button class="btn" id="startBtn">开始游戏</button>
    </div>
    
    <script>
        // 游戏变量
        let score = 0;
        let painLevel = 0;
        let gameActive = false;
        let soundEnabled = true;
        
        // 获取DOM元素
        const hairContainer = document.getElementById('hairContainer');
        const scoreDisplay = document.getElementById('score');
        const painLevelDisplay = document.getElementById('painLevel');
        const mouth = document.getElementById('mouth');
        const leftPupil = document.getElementById('leftPupil');
        const rightPupil = document.getElementById('rightPupil');
        const leftEyebrow = document.getElementById('leftEyebrow');
        const rightEyebrow = document.getElementById('rightEyebrow');
        const gameOverScreen = document.getElementById('gameOver');
        const startBtn = document.getElementById('startBtn');
        const restartBtn = document.getElementById('restartBtn');
        const baldHead = document.getElementById('baldHead');
        const soundToggle = document.getElementById('soundToggle');
        const expressionDisplay = document.getElementById('expression');
        const eyesContainer = document.getElementById('eyesContainer');
        
        // 音效
        const screamSounds = [
            './sounds/man-scream-03.mp3',
            './sounds/man-scream-03.mp3',
            './sounds/man-scream-03.mp3'
        ];
        
        // 弱智表情
        const expressions = [
            "啊！我的头发！",
            "疼死我了！",
            "你在干什么！",
            "我还要脱发吗？",
            "我的发际线！",
            "我要秃了！",
            "别拔了！",
            "我的帅气要没了！",
            "我要报警了！",
            "这是谋杀！"
        ];
        
        // 创建音频元素
        const createAudio = (src) => {
            const audio = new Audio(src);
            audio.volume = 0.5;
            return audio;
        };
        
        // 随机获取音效
        const getRandomScream = () => {
            const index = Math.floor(Math.random() * screamSounds.length);
            return createAudio(screamSounds[index]);
        };
        
        // 初始化游戏
        function initGame() {
            // 重置变量
            score = 0;
            painLevel = 0;
            gameActive = true;
            
            // 清空并创建头发
            hairContainer.innerHTML = '';
            createHairs();
            
            // 更新显示
            scoreDisplay.textContent = score;
            painLevelDisplay.style.width = '0%';
            
            // 重置表情
            resetFace();
            
            // 隐藏游戏结束画面
            gameOverScreen.classList.remove('active');
            baldHead.classList.remove('active');
        }
        
        // 创建头发
        function createHairs() {
            const hairCount = 30;
            
            for (let i = 0; i < hairCount; i++) {
                const hair = document.createElement('div');
                hair.className = 'hair';
                
                // 随机位置
                const left = 10 + (i % 10) * 18;
                const top = 20 + Math.floor(i / 10) * 25;
                
                hair.style.left = `${left}px`;
                hair.style.top = `${top}px`;
                
                // 随机高度和宽度
                const height = 60 + Math.random() * 30;
                const width = 5 + Math.random() * 5;
                
                hair.style.height = `${height}px`;
                hair.style.width = `${width}px`;
                
                // 随机旋转
                const rotation = -10 + Math.random() * 20;
                hair.style.transform = `rotate(${rotation}deg)`;
                
                // 添加点击事件
                hair.addEventListener('click', () => {
                    if (!gameActive) return;
                    
                    pullHair(hair);
                });
                
                hairContainer.appendChild(hair);
            }
        }
        
        // 拔头发
        function pullHair(hair) {
            // 标记为已拔
            hair.classList.add('pulled');
            
            // 增加分数
            score++;
            scoreDisplay.textContent = score;
            
            // 增加痛苦值
            increasePain();
            
            // 改变表情
            changeFaceExpression();
            
            // 播放尖叫声
            if (soundEnabled) {
                const scream = getRandomScream();
                scream.play();
            }
            
            // 显示表情文字
            showRandomExpression();
            
            // 添加汗水
            createSweat();
            
            // 移除头发元素
            setTimeout(() => {
                hair.remove();
                
                // 检查是否所有头发都被拔光
                if (hairContainer.children.length === 0) {
                    endGame();
                }
            }, 500);
        }
        
        // 增加痛苦值
        function increasePain() {
            painLevel += 3 + Math.random() * 2;
            if (painLevel > 100) painLevel = 100;
            
            painLevelDisplay.style.width = `${painLevel}%`;
            
            // 如果痛苦值达到100%，结束游戏
            if (painLevel >= 100) {
                endGame();
            }
        }
        
        // 改变表情
        function changeFaceExpression() {
            // 根据痛苦程度改变表情
            const intensity = painLevel / 100;
            
            // 眼睛变化
            if (Math.random() > 0.7) {
                leftPupil.style.transform = `translate(${Math.random() * 10 - 5}px, ${Math.random() * 10 - 5}px)`;
                rightPupil.style.transform = `translate(${Math.random() * 10 - 5}px, ${Math.random() * 10 - 5}px)`;
            }
            
            // 眉毛变化
            leftEyebrow.style.transform = `rotate(${-10 - 20 * intensity}deg) translateY(-${5 * intensity}px)`;
            rightEyebrow.style.transform = `rotate(${10 + 20 * intensity}deg) translateY(-${5 * intensity}px)`;
            
            // 眉毛高度变化，表现更愤怒的表情
            leftEyebrow.style.height = `${8 + 4 * intensity}px`;
            rightEyebrow.style.height = `${8 + 4 * intensity}px`;
            
            // 嘴巴变化
            mouth.style.height = `${40 + 20 * intensity}px`;
            
            // 随机眼神
            if (Math.random() > 0.7) {
                eyesContainer.style.transform = `rotate(${Math.random() * 10 - 5}deg)`;
            }
            
            // 随机弱智眼神效果
            const eyeEffectRandom = Math.random();
            
            // 清除所有眼神效果
            leftPupil.classList.remove('cross-eye', 'dizzy-eye', 'tiny-eye', 'wide-eye');
            rightPupil.classList.remove('cross-eye', 'dizzy-eye', 'tiny-eye', 'wide-eye');
            
            if (eyeEffectRandom > 0.8) {
                // 交叉眼
                leftPupil.classList.add('cross-eye');
            } else if (eyeEffectRandom > 0.6) {
                // 眩晕眼
                leftPupil.classList.add('dizzy-eye');
                rightPupil.classList.add('dizzy-eye');
            } else if (eyeEffectRandom > 0.4) {
                // 小眼睛
                leftPupil.classList.add('tiny-eye');
                rightPupil.classList.add('tiny-eye');
            } else if (eyeEffectRandom > 0.2) {
                // 大眼睛
                leftPupil.classList.add('wide-eye');
                rightPupil.classList.add('wide-eye');
            }
            
            // 一段时间后恢复正常
            setTimeout(() => {
                leftPupil.classList.remove('cross-eye', 'dizzy-eye', 'tiny-eye', 'wide-eye');
                rightPupil.classList.remove('cross-eye', 'dizzy-eye', 'tiny-eye', 'wide-eye');
            }, 800);
            
            // 尖叫
            mouth.classList.add('screaming');
            setTimeout(() => {
                mouth.classList.remove('screaming');
            }, 500);
        }
        
        // 重置表情
        function resetFace() {
            leftPupil.style.transform = '';
            rightPupil.style.transform = '';
            leftEyebrow.style.transform = 'rotate(-10deg)';
            rightEyebrow.style.transform = 'rotate(10deg)';
            leftEyebrow.style.height = '8px';
            rightEyebrow.style.height = '8px';
            mouth.style.height = '40px';
            mouth.classList.remove('screaming');
            eyesContainer.style.transform = '';
            leftPupil.classList.remove('cross-eye', 'dizzy-eye', 'tiny-eye', 'wide-eye');
            rightPupil.classList.remove('cross-eye', 'dizzy-eye', 'tiny-eye', 'wide-eye');
        }
        
        // 创建汗水
        function createSweat() {
            const sweat = document.createElement('div');
            sweat.className = 'sweat';
            
            // 随机位置
            const left = Math.random() * 180 + 10;
            sweat.style.left = `${left}px`;
            sweat.style.top = '50px';
            
            // 激活动画
            sweat.classList.add('active');
            
            // 添加到头部
            document.querySelector('.head').appendChild(sweat);
            
            // 动画结束后移除
            setTimeout(() => {
                sweat.remove();
            }, 1000);
        }
        
        // 显示随机表情文字
        function showRandomExpression() {
            const expression = expressions[Math.floor(Math.random() * expressions.length)];
            expressionDisplay.textContent = expression;
            expressionDisplay.classList.add('active');
            
            setTimeout(() => {
                expressionDisplay.classList.remove('active');
            }, 2000);
        }
        
        // 结束游戏
        function endGame() {
            gameActive = false;
            gameOverScreen.classList.add('active');
            baldHead.classList.add('active');
        }
        
        // 事件监听
        startBtn.addEventListener('click', () => {
            initGame();
            startBtn.style.display = 'none';
        });
        
        restartBtn.addEventListener('click', () => {
            initGame();
        });
        
        soundToggle.addEventListener('click', () => {
            soundEnabled = !soundEnabled;
            soundToggle.textContent = soundEnabled ? '🔊' : '🔇';
        });
    </script>
</body>
</html> 