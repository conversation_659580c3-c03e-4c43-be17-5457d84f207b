/* 安全区域背景色处理 */

/* 所有页面使用统一的导航栏颜色 */
.page-index .uni-safe-area-bottom,
.page-scan .uni-safe-area-bottom,
.page-orders .uni-safe-area-bottom,
.page-contact .uni-safe-area-bottom,
.page-profile .uni-safe-area-bottom,
.page-report .uni-safe-area-bottom {
  background-color: rgba(30, 30, 30, 0.95) !important;
}

/* iOS安全区域直接添加到页面底部 - 统一使用导航栏颜色 */
@supports (padding-bottom: env(safe-area-inset-bottom)) {
  .page-index::after,
  .page-scan::after,
  .page-orders::after,
  .page-contact::after,
  .page-profile::after,
  .page-report::after {
    content: '';
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: env(safe-area-inset-bottom);
    background-color: rgba(30, 30, 30, 0.95);
    z-index: 9990;
    pointer-events: none;
  }
}

/* TabBar底部需要添加安全区域高度 */
.tab-bar {
  padding-bottom: constant(safe-area-inset-bottom) !important;
  padding-bottom: env(safe-area-inset-bottom) !important;
} 