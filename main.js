import Vue from 'vue'
import App from './App'
import './uni.promisify.adaptor'
import store from './store'

// 导入蓝牙锁服务
import { lockService, blueToothManager } from './utils/index.js'

// 引入全局登录组件
import GlobalLogin from './components/global-login/index.vue'

// 引入安卓设备适配混入
import androidMixin from './utils/androidMixin.js'

Vue.config.productionTip = false

// 挂载蓝牙锁服务到全局
Vue.prototype.$lockService = lockService
Vue.prototype.$blueToothManager = blueToothManager

// 注册全局登录组件
Vue.component('global-login', GlobalLogin)

// 全局混入，提供登录相关方法和安卓设备适配
Vue.mixin({
  mixins: [androidMixin], // 混入安卓设备适配
  methods: {
    // 显示登录弹窗
    showLoginModal() {
      this.$store.dispatch('showLoginModal')
    },
    // 检查是否已登录，未登录则显示登录弹窗
    checkLogin() {
      const isLoggedIn = this.$store.getters.isLoggedIn
      if (!isLoggedIn) {
        this.$store.dispatch('showLoginModal')
      }
      return isLoggedIn
    }
  }
})

App.mpType = 'app'

// #ifndef VUE3
const app = new Vue({
  store,
  ...App
})
app.$mount()
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue'
export function createApp() {
  const app = createSSRApp(App)
  
  // 挂载蓝牙锁服务到全局
  app.config.globalProperties.$lockService = lockService
  app.config.globalProperties.$blueToothManager = blueToothManager
  
  return {
    app
  }
}
// #endif