<template>
	<view class="container page-index" @touchmove.prevent>
		<!-- 页面背景 -->
		<view class="page-background">
			<!-- 背景图片 -->
			<image class="background-image" src="https://wuji1112-1312674791.cos.ap-guangzhou.myqcloud.com/static/编组 <EMAIL>" mode="aspectFill"></image>
		</view>
		
		<!-- 顶部状态栏占位 -->
		<view class="status-bar safe-area-inset-top"></view>
		
		<!-- 页面内容 -->
		<view class="content" @touchmove.stop>
			<!-- 顶部欢迎区域 -->
			<view class="welcome-section">
				<!-- 添加顶部banner -->
				<view class="banner-container" @tap="navigateToZhaoshang">
					<image class="banner-image" src="https://wuji1112-1312674791.cos.ap-guangzhou.myqcloud.com/static/<EMAIL>" mode="contain"></image>
				</view>
				
				<!-- 移除logo部分，因为背景图片已包含logo -->
				<view class="welcome-text">
				</view>
			</view>
			
			<!-- 快捷功能区域 -->
			<view class="quick-actions">
				<!-- 标题移到中部位置 -->
				<view class="scan-title-container">
					<view class="neon-title">点击爱心</view>
					<view class="neon-title mt-xs">扫码开门</view>
				</view>
				
				<!-- 显示扫码按钮，无论是否登录 -->
				<view class="action-container">
					<view class="scan-button-wrapper" @tap="scanQRCode">
						<view class="scan-icon-container">
							<text class="material-icons heart-icon">favorite</text>
						</view>
					</view>
				</view>
				
				<!-- 添加协议提示文字 -->
				<view class="agreement-tip text-tertiary">
					<text>点击开门即视为同意《用户协议》及《隐私协议》</text>
				</view>
			</view>
			
			<!-- 底部空白区域，确保内容不被遮挡 -->
			<view class="bottom-space"></view>
		</view>
		
		<!-- 自定义TabBar -->
		<custom-tab-bar ref="tabBar"></custom-tab-bar>
		
		<!-- 全局登录组件 -->
		<global-login ref="globalLogin"></global-login>
	</view>
</template>

<script>
	import customTabBar from "@/custom-tab-bar/index.vue"
	import QRScanner from '@/static/js/qrScanner.js';
	import API from '@/static/js/api.js';
	import { lockService, blueToothManager } from '@/utils/index.js';
	import { mapState, mapMutations, mapActions } from 'vuex';
	
	export default {
		components: {
			customTabBar
		},
		data() {
			return {
				title: '首页',
				isNavigating: false, // 防止重复导航
				bluetoothInitialized: false,
				bluetoothInitializing: false
			}
		},
		computed: {
			...mapState({
				isLoggedIn: state => state.isLoggedIn,
				userInfo: state => state.userInfo
			})
		},
		onLoad() {
			console.log('首页加载');
			// 检查登录状态
			this.checkLoginStatus();
			
			// 在页面加载时初始化蓝牙环境
			this.initBluetooth();
		},
		onShow() {
			// 重置导航状态
			this.isNavigating = false;

			// 确保TabBar正确显示当前页面
			this.updateTabBar();

			// 检查登录状态
			this.checkLoginStatus();

			// 移除自动弹出登录框的逻辑，让用户先体验功能
			// 符合微信小程序审核规范：用户应先浏览体验功能，再选择是否登录

			// 检查是否有存储的扫码参数
			this.checkLastScanParams();

			// 断开可能存在的蓝牙连接，避免在首页仍然尝试连接设备
			this.disconnectBluetooth();
		},
		onReady() {
			// 页面渲染完成后，再次更新TabBar
			setTimeout(() => {
				this.updateTabBar();
			}, 100);
		},
		methods: {
			...mapActions([
				'checkLoginStatus',
				'showLoginModal',
				'hideLoginModal'
			]),
			
			// 更新TabBar状态
			updateTabBar() {
				if (this.$refs.tabBar) {
					try {
						// 如果有updateCurrentPath方法，则调用
						if (typeof this.$refs.tabBar.updateCurrentPath === 'function') {
							this.$refs.tabBar.updateCurrentPath();
						}
						
						// 如果有startPathCheck方法，则调用
						if (typeof this.$refs.tabBar.startPathCheck === 'function') {
							this.$refs.tabBar.startPathCheck();
						}
						
						// 强制重新渲染TabBar
						if (typeof this.$refs.tabBar.$forceUpdate === 'function') {
							this.$refs.tabBar.$forceUpdate();
						}
					} catch (error) {
						console.error('更新TabBar状态失败:', error);
					}
				}
			},
			navigateTo(url) {
				// 防止重复导航
				if (this.isNavigating) return;
				
				this.isNavigating = true;
				
				// 使用setTimeout确保导航完成后才重置状态
				setTimeout(() => {
					uni.navigateTo({
						url: url,
						success: () => {
							console.log('导航成功:', url);
						},
						fail: (err) => {
							console.error('导航失败:', err);
							this.isNavigating = false;
						},
						complete: () => {
							// 导航完成后延迟重置状态，避免快速点击
							setTimeout(() => {
								this.isNavigating = false;
							}, 500);
						}
					});
				}, 50);
			},
			// 导航到招商页面
			navigateToZhaoshang() {
				// 防止重复导航
				if (this.isNavigating) return;
				
				this.isNavigating = true;
				
				setTimeout(() => {
					uni.navigateTo({
						url: '/packageA/pages/zhaoshang/index',
						success: () => {
							console.log('导航到招商页面成功');
						},
						fail: (err) => {
							console.error('导航到招商页面失败:', err);
							this.isNavigating = false;
							
							// 如果导航失败，尝试使用switchTab
							if (err.errMsg && err.errMsg.includes('routeDone')) {
								console.log('尝试使用switchTab导航');
								uni.switchTab({
									url: '/pages/index/index',
									complete: () => {
										setTimeout(() => {
											uni.navigateTo({
												url: '/packageA/pages/zhaoshang/index'
											});
										}, 300);
									}
								});
							}
						},
						complete: () => {
							// 导航完成后延迟重置状态
							setTimeout(() => {
								this.isNavigating = false;
							}, 500);
						}
					});
				}, 50);
			},
			
			// 自动显示登录框
			autoShowLogin() {
				// 如果未登录，自动弹出登录框
				if (!this.isLoggedIn) {
					this.showLoginModal();
				}
			},
			
			// 扫描二维码
			scanQRCode() {
				// 防止重复操作
				if (this.isNavigating) return;

				// 检查是否已登录
				if (!this.isLoggedIn) {
					// 未登录，先显示功能说明，让用户了解价值后再选择是否登录
					this.showFunctionIntroduction();
					return;
				}

				// 已登录，直接扫码
				this.startScan();
			},

			// 显示功能介绍，符合微信审核规范
			showFunctionIntroduction() {
				// 直接显示登录框，但保留用户选择权
				this.showLoginModal();
			},
			
			// 开始扫码
			startScan() {
				try {
					// 获取系统信息，检查平台类型
					const systemInfo = uni.getSystemInfoSync();
					const platform = systemInfo.platform;
					
					// 判断是否是鸿蒙系统
					const isHarmonyOS = platform === 'ohos' || (systemInfo.system && systemInfo.system.toLowerCase().includes('harmony'));
					
					console.log('系统平台:', platform, '是否鸿蒙系统:', isHarmonyOS);
					
					// 使用QRScanner工具扫码，不在这里显示loading，而是通过参数传递给QRScanner
					QRScanner.scan({
						onlyFromCamera: true,
						showLoading: true, // 通过参数控制是否显示loading
						success: (result) => {
							console.log('扫码成功:', result);
							
							// 处理扫码结果
							QRScanner.handleScanResult(result)
								.then(res => {
									console.log('处理扫码结果成功:', res);
								})
								.catch(err => {
									console.error('处理扫码结果失败:', err);
									
									// 显示错误提示
									uni.showToast({
										title: '无法识别的二维码',
										icon: 'none'
									});
								});
						},
						fail: (err) => {
							console.log('扫码失败:', err);
							
							// 用户取消扫码，不显示错误提示
							if (err.errMsg && err.errMsg.includes('cancel')) {
								console.log('用户取消了扫码');
								return;
							}
							
							// 对于鸿蒙系统的特殊处理
							if (isHarmonyOS && err.errMsg === 'scanCode:fail') {
								console.log('鸿蒙系统扫码失败，可能是权限问题，尝试使用替代方案');
								
								// 提示用户
								uni.showModal({
									title: '提示',
									content: '扫码失败，请确保已授予相机权限',
									confirmText: '重试',
									cancelText: '取消',
									success: (res) => {
										if (res.confirm) {
											// 用户点击重试，延迟一下再次尝试扫码
											setTimeout(() => {
												// 第二次尝试时，使用不同的配置
												uni.scanCode({
													onlyFromCamera: true,
													scanType: ['qrCode'],
													success: (scanRes) => {
														console.log('重试扫码成功:', scanRes);
														const result = QRScanner.parseQRResult(scanRes.result);
														QRScanner.handleScanResult(result);
													},
													fail: (scanErr) => {
														console.error('重试扫码失败:', scanErr);
														uni.showToast({
															title: '扫码失败，请稍后再试',
															icon: 'none'
														});
													}
												});
											}, 500);
										}
									}
								});
								return;
							}
							
							// 其他错误才显示提示
							uni.showToast({
								title: '扫码失败，请重试',
								icon: 'none'
							});
						}
					});
				} catch (error) {
					// 捕获可能的异常
					console.error('扫码过程发生异常:', error);
					uni.hideLoading(); // 确保loading被关闭
					uni.showToast({
						title: '扫码功能异常，请重试',
						icon: 'none'
					});
				}
			},
			
			// 新增：检查上次扫码参数
			checkLastScanParams() {
				// 检查登录状态
				const token = uni.getStorageSync('token');
				if (!token) {
					return; // 未登录不处理
				}
				
				// 获取上次扫码参数
				const lastScanParams = uni.getStorageSync('lastScanParams');
				if (lastScanParams) {
					// 检查时间戳，如果超过10分钟则不处理
					const now = Date.now();
					const scanTime = lastScanParams.timestamp || 0;
					
					if (now - scanTime < 10 * 60 * 1000) { // 10分钟内的扫码记录
						console.log('检测到上次扫码参数，准备跳转:', lastScanParams);
						
						// 清除扫码参数，避免重复跳转
						uni.removeStorageSync('lastScanParams');
						
						// 构建跳转URL
						let url = '/pages/scan/device?';
						if (lastScanParams.mac) {
							url += `mac=${lastScanParams.mac}&`;
						}
						if (lastScanParams.qrCode) {
							url += `qrCode=${encodeURIComponent(lastScanParams.qrCode)}&`;
						}
						
						// 去掉最后一个&
						if (url.endsWith('&')) {
							url = url.slice(0, -1);
						}
						
						// 延迟跳转，确保页面已完全加载
						setTimeout(() => {
							uni.navigateTo({
								url: url,
								fail: (err) => {
									console.error('跳转到设备页面失败:', err);
								}
							});
						}, 500);
					} else {
						// 超时的扫码记录，清除
						uni.removeStorageSync('lastScanParams');
					}
				}
			},
			
			/**
			 * 断开蓝牙连接
			 */
			disconnectBluetooth() {
				console.log('尝试断开蓝牙连接 - disconnectDevice方法');
				
				// 使用blueToothManager断开连接
				if (blueToothManager && typeof blueToothManager.disconnect === 'function') {
					try {
						blueToothManager.disconnect().catch(err => {
							console.warn('断开蓝牙连接失败:', err);
						});
					} catch (e) {
						console.error('调用blueToothManager.disconnect出错:', e);
					}
				} else {
					console.log('blueToothManager不可用或没有disconnect方法');
					
					// 如果两种方法都不可用，尝试使用uni API直接断开
					try {
						// 获取已连接的蓝牙设备
						uni.getConnectedBluetoothDevices({
							success: (res) => {
								if (res.devices && res.devices.length > 0) {
									// 断开所有连接的设备
									res.devices.forEach(device => {
										uni.closeBLEConnection({
											deviceId: device.deviceId,
											success: () => {
												console.log('成功断开设备连接:', device.deviceId);
											},
											fail: (err) => {
												console.warn('断开设备连接失败:', err);
											}
										});
									});
								}
							},
							fail: (err) => {
								console.warn('获取已连接的蓝牙设备失败:', err);
							}
						});
					} catch (e) {
						console.error('使用uni API断开蓝牙连接失败:', e);
					}
				}
			},
			
			/**
			 * 初始化蓝牙环境
			 */
			initBluetooth() {
				console.log('初始化蓝牙环境');
				
				// 检查当前平台是否为Windows开发环境
				try {
					const systemInfo = wx.getAppBaseInfo ? wx.getAppBaseInfo() : uni.getSystemInfoSync();
					console.log('首页 - 系统信息:', systemInfo);
					
					const isDevTools = systemInfo.platform === 'devtools';
					const isMac = systemInfo.platform === 'mac';
					// 修复system可能为undefined的问题
					const isWindows = systemInfo.platform === 'windows' || 
					                 (systemInfo.system && typeof systemInfo.system === 'string' && 
					                  systemInfo.system.toLowerCase().includes('windows'));
					
					// 在非Mac开发环境下自动切换到模拟模式
					if (isDevTools && !isMac) {
						console.log('首页 - 非Mac开发环境自动切换到模拟模式');
						this.isBluetoothAvailable = true;
						this.isSimulatedMode = true;
						
						// 设置全局模拟模式标志
						if (typeof global !== 'undefined') {
							global.isSimulatedMode = true;
						}
						
						// 显示提示
						uni.showToast({
							title: '当前平台不支持蓝牙调试，已切换到模拟模式',
							icon: 'none',
							duration: 3000
						});
						
						return Promise.resolve({
							success: true,
							message: '蓝牙环境初始化成功(模拟模式)',
							simulated: true
						});
					}
				} catch (e) {
					console.error('获取系统信息失败:', e);
					// 出错时也切换到模拟模式
					this.isBluetoothAvailable = true;
					this.isSimulatedMode = true;
					
					return Promise.resolve({
						success: true,
						message: '蓝牙环境初始化成功(模拟模式-错误恢复)',
						simulated: true
					});
				}
				
				// 调用锁服务初始化
				return lockService.init()
					.then(res => {
						console.log('蓝牙初始化成功:', res);
						this.isBluetoothAvailable = true;
						
						// 如果是模拟模式，设置标志
						if (res.simulated) {
							this.isSimulatedMode = true;
							console.log('使用模拟模式');
							
							// 设置全局模拟模式标志
							if (typeof global !== 'undefined') {
								global.isSimulatedMode = true;
							}
						}
						
						return res;
					})
					.catch(err => {
						console.error('蓝牙不可用:', err);
						
						// 即使蓝牙不可用，也设置为可用并使用模拟模式
						this.isBluetoothAvailable = true;
						this.isSimulatedMode = true;
						
						// 设置全局模拟模式标志
						if (typeof global !== 'undefined') {
							global.isSimulatedMode = true;
						}
						
						console.log('切换到模拟模式');
						
						// 不显示错误提示，而是显示已切换到模拟模式
						uni.showToast({
							title: '已切换到模拟模式',
							icon: 'none',
							duration: 3000
						});
						
						return {
							success: true,
							message: '蓝牙环境初始化成功(模拟模式-错误恢复)',
							simulated: true
						};
					});
			},
			
			/**
			 * 初始化BLELockController
			 * @returns {Promise} 初始化结果
			 * @private
			 */
			_initBLEController() {
				return new Promise((resolve, reject) => {
					try {
						// 使用已导入的blueToothManager
						if (!blueToothManager) {
							console.error('blueToothManager不可用');
							return reject(new Error('blueToothManager不可用'));
						}
						
						// 初始化blueToothManager
						if (typeof blueToothManager.init === 'function' && !blueToothManager.isInitialized) {
							blueToothManager.init();
							blueToothManager.isInitialized = true;
						}
						
						// 保存到全局对象
						global.blueToothManager = blueToothManager;
						
						console.log('blueToothManager初始化成功');
						resolve(blueToothManager);
					} catch (e) {
						console.error('初始化blueToothManager失败:', e);
						reject(new Error('初始化blueToothManager失败: ' + e.message));
					}
				});
			},
			
			/**
			 * 跳转到智能锁页面
			 */
			navigateToLock() {
				// 防止重复点击
				if (this.isNavigating) return;
				
				this.isNavigating = true;
				
				// 跳转到扫码页面（原来是锁页面，但锁页面不存在，所以改为扫码页面）
				uni.navigateTo({
					url: '/pages/scan/scan',
					complete: () => {
						// 重置导航状态
						setTimeout(() => {
							this.isNavigating = false;
						}, 500);
					}
				});
			},
			/**
			 * 检查蓝牙是否可用
			 * @returns {Promise} 蓝牙可用状态
			 * @private
			 */
			_checkBluetoothAvailable() {
				return new Promise((resolve, reject) => {
					// 初始化蓝牙适配器
					uni.openBluetoothAdapter({
						success: (res) => {
							console.log('蓝牙适配器初始化成功:', res);
							
							// 获取蓝牙适配器状态
							uni.getBluetoothAdapterState({
								success: (res) => {
									console.log('蓝牙适配器状态:', res);
									
									if (!res.available) {
										// 提示用户开启蓝牙
										uni.showModal({
											title: '提示',
											content: '请开启蓝牙后重试',
											showCancel: false,
											success: () => {
												reject(new Error('蓝牙不可用'));
											}
										});
										return;
									}
									
									resolve();
								},
								fail: (err) => {
									console.error('获取蓝牙适配器状态失败:', err);
									reject(err);
								}
							});
						},
						fail: (err) => {
							console.error('蓝牙适配器初始化失败:', err);
							
							// 检查错误类型
							if (err.errCode === 10001) {
								// 蓝牙未开启
								uni.showModal({
									title: '提示',
									content: '请开启蓝牙后重试',
									showCancel: false,
									success: () => {
										reject(new Error('蓝牙未开启'));
									}
								});
							} else if (err.errCode === 10002) {
								// 没有蓝牙权限
								uni.showModal({
									title: '提示',
									content: '请授予蓝牙权限后重试',
									showCancel: false,
									success: () => {
										// 打开应用设置页面
										uni.openSetting({
											success: (res) => {
												console.log('打开设置页面成功:', res);
											},
											fail: (err) => {
												console.error('打开设置页面失败:', err);
											}
										});
										
										reject(new Error('没有蓝牙权限'));
									}
								});
							} else {
								reject(err);
							}
						}
					});
				});
			}
		}
	};
</script>

<style>
	/* CSS变量定义 */
	page {
		--primary-light: #A875FF;
		--neon-pink: #ff36f9;
		height: 100%;
		overflow: hidden;
	}
	
	/* 页面基础样式 */
	.page-index {
		padding-top: 100rpx; /* 减少顶部间距 */
		padding-bottom: calc(120rpx + env(safe-area-inset-bottom)); /* 增加底部padding，为TabBar留出空间 */
		color: #ffffff;
		height: 100vh;
		min-height: 100vh;
		box-sizing: border-box;
		position: relative;
		overflow: hidden; /* 防止页面滚动 */
		touch-action: none; /* 禁止触摸操作 */
	}
	
	/* 页面背景样式 */
	.page-background {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		z-index: 0;
	}
	
	/* 背景图片样式 */
	.background-image {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		z-index: 0;
		object-fit: cover; /* 确保图片覆盖整个容器 */
	}
	
	.status-bar {
		width: 100%;
		background: transparent; /* 使顶部安全区域透明，与页面背景一致 */
		backdrop-filter: none;
		-webkit-backdrop-filter: none;
		position: fixed;
		top: 0;
		left: 0;
		z-index: 100;
	}
	
	.content {
		padding: 20rpx 30rpx;
		position: relative;
		z-index: 2;
		height: calc(100vh - 100rpx - 120rpx - env(safe-area-inset-bottom)); /* 修改高度计算，为TabBar留出空间 */
		display: flex;
		flex-direction: column;
		overflow: hidden; /* 防止内容滚动 */
		touch-action: none; /* 禁止触摸操作 */
	}
	
	/* 欢迎区域 */
	.welcome-section {
		margin-bottom: 0; /* 减少底部间距 */
		margin-top: 0; /* 减少顶部间距 */
	}
	
	/* 顶部banner样式 */
	.banner-container {
		position: relative;
		z-index: 2;
		margin-bottom: 10rpx; /* 减小底部间距 */
		margin-top: 30rpx; /* 增加顶部间距，让banner往下移 */
		width: 100%;
		display: flex;
		justify-content: center;
		overflow: visible;
		padding: 0;
	}
	
	.banner-image {
		width: 100%;
		max-width: 1000rpx;
		height: 180rpx;
		object-fit: contain;
	}
	
	.welcome-text {
		margin-left: 10rpx;
	}
	
	.user-info {
		position: relative;
		z-index: 2;
		margin-bottom: 20rpx;
	}
	
	.user-summary {
		margin-top: 30rpx;
		padding: 30rpx;
		background-color: rgba(255, 255, 255, 0.08);
		border-radius: 20rpx;
		backdrop-filter: blur(10px);
		-webkit-backdrop-filter: blur(10px);
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);
	}
	
	/* 快捷功能区域 */
	.quick-actions {
		margin-top: 530rpx; /* 从500rpx增加到530rpx，使整体下移 */
		margin-bottom: 40rpx;
		flex: 1;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: flex-start; /* 从顶部开始排列 */
	}
	
	/* 标题容器 */
	.scan-title-container {
		margin-bottom: 40rpx;
		text-align: center;
		width: 100%;
		position: relative;
		top: 50rpx; /* 从30rpx增加到50rpx，使标题进一步向下移动 */
	}
	
	/* 增加标题之间的间距 */
	.neon-title.mt-xs {
		margin-top: 15rpx; /* 增加两行标题之间的间距 */
	}
	
	/* 标题下方的爱心图标容器 */
	.heart-icon-container {
		margin-top: 20rpx;
		display: flex;
		justify-content: center;
		align-items: center;
	}
	
	/* 标题下方的爱心图标 */
	.title-heart-icon {
		font-size: 80rpx;
		color: #d4a9ff;
		text-shadow: 
			0 0 15rpx rgba(212, 169, 255, 0.7),
			0 0 30rpx rgba(212, 169, 255, 0.5);
		animation: title-heart-pulse 2s ease-in-out infinite;
	}
	
	@keyframes title-heart-pulse {
		0%, 100% {
			transform: scale(1);
			opacity: 0.9;
		}
		50% {
			transform: scale(1.1);
			opacity: 1;
		}
	}
	
	/* 霓虹灯标题效果增强 */
	.neon-title {
		font-size: 50rpx; /* 增大字体大小 */
		line-height: 1.3;
		font-weight: 500;
		text-align: center;
		color: #fff;
		font-family: "YouYuan", "幼圆", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", sans-serif; /* 添加幼圆字体 */
		text-shadow: 
			0 0 5rpx rgba(255, 255, 255, 0.6),
			0 0 10rpx rgba(255, 255, 255, 0.4), 
			0 0 15rpx rgba(168, 117, 255, 0.5),
			0 0 25rpx rgba(168, 117, 255, 0.4);
		letter-spacing: 8rpx; /* 增加字间距 */
		animation: soft-flicker 4s infinite alternate;
		transform: scale(1, 0.95); /* 稍微压扁字体，模拟圆体效果 */
	}
	
	/* 添加霓虹灯闪烁动画 */
	@keyframes soft-flicker {
		0%, 100% {
			text-shadow: 
				0 0 5rpx rgba(255, 255, 255, 0.6),
				0 0 10rpx rgba(255, 255, 255, 0.4), 
				0 0 15rpx rgba(168, 117, 255, 0.5),
				0 0 25rpx rgba(168, 117, 255, 0.4);
		}
		50% {
			text-shadow: 
				0 0 8rpx rgba(255, 255, 255, 0.7),
				0 0 15rpx rgba(255, 255, 255, 0.5), 
				0 0 20rpx rgba(168, 117, 255, 0.6),
				0 0 30rpx rgba(168, 117, 255, 0.5);
		}
	}
	
	/* 扫码按钮容器 */
	.action-container {
		display: flex;
		justify-content: center;
		align-items: center;
		margin-top: -10rpx; /* 从10rpx改为-10rpx，使按钮往上移 */
		margin-bottom: 10rpx; /* 从20rpx改为10rpx，减少底部间距 */
		width: 100%;
		position: relative; /* 添加相对定位 */
		left: 0; /* 确保不偏移 */
	}
	
	/* 主扫码按钮样式 */
	.scan-button-wrapper {
		position: relative;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		width: 320rpx; /* 从300rpx增加到320rpx */
		height: 320rpx; /* 从300rpx增加到320rpx */
		margin-top: 0; /* 移除顶部间距 */
		padding: 0; /* 移除内边距 */
		transition: transform 0.3s, box-shadow 0.3s;
		margin: 0 auto; /* 确保按钮居中 */
		left: 0; /* 确保不偏移 */
		right: 0; /* 确保不偏移 */
	}
	
	.scan-button-wrapper:active {
		transform: scale(0.96);
	}
	
	/* 扫码按钮内层 */
	.scan-button-inner {
		position: relative;
		display: flex;
		justify-content: center;
		align-items: center;
		width: 220rpx;
		height: 220rpx;
		border-radius: 50%;
		background: rgba(168, 117, 255, 0.12);
		box-shadow: 
			0 12rpx 36rpx rgba(0, 0, 0, 0.35),
			0 20rpx 50rpx rgba(0, 0, 0, 0.2),
			inset 0 2rpx 10rpx rgba(255, 255, 255, 0.5),
			inset 0 -5rpx 15rpx rgba(0, 0, 0, 0.3);
		z-index: 2;
		overflow: visible;
		position: relative;
		animation: circle-breathing 4s cubic-bezier(0.4, 0, 0.6, 1) infinite; /* 改为4秒，使用更平滑的缓动函数 */
	}
	
	/* 修改背景圆圈呼吸灯效果，显著增强效果并与爱心同步 */
	@keyframes circle-breathing {
		0% {
			background: rgba(168, 117, 255, 0.08); /* 降低初始亮度 */
			box-shadow: 
				0 12rpx 36rpx rgba(0, 0, 0, 0.35),
				0 20rpx 50rpx rgba(0, 0, 0, 0.2),
				inset 0 2rpx 10rpx rgba(255, 255, 255, 0.4),
				inset 0 -5rpx 15rpx rgba(0, 0, 0, 0.3);
		}
		25% { /* 放大过程占25% */
			background: rgba(168, 117, 255, 0.45); /* 显著增强亮度 */
			box-shadow: 
				0 12rpx 36rpx rgba(168, 117, 255, 0.4), /* 添加紫色外发光 */
				0 20rpx 50rpx rgba(168, 117, 255, 0.25),
				inset 0 2rpx 20rpx rgba(255, 255, 255, 0.9), /* 增强内发光 */
				inset 0 -5rpx 15rpx rgba(0, 0, 0, 0.2);
		}
		50% { /* 保持最大状态占25% */
			background: rgba(168, 117, 255, 0.45); /* 保持增强的亮度 */
			box-shadow: 
				0 12rpx 36rpx rgba(168, 117, 255, 0.4), /* 添加紫色外发光 */
				0 20rpx 50rpx rgba(168, 117, 255, 0.25),
				inset 0 2rpx 20rpx rgba(255, 255, 255, 0.9), /* 增强内发光 */
				inset 0 -5rpx 15rpx rgba(0, 0, 0, 0.2);
		}
		75% { /* 缩小过程占25% */
			background: rgba(168, 117, 255, 0.08); /* 降低初始亮度 */
			box-shadow: 
				0 12rpx 36rpx rgba(0, 0, 0, 0.35),
				0 20rpx 50rpx rgba(0, 0, 0, 0.2),
				inset 0 2rpx 10rpx rgba(255, 255, 255, 0.4),
				inset 0 -5rpx 15rpx rgba(0, 0, 0, 0.3);
		}
		100% {
			background: rgba(168, 117, 255, 0.08); /* 降低初始亮度 */
			box-shadow: 
				0 12rpx 36rpx rgba(0, 0, 0, 0.35),
				0 20rpx 50rpx rgba(0, 0, 0, 0.2),
				inset 0 2rpx 10rpx rgba(255, 255, 255, 0.4),
				inset 0 -5rpx 15rpx rgba(0, 0, 0, 0.3);
		}
	}
	
	/* 图标样式 */
	.scan-icon-container {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 160rpx; /* 从140rpx增加到160rpx */
		height: 160rpx; /* 从140rpx增加到160rpx */
		z-index: 3;
		position: relative; /* 添加相对定位 */
		margin: 0 auto; /* 确保容器居中 */
		left: 0; /* 确保不偏移 */
		right: 0; /* 确保不偏移 */
	}
	
	.scan-icon-container .material-icons.heart-icon {
		font-size: 160rpx;
		color: #c49aef; /* 使用更深的紫色 */
		background: none;
		-webkit-background-clip: initial;
		background-clip: initial;
		text-shadow: none;
		animation: heart-natural-grow 4s cubic-bezier(0.4, 0, 0.6, 1) infinite; /* 改为4秒，使用更平滑的缓动函数 */
		transform-style: preserve-3d;
		filter: none;
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%); /* 保持居中 */
		transform-origin: center center;
		width: 100%; /* 确保图标占满容器宽度 */
		text-align: center; /* 确保文本居中 */
		display: flex; /* 添加flex布局 */
		justify-content: center; /* 水平居中 */
		align-items: center; /* 垂直居中 */
		height: 100%; /* 确保高度占满容器 */
		box-sizing: border-box; /* 确保盒模型计算正确 */
	}
	
	/* 重新设计爱心放大动画 - 自然放大效果，放大和缩小速度一致且更慢 */
	@keyframes heart-natural-grow {
		0% {
			transform: translate(-50%, -50%) scale(1);
			color: #c49aef;
		}
		25% { /* 放大过程占25% */
			transform: translate(-50%, -50%) scale(1.35);
			color: #e5bcf7;
		}
		50% { /* 保持最大状态占25% */
			transform: translate(-50%, -50%) scale(1.35);
			color: #e5bcf7;
		}
		75% { /* 缩小过程占25% */
			transform: translate(-50%, -50%) scale(1);
			color: #c49aef;
		}
		100% { /* 保持原始状态 */
			transform: translate(-50%, -50%) scale(1);
			color: #c49aef;
		}
	}
	
	/* 文本容器 */
	.scan-text {
		display: flex;
		flex-direction: column;
		align-items: center;
		margin-top: 40rpx;
	}
	
	.scan-text .action-title {
		font-weight: 600;
		font-size: 36rpx;
		margin-bottom: 10rpx;
		color: rgba(255, 255, 255, 0.9);
		letter-spacing: 2rpx;
	}
	
	.scan-text .text-tertiary {
		font-size: 28rpx;
		color: rgba(255, 255, 255, 0.5);
	}
	
	/* Material Icons 字体 */
	@font-face {
		font-family: 'Material Icons';
		font-style: normal;
		font-weight: 400;
		src: url(https://fonts.gstatic.com/s/materialicons/v139/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format('woff2');
		font-display: block;
	}

	.material-icons {
		font-family: 'Material Icons';
		font-weight: normal;
		font-style: normal;
		font-size: 48rpx;
		line-height: 1;
		letter-spacing: normal;
		text-transform: none;
		display: inline-block;
		white-space: nowrap;
		word-wrap: normal;
		direction: ltr;
		-webkit-font-smoothing: antialiased;
		-moz-osx-font-smoothing: grayscale;
		text-rendering: optimizeLegibility;
	}
	
	.material-icons.primary-light {
		color: var(--primary-light, #A875FF);
	}
	
	/* 状态标签 */
	.status {
		padding: 8rpx 20rpx;
		border-radius: 30rpx;
		font-size: 24rpx;
	}
	
	.status.active {
		background-color: rgba(168, 117, 255, 0.2);
		color: var(--primary-light, #A875FF);
	}
	
	/* 辅助类 */
	.flex {
		display: flex;
	}
	
	.flex-col {
		display: flex;
		flex-direction: column;
	}
	
	.justify-between {
		justify-content: space-between;
	}
	
	.items-center {
		align-items: center;
	}
	
	.text-center {
		text-align: center;
	}
	
	.mt-xs {
		margin-top: 8rpx;
	}
	
	.mt-sm {
		margin-top: 16rpx;
	}
	
	.mt-md {
		margin-top: 30rpx;
	}
	
	.mt-lg {
		margin-top: 40rpx; /* 减小顶部大间距 */
	}
	
	.mb-sm {
		margin-bottom: 16rpx;
	}
	
	.gap-md {
		gap: 24rpx;
	}
	
	.flex-1 {
		flex: 1;
	}
	
	/* 标题和文本样式 */
	.title-lg {
		font-size: 46rpx;
		font-weight: 600;
		color: #ffffff;
		margin-bottom: 10rpx;
	}
	
	.title-md {
		font-size: 38rpx;
		font-weight: 600;
		color: #ffffff;
	}
	
	.title-sm {
		font-size: 32rpx;
		font-weight: 500;
		color: #ffffff;
		margin-bottom: 8rpx;
	}
	
	.text-secondary {
		color: rgba(255, 255, 255, 0.7);
	}
	
	.text-tertiary {
		color: rgba(255, 255, 255, 0.5);
	}
	
	.primary-light {
		color: var(--primary-light, #A875FF);
	}
	
	/* 底部空间 */
	.bottom-space {
		height: 20rpx;
	}
	
	/* 卡片样式 */
	.card {
		border-radius: 20rpx;
		background-color: rgba(255, 255, 255, 0.08);
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);
	}
	
	/* 协议提示文字 */
	.agreement-tip {
		margin-top: 20rpx; /* 从30rpx减少到20rpx，减少与按钮的间距 */
		text-align: center;
		font-size: 24rpx;
		color: rgba(255, 255, 255, 0.6);
	}
</style>