/**
 * 蓝牙锁管理工具类
 * 基于uni-app蓝牙API封装，提供蓝牙锁设备的连接、认证和控制功能
 */

// 蓝牙服务和特征值UUID
const SERVICE_UUID = "0000ffc0-0000-1000-8000-00805f9b34fb"
const WRITE_CHAR_UUID = "0000ffc1-0000-1000-8000-00805f9b34fb"  // 用于写入命令
const NOTIFY_CHAR_UUID = "0000ffc2-0000-1000-8000-00805f9b34fb"  // 用于接收通知

// 命令常量
const AUTH_CMD = "8efb06170ca968237b4cd22ddffa665b4fe8"  // 认证命令

// 锁协议命令
const OPEN_LOCK_CMD_PREFIX = "ff4f50454e00"  // 开锁命令前缀 (OPEN)
const OPEN_LOCK_CMD_SUFFIX = "0000fe"  // 开锁命令后缀
const QUERY_DEVICE_INFO_CMD = "66f00077"  // 查询设备信息
const QUERY_LOCK_STATUS_CMD = "66f10077"  // 查询锁状态

// 蜂鸣器命令
const BUZZER_ON_CMD = "66f000bb"  // 蜂鸣器开启命令
const BUZZER_OFF_CMD = "66f000bc"  // 蜂鸣器关闭命令

// 已知的智能锁设备前缀列表
const SMART_LOCK_PREFIXES = ['D30', 'Lock', 'Smart', 'BLE', 'JY', 'TY', 'JYLOCK', 'CASTLE'];

// 检查当前平台是否支持蓝牙调试
const isMacPlatform = () => {
  // 获取系统信息
  try {
    // 使用新的API获取平台信息
    const systemInfo = wx.getAppBaseInfo ? wx.getAppBaseInfo() : uni.getSystemInfoSync();
    return systemInfo.platform === 'mac';
  } catch (e) {
    console.error('获取系统信息失败:', e);
    return false;
  }
};

// 是否为调试环境
const isDevEnvironment = () => {
  // 判断是否为开发者工具环境
  try {
    // 使用新的API获取平台信息
    const systemInfo = wx.getAppBaseInfo ? wx.getAppBaseInfo() : uni.getSystemInfoSync();
    return systemInfo.platform === 'devtools';
  } catch (e) {
    console.error('获取系统信息失败:', e);
    return false;
  }
};

// 检查设备是否为智能锁
const isSmartLockDevice = (deviceName) => {
  if (!deviceName) return false;
  
  const upperName = deviceName.toUpperCase();
  for (const prefix of SMART_LOCK_PREFIXES) {
    if (upperName.includes(prefix.toUpperCase())) {
      return true;
    }
  }
  return false;
};

/**
 * 蓝牙锁管理类
 */
class BlueToothManager {
  constructor() {
    // 初始化属性
    this.isInitialized = false;
    this.isScanning = false;
    this.isConnecting = false;
    this.isConnected = false;
    this.deviceId = '';
    this.serviceId = '';
    this.writeCharacteristicId = '';
    this.notifyCharacteristicId = '';
    this.deviceMac = '';
    this.expectedDeviceName = ''; // 预期设备名称
    this.onConnectedCallback = null;
    this.onDisconnectedCallback = null;
    this.onCharacteristicValueChangeCallback = null;
    this.onBluetoothAdapterStateChangeCallback = null;
    this.onBLEConnectionStateChangeCallback = null;
    this.onErrorCallback = null;
    this.onLockStatusChangeCallback = null; // 锁状态变化回调
    this.onBatteryUpdateCallback = null; // 电池电量更新回调
    this.onDeviceFoundCallback = null; // 设备发现回调
    
    // 扫描控制变量
    this.scanTimeoutId = null;
    this.scanStartTime = 0;
    this.scanAttempts = 0;
    
    // 是否支持蓝牙调试
    this.supportBluetooth = isMacPlatform() || !isDevEnvironment();
    
    console.log('蓝牙管理器初始化，当前平台是否支持蓝牙:', this.supportBluetooth);
  }
  
  /**
   * 设置回调函数
   * @param {Object} callbacks 回调函数对象
   */
  setCallbacks(callbacks) {
    this.onConnectedCallback = callbacks.onConnected;
    this.onDisconnectedCallback = callbacks.onDisconnected;
    this.onCharacteristicValueChangeCallback = callbacks.onCharacteristicValueChange;
    this.onBluetoothAdapterStateChangeCallback = callbacks.onBluetoothAdapterStateChange;
    this.onBLEConnectionStateChangeCallback = callbacks.onBLEConnectionStateChange;
    this.onErrorCallback = callbacks.onError;
    this.onLockStatusChangeCallback = callbacks.onLockStatusChange; // 锁状态变化回调
    this.onBatteryUpdateCallback = callbacks.onBatteryUpdate; // 电池电量更新回调
    this.onDeviceFoundCallback = callbacks.onDeviceFound; // 设备发现回调
    return this;
  }
  
  /**
   * 设置预期设备名称
   * @param {string} deviceName 设备名称
   */
  setExpectedDeviceName(deviceName) {
    console.log('设置预期设备名称:', deviceName);
    this.expectedDeviceName = deviceName;
    return this;
  }
  
  /**
   * 初始化蓝牙模块
   * @returns {Promise} 初始化结果
   */
  init() {
    console.log('蓝牙管理器 - 初始化');
    
    return new Promise((resolve, reject) => {
      // 检查当前平台是否为开发环境
      try {
        const systemInfo = wx.getAppBaseInfo ? wx.getAppBaseInfo() : uni.getSystemInfoSync();
        console.log('蓝牙管理器 - 系统信息:', systemInfo);
        
        const isDevTools = systemInfo.platform === 'devtools';
        const isMac = systemInfo.platform === 'mac';
        // 修复system可能为undefined的问题
        const isWindows = systemInfo.platform === 'windows' || 
                         (systemInfo.system && typeof systemInfo.system === 'string' && 
                          systemInfo.system.toLowerCase().includes('windows'));
        
        // 在非Mac开发环境下自动切换到模拟模式
        if (isDevTools && !isMac) {
          console.log('蓝牙管理器 - 当前为非Mac开发环境，自动切换到模拟模式');
          this.isSimulated = true;
          this.isInitialized = true; // 标记为已初始化
          
          // 显示提示
          uni.showToast({
            title: '当前平台不支持蓝牙调试，已切换到模拟模式',
            icon: 'none',
            duration: 3000
          });
          
          return resolve({
            success: true,
            message: '蓝牙环境初始化成功(模拟模式)',
            simulated: true
          });
        }
      } catch (e) {
        console.error('获取系统信息失败:', e);
        // 出错时也切换到模拟模式
        this.isSimulated = true;
        this.isInitialized = true;
        
        return resolve({
          success: true,
          message: '蓝牙环境初始化成功(模拟模式-错误恢复)',
          simulated: true
        });
      }
      
      // 初始化蓝牙适配器
      uni.openBluetoothAdapter({
        success: (res) => {
          console.log('蓝牙适配器初始化成功:', res);
          this.isInitialized = true;
          
          // 获取蓝牙适配器状态
          this._getBluetoothAdapterState()
            .then(() => {
              resolve({
                success: true,
                message: '蓝牙环境初始化成功'
              });
            })
            .catch(reject);
        },
        fail: (err) => {
          console.error('蓝牙适配器初始化失败:', err);
          
          // 判断是否为Windows平台开发环境
          try {
            const systemInfo = wx.getAppBaseInfo ? wx.getAppBaseInfo() : uni.getSystemInfoSync();
            const isDevTools = systemInfo.platform === 'devtools';
            // 修复system可能为undefined的问题
            const isWindows = systemInfo.platform === 'windows' || 
                             (systemInfo.system && typeof systemInfo.system === 'string' && 
                              systemInfo.system.toLowerCase().includes('windows'));
            
            if (isDevTools && isWindows) {
              console.log('蓝牙管理器 - Windows开发环境自动切换到模拟模式');
              this.isSimulated = true;
              
              // 显示提示
              uni.showToast({
                title: 'Windows平台不支持蓝牙调试，已切换到模拟模式',
                icon: 'none',
                duration: 3000
              });
              
              return resolve({
                success: true,
                message: '蓝牙环境初始化成功(模拟模式)',
                simulated: true
              });
            }
          } catch (e) {
            console.error('获取系统信息失败:', e);
          }
          
          reject({
            success: false,
            message: '蓝牙适配器初始化失败',
            error: err
          });
        }
      });
    });
  }
  
  /**
   * 获取蓝牙适配器状态
   * @returns {Promise} 蓝牙适配器状态
   * @private
   */
  _getBluetoothAdapterState() {
    return new Promise((resolve, reject) => {
      uni.getBluetoothAdapterState({
        success: (res) => {
          console.log('蓝牙适配器状态:', res);
          
          if (!res.available) {
            reject({
              success: false,
              message: '蓝牙适配器不可用',
              error: res
            });
            return;
          }
          
          // 监听蓝牙适配器状态变化
          this._setupBluetoothAdapterStateChange();
          
          // 监听蓝牙连接状态变化
          this._setupBLEConnectionStateChange();
          
          // 设置设备发现监听
          this._setupDeviceDiscovery();
          
          resolve(res);
        },
        fail: (err) => {
          console.error('获取蓝牙适配器状态失败:', err);
          reject({
            success: false,
            message: '获取蓝牙适配器状态失败',
            error: err
          });
        }
      });
    });
  }
  
  /**
   * 设置设备发现监听
   * @private
   */
  _setupDeviceDiscovery() {
    // 移除之前的监听器（如果存在）
    try {
      uni.offBluetoothDeviceFound();
    } catch (e) {
      // 忽略错误
    }
    
    // 设置新的监听器
    uni.onBluetoothDeviceFound((res) => {
      if (!res.devices || res.devices.length === 0) return;
      
      res.devices.forEach(device => {
        const deviceName = device.name || device.localName || '';
        const isLockDevice = isSmartLockDevice(deviceName);
        
        // 添加锁设备标识
        device.isLockDevice = isLockDevice;
        
        // 记录RSSI信号强度
        device.RSSI = device.RSSI || -100;
        
        console.log('发现设备:', deviceName, 'isLock:', isLockDevice, 'RSSI:', device.RSSI);
        
        // 调用设备发现回调
        if (this.onDeviceFoundCallback) {
          this.onDeviceFoundCallback(device);
        }
        
        // 如果有预期设备名称，检查是否匹配
        if (this.expectedDeviceName && 
            (deviceName === this.expectedDeviceName || 
             (isLockDevice && deviceName.includes(this.expectedDeviceName)))) {
          
          // 如果设备匹配且信号强度足够好（大于-80dBm），立即尝试连接
          if (device.RSSI > -80) {
            console.log('找到匹配的设备且信号强度良好，立即尝试连接:', deviceName);
            
            // 如果当前没有连接操作，立即尝试连接
            if (!this.isConnecting && !this.isConnected) {
              this._autoConnectDevice(device);
            }
          }
        }
      });
    });
  }
  
  /**
   * 自动连接发现的设备
   * @param {Object} device 设备对象
   * @private
   */
  _autoConnectDevice(device) {
    // 如果已经在连接或已连接，不重复操作
    if (this.isConnecting || this.isConnected) {
      return;
    }
    
    console.log('自动连接设备:', device.name || device.localName || device.deviceId);
    
    // 标记为正在连接
    this.isConnecting = true;
    
    // 停止扫描
    this.stopScan().catch(() => {});
    
    // 连接设备
    this.connect(device.deviceId)
      .then(res => {
        console.log('自动连接成功:', res);
        // 连接成功后的处理由回调函数负责
      })
      .catch(err => {
        console.error('自动连接失败:', err);
        this.isConnecting = false;
        
        // 如果连接失败，可以选择重新扫描或其他处理
        if (this.scanAttempts < 3) {
          console.log('尝试重新扫描设备');
          this.scanAttempts++;
          this._startScan();
        }
      });
  }
  
  /**
   * 关闭蓝牙模块
   * @returns {Promise} 关闭结果
   */
  close() {
    console.log('关闭蓝牙模块');
    
    // 先断开连接
    if (this.isConnected) {
      return this.disconnect()
        .then(() => this._closeBluetoothAdapter())
        .catch(() => this._closeBluetoothAdapter());
    }
    
    return this._closeBluetoothAdapter();
  }
  
  /**
   * 关闭蓝牙适配器
   * @returns {Promise} 关闭结果
   * @private
   */
  _closeBluetoothAdapter() {
    return new Promise((resolve, reject) => {
      uni.closeBluetoothAdapter({
        success: (res) => {
          console.log('蓝牙适配器关闭成功:', res);

          // 重置初始化状态，确保下次进入时能重新初始化
          this.isInitialized = false;
          this.isConnected = false;
          this.deviceId = null;
          this.serviceId = null;
          this.characteristicId = null;

          console.log('蓝牙管理器状态已重置');

          resolve(res);
        },
        fail: (err) => {
          console.error('蓝牙适配器关闭失败:', err);

          // 即使关闭失败，也要重置状态
          this.isInitialized = false;
          this.isConnected = false;
          this.deviceId = null;
          this.serviceId = null;
          this.characteristicId = null;

          reject(err);
        }
      });
    });
  }
  
  /**
   * 设置蓝牙适配器状态变化监听
   * @private
   */
  _setupBluetoothAdapterStateChange() {
    uni.onBluetoothAdapterStateChange((res) => {
      console.log('蓝牙适配器状态变化:', res);
      
      if (!res.available) {
        // 如果之前是连接状态，触发断开连接回调
        if (this.isConnected) {
          this.isConnected = false;
          this._callDisconnectedCallback({
            deviceId: this.deviceId,
            reason: '蓝牙适配器不可用'
          });
        }
      }
    });
  }
  
  /**
   * 设置蓝牙连接状态变化监听
   * @private
   */
  _setupBLEConnectionStateChange() {
    uni.onBLEConnectionStateChange((res) => {
      console.log('蓝牙连接状态变化:', res);
      
      // 设备连接已断开
      if (!res.connected) {
        this.isConnected = false;
        
        // 调用断开连接回调
        if (this.onBLEConnectionStateChangeCallback) {
          this.onBLEConnectionStateChangeCallback(res);
        }
        
        // 调用断开连接回调
        this._callDisconnectedCallback({
          deviceId: res.deviceId,
          reason: '设备连接已断开'
        });
      }
    });
  }
  
  /**
   * 通过设备名称连接设备
   * @param {string} deviceName 设备名称
   * @returns {Promise} 连接结果
   */
  connectByName(deviceName) {
    console.log('通过设备名称连接设备:', deviceName);
    
    // 确保停止任何正在进行的扫描
    return this.stopScan()
      .catch(() => {
        // 忽略停止扫描时的错误
        console.log('停止扫描失败或没有正在进行的扫描，继续连接');
      })
      .then(() => {
        // 设置预期设备名称
        this.setExpectedDeviceName(deviceName);
        
        return new Promise((resolve, reject) => {
          // 设置扫描超时时间
          const scanTimeout = 15000;
          let deviceFound = false;
          let foundDevice = null;
          
          // 重置扫描尝试次数
          this.scanAttempts = 0;
          
          // 先获取所有已发现的设备
          uni.getBluetoothDevices({
            success: (res) => {
              console.log('获取已发现的设备:', res);
              
              if (res.devices && res.devices.length > 0) {
                // 查找匹配的设备
                const matchedDevice = res.devices.find(device => 
                  device.name === deviceName || device.localName === deviceName ||
                  (isSmartLockDevice(device.name || device.localName) && 
                   (device.name || device.localName || '').includes(deviceName))
                );
                
                if (matchedDevice) {
                  deviceFound = true;
                  foundDevice = matchedDevice;
                  
                  console.log('在已发现设备中找到目标设备:', deviceName, matchedDevice);
                  
                  // 连接设备
                  this.connect(matchedDevice.deviceId)
                    .then((res) => {
                      resolve(res);
                    })
                    .catch((err) => {
                      deviceFound = false; // 重置标志，允许扫描连接
                      
                      console.log('连接已发现设备失败，开始扫描新设备');
                      // 如果连接失败，开始扫描
                      this._startScanAndConnect(deviceName, scanTimeout, resolve, reject);
                    });
                  return;
                }
              }
              
              // 如果没有找到匹配的设备，开始扫描
              console.log('开始扫描设备，目标名称:', deviceName);
              this._startScanAndConnect(deviceName, scanTimeout, resolve, reject);
            },
            fail: (err) => {
              console.error('获取已发现的设备失败:', err);
              
              // 开始扫描设备
              console.log('开始扫描设备，目标名称:', deviceName);
              this._startScanAndConnect(deviceName, scanTimeout, resolve, reject);
            }
          });
        });
      });
  }
  
  /**
   * 开始扫描并连接设备
   * @param {string} deviceName 设备名称
   * @param {number} timeout 超时时间
   * @param {Function} resolve Promise解决函数
   * @param {Function} reject Promise拒绝函数
   * @private
   */
  _startScanAndConnect(deviceName, timeout, resolve, reject) {
    // 设置设备发现回调
    const originalDeviceFoundCallback = this.onDeviceFoundCallback;
    
    // 临时设置设备发现回调，用于实时连接
    this.onDeviceFoundCallback = (device) => {
      // 保留原始回调
      if (originalDeviceFoundCallback) {
        originalDeviceFoundCallback(device);
      }
      
      // 检查是否为目标设备
      const deviceName = device.name || device.localName || '';
      if (deviceName === this.expectedDeviceName || 
          (isSmartLockDevice(deviceName) && deviceName.includes(this.expectedDeviceName))) {
        
        console.log('扫描中发现目标设备:', deviceName);
        
        // 如果当前没有连接操作，立即尝试连接
        if (!this.isConnecting && !this.isConnected) {
          console.log('立即连接设备:', deviceName);
          
          // 停止扫描
          this.stopScan().catch(() => {});
          
          // 连接设备
          this.connect(device.deviceId)
            .then((res) => {
              // 恢复原始回调
              this.onDeviceFoundCallback = originalDeviceFoundCallback;
              resolve(res);
            })
            .catch((err) => {
              console.error('连接失败:', err);
              
              // 如果连接失败但扫描仍在进行，继续扫描
              if (this.isScanning) {
                console.log('连接失败，继续扫描其他设备');
              } else {
                // 恢复原始回调
                this.onDeviceFoundCallback = originalDeviceFoundCallback;
                reject(err);
              }
            });
        }
      }
    };
    
    // 开始扫描
    this._startScan(timeout)
      .then((res) => {
        // 恢复原始回调
        this.onDeviceFoundCallback = originalDeviceFoundCallback;
        
        // 如果已经找到并连接了设备，不再处理
        if (this.isConnected) return;
        
        console.log('扫描完成，未找到目标设备');
        
        // 如果是模拟模式，返回模拟结果
        if (res.simulated) {
          console.log('切换到模拟模式');
          resolve({
            success: true,
            message: '连接成功(模拟模式)',
            deviceId: 'simulated-device-id',
            name: deviceName,
            simulated: true
          });
        } else {
          reject(new Error(`未找到名称为 ${deviceName} 的设备`));
        }
      })
      .catch((err) => {
        // 恢复原始回调
        this.onDeviceFoundCallback = originalDeviceFoundCallback;
        
        // 如果已经连接了设备，不再处理
        if (this.isConnected) return;
        
        console.error('扫描设备失败:', err);
        reject(err);
      });
  }
  
  /**
   * 连接设备
   * @param {string} deviceId 设备ID
   * @returns {Promise} 连接结果
   */
  connect(deviceId) {
    console.log('蓝牙管理器 - 连接设备:', deviceId);
    
    // 如果已经连接，不重复连接
    if (this.isConnected && this.deviceId === deviceId) {
      console.log('蓝牙管理器 - 设备已连接，不重复连接');
      return Promise.resolve({
        success: true,
        message: '设备已连接',
        deviceId: deviceId
      });
    }
    
    // 先停止扫描
    this.stopScan().catch(() => {
      console.log('蓝牙管理器 - 停止扫描失败，继续执行连接');
    });
    
    return new Promise((resolve, reject) => {
      // 先关闭之前的连接
      this._disconnectIfNeeded()
        .then(() => {
          // 连接设备
          uni.createBLEConnection({
            deviceId: deviceId,
            timeout: 10000, // 10秒超时
            success: (res) => {
              console.log('蓝牙管理器 - 连接成功:', res);
              
              // 保存连接状态和设备ID
              this.isConnected = true;
              this.deviceId = deviceId;
              
              // 查找服务
              setTimeout(() => {
                this._getDeviceServices(deviceId)
                  .then(() => {
                    console.log('蓝牙管理器 - 服务和特征值获取成功');
                    
                    // 调用连接成功回调
                    if (this.onConnectedCallback) {
                      this.onConnectedCallback({
                        deviceId: deviceId
                      });
                    }
                    
                    // 设置连接监听
                    this._setupConnectionListener(deviceId);
                    
                    resolve({
                      success: true,
                      message: '连接成功',
                      deviceId: deviceId
                    });
                  })
                  .catch((err) => {
                    console.error('蓝牙管理器 - 获取服务和特征值失败:', err);
                    uni.hideLoading();
                    
                    // 断开连接
                    this.disconnect().catch(() => {});
                    
                    reject(err);
                  });
              }, 300); // 延迟300ms，确保连接稳定后再获取服务
            },
            fail: (err) => {
              console.error('蓝牙管理器 - 连接失败:', err);
              uni.hideLoading();
              reject(err);
            }
          });
        })
        .catch((err) => {
          console.error('蓝牙管理器 - 断开之前的连接失败:', err);
          uni.hideLoading();
          
          // 连接设备
          uni.createBLEConnection({
            deviceId: deviceId,
            timeout: 10000, // 10秒超时
            success: (res) => {
              console.log('蓝牙管理器 - 连接成功:', res);
              
              // 保存连接状态和设备ID
              this.isConnected = true;
              this.deviceId = deviceId;
              
              // 查找服务
              setTimeout(() => {
                this._getDeviceServices(deviceId)
                  .then(() => {
                    console.log('蓝牙管理器 - 服务和特征值获取成功');
                    uni.hideLoading();
                    
                    // 调用连接成功回调
                    if (this.onConnectedCallback) {
                      this.onConnectedCallback({
                        deviceId: deviceId
                      });
                    }
                    
                    // 设置连接监听
                    this._setupConnectionListener(deviceId);
                    
                    resolve({
                      success: true,
                      message: '连接成功',
                      deviceId: deviceId
                    });
                  })
                  .catch((err) => {
                    console.error('蓝牙管理器 - 获取服务和特征值失败:', err);
                    uni.hideLoading();
                    
                    // 断开连接
                    this.disconnect().catch(() => {});
                    
                    reject(err);
                  });
              }, 300); // 延迟300ms，确保连接稳定后再获取服务
            },
            fail: (err) => {
              console.error('蓝牙管理器 - 连接失败:', err);
              uni.hideLoading();
              reject(err);
            }
          });
        });
    });
  }
  
  /**
   * 认证设备
   * @returns {Promise} 认证结果
   */
  authenticate() {
    console.log('蓝牙管理器 - 开始认证设备');
    
    // 检查是否已连接
    if (!this.isConnected) {
      console.error('蓝牙管理器 - 设备未连接，无法认证');
      return Promise.reject(new Error('设备未连接，无法认证'));
    }
    
    // 检查服务和特征值是否已获取
    if (!this.serviceId || !this.writeCharacteristicId) {
      console.error('蓝牙管理器 - 服务或特征值未获取，尝试获取设备服务');
      
      // 尝试重新获取设备服务
      return this._getDeviceServices(this.deviceId)
        .then(() => this._authenticateWithRetry())
        .catch(err => {
          console.error('蓝牙管理器 - 获取设备服务失败，无法认证:', err);
          return Promise.reject(err);
        });
    }
    
    // 执行认证流程
    return this._authenticateWithRetry();
  }
  
  /**
   * 执行认证流程（带重试）
   * @param {number} retryCount 重试次数
   * @param {number} maxRetries 最大重试次数
   * @returns {Promise} 认证结果
   * @private
   */
  _authenticateWithRetry(retryCount = 0, maxRetries = 3) {
    // 为确保连接稳定，先添加延迟
    return new Promise((resolve, reject) => {
      // 延迟随重试次数增加
      const delay = 500 + (retryCount * 200); 
      
      console.log(`蓝牙管理器 - 认证延迟 ${delay}ms (尝试 ${retryCount + 1}/${maxRetries + 1})`);
      
      setTimeout(() => {
        // 使用固定的认证命令
        console.log('蓝牙管理器 - 发送认证命令:', AUTH_CMD);
        
        // 写入特征值，确保传递正确的服务ID和特征值ID
        this.writeCharacteristic(AUTH_CMD, this.serviceId, this.writeCharacteristicId)
          .then(() => {
            console.log('蓝牙管理器 - 认证成功');
            this.isAuthenticated = true;
            resolve({
              success: true,
              message: '认证成功'
            });
          })
          .catch(err => {
            console.error('蓝牙管理器 - 认证失败:', err);
            
            // 如果还有重试机会，则重试
            if (retryCount < maxRetries) {
              console.log(`蓝牙管理器 - 重试认证 (${retryCount + 1}/${maxRetries})`);
              this._authenticateWithRetry(retryCount + 1, maxRetries)
                .then(resolve)
                .catch(reject);
            } else {
              reject(err);
            }
          });
      }, delay);
    });
  }

  /**
   * 开锁
   * @param {number} lockNumber 锁编号（1-12，默认1）
   * @param {boolean} stopAfterFirst 是否在第一次成功后停止，默认为false
   * @returns {Promise} 开锁结果
   */
  openLock(lockNumber = 1, stopAfterFirst = false) {
    if (!this.isConnected || !this.deviceId) {
      return Promise.reject(new Error('设备未连接'));
    }
    
    // 检查服务和特征值ID是否存在
    if (!this.serviceId || !this.writeCharacteristicId) {
      console.log('蓝牙管理器 - 服务ID或特征值ID不存在，尝试重新获取服务信息');
      return this._getDeviceServices(this.deviceId)
        .then(() => {
          console.log('蓝牙管理器 - 重新获取服务信息成功，继续开锁');
          return this.openLock(lockNumber, stopAfterFirst); // 重新调用开锁
        })
        .catch(err => {
          console.error('蓝牙管理器 - 重新获取服务信息失败:', err);
          return Promise.reject(new Error('获取服务信息失败，无法开锁'));
        });
    }
    
    // 确保设备已认证，如果未认证则先进行认证
    if (!this.isAuthenticated) {
      console.log('蓝牙管理器 - 设备未认证，先进行认证');
      return this.authenticate()
        .then(() => {
          console.log('蓝牙管理器 - 认证成功，继续开锁');
          return this.openLock(lockNumber, stopAfterFirst); // 认证成功后重新调用开锁
        })
        .catch(err => {
          console.error('蓝牙管理器 - 认证失败，尝试强制开锁:', err);
          // 即使认证失败也尝试开锁
          return this._executeOpenLock(lockNumber, stopAfterFirst);
        });
    }
    
    // 执行开锁操作
    return this._executeOpenLock(lockNumber, stopAfterFirst);
  }
  
  /**
   * 执行开锁操作
   * @param {number} lockNumber 锁编号
   * @param {boolean} stopAfterFirst 是否在第一次成功后停止，默认为false
   * @returns {Promise} 开锁结果
   * @private
   */
  _executeOpenLock(lockNumber, stopAfterFirst = false) {
    let lockValue;
    
    if (lockNumber === 'all') {
      lockValue = 0x0D;  // 全部开锁
    } else {
      try {
        lockValue = parseInt(lockNumber);
        if (lockValue < 1 || lockValue > 12) {
          return Promise.reject(new Error('锁编号必须在1-12之间'));
        }
      } catch (e) {
        return Promise.reject(new Error('无效的锁编号'));
      }
    }
    
    // 构建开锁命令 - 确保格式正确: FF4F50454E00[锁编号]0000FE
    const lockNumberHex = lockValue.toString(16).padStart(2, '0');
    const cmd = `${OPEN_LOCK_CMD_PREFIX}${lockNumberHex}${OPEN_LOCK_CMD_SUFFIX}`;
    
    console.log(`蓝牙管理器 - 发送开锁命令 (锁 ${lockNumber})...`);
    console.log('蓝牙管理器 - 开锁命令:', cmd);
    console.log('蓝牙管理器 - 当前服务ID:', this.serviceId, '当前特征值ID:', this.writeCharacteristicId);
    
    // 只发送一次开锁命令，移除所有重试逻辑
    return new Promise((resolve, reject) => {
      // 发送开锁命令
      this._writeWithRetry(cmd, this.serviceId, this.writeCharacteristicId, 0, 3)
        .then(res => {
          console.log('蓝牙管理器 - 开锁命令发送成功:', res);
          resolve({
            success: true,
            message: '开锁命令发送成功',
            result: res
          });
        })
        .catch(err => {
          console.error('蓝牙管理器 - 开锁命令发送失败:', err);
          reject({
            success: false,
            message: '开锁命令发送失败',
            error: err
          });
        });
    });
  }
  
  /**
   * 带重试机制的写入特征值
   * @param {string|ArrayBuffer} data 要写入的数据
   * @param {string} serviceId 服务ID
   * @param {string} characteristicId 特征值ID
   * @param {number} retryCount 当前重试次数
   * @param {number} maxRetries 最大重试次数
   * @returns {Promise} 写入结果
   * @private
   */
  _writeWithRetry(data, serviceId, characteristicId, retryCount = 0, maxRetries = 3) {
    return new Promise((resolve, reject) => {
      console.log(`蓝牙管理器 - 写入特征值 (尝试 ${retryCount + 1}/${maxRetries + 1})`);
      
      this.writeCharacteristic(data, serviceId, characteristicId)
        .then(res => {
          console.log('蓝牙管理器 - 写入特征值成功:', res);
          resolve(res);
        })
        .catch(err => {
          console.error('蓝牙管理器 - 写入特征值失败:', err);
          
          // 如果还有重试机会，则重试
          if (retryCount < maxRetries) {
            console.log(`蓝牙管理器 - 重试写入特征值 (${retryCount + 1}/${maxRetries})`);
            
            // 延迟后重试
            setTimeout(() => {
              this._writeWithRetry(data, serviceId, characteristicId, retryCount + 1, maxRetries)
                .then(resolve)
                .catch(reject);
            }, 500 * (retryCount + 1)); // 延迟时间随重试次数增加
          } else {
            reject(err);
          }
        });
    });
  }
  
  /**
   * 查询设备信息
   * @returns {Promise} 查询结果
   */
  queryDeviceInfo() {
    console.log('查询设备信息');
    return this.writeCharacteristic(QUERY_DEVICE_INFO_CMD);
  }
  
  /**
   * 查询锁状态
   * @returns {Promise} 查询结果
   */
  queryLockStatus() {
    console.log('查询锁状态');
    return this.writeCharacteristic(QUERY_LOCK_STATUS_CMD);
  }
  
  /**
   * 调用断开连接回调
   * @param {Object} info 断开信息
   * @private
   */
  _callDisconnectedCallback(info) {
    if (this.onDisconnectedCallback) {
      this.onDisconnectedCallback(info);
    }
  }
  
  /**
   * 调用错误回调
   * @param {Object} error 错误信息
   * @private
   */
  _callErrorCallback(error) {
    if (this.onErrorCallback) {
      this.onErrorCallback(error);
    }
  }
  
  /**
   * 将ArrayBuffer转换为十六进制字符串
   * @param {ArrayBuffer} buffer ArrayBuffer数据
   * @returns {string} 十六进制字符串
   * @private
   */
  _arrayBufferToHex(buffer) {
    return Array.prototype.map.call(
      new Uint8Array(buffer),
      x => ('00' + x.toString(16)).slice(-2)
    ).join('');
  }
  
  /**
   * 将十六进制字符串转换为ArrayBuffer
   * @param {string} hex 十六进制字符串
   * @returns {ArrayBuffer} ArrayBuffer数据
   * @private
   */
  _hexToArrayBuffer(hex) {
    // 确保输入是字符串
    if (typeof hex !== 'string') {
      console.error('蓝牙管理器 - 十六进制字符串转换失败，输入不是字符串:', hex);
      return new ArrayBuffer(0);
    }
    
    // 移除所有空格
    hex = hex.replace(/\s+/g, '');
    
    // 确保长度为偶数
    if (hex.length % 2 !== 0) {
      console.warn('蓝牙管理器 - 十六进制字符串长度不是偶数，自动补0:', hex);
      hex = '0' + hex;
    }
    
    try {
      const len = hex.length;
      const buffer = new ArrayBuffer(len / 2);
      const dataView = new DataView(buffer);
      
      for (let i = 0; i < len; i += 2) {
        const byteValue = parseInt(hex.substr(i, 2), 16);
        if (isNaN(byteValue)) {
          console.error('蓝牙管理器 - 十六进制字符串包含无效字符:', hex.substr(i, 2));
          return new ArrayBuffer(0);
        }
        dataView.setUint8(i / 2, byteValue);
      }
      
      return buffer;
    } catch (err) {
      console.error('蓝牙管理器 - 十六进制字符串转换失败:', err);
      return new ArrayBuffer(0);
    }
  }

  /**
   * 开始扫描设备
   * @param {number} timeout 超时时间（毫秒）
   * @returns {Promise} 扫描结果
   * @private
   */
  _startScan(timeout = 10000) {
    console.log('开始扫描设备，超时时间:', timeout);
    
    // 记录扫描开始时间
    this.scanStartTime = Date.now();
    
    return new Promise((resolve, reject) => {
      // 如果不支持蓝牙，使用模拟模式
      if (!this.supportBluetooth) {
        setTimeout(() => {
          resolve({
            success: true,
            message: '扫描完成(模拟模式)',
            devices: [],
            simulated: true
          });
        }, 1000);
        return;
      }
      
      // 清除之前的超时定时器
      if (this.scanTimeoutId) {
        clearTimeout(this.scanTimeoutId);
      }
      
      // 开始扫描设备
      uni.startBluetoothDevicesDiscovery({
        allowDuplicatesKey: true,
        services: [], // 不指定服务UUID，扫描所有设备
        success: () => {
          console.log('开始扫描设备');
          this.isScanning = true;
          
          // 设置超时
          this.scanTimeoutId = setTimeout(() => {
            // 停止扫描
            if (this.isScanning) {
              console.log('扫描超时，停止扫描');
              uni.stopBluetoothDevicesDiscovery({
                complete: () => {
                  this.isScanning = false;
                  resolve({
                    success: true,
                    message: '扫描完成',
                    devices: []
                  });
                }
              });
            }
          }, timeout);
        },
        fail: (err) => {
          console.error('开始扫描失败:', err);
          this.isScanning = false;
          reject(err);
        }
      });
    });
  }

  /**
   * 停止扫描设备
   * @returns {Promise} 停止结果
   */
  stopScan() {
    console.log('停止扫描设备');
    
    // 如果没有在扫描，直接返回成功
    if (!this.isScanning) {
      return Promise.resolve({
        success: true,
        message: '没有正在进行的扫描'
      });
    }
    
    // 清除扫描超时定时器
    if (this.scanTimeoutId) {
      clearTimeout(this.scanTimeoutId);
      this.scanTimeoutId = null;
    }
    
    return new Promise((resolve, reject) => {
      // 如果不支持蓝牙，直接返回成功
      if (!this.supportBluetooth) {
        this.isScanning = false;
        resolve({
          success: true,
          message: '停止扫描成功(模拟模式)'
        });
        return;
      }
      
      // 停止扫描
      uni.stopBluetoothDevicesDiscovery({
        success: () => {
          console.log('停止扫描成功');
          this.isScanning = false;
          resolve({
            success: true,
            message: '停止扫描成功'
          });
        },
        fail: (err) => {
          console.error('停止扫描失败:', err);
          this.isScanning = false;
          reject(err);
        }
      });
    });
  }

  /**
   * 获取设备服务和特征值
   * @param {string} deviceId 设备ID
   * @returns {Promise} 获取结果
   * @private
   */
  _getDeviceServices(deviceId) {
    console.log('蓝牙管理器 - 获取设备服务，设备ID:', deviceId);
    
    return new Promise((resolve, reject) => {
      // 获取设备服务
      uni.getBLEDeviceServices({
        deviceId: deviceId,
        success: (servicesRes) => {
          console.log('蓝牙管理器 - 获取设备服务成功:', JSON.stringify(servicesRes));
          
          // 查找目标服务
          const services = servicesRes.services || [];
          let targetService = null;
          
          // 遍历所有服务，详细打印
          console.log(`蓝牙管理器 - 发现 ${services.length} 个服务:`);
          services.forEach((service, index) => {
            console.log(`服务 ${index + 1}:`, service.uuid);
          });
          
          // 先尝试查找FFC0服务
          for (const service of services) {
            if (service.uuid && service.uuid.toUpperCase().includes('FFC0')) {
              console.log('蓝牙管理器 - 找到锁服务:', service.uuid);
              targetService = service;
              break;
            }
          }
          
          // 如果没有找到FFC0服务，尝试使用第一个主服务
          if (!targetService && services.length > 0) {
            console.warn('蓝牙管理器 - 未找到FFC0锁服务，尝试使用第一个服务');
            targetService = services[0];
          }
          
          if (!targetService) {
            console.error('蓝牙管理器 - 未找到任何服务');
            reject(new Error('未找到设备服务'));
            return;
          }
          
          // 保存服务ID
          this.serviceId = targetService.uuid;
          console.log('蓝牙管理器 - 已保存服务ID:', this.serviceId);
          
          // 获取特征值
          uni.getBLEDeviceCharacteristics({
            deviceId: deviceId,
            serviceId: this.serviceId,
            success: (charsRes) => {
              console.log('蓝牙管理器 - 获取特征值成功:', JSON.stringify(charsRes));
              
              // 查找写入特征值
              const chars = charsRes.characteristics || [];
              let writeChar = null;
              let notifyChar = null;
              
              // 详细打印所有特征值
              console.log(`蓝牙管理器 - 发现 ${chars.length} 个特征值:`);
              chars.forEach((char, index) => {
                console.log(`特征值 ${index + 1}:`, char.uuid, 
                          '属性:', 
                          char.properties ? JSON.stringify(char.properties) : '未知');
              });
              
              // 遍历所有特征值
              for (const char of chars) {
                if (!char.uuid) continue;
                
                // 查找写入特征值
                if (char.properties && char.properties.write) {
                  // 优先选择FFC1特征值
                  if (char.uuid.toUpperCase().includes('FFC1')) {
                    console.log('蓝牙管理器 - 找到FFC1写入特征值:', char.uuid);
                    writeChar = char;
                  } else if (!writeChar) {
                    // 如果没有找到FFC1，则使用第一个可写特征值
                    console.log('蓝牙管理器 - 找到备用写入特征值:', char.uuid);
                    writeChar = char;
                  }
                }
                
                // 查找通知特征值
                if (char.properties && (char.properties.notify || char.properties.indicate)) {
                  // 优先选择FFC2特征值
                  if (char.uuid.toUpperCase().includes('FFC2')) {
                    console.log('蓝牙管理器 - 找到FFC2通知特征值:', char.uuid);
                    notifyChar = char;
                  } else if (!notifyChar) {
                    // 如果没有找到FFC2，则使用第一个可通知特征值
                    console.log('蓝牙管理器 - 找到备用通知特征值:', char.uuid);
                    notifyChar = char;
                  }
                }
              }
              
              // 如果没有找到写入特征值，但有特征值存在
              if (!writeChar && chars.length > 0) {
                console.warn('蓝牙管理器 - 未找到可写特征值，尝试使用第一个特征值');
                writeChar = chars[0];
              }
              
              if (!writeChar) {
                console.error('蓝牙管理器 - 未找到写入特征值');
                reject(new Error('未找到写入特征值'));
                return;
              }
              
              // 保存写入特征值ID
              this.writeCharacteristicId = writeChar.uuid;
              console.log('蓝牙管理器 - 已保存写入特征值ID:', this.writeCharacteristicId);
              
              // 如果找到通知特征值
              if (notifyChar) {
                // 保存通知特征值ID
                this.notifyCharacteristicId = notifyChar.uuid;
                console.log('蓝牙管理器 - 已保存通知特征值ID:', this.notifyCharacteristicId);
                
                // 启用通知
                uni.notifyBLECharacteristicValueChange({
                  deviceId: deviceId,
                  serviceId: this.serviceId,
                  characteristicId: this.notifyCharacteristicId,
                  state: true,
                  success: () => {
                    console.log('蓝牙管理器 - 启用通知成功');
                    
                    // 设置特征值变化监听
                    this._setupCharacteristicValueChange();
                    
                    // 立即进行认证，不等待用户操作
                    console.log('蓝牙管理器 - 连接成功后立即进行认证');
                    this._authenticateWithRetry(0, 5)  // 增加重试次数到5次
                      .then(() => {
                        console.log('蓝牙管理器 - 连接后立即认证成功');
                        this.isAuthenticated = true;
                        resolve();
                      })
                      .catch(err => {
                        console.error('蓝牙管理器 - 连接后立即认证失败:', err);
                        // 即使认证失败也继续，后续操作会再次尝试认证
                        resolve();
                      });
                  },
                  fail: err => {
                    console.error('蓝牙管理器 - 启用通知失败:', err);
                    // 即使启用通知失败也继续，只要获取到了服务和特征值就可以
                    
                    // 尝试进行认证
                    this._authenticateWithRetry(0, 5)  // 增加重试次数到5次
                      .then(() => {
                        console.log('蓝牙管理器 - 认证成功(通知失败后)');
                        this.isAuthenticated = true;
                        resolve();
                      })
                      .catch(err => {
                        console.error('蓝牙管理器 - 认证失败(通知失败后):', err);
                        // 即使认证失败也继续
                        resolve();
                      });
                  }
                });
              } else {
                console.warn('蓝牙管理器 - 未找到通知特征值，部分功能可能不可用');
                
                // 尝试进行认证
                this._authenticateWithRetry(0, 5)  // 增加重试次数到5次
                  .then(() => {
                    console.log('蓝牙管理器 - 认证成功(无通知特征值)');
                    this.isAuthenticated = true;
                    resolve();
                  })
                  .catch(err => {
                    console.error('蓝牙管理器 - 认证失败(无通知特征值):', err);
                    // 即使认证失败也继续
                    resolve();
                  });
              }
            },
            fail: err => {
              console.error('蓝牙管理器 - 获取特征值失败:', err);
              reject(new Error(`获取特征值失败: ${err.errMsg || JSON.stringify(err)}`));
            }
          });
        },
        fail: err => {
          console.error('蓝牙管理器 - 获取设备服务失败:', err);
          reject(new Error(`获取设备服务失败: ${err.errMsg || JSON.stringify(err)}`));
        }
      });
    });
  }

  /**
   * 设置连接监听
   * @param {string} deviceId 设备ID
   * @private
   */
  _setupConnectionListener(deviceId) {
    console.log('蓝牙管理器 - 设置连接监听');
    
    // 监听连接状态变化
    uni.onBLEConnectionStateChange(res => {
      console.log('蓝牙管理器 - 连接状态变化:', res);
      
      if (res.deviceId === deviceId) {
        if (!res.connected) {
          console.log('蓝牙管理器 - 设备断开连接');
          this._handleDisconnect();
        }
      }
    });
  }

  /**
   * 设置特征值变化监听
   * @private
   */
  _setupCharacteristicValueChange() {
    console.log('蓝牙管理器 - 设置特征值变化监听');
    
    uni.onBLECharacteristicValueChange(res => {
      console.log('蓝牙管理器 - 特征值变化:', res);
      
      // 处理数据
      this._handleCharacteristicValueChange(res.value);
    });
  }

  /**
   * 解析特征值变化
   * @param {string} hexValue 十六进制字符串
   * @private
   */
  _parseCharacteristicValueChange(hexValue) {
    // 解析锁状态
    if (hexValue.startsWith('66f1')) {
      const isOpen = hexValue.substring(4, 6) === '01';
      console.log('锁状态变化:', isOpen ? '开启' : '关闭');
      
      // 调用锁状态变化回调
      if (this.onLockStatusChangeCallback) {
        this.onLockStatusChangeCallback({
          isOpen: isOpen
        });
      }
    }
    
    // 解析电池电量
    if (hexValue.startsWith('66f0')) {
      const batteryLevel = parseInt(hexValue.substring(4, 6), 16);
      console.log('电池电量:', batteryLevel);
      
      // 调用电池电量更新回调
      if (this.onBatteryUpdateCallback) {
        this.onBatteryUpdateCallback({
          batteryLevel: batteryLevel
        });
      }
    }
  }

  /**
   * 断开连接
   * @returns {Promise} 断开结果
   */
  disconnect() {
    console.log('蓝牙管理器 - 断开连接');
    
    // 如果已经断开连接，直接返回成功
    if (!this.isConnected) {
      console.log('蓝牙管理器 - 设备已断开连接');
      return Promise.resolve({
        success: true,
        message: '设备已断开连接'
      });
    }
    
    // 先停止扫描
    this.stopScan().catch(() => {
      console.log('蓝牙管理器 - 停止扫描失败，继续执行断开连接');
    });
    
    return new Promise((resolve, reject) => {
      uni.closeBLEConnection({
        deviceId: this.deviceId,
        success: (res) => {
          console.log('蓝牙管理器 - 断开连接成功:', res);
          this._handleDisconnect();
          resolve({
            success: true,
            message: '断开连接成功'
          });
        },
        fail: (err) => {
          console.error('蓝牙管理器 - 断开连接失败:', err);
          // 即使失败，也认为已断开连接
          this._handleDisconnect();
          reject(err);
        }
      });
    });
  }

  /**
   * 处理设备断开连接
   * @private
   */
  _handleDisconnect() {
    console.log('蓝牙管理器 - 处理设备断开连接');
    
    // 更新连接状态
    this.isConnected = false;
    this.isConnecting = false;
    this.isAuthenticated = false;
    
    // 调用断开连接回调
    if (this.onDisconnectedCallback) {
      this.onDisconnectedCallback({
        deviceId: this.deviceId
      });
    }
  }

  /**
   * 如果需要，断开之前的连接
   * @returns {Promise} 断开结果
   * @private
   */
  _disconnectIfNeeded() {
    if (this.isConnected) {
      console.log('蓝牙管理器 - 断开之前的连接');
      return this.disconnect();
    } else {
      return Promise.resolve();
    }
  }

  /**
   * 处理特征值变化
   * @param {ArrayBuffer} value 特征值数据
   * @private
   */
  _handleCharacteristicValueChange(value) {
    try {
      // 将ArrayBuffer转换为十六进制字符串
      const hexValue = this._arrayBufferToHex(value);
      console.log('蓝牙管理器 - 收到特征值变化 (HEX):', hexValue);
      
      // 解析特征值内容
      if (hexValue.startsWith('4f504e') || hexValue.startsWith('opne') ||
          hexValue.toLowerCase().startsWith('ff4f') || hexValue.startsWith('1c') || hexValue.startsWith('1e')) {
        // 锁状态相关响应 - 支持1c（开锁）和1e（关锁/状态查询）开头的响应

        // 判断锁状态
        let isOpen = false;

        if (hexValue.startsWith('1c')) {
          // 1c开头的开锁状态响应
          // 1c0f表示开锁状态
          isOpen = hexValue.substring(2, 4) === '0f' || hexValue.substring(2, 4) === 'f0';
          console.log('蓝牙管理器 - 检测到1c开头的锁状态响应:', hexValue.substring(0, 4), '判断为:', isOpen ? '已开锁' : '已关锁');
        } else if (hexValue.startsWith('1e')) {
          // 1e开头的锁状态查询响应或关锁状态
          // 1e0f表示关锁状态，其他值也表示关锁
          isOpen = false; // 1e开头通常表示锁已关闭
          console.log('蓝牙管理器 - 检测到1e开头的锁状态响应:', hexValue.substring(0, 4), '判断为: 已关锁');
        } else {
          // 其他开锁响应
          const statusByte = hexValue.substring(6, 8);
          isOpen = statusByte === '01';
        }
        
        console.log('蓝牙管理器 - 锁状态变化:', isOpen ? '已开锁' : '已关锁');
        
        // 调用锁状态变化回调
        if (this.onLockStatusChangeCallback) {
          this.onLockStatusChangeCallback({
            isOpen: isOpen
          });
        }
      } else if (hexValue.startsWith('4241') || hexValue.startsWith('batt') || 
                hexValue.startsWith('66f0')) { // 电池相关响应
        // 提取电池电量
        let batteryLevel = 0;
        
        if (hexValue.startsWith('66f0')) {
          // 66f0开头的电池电量响应
          batteryLevel = parseInt(hexValue.substring(4, 6), 16);
        } else {
          // 其他电池响应
          batteryLevel = parseInt(hexValue.substring(4, 6), 16);
        }
        
        console.log('蓝牙管理器 - 电池电量:', batteryLevel);
        
        // 调用电池电量更新回调
        if (this.onBatteryUpdateCallback) {
          this.onBatteryUpdateCallback({
            batteryLevel: batteryLevel
          });
        }
      } else if (hexValue.startsWith('8e') || hexValue.includes('8e')) {
        // 认证响应
        console.log('蓝牙管理器 - 收到认证响应:', hexValue);
        this.isAuthenticated = true;
      } else if (hexValue.startsWith('77')) {
        // 设备信息响应 - 参考Python脚本中的解析方式
        console.log('蓝牙管理器 - 收到设备信息响应:', hexValue);
        
        try {
          // 解析设备信息
          // 根据Python脚本中的解析方式，电量值在第6-8位
          const deviceVersion = parseInt(hexValue.substring(2, 4), 16);
          const batteryPowered = parseInt(hexValue.substring(4, 6), 16) === 1;
          const batteryLevel = parseInt(hexValue.substring(6, 8), 16);
          
          console.log('蓝牙管理器 - 解析设备信息: 版本=', deviceVersion, 
                     '电池供电=', batteryPowered ? '是' : '否', 
                     '电量=', batteryLevel + '%');
          
          // 调用电池电量更新回调
          if (this.onBatteryUpdateCallback) {
            this.onBatteryUpdateCallback({
              batteryLevel: batteryLevel,
              batteryPowered: batteryPowered,
              deviceVersion: deviceVersion
            });
          }
        } catch (err) {
          console.error('蓝牙管理器 - 解析设备信息失败:', err);
        }
      }
      
      // 调用原始特征值变化回调
      if (this.onCharacteristicValueChangeCallback) {
        this.onCharacteristicValueChangeCallback({
          value: value,
          hexValue: hexValue
        });
      }
    } catch (err) {
      console.error('蓝牙管理器 - 处理特征值变化出错:', err);
    }
  }

  /**
   * 写入特征值
   * @param {ArrayBuffer|string} buffer 数据缓冲区或十六进制字符串
   * @param {string} serviceId 服务ID，可选，默认使用锁服务ID
   * @param {string} characteristicId 特征值ID，可选，默认使用写入特征值ID
   * @returns {Promise} 写入结果
   */
  writeCharacteristic(buffer, serviceId, characteristicId) {
    console.log('蓝牙管理器 - 写入特征值:', serviceId, characteristicId);
    
    // 检查是否已连接
    if (!this.isConnected) {
      console.error('蓝牙管理器 - 设备未连接，无法写入特征值');
      return Promise.reject(new Error('设备未连接，无法写入特征值'));
    }
    
    // 如果缺少服务或特征ID，尝试使用默认值
    // 确保优先使用传入的参数
    let finalServiceId = serviceId || this.serviceId;
    let finalCharacteristicId = characteristicId || this.writeCharacteristicId;
    
    // 检查参数
    if (!finalServiceId || !finalCharacteristicId) {
      console.error('蓝牙管理器 - 服务ID或特征值ID不能为空', '当前serviceId:', finalServiceId, '当前characteristicId:', finalCharacteristicId);
      
      // 尝试重新获取服务信息
      if (this.deviceId) {
        console.log('蓝牙管理器 - 尝试重新获取服务信息');
        return this._getDeviceServices(this.deviceId)
          .then(() => {
            // 重新获取服务信息后再次尝试写入
            finalServiceId = serviceId || this.serviceId;
            finalCharacteristicId = characteristicId || this.writeCharacteristicId;
            
            if (!finalServiceId || !finalCharacteristicId) {
              return Promise.reject(new Error('重新获取服务信息后，服务ID或特征值ID仍然为空'));
            }
            
            // 递归调用自身，使用新获取的ID
            return this.writeCharacteristic(buffer, finalServiceId, finalCharacteristicId);
          })
          .catch(err => {
            console.error('蓝牙管理器 - 重新获取服务信息失败:', err);
            return Promise.reject(new Error('服务ID或特征值ID不能为空，且重新获取服务信息失败'));
          });
      } else {
        return Promise.reject(new Error('服务ID或特征值ID不能为空'));
      }
    }
    
    console.log('蓝牙管理器 - 实际写入特征值参数:', '设备ID:', this.deviceId, '服务ID:', finalServiceId, '特征值ID:', finalCharacteristicId);
    
    // 转换字符串为ArrayBuffer
    if (typeof buffer === 'string') {
      // 移除所有空格，确保格式正确
      const cleanHex = buffer.replace(/\s+/g, '');
      console.log('蓝牙管理器 - 处理十六进制字符串:', buffer, '清理后:', cleanHex);
      buffer = this._hexToArrayBuffer(cleanHex);
    }
    
    // 确保buffer是ArrayBuffer类型
    if (!(buffer instanceof ArrayBuffer)) {
      console.error('蓝牙管理器 - 数据必须是ArrayBuffer或十六进制字符串');
      return Promise.reject(new Error('数据必须是ArrayBuffer或十六进制字符串'));
    }
    
    // 打印要发送的数据
    console.log('蓝牙管理器 - 发送数据(HEX):', this._arrayBufferToHex(buffer));
    
    return new Promise((resolve, reject) => {
      const writeWithRetry = (retryCount = 0, maxRetries = 3) => {
        uni.writeBLECharacteristicValue({
          deviceId: this.deviceId,
          serviceId: finalServiceId,
          characteristicId: finalCharacteristicId,
          value: buffer,
          success: (res) => {
            console.log('蓝牙管理器 - 写入特征值成功:', res);
            resolve(res);
          },
          fail: (err) => {
            console.error('蓝牙管理器 - 写入特征值失败:', err, '错误码:', err.errCode);
            
            // 错误码10005表示找不到特征值，可能是因为连接刚建立，特征值还未准备好
            if ((err.errCode === 10005 || err.errCode === 10006) && retryCount < maxRetries) {
              console.log(`蓝牙管理器 - 重试写入特征值 (${retryCount + 1}/${maxRetries})`);
              setTimeout(() => {
                writeWithRetry(retryCount + 1, maxRetries);
              }, 500 * (retryCount + 1)); // 重试间隔随着重试次数增加
            } else {
              reject(err);
            }
          }
        });
      };
      
      // 开始第一次尝试
      writeWithRetry();
    });
  }
}

// 导出单例
export default new BlueToothManager(); 