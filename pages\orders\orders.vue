<template>
	<view class="container page-orders">
		<!-- 页面背景 -->
		<view class="page-background">
			<!-- 背景图片 -->
			<image class="background-image" src="https://wuji1112-1312674791.cos.ap-guangzhou.myqcloud.com/static/shouye3.png" mode="aspectFill"></image>
			
			<!-- 深磨砂效果叠加层 -->
			<view class="frosted-overlay"></view>
		</view>
		
		<!-- 导航栏 -->
		<view class="navbar" :style="navbarStyle">
			<view class="navbar-left" @click="goBack">
				<text class="material-icons md-24 text-primary">arrow_back</text>
			</view>
			<view class="navbar-title">我的订单</view>
			<view class="navbar-right"></view>
		</view>
		
		<!-- 页面内容区域 -->
		<view class="page-content">
			<!-- 订单类型选择 -->
			<view class="tabs-container">
				<view class="tabs">
					<view class="tab" :class="{'active': activeTab === 0}" @click="changeTab(0)">
						<text class="material-icons md-18 icon-inline">list_alt</text>
						<text>全部</text>
						<view class="tab-line" v-if="activeTab === 0"></view>
					</view>
					<view class="tab" :class="{'active': activeTab === 1}" @click="changeTab(1)">
						<text class="material-icons md-18 icon-inline">pending_actions</text>
						<text>进行中</text>
						<view class="tab-line" v-if="activeTab === 1"></view>
					</view>
					<view class="tab" :class="{'active': activeTab === 2}" @click="changeTab(2)">
						<text class="material-icons md-18 icon-inline">task_alt</text>
						<text>已完成</text>
						<view class="tab-line" v-if="activeTab === 2"></view>
					</view>
				</view>
				
				<!-- 调试按钮，仅在开发环境显示 -->
				<view class="debug-btn" @click="debugOrderList" v-if="isDevMode">调试</view>
			</view>
			
			<!-- 订单列表 -->
			<view class="content">
				<view v-if="orders.length > 0">
					<view class="order-item" v-for="(order, index) in filteredOrders" :key="index" @click="viewOrderDetail(order.id)">
						<view class="order-header flex justify-between items-center">
							<view class="order-store">
								<text class="title-sm">{{order.orderNo}}</text>
							</view>
							<view class="status" :class="{
								'active': order.rawStatus === 1,
								'unpaid': order.rawStatus === 0,
								'completed': order.rawStatus === 2,
								'cancelled': order.rawStatus === 3,
								'inactive': order.rawStatus !== 0 && order.rawStatus !== 1 && order.rawStatus !== 2 && order.rawStatus !== 3
							}">
								{{order.status}}
							</view>
						</view>
						<view class="divider mt-sm"></view>
						<view class="order-details mt-md">
							<view class="detail-item flex justify-between">
								<text class="detail-label"><text class="material-icons md-18 icon-inline">store</text> 门店</text>
								<text class="detail-value">{{getStoreName(order.storeName)}}</text>
							</view>
							<view class="detail-item flex justify-between mt-sm">
								<text class="detail-label"><text class="material-icons md-18 icon-inline">schedule</text> 下单时间</text>
								<text class="detail-value">{{order.startTime}}</text>
							</view>
							<view class="detail-item flex justify-between mt-sm">
								<text class="detail-label"><text class="material-icons md-18 icon-inline">payments</text> 消费金额</text>
								<text class="primary-light">{{order.amount}}</text>
							</view>
						</view>
						<!-- 修改订单操作按钮部分，移除"结束使用"按钮 -->
						<view class="order-actions flex mt-md" v-if="order.rawStatus === 0">
							<button class="btn btn-primary btn-sm flex-1" @click.stop="payOrder(order.id)">
								<text class="material-icons md-18 icon-inline">payment</text> 去支付
							</button>
						</view>
					</view>
				</view>
				
				<!-- 空状态 -->
				<view v-else class="empty-state">
					<text class="material-icons md-48 text-tertiary">receipt_long</text>
					<text class="empty-text">暂无订单记录</text>
					<button class="btn btn-outline mt-md" @click="navigateTo('/pages/index/index')">
						<text class="material-icons md-18 icon-inline">home</text> 返回首页
					</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import API from '@/static/js/api.js';

export default {
	data() {
		return {
			activeTab: 0,
			orders: [],
			loading: false,
			page: 1,
			// iOS适配相关
			systemInfo: {},
			navbarStyle: {},
			isIOS: false,
			pageSize: 10,
			hasMore: true,
			// 添加订单状态映射
			orderStatusMap: {
				0: '未支付',
				1: '进行中',
				2: '已完成',
				3: '已取消'
			},
			// 是否为开发模式
			isDevMode: process.env.NODE_ENV === 'development' || true
		}
	},
	computed: {
		filteredOrders() {
			if (this.activeTab === 0) {
				return this.orders;
			} else if (this.activeTab === 1) {
				return this.orders.filter(order => order.status === '进行中');
			} else {
				return this.orders.filter(order => order.status === '已完成');
			}
		}
	},
	onLoad() {
		// 加载订单列表
		this.loadOrders();
	},
	onReady() {
		// 获取系统信息并计算iOS适配参数
		this.calculateIOSAdaptation();
	},
	// 下拉刷新
	onPullDownRefresh() {
		this.refreshOrders();
	},
	// 上拉加载更多
	onReachBottom() {
		if (this.hasMore && !this.loading) {
			this.loadMoreOrders();
		}
	},
	methods: {
		// 计算iOS适配参数
		calculateIOSAdaptation() {
			try {
				this.systemInfo = uni.getSystemInfoSync();
				this.isIOS = this.systemInfo.platform === 'ios';

				console.log('系统信息:', this.systemInfo);
				console.log('是否iOS:', this.isIOS);

				if (this.isIOS) {
					const statusBarHeight = this.systemInfo.statusBarHeight || 44;
					const model = this.systemInfo.model || '';
					const safeAreaTop = this.systemInfo.safeArea ? this.systemInfo.safeArea.top : statusBarHeight;

					console.log('状态栏高度:', statusBarHeight);
					console.log('设备型号:', model);
					console.log('安全区域顶部:', safeAreaTop);

					let finalTopPosition;

					// 超激进适配策略 - 尝试多种不同的计算方式
					if (model.includes('iPhone 16 Pro')) {
						// 方案1：直接使用状态栏高度，无额外间距
						finalTopPosition = statusBarHeight;
						console.log('iPhone 16 Pro - 方案1（状态栏高度）:', finalTopPosition);

						// 方案2：如果还是太靠下，尝试更小的值
						if (finalTopPosition > 50) {
							finalTopPosition = 44; // 使用标准状态栏高度
							console.log('iPhone 16 Pro - 方案2（标准高度）:', finalTopPosition);
						}

						// 方案3：如果还是太靠下，尝试负值
						if (finalTopPosition > 45) {
							finalTopPosition = statusBarHeight - 10; // 负偏移
							console.log('iPhone 16 Pro - 方案3（负偏移）:', finalTopPosition);
						}
					} else if (model.includes('iPhone 15 Pro') || model.includes('iPhone 14 Pro')) {
						finalTopPosition = statusBarHeight;
					} else if (model.includes('iPhone X') || model.includes('iPhone 11') ||
							  model.includes('iPhone 12') || model.includes('iPhone 13')) {
						finalTopPosition = statusBarHeight;
					} else {
						finalTopPosition = statusBarHeight;
					}

					this.navbarStyle = {
						marginTop: finalTopPosition + 'px',
						// 添加更多样式确保生效
						position: 'relative',
						top: '0px'
					};

					console.log('超激进适配 - 最终顶部位置:', finalTopPosition + 'px');
					console.log('超激进适配 - 导航栏样式:', this.navbarStyle);
				} else {
					this.navbarStyle = {};
				}
			} catch (error) {
				console.error('计算iOS适配参数失败:', error);
				this.navbarStyle = {};
			}
		},

		goBack() {
			uni.navigateBack();
		},
		changeTab(index) {
			if (this.activeTab !== index) {
				this.activeTab = index;
				this.refreshOrders();
			}
		},
		navigateTo(url) {
			uni.navigateTo({
				url: url
			});
		},
		// 处理门店名称，移除"今夜城堡 - "前缀
		getStoreName(fullName) {
			return fullName.replace('今夜城堡 - ', '');
		},
		
		// 刷新订单列表
		refreshOrders() {
			this.page = 1;
			this.hasMore = true;
			this.orders = [];
			this.loadOrders();
		},
		
		// 加载订单列表
		loadOrders() {
			if (this.loading) return;
			
			this.loading = true;
			
			// 显示加载中
			uni.showLoading({
				title: '加载中...'
			});
			
			// 获取对应状态的订单
			let status = 0;
			if (this.activeTab === 1) {
				status = 1; // 进行中
			} else if (this.activeTab === 2) {
				status = 2; // 已完成
			}
			
			// 构建请求参数
			const params = {
				page: this.page,
				size: this.pageSize
			};
			
			// 只有当status不为0时才添加到参数中
			if (status !== 0) {
				params.status = status;
			}
			
			// 调用API获取订单列表
			API.order.getList(status, this.page, this.pageSize)
				.then(res => {
					uni.hideLoading();
					uni.stopPullDownRefresh();
					this.loading = false;
					
					// 处理返回数据，增强兼容性
					let orderList = [];
					
					// 兼容不同的返回格式
					if (Array.isArray(res.data)) {
						// 直接返回数组的情况
						orderList = res.data;
					} else if (res.data && Array.isArray(res.data.list)) {
						// 包含list数组的情况
						orderList = res.data.list;
					} else if (res.data && Array.isArray(res.data.records)) {
						// 包含records数组的情况
						orderList = res.data.records;
					} else if (res.data && typeof res.data === 'object') {
						// 其他情况，尝试将对象转为数组
						orderList = [res.data];
					}
					
					// 格式化订单数据
					const formattedOrders = orderList.map(item => {
						// 确定订单状态
						let orderStatus = '未知';
						let rawStatus = 0;
						
						// 根据payStatus和status确定状态
						if (item.payStatus === 1) {
							// 已支付
							if (item.endTime) {
								// 已结束
								orderStatus = '已完成';
								rawStatus = 2;
							} else {
								// 进行中
								orderStatus = '进行中';
								rawStatus = 1;
							}
						} else {
							// 未支付或已取消
							if (item.remark && item.remark.includes('取消')) {
								orderStatus = '已取消';
								rawStatus = 3;
							} else {
								orderStatus = '未支付';
								rawStatus = 0;
							}
						}
						
						return {
							id: item.id || item.orderId,
							orderNo: item.orderNo,
							storeName: item.storeName || item.shopName || '未知门店', // 兼容API返回的shopName
							startTime: item.startTime || item.createTime || '未知时间',
							amount: item.amount ? `¥${parseFloat(item.amount).toFixed(2)}` : '¥0.00',
							status: orderStatus,
							rawStatus: rawStatus
						};
					});
					
					// 第一页直接替换，其他页追加
					if (this.page === 1) {
						this.orders = formattedOrders;
					} else {
						this.orders = [...this.orders, ...formattedOrders];
					}
					
					// 判断是否还有更多数据
					this.hasMore = orderList.length === this.pageSize;
					
					// 更新页码
					if (orderList.length > 0) {
						this.page++;
					}
					
					// 处理空数据情况
					if (this.orders.length === 0) {
						console.log('没有订单数据');
					}
				})
				.catch(err => {
					uni.hideLoading();
					uni.stopPullDownRefresh();
					this.loading = false;
					
					// 显示错误提示
					uni.showToast({
						title: err.message || '获取订单失败',
						icon: 'none'
					});
					
					// 如果是第一页加载失败，显示一些默认数据
					if (this.page === 1 && this.orders.length === 0) {
						this.orders = [
							{
								id: '1',
								orderNo: 'JYC202407100001',
								storeName: '今夜城堡 - 西湖店',
								startTime: '2024-07-10 14:00',
								amount: '¥29.00',
								status: '进行中',
								rawStatus: 1
							},
							{
								id: '2',
								orderNo: 'JYC202407050001',
								storeName: '今夜城堡 - 滨江店',
								startTime: '2024-07-05 20:00',
								amount: '¥35.00',
								status: '已完成',
								rawStatus: 2
							}
						];
					}
				});
		},
		
		// 加载更多订单
		loadMoreOrders() {
			if (this.hasMore) {
				this.loadOrders();
			}
		},
		
		// 查看订单详情
		viewOrderDetail(orderId) {
			uni.navigateTo({
				url: `/pages/orders/detail?id=${orderId}`
			});
		},
		
		// 支付订单
		payOrder(orderId) {
			uni.showLoading({
				title: '处理中...'
			});
			
			// 调用支付接口
			API.order.payment(orderId)
				.then(res => {
					uni.hideLoading();
					
					const payInfo = res.data;
					
					// 调用微信支付
					uni.requestPayment({
						timeStamp: payInfo.timeStamp,
						nonceStr: payInfo.nonceStr,
						package: payInfo.packageValue,
						signType: payInfo.signType,
						paySign: payInfo.paySign,
						success: () => {
							// 支付成功
							uni.showToast({
								title: '支付成功',
								icon: 'success'
							});
							
							// 查询支付状态
							this.checkPaymentStatus(orderId);
						},
						fail: (err) => {
							console.error('支付失败:', err);
							
							// 显示错误提示
							uni.showModal({
								title: '支付失败',
								content: '支付未完成，请重试或选择其他支付方式',
								showCancel: false
							});
						}
					});
				})
				.catch(err => {
					uni.hideLoading();
					
					// 显示错误提示
					uni.showModal({
						title: '支付失败',
						content: err.message || '创建支付订单失败，请重试',
						showCancel: false
					});
				});
		},
		
		// 查询支付状态
		checkPaymentStatus(orderId) {
			uni.showLoading({
				title: '查询支付状态...'
			});
			
			// 调用查询支付状态接口
			API.order.getPaymentStatus(orderId)
				.then(res => {
					uni.hideLoading();
					
					if (res.data === true) {
						// 支付成功，刷新订单列表
						this.refreshOrders();
					} else {
						// 支付失败或未完成
						uni.showModal({
							title: '支付状态',
							content: '支付可能未完成，请查看订单状态',
							showCancel: false
						});
					}
				})
				.catch(err => {
					uni.hideLoading();
					
					// 显示错误提示
					uni.showToast({
						title: err.message || '查询支付状态失败',
						icon: 'none'
					});
					
					// 刷新订单列表
					this.refreshOrders();
				});
		},
		
		// 调试订单列表
		debugOrderList() {
			// 实现调试订单列表的逻辑
			console.log('调试订单列表');
			
			// 显示当前状态
			console.log('当前Tab:', this.activeTab);
			console.log('当前页码:', this.page);
			console.log('当前订单数:', this.orders.length);
			
			// 使用硬编码的API地址
			const apiBaseUrl = 'https://api.jycb888.com/';
			console.log('API基础地址:', apiBaseUrl);
			
			// 创建一个测试请求
			uni.showLoading({
				title: '调试中...'
			});
			
			// 获取对应状态的订单
			let status = 0;
			if (this.activeTab === 1) {
				status = 1; // 进行中
			} else if (this.activeTab === 2) {
				status = 2; // 已完成
			}
			
			// 构建请求参数
			const params = { 
				page: 1, 
				size: this.pageSize 
			};
			
			// 只有当status不为0时才添加到参数中
			if (status !== 0) {
				params.status = status;
			}
			
			// 显示请求信息
			const requestInfo = {
				url: '/api/wx/miniapp/order/list',
				method: 'GET',
				data: params
			};
			console.log('请求信息:', requestInfo);
			
			// 发送测试请求
			uni.request({
				url: apiBaseUrl + requestInfo.url,
				method: requestInfo.method,
				data: requestInfo.data,
				header: {
					'Authorization': uni.getStorageSync('token') || '',
					'Content-Type': 'application/json'
				},
				success: (res) => {
					console.log('原始响应:', res);
					uni.hideLoading();
					
					// 显示调试信息
					uni.showModal({
						title: '调试信息',
						content: '请查看控制台输出',
						showCancel: false
					});
				},
				fail: (err) => {
					console.error('请求失败:', err);
					uni.hideLoading();
					
					// 显示错误信息
					uni.showModal({
						title: '请求失败',
						content: JSON.stringify(err),
						showCancel: false
					});
				}
			});
		}
	}
}
</script>

<style>
	/* iOS适配 */
	.page-orders {
		/* 适配iOS设备的安全区域和灵动岛 */
		padding-top: env(safe-area-inset-top);
	}

	/* 页面基础样式 */
	.page-orders {
		color: #ffffff;
		height: 100vh;
		min-height: 100vh;
		box-sizing: border-box;
		position: relative;
		overflow: hidden; /* 确保页面不会整体滚动 */
		display: flex;
		flex-direction: column;
	}
	
	/* 页面背景样式 */
	.page-background {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		z-index: 0;
	}
	
	/* 背景图片样式 */
	.background-image {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		z-index: 0;
		object-fit: cover;
	}
	
	/* 深磨砂效果叠加层 */
	.frosted-overlay {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-color: rgba(18, 18, 18, 0.7); /* 深色半透明背景 */
		backdrop-filter: blur(8px); /* 较强模糊效果 */
		-webkit-backdrop-filter: blur(8px);
		z-index: 1;
	}
	
	/* 顶部导航样式 */
	.navbar {
		height: 90rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 30rpx;
		/* 完全移除CSS的margin-top，只使用JavaScript动态设置 */
		margin-top: 0;
		position: relative;
		z-index: 100;
	}
	
	.navbar-title {
		font-size: 40rpx;
		font-weight: 600;
	}
	
	.navbar-left, .navbar-right {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	/* 页面内容区域 */
	.page-content {
		flex: 1;
		display: flex;
		flex-direction: column;
		position: relative;
		z-index: 5;
		overflow: hidden; /* 确保内部content滚动 */
		height: calc(100vh - var(--status-bar-height) - 90rpx); /* 计算剩余高度 */
	}
	
	/* 标签页样式 - 往下移动 */
	.tabs-container {
		position: relative;
		z-index: 10;
		border-bottom: 1px solid rgba(255, 255, 255, 0.1);
		margin-bottom: 20rpx;
		margin-top: 20rpx;
		background-color: rgba(30, 30, 40, 0.5);
		border-radius: 20rpx;
		backdrop-filter: blur(10px);
		-webkit-backdrop-filter: blur(10px);
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
		padding: 0 10rpx;
		flex-shrink: 0; /* 防止标签栏被压缩 */
	}
	
	.tabs {
		display: flex;
		height: 90rpx;
		border-bottom: none;
		position: relative;
		overflow: hidden;
	}
	
	.tab {
		flex: 1;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: center;
		position: relative;
		color: var(--text-tertiary, rgba(255, 255, 255, 0.5));
		font-size: 30rpx;
		transition: all 0.3s ease;
		overflow: hidden;
		gap: 8rpx;
	}
	
	.tab.active {
		color: var(--primary-light, #A875FF);
		font-weight: 500;
		text-shadow: 0 0 8rpx rgba(168, 117, 255, 0.5);
	}
	
	.tab:before {
		content: '';
		position: absolute;
		width: 100%;
		height: 100%;
		top: 0;
		left: 0;
		background: rgba(168, 117, 255, 0.05);
		opacity: 0;
		transition: opacity 0.3s ease;
		z-index: -1;
	}
	
	.tab.active:before {
		opacity: 1;
	}
	
	.tab-line {
		position: absolute;
		bottom: 0;
		width: 40rpx;
		height: 4rpx;
		background: linear-gradient(to right, rgba(168, 117, 255, 0.8), rgba(139, 92, 246, 0.9));
		border-radius: 2rpx;
		box-shadow: 0 0 10rpx rgba(168, 117, 255, 0.5);
		transition: width 0.3s ease;
	}
	
	.tab:active {
		transform: scale(0.98);
	}
	
	/* 内容区域 */
	.content {
		flex: 1;
		padding: 30rpx 30rpx;
		position: relative;
		width: 100%;
		box-sizing: border-box;
		overflow-y: auto !important; /* 强制确保可以垂直滚动 */
		-webkit-overflow-scrolling: touch; /* 增加滚动惯性 */
		height: 100%; /* 确保高度占满 */
		padding-bottom: 50rpx; /* 添加底部内边距，确保内容不会被遮挡 */
		max-height: calc(100vh - var(--status-bar-height) - 90rpx - 130rpx); /* 减去导航栏和标签栏的高度 */
	}
	
	/* 订单卡片样式 */
	.order-item {
		margin-bottom: 30rpx;
		padding: 30rpx;
		background-color: rgba(30, 30, 40, 0.5);
		border-radius: 20rpx;
		border: 1px solid rgba(168, 117, 255, 0.3);
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
		backdrop-filter: blur(10px);
		-webkit-backdrop-filter: blur(10px);
		transition: all 0.3s ease;
		width: 100%;
		box-sizing: border-box;
		margin-left: auto;
		margin-right: auto;
	}
	
	.order-item:active {
		transform: scale(0.98);
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
	}
	
	.order-header {
		margin-bottom: 10rpx;
	}
	
	.order-store {
		display: flex;
		flex-direction: column;
	}
	
	.order-store .title-sm {
		margin-bottom: 0;
	}
	
	/* 分隔线 */
	.divider {
		height: 1px;
		background: linear-gradient(to right, rgba(168, 117, 255, 0.1), rgba(168, 117, 255, 0.3), rgba(168, 117, 255, 0.1));
		margin: 10rpx 0;
	}
	
	/* 状态标签 */
	.status {
		padding: 6rpx 18rpx;
		border-radius: 20rpx;
		font-size: 26rpx;
		font-weight: 500;
		display: inline-flex;
		align-items: center;
		justify-content: center;
		height: 40rpx;
		min-width: 80rpx;
		text-align: center;
		line-height: 1;
		white-space: nowrap;
		box-sizing: border-box;
	}
	
	.status.active {
		background-color: rgba(168, 117, 255, 0.15);
		color: var(--primary-light, #A875FF);
		border: 1px solid rgba(168, 117, 255, 0.25);
		box-shadow: 0 0 8rpx rgba(168, 117, 255, 0.2);
	}
	
	.status.unpaid {
		background-color: rgba(255, 152, 0, 0.15);
		color: #FF9800;
		border: 1px solid rgba(255, 152, 0, 0.25);
		box-shadow: 0 0 8rpx rgba(255, 152, 0, 0.2);
	}
	
	.status.completed {
		background-color: rgba(76, 175, 80, 0.15);
		color: #4CAF50;
		border: 1px solid rgba(76, 175, 80, 0.25);
		box-shadow: 0 0 8rpx rgba(76, 175, 80, 0.2);
	}
	
	.status.cancelled {
		background-color: rgba(244, 67, 54, 0.15);
		color: #F44336;
		border: 1px solid rgba(244, 67, 54, 0.25);
		box-shadow: 0 0 8rpx rgba(244, 67, 54, 0.2);
	}
	
	.status.inactive {
		background-color: rgba(255, 255, 255, 0.07);
		color: var(--text-tertiary, rgba(255, 255, 255, 0.5));
		border: 1px solid rgba(255, 255, 255, 0.1);
	}
	
	/* 订单详情项 */
	.detail-item {
		margin-bottom: 20rpx;
	}
	
	/* 详情标签和值 - 增大字体 */
	.detail-label {
		color: rgba(255, 255, 255, 0.7);
		font-size: 30rpx;
		display: flex;
		align-items: center;
	}
	
	.detail-value {
		color: #FFFFFF;
		font-size: 30rpx;
	}
	
	/* 按钮样式 */
	.btn {
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 15rpx;
		padding: 20rpx;
		font-size: 28rpx;
		font-weight: 600;
		transition: all 0.3s ease;
	}
	
	.btn-primary {
		background: linear-gradient(135deg, rgba(168, 117, 255, 0.8), rgba(139, 92, 246, 0.9));
		color: #FFFFFF;
		box-shadow: 0 4rpx 20rpx rgba(168, 117, 255, 0.4);
		border: none;
	}
	
	.btn-primary:active {
		transform: scale(0.98);
		box-shadow: 0 2rpx 10rpx rgba(168, 117, 255, 0.3);
	}
	
	.btn-outline {
		background-color: transparent;
		border: 1px solid rgba(168, 117, 255, 0.4);
		color: var(--primary-light, #A875FF);
	}
	
	.btn-outline:active {
		background-color: rgba(168, 117, 255, 0.1);
		transform: scale(0.98);
	}
	
	.btn-sm {
		font-size: 26rpx;
		padding: 15rpx;
	}
	
	/* 空状态样式 */
	.empty-state {
		padding: 100rpx 0;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		background-color: rgba(30, 30, 40, 0.5);
		border-radius: 20rpx;
		backdrop-filter: blur(10px);
		-webkit-backdrop-filter: blur(10px);
		border: 1px solid rgba(255, 255, 255, 0.1);
		margin-top: 30rpx; /* 添加顶部间距 */
		min-height: 400rpx; /* 确保空状态有足够高度 */
	}
	
	.empty-text {
		margin-top: 30rpx;
		margin-bottom: 30rpx;
		color: var(--text-tertiary, rgba(255, 255, 255, 0.5));
		font-size: 28rpx;
	}
	
	/* Material Icons 字体 */
	@font-face {
		font-family: 'Material Icons';
		font-style: normal;
		font-weight: 400;
		src: url(https://fonts.gstatic.com/s/materialicons/v139/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format('woff2');
	}

	.material-icons {
		font-family: 'Material Icons';
		font-weight: normal;
		font-style: normal;
		font-size: 24rpx;
		line-height: 1;
		letter-spacing: normal;
		text-transform: none;
		display: inline-block;
		white-space: nowrap;
		word-wrap: normal;
		direction: ltr;
		-webkit-font-smoothing: antialiased;
		-moz-osx-font-smoothing: grayscale;
	}
	
	.material-icons.md-18 {
		font-size: 36rpx;
	}
	
	.material-icons.md-24 {
		font-size: 48rpx;
	}
	
	.material-icons.md-48 {
		font-size: 96rpx;
	}
	
	.material-icons.text-primary {
		color: var(--text-primary, #FFFFFF);
	}
	
	.material-icons.text-tertiary {
		color: var(--text-tertiary, rgba(255, 255, 255, 0.5));
	}
	
	.icon-inline {
		margin-right: 8rpx;
		vertical-align: middle;
	}
	
	/* 辅助类 */
	.flex {
		display: flex;
	}
	
	.flex-col {
		display: flex;
		flex-direction: column;
	}
	
	.justify-between {
		justify-content: space-between;
	}
	
	.items-center {
		align-items: center;
	}
	
	.text-center {
		text-align: center;
	}
	
	.mt-sm {
		margin-top: 16rpx;
	}
	
	.mt-md {
		margin-top: 30rpx;
	}
	
	.mt-lg {
		margin-top: 60rpx;
	}
	
	.gap-md {
		gap: 20rpx;
	}
	
	.flex-1 {
		flex: 1;
	}
	
	/* 文本样式 */
	.title-sm {
		font-size: 32rpx;
		font-weight: 500;
		color: #ffffff;
	}
	
	.text-secondary {
		color: rgba(255, 255, 255, 0.7);
		font-size: 28rpx;
	}
	
	.text-tertiary {
		color: rgba(255, 255, 255, 0.5);
		font-size: 26rpx;
	}
	
	.text-primary {
		color: #FFFFFF;
		font-size: 28rpx;
	}
	
	.primary-light {
		color: var(--primary-light, #A875FF);
		font-size: 34rpx;
		font-weight: 600;
	}
	
	.agreement-tip {
		margin-top: 20rpx;
		text-align: center;
		font-size: 24rpx;
		color: rgba(255, 255, 255, 0.6);
	}
	
	/* 调试按钮样式 */
	.debug-btn {
		position: absolute;
		right: 20rpx;
		top: 10rpx;
		background-color: rgba(255, 0, 0, 0.7);
		color: white;
		padding: 4rpx 16rpx;
		border-radius: 20rpx;
		font-size: 24rpx;
		z-index: 10;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
		border: 1px solid rgba(255, 0, 0, 0.3);
	}
	
	.debug-btn:active {
		transform: scale(0.95);
		background-color: rgba(255, 0, 0, 0.8);
	}

	/* 移除所有CSS适配，完全依赖JavaScript动态设置 */
</style>