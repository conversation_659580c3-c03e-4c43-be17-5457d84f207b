/**
 * 今夜城堡用户端登录工具
 * 封装了微信登录相关的功能
 */

import API from './api.js';

// 登录状态
const LOGIN_STATUS = {
  NOT_LOGGED_IN: 0,  // 未登录
  LOGGING_IN: 1,     // 登录中
  LOGGED_IN: 2       // 已登录
};

// 登录工具类
const LoginManager = {
  // 登录状态
  status: LOGIN_STATUS.NOT_LOGGED_IN,
  
  // 登录中的Promise
  loginPromise: null,
  
  /**
   * 检查登录状态
   * @returns {Boolean} 是否已登录
   */
  isLoggedIn() {
    const token = uni.getStorageSync('token');
    return !!token;
  },
  
  /**
   * 获取登录状态
   * @returns {Number} 登录状态码
   */
  getLoginStatus() {
    if (this.isLoggedIn()) {
      return LOGIN_STATUS.LOGGED_IN;
    }
    return this.status;
  },
  
  /**
   * 获取手机号
   * @returns {Promise} Promise对象，resolve时返回手机号凭证
   */
  getPhoneNumber() {
    return new Promise((resolve, reject) => {
      // 微信小程序中获取手机号需要通过button组件的open-type="getPhoneNumber"
      // 这里只能通过提示用户去点击获取手机号的按钮
      uni.showModal({
        title: '授权提示',
        content: '需要您授权获取手机号以完成登录，请点击确认后在下一步操作中点击"获取手机号"按钮',
        confirmText: '确认',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            // 用户点击确认，但无法直接获取手机号
            // 返回null，让调用方知道需要通过button获取
            resolve(null);
          } else {
            // 用户拒绝授权
            reject(new Error('用户拒绝授权手机号'));
          }
        }
      });
    });
  },
  
  /**
   * 执行微信登录
   * @param {Boolean} force 是否强制重新登录
   * @param {Object} userInfo 用户信息对象（可选，包含头像和昵称）
   * @param {String} phoneCode 手机号凭证（可选）
   * @returns {Promise} Promise对象
   */
  login(force = false, userInfo = null, phoneCode = null) {
    // 如果已登录且不强制重新登录，直接返回成功
    if (this.isLoggedIn() && !force) {
      return Promise.resolve({ success: true });
    }
    
    // 如果正在登录中，返回已有的Promise
    if (this.status === LOGIN_STATUS.LOGGING_IN && this.loginPromise) {
      return this.loginPromise;
    }
    
    // 开始登录流程
    this.status = LOGIN_STATUS.LOGGING_IN;
    
    // 创建登录Promise
    this.loginPromise = new Promise((resolve, reject) => {
      // 每次登录都重新获取微信登录code，避免使用过期code
      uni.login({
        provider: 'weixin',
        success: (loginRes) => {
          if (loginRes.code) {
            console.log('微信登录成功，获取到新code:', loginRes.code);
            
            // 提取用户信息（如果有）
            const avatarUrl = userInfo ? userInfo.avatarUrl : '';
            const nickname = userInfo ? userInfo.nickName : '';
            
            // 调用后端登录接口
            API.user.login(loginRes.code, avatarUrl, nickname, phoneCode)
              .then(res => {
                console.log('登录成功了', res);
                // 登录成功，保存token
                uni.setStorageSync('token', res.data.token);
                
                // 保存用户ID和其他信息
                if (res.data.userId) {
                  uni.setStorageSync('userId', res.data.userId);
                }
                
                // 更新登录状态
                this.status = LOGIN_STATUS.LOGGED_IN;
                
                // 处理新用户
                const isNewUser = res.data.isNewUser || false;
                
                // 返回成功结果
                resolve({
                  success: true,
                  isNewUser: isNewUser,
                  userId: res.data.userId,
                  token: res.data.token
                });
              })
              .catch(err => {
                // 登录失败
                console.error('后端登录接口调用失败:', err);
                this.status = LOGIN_STATUS.NOT_LOGGED_IN;
                
                // 清除可能存在的过期token
                if (err.code === 500 && err.message && err.message.includes('无效的code')) {
                  console.log('检测到无效code错误，清除token');
                  uni.removeStorageSync('token');
                }
                
                reject(err);
              });
          } else {
            // 获取code失败
            console.error('微信登录失败:', loginRes);
            this.status = LOGIN_STATUS.NOT_LOGGED_IN;
            reject(new Error('微信登录失败'));
          }
        },
        fail: (err) => {
          // 微信登录失败
          console.error('微信登录失败:', err);
          this.status = LOGIN_STATUS.NOT_LOGGED_IN;
          reject(err);
        }
      });
    });
    
    return this.loginPromise;
  },
  
  /**
   * 处理获取用户信息后的登录
   * @param {Object} e 用户信息事件对象
   * @returns {Promise} Promise对象
   */
  handleUserInfoLogin(e) {
    if (e.detail && e.detail.userInfo) {
      // 获取到用户信息
      return this.login(true, e.detail.userInfo);
    } else {
      // 用户拒绝授权
      console.error('用户拒绝授权获取信息');
      // 仍然尝试登录，但不传递用户信息
      return this.login(true);
    }
  },
  
  /**
   * 确保已登录，如果未登录则自动登录
   * @returns {Promise} Promise对象
   */
  ensureLogin() {
    if (this.isLoggedIn()) {
      return Promise.resolve({ success: true });
    }
    return this.login();
  },
  
  /**
   * 退出登录
   */
  logout() {
    // 清除token
    uni.removeStorageSync('token');
    uni.removeStorageSync('userId');
    
    // 更新状态
    this.status = LOGIN_STATUS.NOT_LOGGED_IN;
    
    // 清除登录Promise
    this.loginPromise = null;
    
    return Promise.resolve({ success: true });
  }
};

export default LoginManager; 