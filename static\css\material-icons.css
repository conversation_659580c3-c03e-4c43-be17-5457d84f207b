/* Material Icons 字体 - 使用本地字符映射代替在线字体 */
.material-icons {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-smoothing: antialiased;
}

/* 图标尺寸 */
.material-icons.md-18 { font-size: 18px; }
.material-icons.md-24 { font-size: 24px; }
.material-icons.md-36 { font-size: 36px; }
.material-icons.md-48 { font-size: 48px; }

/* 图标颜色 */
.material-icons.md-dark { color: rgba(0, 0, 0, 0.54); }
.material-icons.md-dark.md-inactive { color: rgba(0, 0, 0, 0.26); }

.material-icons.md-light { color: rgba(255, 255, 255, 1); }
.material-icons.md-light.md-inactive { color: rgba(255, 255, 255, 0.3); }

/* 自定义颜色 */
.material-icons.primary { color: var(--primary, #8B5CF6); }
.material-icons.primary-light { color: var(--primary-light, #A875FF); }
.material-icons.neon-green { color: var(--neon-green, #39D353); }
.material-icons.neon-pink { color: var(--neon-pink, #FF69B4); }
.material-icons.neon-yellow { color: var(--neon-yellow, #FFD700); }
.material-icons.neon-blue { color: var(--neon-blue, #00BFFF); } 