














































































































































































































































































































































































































































/* iOS适配 */
.page-order-detail {
	/* 适配iOS设备的安全区域和灵动岛 */
	padding-top: env(safe-area-inset-top);
}
/* 页面基础样式 */
.page-order-detail {
	color: #ffffff;
	height: 100vh;
	min-height: 100vh;
	box-sizing: border-box;
	position: relative;
	overflow: hidden;
	display: flex;
	flex-direction: column;
}
/* 页面背景样式 */
.page-background {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 0;
}
/* 背景图片样式 */
.background-image {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 0;
	object-fit: cover;
}
/* 深磨砂效果叠加层 */
.frosted-overlay {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(18, 18, 18, 0.7); /* 深色半透明背景 */
	backdrop-filter: blur(8px); /* 较强模糊效果 */
	-webkit-backdrop-filter: blur(8px);
	z-index: 1;
}
/* 顶部导航样式 */
.navbar {
	height: 90rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 30rpx;
	/* 完全移除CSS的margin-top，只使用JavaScript动态设置 */
	margin-top: 0;
	position: relative;
	z-index: 100;
}
.navbar-title {
	font-size: 40rpx;
	font-weight: 600;
}
.navbar-left, .navbar-right {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}
.icon-back {
	font-size: 40rpx;
	color: #A875FF;
}
/* 页面内容区域 */
.page-content {
	flex: 1;
	display: flex;
	flex-direction: column;
	position: relative;
	z-index: 5;
	padding: 0 30rpx;
}
.content {
	flex: 1;
	padding: 20rpx 0;
	position: relative;
	width: 100%;
	box-sizing: border-box;
}
/* 订单状态卡片 */
.order-status-card {
	display: flex;
	align-items: center;
	padding: 30rpx;
	border-radius: 16rpx;
	background: rgba(30, 30, 40, 0.5);
	backdrop-filter: blur(10px);
	-webkit-backdrop-filter: blur(10px);
	border: 1px solid rgba(168, 117, 255, 0.3);
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
	margin-top: 20rpx;
}
.order-status-card.active {
	background: rgba(168, 117, 255, 0.15);
}
.order-status-card.active .status-icon {
	color: #A875FF;
}
.order-status-card.active .status-title {
	color: #A875FF;
}
.order-status-card.unpaid {
	background: rgba(255, 152, 0, 0.15);
}
.order-status-card.unpaid .status-icon {
	color: #FF9800;
}
.order-status-card.unpaid .status-title {
	color: #FF9800;
}
.order-status-card.completed {
	background: rgba(76, 175, 80, 0.15);
}
.order-status-card.completed .status-icon {
	color: #4CAF50;
}
.order-status-card.completed .status-title {
	color: #4CAF50;
}
.order-status-card.cancelled {
	background: rgba(244, 67, 54, 0.15);
}
.order-status-card.cancelled .status-icon {
	color: #F44336;
}
.order-status-card.cancelled .status-title {
	color: #F44336;
}
.order-status-card.inactive {
	background: rgba(255, 255, 255, 0.07);
}
.order-status-card.inactive .status-icon {
	color: rgba(255, 255, 255, 0.5);
}
.order-status-card.inactive .status-title {
	color: rgba(255, 255, 255, 0.5);
}
.status-icon {
	margin-right: 20rpx;
	font-size: 40rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}
.status-text {
	display: flex;
	flex-direction: column;
}
.status-title {
	font-size: 32rpx;
	font-weight: 500;
}
.status-desc {
	font-size: 24rpx;
	color: rgba(255, 255, 255, 0.6);
	margin-top: 4rpx;
}
/* 卡片样式 */
.card {
	background: rgba(30, 30, 40, 0.5);
	border-radius: 16rpx;
	overflow: hidden;
	backdrop-filter: blur(10px);
	-webkit-backdrop-filter: blur(10px);
	border: 1px solid rgba(255, 255, 255, 0.1);
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
}
.card-header {
	padding: 20rpx 30rpx;
	border-bottom: 1px solid rgba(255, 255, 255, 0.1);
	font-size: 28rpx;
	font-weight: 500;
	display: flex;
	align-items: center;
}
.card-icon {
	margin-right: 10rpx;
}
.card-content {
	padding: 20rpx 30rpx;
}
/* 详情项样式 */
.detail-item {
	display: flex;
	justify-content: space-between;
	margin-bottom: 10rpx;
}
.detail-label {
	color: rgba(255, 255, 255, 0.7);
	font-size: 28rpx;
}
.detail-value {
	color: #FFFFFF;
	font-size: 28rpx;
}
/* 按钮样式 */
.btn {
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 40rpx;
	padding: 20rpx;
	font-size: 28rpx;
	font-weight: 600;
	transition: all 0.3s ease;
}
.btn-primary {
	background: linear-gradient(135deg, rgba(168, 117, 255, 0.8), rgba(139, 92, 246, 0.9));
	color: #FFFFFF;
	box-shadow: 0 4rpx 20rpx rgba(168, 117, 255, 0.4);
	border: none;
}
.btn-outline {
	background-color: transparent;
	border: 1px solid rgba(168, 117, 255, 0.4);
	color: #A875FF;
}
.btn-block {
	width: 100%;
}
.btn-icon {
	margin-right: 8rpx;
}
/* 辅助类 */
.primary-light {
	color: #A875FF;
	font-weight: 600;
}
.success-light {
	color: #4CAF50;
}
.error-light {
	color: #F44336;
}
.actions {
	padding: 20rpx 0 40rpx;
}
.flex {
	display: flex;
}
.justify-between {
	justify-content: space-between;
}
.mt-sm {
	margin-top: 10rpx;
}
.mt-md {
	margin-top: 20rpx;
}
.mt-lg {
	margin-top: 30rpx;
}
/* Material Icons 字体 */
@font-face {
	font-family: 'Material Icons';
	font-style: normal;
	font-weight: 400;
	src: url(https://fonts.gstatic.com/s/materialicons/v139/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format('woff2');
}
.material-icons {
	font-family: 'Material Icons';
	font-weight: normal;
	font-style: normal;
	font-size: 24rpx;
	line-height: 1;
	letter-spacing: normal;
	text-transform: none;
	display: inline-block;
	white-space: nowrap;
	word-wrap: normal;
	direction: ltr;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}
.material-icons.md-18 {
	font-size: 36rpx;
}
.material-icons.md-24 {
	font-size: 48rpx;
}
.material-icons.md-48 {
	font-size: 96rpx;
}
.material-icons.text-primary {
	color: var(--text-primary, #FFFFFF);
}
.material-icons.text-tertiary {
	color: var(--text-tertiary, rgba(255, 255, 255, 0.5));
}
.icon-inline {
	margin-right: 8rpx;
	vertical-align: middle;
}
/* 移除所有CSS适配，完全依赖JavaScript动态设置 */

