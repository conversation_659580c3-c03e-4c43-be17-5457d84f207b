
































































































































































































































































































/* CSS变量定义 */
page {
	--primary-light: #A875FF;
	--neon-pink: #ff36f9;
	--neon-blue: #00BFFF;
	--neon-yellow: #FFD700;
	--divider-color: rgba(255, 255, 255, 0.08);
}
/* 页面基础样式 */
.page-profile {
	padding-top: 140rpx;
	padding-bottom: calc(170rpx + env(safe-area-inset-bottom));
	color: #ffffff;
	height: 100vh;
	min-height: 100vh;
	box-sizing: border-box;
	position: relative;
	overflow: hidden;
}
/* 页面背景样式 */
.page-background {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 0;
}
/* 背景图片样式 */
.background-image {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 0;
	object-fit: cover;
}
/* 磨砂效果叠加层 */
.frosted-overlay {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(18, 18, 18, 0.4);
	backdrop-filter: blur(5px);
	-webkit-backdrop-filter: blur(5px);
	z-index: 1;
}
.status-bar {
	width: 100%;
	background: rgba(18, 18, 18, 0.8);
	backdrop-filter: blur(10px);
	-webkit-backdrop-filter: blur(10px);
	position: fixed;
	top: 0;
	left: 0;
	z-index: 100;
}
.content {
	padding: 30rpx;
	position: relative;
	z-index: 2;
	height: calc(100vh - 140rpx - env(safe-area-inset-bottom) - 170rpx);
	display: flex;
	flex-direction: column;
	overflow-y: auto;
	overflow-x: hidden;
	-webkit-overflow-scrolling: touch;
}
/* 个人信息区域 - 移除背景相关样式 */
.profile-header {
	display: flex;
	align-items: center;
	padding: 40rpx;
	margin-bottom: 30rpx;
}
.avatar-container {
	margin-right: 30rpx;
}
/* 美化头像样式 */
.avatar-wrapper {
	width: 120rpx;
	height: 120rpx;
	border-radius: 60rpx;
	background: linear-gradient(145deg, rgba(168, 117, 255, 0.8), rgba(255, 54, 249, 0.5));
	padding: 4rpx;
	box-shadow: 0 0 20rpx rgba(168, 117, 255, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
}
.avatar {
	width: 112rpx;
	height: 112rpx;
	border-radius: 56rpx;
	background-color: rgba(120, 50, 200, 0.7);
	border: 1px solid rgba(255, 255, 255, 0.3);
	box-shadow: inset 0 0 10rpx rgba(255, 255, 255, 0.2);
}
/* 用户名和状态横向排列 */
.user-name-row {
	display: flex;
	align-items: center;
	margin-bottom: 10rpx;
}
.user-name-row .title-md {
	margin-right: 16rpx;
}
/* 菜单区域 */
.menu-section {
	margin-bottom: 30rpx;
	flex: 1;
	display: flex;
	flex-direction: column;
}
.menu-list {
	display: flex;
	flex-direction: column;
	margin-bottom: 30rpx;
	background-color: rgba(168, 117, 255, 0.05);
	border-radius: 20rpx;
	overflow: hidden;
	border: 2rpx solid rgba(168, 117, 255, 0.4);
	box-shadow: 0 0 20rpx rgba(168, 117, 255, 0.2);
}
.menu-item {
	display: flex;
	align-items: center;
	padding: 36rpx;
	transition: background-color 0.2s;
	position: relative;
}
/* 除最后一个菜单项外，其他菜单项底部添加分隔线 */
.menu-item:not(:last-child)::after {
	content: '';
	position: absolute;
	left: 90rpx;
	right: 30rpx;
	bottom: 0;
	height: 1px;
	background-color: var(--divider-color);
}
.menu-item:active {
	background-color: rgba(255, 255, 255, 0.05);
}
.menu-icon {
	margin-right: 20rpx;
	width: 70rpx;
	height: 70rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	border-radius: 50%;
	background-color: transparent;
}
.menu-icon .material-icons {
	font-size: 44rpx;
}
.menu-content {
	flex: 1;
}
.menu-title {
	font-size: 36rpx;
	font-weight: 500;
	color: #ffffff;
	margin-bottom: 8rpx;
}
.menu-arrow {
	width: 40rpx;
	display: flex;
	justify-content: center;
	align-items: center;
}
.menu-arrow .material-icons {
	font-size: 28rpx;
}
/* 关于区域 */
.about-section {
	margin-top: 40rpx;
	margin-bottom: 80rpx;
	padding-bottom: 100rpx;
}
/* Material Icons 字体 */
@font-face {
	font-family: 'Material Icons';
	font-style: normal;
	font-weight: 400;
	src: url(https://fonts.gstatic.com/s/materialicons/v139/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format('woff2');
}
.material-icons {
	font-family: 'Material Icons';
	font-weight: normal;
	font-style: normal;
	font-size: 24rpx;
	line-height: 1;
	letter-spacing: normal;
	text-transform: none;
	display: inline-block;
	white-space: nowrap;
	word-wrap: normal;
	direction: ltr;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}
.material-icons.primary-light {
	color: var(--primary-light, #A875FF);
}
.material-icons.text-tertiary {
	color: var(--text-tertiary, rgba(255, 255, 255, 0.5));
}
/* 辅助类 */
.flex {
	display: flex;
}
.justify-between {
	justify-content: space-between;
}
.items-center {
	align-items: center;
}
.text-center {
	text-align: center;
}
.mt-sm {
	margin-top: 10rpx;
}
.mt-md {
	margin-top: 20rpx;
}
.mt-lg {
	margin-top: 40rpx;
}
.mb-sm {
	margin-bottom: 10rpx;
}
/* 状态标签 */
.status {
	padding: 6rpx 16rpx;
	border-radius: 30rpx;
	font-size: 24rpx;
	display: inline-block;
}
.status.active {
	background-color: rgba(168, 117, 255, 0.2);
	color: var(--primary-light, #A875FF);
}
/* 文本样式 */
.title-md {
	font-size: 36rpx;
	font-weight: 600;
	color: #ffffff;
}
.title-sm {
	font-size: 30rpx;
	font-weight: 500;
	color: #ffffff;
}
.text-secondary {
	color: rgba(255, 255, 255, 0.7);
}
.text-tertiary {
	color: rgba(255, 255, 255, 0.5);
	font-size: 30rpx;
}
.text-primary {
	color: #ffffff;
}
.primary-light {
	color: var(--primary-light, #A875FF);
}
.neon-pink {
	color: var(--neon-pink, #ff36f9);
}
.neon-blue {
	color: var(--neon-blue, #00BFFF);
}
.neon-yellow {
	color: var(--neon-yellow, #FFD700);
}
/* 按钮样式 */
.btn {
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 8rpx;
	padding: 16rpx 24rpx;
	font-size: 28rpx;
	font-weight: 500;
}
.btn-outline {
	background-color: transparent;
	border: 1px solid rgba(255, 255, 255, 0.2);
	color: #ffffff;
}
.btn-sm {
	padding: 12rpx 20rpx;
	font-size: 24rpx;
}

