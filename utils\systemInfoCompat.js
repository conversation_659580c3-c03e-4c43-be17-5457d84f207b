/**
 * 微信小程序系统信息API兼容性工具
 * 用于替代已弃用的 wx.getSystemInfoSync
 * 使用新的分离式API: wx.getSystemSetting/wx.getAppAuthorizeSetting/wx.getDeviceInfo/wx.getWindowInfo/wx.getAppBaseInfo
 */

/**
 * 获取完整的系统信息（同步版本）
 * 替代 wx.getSystemInfoSync()
 * @returns {Object} 系统信息对象
 */
export function getSystemInfoSync() {
  try {
    // 检查是否在微信小程序环境
    if (typeof wx === 'undefined') {
      // 非微信小程序环境，使用uni-app API
      return uni.getSystemInfoSync();
    }

    // 使用新的分离式API获取各种信息
    const deviceInfo = wx.getDeviceInfo ? wx.getDeviceInfo() : {};
    const windowInfo = wx.getWindowInfo ? wx.getWindowInfo() : {};
    const appBaseInfo = wx.getAppBaseInfo ? wx.getAppBaseInfo() : {};
    const systemSetting = wx.getSystemSetting ? wx.getSystemSetting() : {};
    const appAuthorizeSetting = wx.getAppAuthorizeSetting ? wx.getAppAuthorizeSetting() : {};

    // 如果新API不可用，回退到旧API
    if (!wx.getDeviceInfo || !wx.getWindowInfo || !wx.getAppBaseInfo) {
      console.warn('新的系统信息API不可用，回退到 wx.getSystemInfoSync');
      return wx.getSystemInfoSync();
    }

    // 合并所有信息，保持与旧API的兼容性
    const systemInfo = {
      // 设备信息 (来自 wx.getDeviceInfo)
      brand: deviceInfo.brand || '',
      model: deviceInfo.model || '',
      system: deviceInfo.system || '',
      platform: deviceInfo.platform || '',
      
      // 窗口信息 (来自 wx.getWindowInfo)
      pixelRatio: windowInfo.pixelRatio || 1,
      screenWidth: windowInfo.screenWidth || 0,
      screenHeight: windowInfo.screenHeight || 0,
      windowWidth: windowInfo.windowWidth || 0,
      windowHeight: windowInfo.windowHeight || 0,
      statusBarHeight: windowInfo.statusBarHeight || 0,
      safeArea: windowInfo.safeArea || {},
      
      // 应用基础信息 (来自 wx.getAppBaseInfo)
      SDKVersion: appBaseInfo.SDKVersion || '',
      version: appBaseInfo.version || '',
      language: appBaseInfo.language || '',
      theme: appBaseInfo.theme || 'light',
      
      // 系统设置 (来自 wx.getSystemSetting)
      bluetoothEnabled: systemSetting.bluetoothEnabled || false,
      locationEnabled: systemSetting.locationEnabled || false,
      wifiEnabled: systemSetting.wifiEnabled || false,
      deviceOrientation: systemSetting.deviceOrientation || 'portrait',
      
      // 应用授权设置 (来自 wx.getAppAuthorizeSetting)
      albumAuthorized: appAuthorizeSetting.albumAuthorized || 'not determined',
      bluetoothAuthorized: appAuthorizeSetting.bluetoothAuthorized || 'not determined',
      cameraAuthorized: appAuthorizeSetting.cameraAuthorized || 'not determined',
      locationAuthorized: appAuthorizeSetting.locationAuthorized || 'not determined',
      locationReducedAccuracy: appAuthorizeSetting.locationReducedAccuracy || false,
      microphoneAuthorized: appAuthorizeSetting.microphoneAuthorized || 'not determined',
      notificationAuthorized: appAuthorizeSetting.notificationAuthorized || 'not determined',
      notificationAlertAuthorized: appAuthorizeSetting.notificationAlertAuthorized || 'not determined',
      notificationBadgeAuthorized: appAuthorizeSetting.notificationBadgeAuthorized || 'not determined',
      notificationSoundAuthorized: appAuthorizeSetting.notificationSoundAuthorized || 'not determined',
      phoneCalendarAuthorized: appAuthorizeSetting.phoneCalendarAuthorized || 'not determined'
    };

    return systemInfo;
  } catch (error) {
    console.error('获取系统信息失败:', error);
    
    // 出错时尝试使用旧API作为最后的回退
    try {
      if (typeof wx !== 'undefined' && wx.getSystemInfoSync) {
        return wx.getSystemInfoSync();
      } else if (typeof uni !== 'undefined' && uni.getSystemInfoSync) {
        return uni.getSystemInfoSync();
      }
    } catch (fallbackError) {
      console.error('回退API也失败:', fallbackError);
    }
    
    // 返回默认值
    return {
      brand: 'unknown',
      model: 'unknown',
      system: 'unknown',
      platform: 'unknown',
      pixelRatio: 1,
      screenWidth: 375,
      screenHeight: 667,
      windowWidth: 375,
      windowHeight: 667,
      statusBarHeight: 20,
      SDKVersion: '2.0.0',
      version: '1.0.0',
      language: 'zh_CN',
      theme: 'light',
      bluetoothEnabled: false,
      locationEnabled: false,
      wifiEnabled: false,
      deviceOrientation: 'portrait',
      safeArea: { top: 0, left: 0, right: 375, bottom: 667, width: 375, height: 667 }
    };
  }
}

/**
 * 获取完整的系统信息（异步版本）
 * 替代 wx.getSystemInfo()
 * @param {Object} options 选项对象 {success, fail, complete}
 */
export function getSystemInfo(options = {}) {
  try {
    // 检查是否在微信小程序环境
    if (typeof wx === 'undefined') {
      // 非微信小程序环境，使用uni-app API
      return uni.getSystemInfo(options);
    }

    // 尝试使用同步版本获取信息
    const systemInfo = getSystemInfoSync();
    
    if (options.success) {
      options.success(systemInfo);
    }
    if (options.complete) {
      options.complete(systemInfo);
    }
    
    return systemInfo;
  } catch (error) {
    console.error('获取系统信息失败:', error);
    
    if (options.fail) {
      options.fail(error);
    }
    if (options.complete) {
      options.complete(error);
    }
    
    // 回退到原生API
    if (typeof wx !== 'undefined' && wx.getSystemInfo) {
      return wx.getSystemInfo(options);
    } else if (typeof uni !== 'undefined' && uni.getSystemInfo) {
      return uni.getSystemInfo(options);
    }
  }
}

/**
 * 获取设备信息
 * @returns {Object} 设备信息
 */
export function getDeviceInfo() {
  if (typeof wx !== 'undefined' && wx.getDeviceInfo) {
    return wx.getDeviceInfo();
  }
  
  // 回退方案
  const systemInfo = getSystemInfoSync();
  return {
    brand: systemInfo.brand,
    model: systemInfo.model,
    system: systemInfo.system,
    platform: systemInfo.platform
  };
}

/**
 * 获取窗口信息
 * @returns {Object} 窗口信息
 */
export function getWindowInfo() {
  if (typeof wx !== 'undefined' && wx.getWindowInfo) {
    return wx.getWindowInfo();
  }
  
  // 回退方案
  const systemInfo = getSystemInfoSync();
  return {
    pixelRatio: systemInfo.pixelRatio,
    screenWidth: systemInfo.screenWidth,
    screenHeight: systemInfo.screenHeight,
    windowWidth: systemInfo.windowWidth,
    windowHeight: systemInfo.windowHeight,
    statusBarHeight: systemInfo.statusBarHeight,
    safeArea: systemInfo.safeArea
  };
}

/**
 * 获取应用基础信息
 * @returns {Object} 应用基础信息
 */
export function getAppBaseInfo() {
  if (typeof wx !== 'undefined' && wx.getAppBaseInfo) {
    return wx.getAppBaseInfo();
  }
  
  // 回退方案
  const systemInfo = getSystemInfoSync();
  return {
    SDKVersion: systemInfo.SDKVersion,
    version: systemInfo.version,
    language: systemInfo.language,
    theme: systemInfo.theme
  };
}

/**
 * 获取系统设置
 * @returns {Object} 系统设置信息
 */
export function getSystemSetting() {
  if (typeof wx !== 'undefined' && wx.getSystemSetting) {
    return wx.getSystemSetting();
  }
  
  // 回退方案
  const systemInfo = getSystemInfoSync();
  return {
    bluetoothEnabled: systemInfo.bluetoothEnabled,
    locationEnabled: systemInfo.locationEnabled,
    wifiEnabled: systemInfo.wifiEnabled,
    deviceOrientation: systemInfo.deviceOrientation
  };
}

/**
 * 获取应用授权设置
 * @returns {Object} 应用授权设置信息
 */
export function getAppAuthorizeSetting() {
  if (typeof wx !== 'undefined' && wx.getAppAuthorizeSetting) {
    return wx.getAppAuthorizeSetting();
  }
  
  // 回退方案：返回默认值
  return {
    albumAuthorized: 'not determined',
    bluetoothAuthorized: 'not determined',
    cameraAuthorized: 'not determined',
    locationAuthorized: 'not determined',
    locationReducedAccuracy: false,
    microphoneAuthorized: 'not determined',
    notificationAuthorized: 'not determined',
    notificationAlertAuthorized: 'not determined',
    notificationBadgeAuthorized: 'not determined',
    notificationSoundAuthorized: 'not determined',
    phoneCalendarAuthorized: 'not determined'
  };
}

// 默认导出
export default {
  getSystemInfoSync,
  getSystemInfo,
  getDeviceInfo,
  getWindowInfo,
  getAppBaseInfo,
  getSystemSetting,
  getAppAuthorizeSetting
};
