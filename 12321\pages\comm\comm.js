// pages/comm/comm.js
var app = getApp();
var timer;
var autoTimer;

// 引入二维码工具
const qrcode = require('../../utils/qrcode.js');

// 添加认证命令常量
const AUTH_CMD = "8efb06170ca968237b4cd22ddffa665b4fe8";  // 认证命令

function inArray(arr, key, val) {
  for (let i = 0; i < arr.length; i++) {
    if (arr[i][key] === val) {
      return i;
    }
  }
  return -1;
}
// ArrayBuffer转16进度字符串示例
function ab2hex(buffer) {
  var hexArr = Array.prototype.map.call(
    new Uint8Array(buffer),
    function (bit) {
      return ('00' + bit.toString(16)).slice(-2)+" "
    }
  )
  return (hexArr.join('')).toUpperCase();
}
function ab2Str(arrayBuffer){
  let unit8Arr = new Uint8Array(arrayBuffer);
  let encodedString = String.fromCharCode.apply(null, unit8Arr);
  //var decodedString = decodeURIComponent(mencode.encodeURIComponent((encodedString)));//没有这一步中文会乱码
  //var decodedString = mencode.encodeURIComponent((encodedString));
  //console.log(decodedString);
  //return decodedString
  return encodedString
}
function stringToBytes(str) {
  var ch, st, re = [];
  for (var i = 0; i < str.length; i++) {
    ch = str.charCodeAt(i);  // get char  
    st = [];                 // set up "stack"  
    do {
      st.push(ch & 0xFF);  // push byte to stack  
      ch = ch >> 8;          // shift value down by 1 byte  
    }
    while (ch);
    // add stack contents to result  
    // done because chars have "wrong" endianness  
    re = re.concat(st.reverse());
  }
  // return an array of bytes  
  return re;
}

// 16进制字符串转换为ArrayBuffer
function hexStringToArrayBuffer(hexString) {
  // 去除所有空格
  hexString = hexString.replace(/\s/g, "");
  
  // 确保字符串长度为偶数
  if (hexString.length % 2 != 0) {
    hexString = "0" + hexString;
  }
  
  var buffer = new ArrayBuffer(hexString.length / 2);
  var dataView = new DataView(buffer);
  
  for (var i = 0; i < hexString.length; i += 2) {
    dataView.setUint8(i / 2, parseInt(hexString.substr(i, 2), 16));
  }
  
  return buffer;
}

Page({
  /**
   * 页面的初始数据
   */
  data: {
    device: null,
    deviceId: "",
    name: "",
    connected: false,
    readyRec: false,
    hexSend: false,
    hexRec: false,
    chs: [], 
    deviceadd: "未知设备",
    windowHeight: 0,// 页面总高度将会放在这里
    navbarHeight: 0,// navbar的高度
    headerHeight: 0,// header的高度
    scrollViewHeight: 300, // scroll-view的高度
    recdata: "",
    rxCount: 0,
    txCount: 0,
    rxRate: 0,
    txRate: 0,
    connectState: "正在连接",
    reconnect: "连接中...",
    timRX: 0,
    timTX: 0,
    sendText: "",
    autoSendInv: 50,
    autosendEn: false,
    autosendText: "自动发送",
    showModal: false,
    showModalStatus: false,
    showTips: "",
    batteryLevel: 0, // 电池电量百分比
    batteryVoltage: 0, // 电池电压(mV)
    calculatedVoltage: "0.00", // 计算后的电压值(V)
    lastBatteryCheck: 0, // 上次电量检查时间
    batteryCheckInterval: 300000, // 电量检查间隔(ms)，默认5分钟
    lowBatteryWarningShown: false, // 是否已显示低电量警告
    lockStatus: -1, // 锁状态: -1=未知, 0=开锁状态, 1=锁定状态
    showQRCodeModal: false, // 是否显示二维码模态框
  },
  
  // 控制蜂鸣器(P17)
  controlBuzzer(e) {
    if (!this.data.connected) {
      this.showModalTips("请先连接BLE设备...");
      return;
    }
    
    // 检查特征值是否已准备好
    if (!this._deviceId || !this._serviceId || !this._characteristicId) {
      this.showModalTips("特征值未准备好，请稍后再试");
      return;
    }
    
    // 从事件中获取参数，如果没有事件则默认为false（关闭蜂鸣器）
    const isOn = e && e.currentTarget && e.currentTarget.dataset.isOn !== undefined ? 
                 e.currentTarget.dataset.isOn : false;
    
    // 构建IO控制指令: CCD1 E7 01/00 00 00 00 (E7对应P17)
    const command = isOn ? "CC D1 E7 01 00 00 00" : "CC D1 E7 00 00 00 00";
    const buffer = hexStringToArrayBuffer(command);
    
    console.log('准备发送蜂鸣器控制命令:', {
      deviceId: this._deviceId,
      serviceId: this._serviceId,
      characteristicId: this._characteristicId
    });
    
    wx.writeBLECharacteristicValue({
      deviceId: this._deviceId,
      serviceId: this._serviceId,
      characteristicId: this._characteristicId,
      value: buffer,
      success: (res) => {
        console.log('蜂鸣器控制成功:', isOn ? '开启' : '关闭');
        if (isOn) {
          // 蜂鸣器响1秒后自动关闭
          setTimeout(() => {
            this.controlBuzzer();
          }, 1000);
        }
      },
      fail: (res) => {
        console.error('蜂鸣器控制失败:', res);
        this.showModalTips("蜂鸣器控制失败: " + res.errMsg);
      }
    });
  },
  
  // 控制门锁
  controlLock(isOpen) {
    if (!this.data.connected) {
      this.showModalTips("请先连接BLE设备...");
      return;
    }
    
    // 检查特征值是否已准备好
    if (!this._deviceId || !this._serviceId || !this._characteristicId) {
      this.showModalTips("特征值未准备好，请稍后再试");
      return;
    }
    
    // 如果是开锁操作，先发送认证命令
    if (isOpen) {
      this.sendAuthCommand(() => {
        // 认证成功后发送开锁命令
        // 构建开锁命令，使用Python脚本中的指令格式
        // 开锁命令: ff 4f 50 45 4e 00 [锁编号] 00 00 fe
        const lockNumber = 1; // 默认操作1号锁
        const openLockPrefix = "FF4F50454E00"; // 开锁命令前缀
        const openLockSuffix = "0000FE"; // 开锁命令后缀
        
        const command = `${openLockPrefix}${lockNumber.toString(16).padStart(2, '0')}${openLockSuffix}`;
        const buffer = hexStringToArrayBuffer(command);
        
        console.log('准备发送门锁开启命令:', {
          deviceId: this._deviceId,
          serviceId: this._serviceId,
          characteristicId: this._characteristicId,
          command: command
        });
        
        wx.writeBLECharacteristicValue({
          deviceId: this._deviceId,
          serviceId: this._serviceId,
          characteristicId: this._characteristicId,
          value: buffer,
          success: (res) => {
            console.log('门锁开启命令发送成功');
            this.showModalTips("开锁命令已发送");
            
            // 开锁时触发蜂鸣器提示
            setTimeout(() => {
              this.controlBuzzer({ currentTarget: { dataset: { isOn: true } } });
            }, 500);
          },
          fail: (res) => {
            console.error('门锁开启失败:', res);
            this.showModalTips("门锁开启失败: " + res.errMsg);
          }
        });
      });
    } else {
      // 查询锁状态命令不需要认证
      const command = "66F10077"; // 查询锁状态命令
      const buffer = hexStringToArrayBuffer(command);
      
      console.log('准备发送锁状态查询命令:', {
        deviceId: this._deviceId,
        serviceId: this._serviceId,
        characteristicId: this._characteristicId,
        command: command
      });
      
      wx.writeBLECharacteristicValue({
        deviceId: this._deviceId,
        serviceId: this._serviceId,
        characteristicId: this._characteristicId,
        value: buffer,
        success: (res) => {
          console.log('锁状态查询命令发送成功');
          this.showModalTips("查询锁状态命令已发送");
        },
        fail: (res) => {
          console.error('锁状态查询失败:', res);
          this.showModalTips("锁状态查询失败: " + res.errMsg);
        }
      });
    }
  },
  
  // 添加发送认证命令的函数
  sendAuthCommand(callback) {
    const command = AUTH_CMD;
    const buffer = hexStringToArrayBuffer(command);
    
    console.log('准备发送认证命令:', {
      deviceId: this._deviceId,
      serviceId: this._serviceId,
      characteristicId: this._characteristicId,
      command: command
    });
    
    this.showModalTips("正在发送认证命令...");
    
    wx.writeBLECharacteristicValue({
      deviceId: this._deviceId,
      serviceId: this._serviceId,
      characteristicId: this._characteristicId,
      value: buffer,
      success: (res) => {
        console.log('认证命令发送成功');
        this.showModalTips("认证命令已发送");
        
        // 延迟一段时间后执行回调，确保认证命令被设备处理
        setTimeout(() => {
          if (typeof callback === 'function') {
            callback();
          }
        }, 500);
      },
      fail: (res) => {
        console.error('认证命令发送失败:', res);
        this.showModalTips("认证命令发送失败: " + res.errMsg);
      }
    });
  },
  
  // 获取电池电量
  getBatteryLevel() {
    // 直接调用查询设备信息函数，设备信息中包含电池电量
    this.queryDeviceInfo();
    
    // 更新上次电量检查时间
    this.setData({
      lastBatteryCheck: Date.now()
    });
  },
  
  // 处理接收到的电量数据
  processBatteryData(data) {
    // 检查是否为电量数据响应(CCD6开头)
    if (data.startsWith("CC D6")) {
      // 获取电压值(两个字节)
      console.log("电量数据 = ", data);
      
      // 从数据中正确提取电压值
      // 格式为: CC D6 02 XX XX，其中XX XX是电压值的两个字节
      const parts = data.split(" ");
      if (parts.length >= 5) {
        try {
          // 提取第4和第5个部分作为电压值的高字节和低字节
          const highByte = parseInt(parts[3], 16);
          const lowByte = parseInt(parts[4], 16);
          
          // 组合高字节和低字节得到完整电压值
          const voltageValue = (highByte << 8) | lowByte;
          console.log("高字节:", parts[3], "低字节:", parts[4], "组合值:", voltageValue);
          
          // 计算电池电量
          // 原始值乘以6减4.8，小于等于0表示没电
          const batteryVoltage = voltageValue;
          const voltageCompensation = 0.048; // 减少0.048V的补偿值
          const calculatedValue = ((voltageValue / 1000) * 7.2) - 4.8 - voltageCompensation;
          
          // 将计算值转换为百分比 (0-100%)
          let batteryPercentage = 0;
          if (calculatedValue <= 0) {
            batteryPercentage = 0; // 电量为0
          } else if (calculatedValue >= 1.2) { // 假设满电为1.2V (6V - 4.8V)
            batteryPercentage = 100;
          } else {
            batteryPercentage = Math.round((calculatedValue / 1.2) * 100);
          }
          
          // 确保百分比在0-100范围内
          batteryPercentage = Math.min(100, Math.max(0, batteryPercentage));
          
          // 更新上次检查时间
          const now = Date.now();
          
          // 保存计算后的电压值，用于UI显示
          const calculatedVoltageStr = calculatedValue.toFixed(3);
          
          // 详细打印计算过程
          console.log("===== 电量计算详情 =====");
          console.log(`原始电压值: ${voltageValue}mV`);
          console.log(`计算公式: ((${voltageValue}/1000)*6)-4.8=${calculatedVoltageStr}V`);
          console.log(`计算步骤: ${voltageValue/1000} * 6 = ${(voltageValue/1000)*6}, 再减4.8加 = ${calculatedVoltageStr}V`);
          console.log(`电量百分比计算: ${calculatedValue <= 0 ? '0%' : calculatedValue >= 1.2 ? '100%' : Math.round((calculatedValue / 1.2) * 100) + '%'}`);
          console.log(`最终电量百分比: ${batteryPercentage}%`);
          console.log("========================");
          
          this.setData({
            batteryVoltage: batteryVoltage,
            batteryLevel: batteryPercentage,
            lastBatteryCheck: now,
            calculatedVoltage: calculatedVoltageStr // 添加计算后的电压值
          });
          
          // 显示电量信息
          const infoText = `电池电量: ${batteryPercentage}%, 计算值: ${calculatedVoltageStr}V`;
          this.showModalTips(infoText);
          
          // 检查是否需要显示低电量警告
          if (batteryPercentage <= 20 && !this.data.lowBatteryWarningShown) {
            setTimeout(() => {
              wx.showModal({
                title: '低电量警告',
                content: `设备电量仅剩${batteryPercentage}%，计算值${calculatedVoltageStr}V，请及时充电或更换电池，以免影响使用。`,
                showCancel: false,
                success: (res) => {
                  this.setData({
                    lowBatteryWarningShown: true
                  });
                }
              });
            }, 1000);
          }
          
          // 如果电量恢复到30%以上，重置低电量警告状态
          if (batteryPercentage > 30) {
            this.setData({
              lowBatteryWarningShown: false
            });
          }
        } catch (error) {
          console.error("处理电量数据时出错:", error);
        }
      } else {
        console.error("电量数据格式不正确:", data);
      }
    }
  },

  goclear(){
    this.setData({
      recdata: "",
      rxCount: 0,
      txCount: 0,
    })
  }, Countdown() {
    var that = this;
    timer = setTimeout(function () {
      //console.log("----Countdown----");
      that.setData({
        rxRate: that.data.timRX*2,
        txRate: that.data.timTX*2,
      })
      that.setData({
        timRX: 0,
        timTX: 0,
      })
      that.Countdown();
    }, 500);
  }, autoSend() { //定时发送
    var that = this;
    if (this.data.connected){
      this.data.autosendEn = true
      autoTimer = setTimeout(function () {
        that.autoSend();
        that.gosend();
      }, this.data.autoSendInv);
  }else{   //已经断开了连接  禁止自动 发送
      this.data.autosendEn = false
      clearTimeout(autoTimer);
      this.setData({
        autosendText: "自动发送"
      })
  }
  }, preventTouchMove: function () {
  },
  goautosend(){
    if (!this.data.connected) {
      this.showModalTips("请先连接BLE设备...")
      return
    }
    if (!this.data.autosendEn){
      this.autoSend();
      this.setData({
        autosendText: "停止发送"
      })
    }else{
      this.data.autosendEn = false
      clearTimeout(autoTimer);
      this.setData({
        autosendText: "自动发送"
      })
    }

  }, voteTitle: function (e) {
    this.data.sendText = e.detail.value;
  },
  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {
    console.warn("onHide goto disconnect")
    if (this.data.connected) {
      wx.closeBLEConnection({
        deviceId: this.data.deviceId
      })
      this.setData({
        connected: false,
        reconnect: "重新连接",
        connectState: "已断开",
      })
      
      // 检查device是否存在，避免空指针错误
      const deviceName = this.data.device && this.data.device.name ? this.data.device.name : "未知设备";
      
      wx.setNavigationBarTitle({
        title: "已断开 " + deviceName
      })
      console.warn("DisConnect ", this.data.deviceId)
    }
  },
    /**
   * 生命周期函数--监听页面卸载
   */
   onUnload: function () {
     app.saveSetting(this.data.autoSendInv, this.data.sendText)
     if (this.data.connected) {
       wx.closeBLEConnection({
         deviceId: this.data.deviceId
       })
       console.warn("DisConnect ", this.data.deviceId)
       this.data.connected=false
     }
     
     // 释放音频资源
     if (this.audioContext) {
       this.audioContext.destroy();
       this.audioContext = null;
     }
     
     // 停止电量监测
     this.stopBatteryMonitoring();
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 从app.globalData获取设备信息
    const bleDevice = app.globalData.ble_device;
    
    // 初始化UUID
    this.serviceu = app.globalData.mserviceuuid;
    this.txdu = app.globalData.mtxduuid;
    this.rxdu = app.globalData.mrxduuid;
    
    console.log("device = ", bleDevice);
    console.log("使用的UUID: 服务UUID=", this.serviceu, "写入特征值=", this.txdu, "通知特征值=", this.rxdu);
    
    // 安全地设置设备信息
    if (bleDevice) {
      this.setData({
        device: bleDevice,
        deviceId: bleDevice.deviceId || "",
        name: bleDevice.name || bleDevice.localName || "未知设备",
        deviceadd: bleDevice.deviceId || "未知设备",
      });
    } else {
      this.setData({
        deviceadd: "未知设备",
      });
      console.warn("未获取到设备信息");
    }
    
    // 计算滚动区域高度
    this.calScrolHigh();
    
    // 启动倒计时
    this.Countdown();
    
    // 初始化音频对象，用于开锁提示音
    this.initAudio();
    
    // 如果有设备信息，创建蓝牙连接
    if (bleDevice) {
      this.createBLEConnection();
    } else {
      this.showModalTips("未获取到设备信息，请返回重试");
    }
  },
  
  // 复制设备MAC地址到剪贴板
  copyDeviceMac() {
    if (!this.data.deviceadd || this.data.deviceadd === "未知设备") {
      this.showModalTips("无可用设备MAC地址");
      return;
    }
    
    wx.setClipboardData({
      data: this.data.deviceadd,
      success: () => {
        this.showModalTips("设备MAC地址已复制");
      }
    });
  },
  
  calScrolHigh(){
    var that = this
    // 先取出页面高度 windowHeight
    wx.getSystemInfo({
      success: function (res) {
        that.setData({
          windowHeight: res.windowHeight
        });
      }
    });
    // 然后取出navbar和header的高度
    // 根据文档，先创建一个SelectorQuery对象实例
    let query = wx.createSelectorQuery().in(this);
    // 然后逐个取出navbar和header的节点信息
    // 选择器的语法与jQuery语法相同
    query.select('.app-title').boundingClientRect(); // 标题
    query.select('#v1').boundingClientRect();
    query.select('#v2').boundingClientRect();
    query.select('#v3').boundingClientRect();
    query.select('#v4').boundingClientRect();
    query.select('#v5').boundingClientRect();
    query.select('#v6').boundingClientRect(); // 电池显示区域
    query.select('#v7').boundingClientRect(); // 控制按钮区域
    query.select('#v8').boundingClientRect(); // 锁状态区域
    query.select('.footer-tip').boundingClientRect(); // 底部提示
    // 执行上面所指定的请求，结果会按照顺序存放于一个数组中，在callback的第一个参数中返回
    query.exec((res) => {
      try {
        // 检查res是否为数组且长度足够
        if (!res || !Array.isArray(res) || res.length < 10) {
          console.error('查询结果不完整:', res);
          // 设置一个默认的滚动区域高度
          this.setData({
            scrollViewHeight: 300
          });
          return;
        }
        
        // 安全地获取各元素高度，如果元素不存在则返回0
        let titleHeight = res[0] ? res[0].height : 0;
        let navbarHeight = 0;
        if (res[1]) navbarHeight += res[1].height;
        if (res[5]) navbarHeight += res[5].height;
        if (res[6]) navbarHeight += res[6].height;
        if (res[8]) navbarHeight += res[8].height;
        
        let headerHeight = 0;
        if (res[2]) headerHeight += res[2].height;
        if (res[3]) headerHeight += res[3].height;
        if (res[4]) headerHeight += res[4].height;
        if (res[7]) headerHeight += res[7].height;
        headerHeight += 15; // 额外边距
        
        let footerHeight = res[9] ? res[9].height : 0;
        
        // 计算滚动区域高度
        let scrollViewHeight = this.data.windowHeight - titleHeight - navbarHeight - headerHeight - footerHeight;
        
        // 确保滚动区域高度不小于100
        scrollViewHeight = Math.max(scrollViewHeight, 100);
        
        // 更新数据
        this.setData({
          scrollViewHeight: scrollViewHeight
        });
        
        console.log('计算滚动区域高度:', {
          windowHeight: this.data.windowHeight,
          titleHeight,
          navbarHeight,
          headerHeight,
          footerHeight,
          scrollViewHeight
        });
      } catch (error) {
        console.error('计算滚动区域高度出错:', error);
        // 设置默认高度
        this.setData({
          scrollViewHeight: 300
        });
      }
    });
  },
  getBLEDeviceServices(deviceId) {
    wx.getBLEDeviceServices({
      deviceId,
      success: (res) => {
        console.log('getBLEDeviceServices success', res.services)
        this.setData({
          connectState: "查找服务",
        })
        // 遍历所有服务
        let foundService = false
        for (let i = 0; i < res.services.length; i++) {
          if (res.services[i].uuid.toUpperCase() === this.serviceu) {
            this.getBLEDeviceCharacteristics(deviceId, res.services[i].uuid)
            foundService = true
            break
          }
        }
        // 如果没有找到指定服务，尝试使用第一个主服务
        if (!foundService) {
          for (let i = 0; i < res.services.length; i++) {
            if (res.services[i].isPrimary) {
              console.log('使用备选服务:', res.services[i].uuid)
              this.getBLEDeviceCharacteristics(deviceId, res.services[i].uuid)
              return
            }
          }
        }
      },
      fail: (res) => {
        console.error('getBLEDeviceServices fail', res)
        this.setData({
          connectState: "获取服务失败",
        })
        this.showModalTips("获取蓝牙服务失败，请重试...")
      }
    })
  },
  getBLEDeviceCharacteristics(deviceId, serviceId) {
   const that = this
    wx.getBLEDeviceCharacteristics({
      deviceId,
      serviceId,
      success: (res) => {
        var ismy_service = false
        console.log("compute ", serviceId, this.serviceu)
        if (serviceId == this.serviceu) {
          ismy_service = true
          console.warn("this is my service ")
        }
        console.log('getBLEDeviceCharacteristics success', res.characteristics)
        for (let i = 0; i < res.characteristics.length; i++) {
          let item = res.characteristics[i]
          if (ismy_service){
            console.log("-----------------------")
          }
          console.log("this properties = ", item.properties)
          if (item.properties.read) {
            console.log("[Read]", item.uuid)
            wx.readBLECharacteristicValue({
              deviceId,
              serviceId,
              characteristicId: item.uuid,
            })
          }
          if (item.properties.write) {
            this.setData({
              canWrite: true
            })
            console.log("[Write]",item.uuid)
            this._deviceId = deviceId
            if (ismy_service && (this.txdu == item.uuid)){
              console.warn("find write uuid  ready to ", item.uuid)
              this._characteristicId = item.uuid
              this._serviceId = serviceId
             // this.showModalTips(this.txdu+ "\r找到发送特征值")
            }
            //this.writeBLECharacteristicValue()
          }
          if (item.properties.notify || item.properties.indicate) {
            console.log("[Notify]", item.uuid)
            if (ismy_service && (this.rxdu == item.uuid)){
              console.warn("find notity uuid try enablec....", item.uuid)
             // this.showModalTips(this.rxdu + "\r正在开启通知...")
              wx.notifyBLECharacteristicValueChange({  //开启通知
                deviceId,
                serviceId,
                characteristicId: item.uuid,
                state: true, 
                success(res) {
                  console.warn('notifyBLECharacteristicValueChange success', res.errMsg)
                  that.setData({
                    connectState: "连接成功"
                  })
                  that.data.readyRec=true
                  
                  // 执行连接成功回调，如果存在的话
                  if (typeof that.connectionSuccessCallback === 'function') {
                    console.log('执行连接成功回调');
                    that.connectionSuccessCallback();
                  } else {
                    console.log('未设置连接成功回调，执行默认操作');
                    // 默认操作：连接成功后自动发送认证命令，然后获取电池电量
                    setTimeout(() => {
                      that.sendAuthCommand(() => {
                        // 认证成功后获取电池电量
                        setTimeout(() => {
                          that.getBatteryLevel();
                        }, 500);
                        
                        // 然后查询锁状态
                        setTimeout(() => {
                          that.checkLockStatus();
                        }, 1000);
                      });
                    }, 1000);
                  }
                  
                  // 连接成功后延迟一段时间再触发蜂鸣器提示
                  setTimeout(() => {
                    // 确保特征值已准备好
                    if (that._deviceId && that._serviceId && that._characteristicId) {
                      console.log("特征值已准备好，触发蜂鸣器提示");
                      that.controlBuzzer({
                        currentTarget: {
                          dataset: {
                            isOn: true
                          }
                        }
                      });
                    } else {
                      console.warn("特征值未准备好，无法触发蜂鸣器");
                    }
                  }, 2000);
                }
              })
            }
          }
        }
      },
      fail(res) {
        console.error('getBLEDeviceCharacteristics', res)
      }
    })
    // 操作之前先监听，保证第一时间获取数据
    wx.onBLECharacteristicValueChange((characteristic) => {
      var buf = new Uint8Array(characteristic.value)
      var nowrecHEX = ab2hex(characteristic.value)
      console.warn("rec: ", nowrecHEX, characteristic.characteristicId)
      var recStr = ab2Str(characteristic.value)
      console.warn("recstr: ", recStr, characteristic.characteristicId)
      if (this.rxdu != characteristic.characteristicId){
        console.error("no same : ", this.rxdu, characteristic.characteristicId)
        return
      }
      if (!this.data.readyRec)return
      var mrecstr
      if (this.data.hexRec){
        mrecstr = nowrecHEX
      }else{
        mrecstr = recStr
      }
      if (this.data.recdata.length>3000){
        this.data.recdata = this.data.recdata.substring(mrecstr.length, this.data.recdata.length)
      }
      console.warn("RXlen: ", buf.length)
      this.setData({
        recdata: this.data.recdata + mrecstr,
        rxCount: this.data.rxCount + buf.length,
        timRX: this.data.timRX+buf.length
      })
      
      // 处理电量数据响应
      this.processBatteryData(nowrecHEX);
      
      // 处理锁状态数据响应
      this.processLockStatusData(nowrecHEX);
    })
  },
  writeBLECharacteristicValue() {
    let buffer = new ArrayBuffer(this.data.sendText.length)
    let dataView = new DataView(buffer)
    if (this.data.hexSend) {
      var hexs = this.data.sendText.split(" ");
      var len = hexs.length;
      buffer = new ArrayBuffer(len)
      dataView = new DataView(buffer)
      for (var i = 0; i < len; i++) {
        var val = parseInt(hexs[i], 16);
        dataView.setUint8(i, val);
      }
    } else {
      var bytes = stringToBytes(this.data.sendText)
      buffer = new ArrayBuffer(bytes.length)
      dataView = new DataView(buffer)
      for (var i = 0; i < bytes.length; i++) {
        dataView.setUint8(i, bytes[i]);
      }
    }
    wx.writeBLECharacteristicValue({
      deviceId: this._deviceId,
      serviceId: this._serviceId,
      characteristicId: this._characteristicId,
      value: buffer,
      success(res) {
        console.log('writeBLECharacteristicValue success', res)
      },
      fail(res) {
        console.log('writeBLECharacteristicValue fail', res)
      }
    })
    this.setData({
      txCount: this.data.txCount + this.data.sendText.length,
      timTX: this.data.timTX + this.data.sendText.length,
    })
  },
  gotoback:function(){
    if (this.data.device == null){
      wx.navigateTo({
        url: '/pages/index/index',
      })
       return
    }
    clearTimeout(timer)
    wx.closeBLEConnection({
      deviceId: this.data.deviceId
    })
    this.setData({
      connected: false,
      chs: [],
    })
    wx.navigateBack({
      delta: 1
    })
  }, gosend(){
    if (!this.data.connected){
      this.showModalTips("请先连接BLE设备...")
      return
    }
    var that = this;
    var hex = this.data.sendText //要发送的数据
    var buffer1
    if (this.data.hexSend){ //十六进制发送
      
      var typedArray = new Uint8Array(hex.match(/[\da-f]{2}/gi).map(function (h) {
        return parseInt(h, 16)
      }))
      console.log("hextobyte ", typedArray)
      buffer1 = typedArray.buffer
    }else{ //string发送
      var strbuf = new Uint8Array(stringToBytes(hex))
      console.log("strtobyte ", strbuf)
      buffer1 = strbuf.buffer
    }
    console.log("Txbuf = ",buffer1)
    if (buffer1==null)return
    const txlen = buffer1.byteLength
    wx.writeBLECharacteristicValue({
      deviceId: that._deviceId,
      serviceId: that._serviceId,
      characteristicId: that._characteristicId,
      value: buffer1,
      success: function (res) {
        // success
        that.setData({
          txCount: that.data.txCount + txlen,
          timTX:that.data.timTX+txlen
        })
        console.log("success  指令发送成功");
        console.log(res);
      },
      fail: function (res) {
        // fail
        console.log(res);
      },
      complete: function (res) {
        // complete
      }
    })
  }, hexsend: function (e) {
    console.log("checking ", e.detail.value)
    var selected //= e.target.dataset.checks ? false : true;
    if (e.detail.value.length == 0){
      selected=false
    }else{
      selected=true
    }
    this.setData({
      hexSend: selected,
    })
    console.log("hexsend ", this.data.hexSend)
  }, hexrec: function (e) {
    console.log("checking ")
    var selected //= e.target.dataset.checks ? false : true;
    if (e.detail.value.length == 0) {
      selected = false
    } else {
      selected = true
    }
    this.setData({
      hexRec: selected,
    })
    console.log("hexRec = ", this.data.hexRec)
  }, godisconnect(){
    if (this.data.connected){
      wx.closeBLEConnection({
        deviceId: this.data.deviceId
      })
      this.setData({
        connected: false,
        reconnect:"重新连接",
        connectState: "已断开",
        lockStatus: -1, // 重置锁状态为未知
      })
      
      // 检查device是否存在，避免空指针错误
      const deviceName = this.data.device && this.data.device.name ? this.data.device.name : "未知设备";
      
      wx.setNavigationBarTitle({
        title: "已断开 " + deviceName
      })
      this.showModalTips(deviceName + "已断开连接...");
    }else{
      // 检查device是否存在，避免空指针错误
      const deviceName = this.data.device && this.data.device.name ? this.data.device.name : "未知设备";
      
      wx.setNavigationBarTitle({
        title: "正在连接 " + deviceName
      })
      this.setData({
        connectState: "正在连接",
        reconnect: "连接中...",
      })
      wx.createBLEConnection({
        deviceId: this.data.deviceId,
        success: (res) => {
          this.setData({
            connected: true,
            connectState: "读取服务",
            reconnect: "断开连接",
            recdata: "",
            rxCount: 0,
            txCount: 0,
          })
          wx.setNavigationBarTitle({
            title: "已连接 " + deviceName
          })
          this.getBLEDeviceServices(this.data.deviceId)
          
          // 重新连接成功后，设置回调函数，包含认证流程
          this.connectionSuccessCallback = () => {
            // 延迟执行，确保特征值已准备好
            setTimeout(() => {
              // 首先发送认证命令
              this.sendAuthCommand(() => {
                console.log("重连后认证命令发送完成，继续执行后续操作");
                
                // 查询设备信息（包含电池电量）
                setTimeout(() => {
                  this.queryDeviceInfo();
                }, 500);
                
                // 延迟后查询锁状态
                setTimeout(() => {
                  this.checkLockStatus();
                }, 1000);
                
                // 启动电池监控
                this.startBatteryMonitoring();
              });
            }, 1500);
          };
        }
      })
 
    }
  },
  settime(){
    console.log("Click Time set");
    this.autoC = false
    this.inputinv = "" + this.data.autoSendInv
    if (this.data.autosendEn){ //正在自动发送，停止它
      this.data.autosendEn = false
      clearTimeout(autoTimer);
      this.setData({
        autosendText: "自动发送"
      })
    }
    this.setData({
      showModal: true
    });
  }, timeinputChange:function(e){
    this.autoC = true
    this.inputinv = e.detail.value;
    console.log("minputC", this.inputinv)
  },
   hideModal: function () {
    this.setData({
      showModal: false
    });
  },
  onCancel: function () {
    this.hideModal();
  },
  onConfirm: function () {
    this.hideModal();
    if (this.autoC){
      //this.data.autoSendInv = new Number(this.inputinv)
      this.setData({
        autoSendInv: parseInt(this.inputinv)
      })
      console.log("time change")
    }
    console.log("minputOK", this.inputinv, this.data.autoSendInv)
  },showModalTips: function (str) {
    // 先设置内容和显示状态
    this.setData({
      showTips: str,
      showModalStatus: true,
    });
    
    // 创建动画实例
    var animation = wx.createAnimation({
      duration: 300,
      timingFunction: 'ease',
    });
    
    // 先设置初始状态（只改变透明度和缩放，不改变位置）
    animation.opacity(0).scale(0.9).step();
    
    this.setData({
      animationData: animation.export()
    });
    
    // 延迟一帧后设置最终状态
    setTimeout(() => {
      animation.opacity(1).scale(1).step();
      this.setData({
        animationData: animation.export()
      });
    }, 50);
    
    // 3秒后自动关闭
    setTimeout(() => {
      this.hideModalTips();
    }, 3000);
  },
  hideModalTips: function () {
    if (!this.data.showModalStatus) return;
    
    // 创建动画实例
    var animation = wx.createAnimation({
      duration: 200,
      timingFunction: 'ease',
    });
    
    // 设置动画（只改变透明度和缩放，不改变位置）
    animation.opacity(0).scale(0.9).step();
    
    this.setData({
      animationData: animation.export()
    });
    
    setTimeout(() => {
      this.setData({
        showModalStatus: false
      });
    }, 200);
  },
  // 查询锁状态
  checkLockStatus() {
    if (!this.data.connected) {
      this.showModalTips("请先连接BLE设备...");
      return;
    }
    
    // 检查特征值是否已准备好
    if (!this._deviceId || !this._serviceId || !this._characteristicId) {
      this.showModalTips("特征值未准备好，请稍后再试");
      return;
    }
    
    // 构建查询锁状态指令，使用Python脚本中的指令
    const command = "66F10077"; // 查询锁状态命令
    const buffer = hexStringToArrayBuffer(command);
    
    console.log('准备发送锁状态查询命令:', {
      deviceId: this._deviceId,
      serviceId: this._serviceId,
      characteristicId: this._characteristicId,
      command: command
    });
    
    wx.writeBLECharacteristicValue({
      deviceId: this._deviceId,
      serviceId: this._serviceId,
      characteristicId: this._characteristicId,
      value: buffer,
      success: (res) => {
        console.log('发送锁状态查询指令成功');
        this.showModalTips("正在查询锁状态...");
      },
      fail: (res) => {
        console.error('发送锁状态查询指令失败:', res);
        this.showModalTips("锁状态查询失败: " + res.errMsg);
      }
    });
  },
  
  // 处理接收到的锁状态数据
  processLockStatusData(data) {
    // 检查数据是否为锁状态响应
    // 可能的响应格式：
    // 1. 开锁反馈：以"1c"开头
    // 2. 关锁反馈：以"1d"开头
    // 3. 锁状态查询响应：以"1e"开头
    // 4. 认证响应：以"8e"开头
    
    console.log("收到数据: ", data);
    
    const hexData = data.replace(/\s+/g, '').toLowerCase(); // 移除所有空格并转为小写
    
    if (hexData.startsWith("8e")) {
      // 认证响应处理
      console.log("收到认证响应: ", hexData);
      this.showModalTips("认证响应已接收");
      return;
    }
    
    if (hexData.startsWith("1c") || hexData.startsWith("1d") || hexData.startsWith("1e")) {
      console.log("收到锁状态数据: ", hexData);
      
      // 解析锁状态，从第2个字符开始每2个字符表示一个锁的状态
      // F0表示锁开启，其他值表示锁关闭
      const lockStatusHex = hexData.substring(2, 4);
      const isLockOpen = lockStatusHex.toUpperCase() === "F0";
      
      console.log("锁状态:", isLockOpen ? "开锁状态" : "锁定状态");
      
      // 记录之前的锁状态
      const previousLockStatus = this.data.lockStatus;
      
      this.setData({
        lockStatus: isLockOpen ? 0 : 1
      });
      
      // 显示锁状态
      const statusText = isLockOpen ? "开锁状态" : "锁定状态";
      this.showModalTips(`当前锁状态: ${statusText}`);
      
      // 更新UI显示
      if (isLockOpen) {
        wx.showToast({
          title: '锁已打开',
          icon: 'success',
          duration: 1500
        });
        
        // 如果状态从锁定变为开锁，播放开锁音频
        if (previousLockStatus === 1) {
          this.playUnlockAudio();
        }
      } else {
        wx.showToast({
          title: '锁已关闭',
          icon: 'none',
          duration: 1500
        });
      }
    } else if (hexData.startsWith("77")) {
      // 设备信息响应
      try {
        // 解析设备信息
        const deviceVersion = parseInt(hexData.substring(2, 4), 16);
        const batteryPowered = parseInt(hexData.substring(4, 6), 16) === 1;
        const powerValue = parseInt(hexData.substring(6, 8), 16);
        
        console.log(`设备信息: 版本=${deviceVersion}, 电池供电=${batteryPowered ? '是' : '否'}, 电量=${powerValue}%`);
        
        // 更新电池信息
        this.setData({
          batteryLevel: powerValue,
          calculatedVoltage: (powerValue * 0.04 + 2.0).toFixed(2) // 简单估算电压
        });
        
        this.showModalTips(`设备信息: 电量${powerValue}%, 电压约${this.data.calculatedVoltage}V`);
      } catch (e) {
        console.error("解析设备信息失败:", e);
      }
    }
  },
  // 初始化音频对象
  initAudio: function() {
    // 使用wx.createInnerAudioContext创建音频上下文（更可靠）
    this.audioContext = wx.createInnerAudioContext();
    // 设置音频文件路径 - 使用相对路径
    this.audioContext.src = 'assets/audio/ttsmaker-file-2025-6-11-20-49-17.mp3';
    // 设置音频播放选项
    this.audioContext.obeyMuteSwitch = false; // 忽略静音开关
    this.audioContext.volume = 1.0; // 最大音量
    
    // 监听音频播放错误事件
    this.audioContext.onError((res) => {
      console.error('音频播放错误:', res);
      this.showModalTips("音频播放错误: " + JSON.stringify(res));
    });
    
    // 监听音频播放开始事件
    this.audioContext.onPlay(() => {
      console.log('音频开始播放');
    });
    
    console.log('InnerAudioContext已初始化, 音频路径:', this.audioContext.src);
  },
  
  // 播放开锁音频
  playUnlockAudio: function() {
    if (this.audioContext) {
      console.log('尝试播放开锁音频');
      
      // 确保音频停止并重置
      this.audioContext.stop();
      
      // 播放音频
      setTimeout(() => {
        this.audioContext.play();
        
        // 显示播放状态
        this.showModalTips("正在播放开锁提示音");
        
        // 强制震动反馈，增强体验
        wx.vibrateShort({
          type: 'heavy'
        });
      }, 100);
    } else {
      console.error('音频上下文未初始化');
      // 尝试重新初始化
      this.initAudio();
      setTimeout(() => {
        if (this.audioContext) {
          this.audioContext.play();
        }
      }, 200);
    }
  },
  
  // 修改openLock函数，添加音频播放
  openLock() {
    // 先检查当前锁状态
    this.checkLockStatus();
    
    // 等待锁状态返回后再操作
    setTimeout(() => {
      // 如果锁已经是开锁状态，提示用户
      if (this.data.lockStatus === 0) {
        wx.showToast({
          title: '锁已经是开启状态',
          icon: 'none',
          duration: 1500
        });
        return;
      }
      
      // 显示开锁动画
      wx.showLoading({
        title: '开锁中...',
        mask: true
      });
      
      // 先发送认证命令，然后控制门锁打开
      this.sendAuthCommand(() => {
        // 控制门锁打开
        this.controlLock(true);
        
        // 添加开锁成功的动画和反馈
        setTimeout(() => {
          wx.hideLoading();
          
          // 显示开锁成功图标
          wx.showToast({
            title: '开锁成功',
            icon: 'success',
            duration: 1500
          });
          
          // 播放开锁音频
          this.playUnlockAudio();
          
          // 震动反馈
          wx.vibrateShort({
            type: 'medium'
          });
          
          // 2秒后自动关闭锁
          setTimeout(() => {
            this.controlLock(false);
            
            // 显示锁已关闭的提示
            setTimeout(() => {
              wx.showToast({
                title: '锁已关闭',
                icon: 'none',
                duration: 1000
              });
              
              // 关锁后再次检查锁状态
              setTimeout(() => {
                this.checkLockStatus();
              }, 500);
            }, 500);
          }, 2000);
        }, 1000);
      });
    }, 500); // 给锁状态检查留出时间
  },
  
  // 定期检查电池电量
  startBatteryMonitoring() {
    // 设置定时器，定期检查电池电量
    this.batteryCheckTimer = setInterval(() => {
      const now = Date.now();
      // 如果距离上次检查时间超过设定间隔，且设备已连接，则重新获取电量
      if (now - this.data.lastBatteryCheck > this.data.batteryCheckInterval && this.data.connected) {
        console.log("定期检查电池电量...");
        this.getBatteryLevel();
      }
    }, 60000); // 每分钟检查一次是否需要更新电量
  },
  
  stopBatteryMonitoring() {
    if (this.batteryCheckTimer) {
      clearInterval(this.batteryCheckTimer);
      this.batteryCheckTimer = null;
    }
  },
  
  // 测试音频播放功能
  testAudioPlay: function() {
    console.log("测试音频播放...");
    
    // 创建临时音频对象进行测试
    const testAudio = wx.createInnerAudioContext();
    testAudio.src = 'assets/audio/ttsmaker-file-2025-6-11-20-49-17.mp3';
    testAudio.obeyMuteSwitch = false;
    testAudio.volume = 1.0;
    
    testAudio.onError((res) => {
      console.error('测试音频播放错误:', res);
      this.showModalTips("音频测试失败: " + JSON.stringify(res));
    });
    
    testAudio.onPlay(() => {
      console.log('测试音频开始播放');
      this.showModalTips("正在测试音频播放...");
    });
    
    testAudio.onEnded(() => {
      console.log('测试音频播放结束');
      // 销毁测试音频对象
      testAudio.destroy();
    });
    
    // 播放测试音频
    testAudio.play();
    
    // 震动反馈
    wx.vibrateShort({
      type: 'heavy'
    });
  },
  
  // 创建蓝牙连接
  createBLEConnection() {
    this.data.readyRec = false;
    
    this.setData({
      autoSendInv: app.globalData.mautoSendInv,
      sendText: app.globalData.msendText,
      lockStatus: -1, // 初始化锁状态为未知
    });
    
    // 检查设备信息是否存在
    if (!this.data.device || !this.data.deviceId) {
      console.error("设备信息不存在，无法创建连接");
      this.showModalTips("设备信息不存在，请返回重试");
      return;
    }
    
    const deviceId = this.data.deviceId;
    const deviceName = this.data.name || "未知设备";
    
    console.log("target uuids = ", this.serviceu, this.txdu, this.rxdu);
    
    wx.setNavigationBarTitle({
      title: "正在连接 " + deviceName
    });
    
    wx.createBLEConnection({
      deviceId,
      success: (res) => {
        this.setData({
          connected: true,
          connectState: "读取服务",
          reconnect: "断开连接"
        });
        
        wx.setNavigationBarTitle({
          title: "已连接 " + deviceName
        });
        
        // 获取设备服务
        this.getBLEDeviceServices(deviceId);
        
        // 设置连接成功后的回调，用于发送认证命令、查询设备信息和锁状态
        this.connectionSuccessCallback = () => {
          // 延迟执行，确保特征值已准备好
          setTimeout(() => {
            // 首先发送认证命令
            this.sendAuthCommand(() => {
              console.log("认证命令发送完成，继续执行后续操作");
              
              // 查询设备信息（包含电池电量）
              setTimeout(() => {
                this.queryDeviceInfo();
              }, 500);
              
              // 延迟后查询锁状态
              setTimeout(() => {
                this.checkLockStatus();
              }, 1000);
              
              // 启动电池监控
              this.startBatteryMonitoring();
            });
          }, 1500);
        };
      },
      fail: (err) => {
        console.error("蓝牙连接失败:", err);
        this.showModalTips("连接失败: " + (err.errMsg || "未知错误"));
        this.setData({
          connectState: "连接失败",
          reconnect: "重新连接"
        });
      }
    });
  },
  
  // 显示二维码模态框
  showQRCodeModal() {
    this.setData({
      showQRCodeModal: true
    });
    
    // 延迟一帧，确保canvas已经渲染
    setTimeout(() => {
      this.generateQRCode();
    }, 100);
  },
  
  // 隐藏二维码模态框
  hideQRCodeModal() {
    this.setData({
      showQRCodeModal: false
    });
  },
  
  // 生成二维码
  generateQRCode() {
    const deviceId = this.data.deviceadd;
    if (!deviceId || deviceId === "未知设备") {
      this.showModalTips("无法获取设备MAC地址");
      return;
    }
    
    // 构建URL
    const qrUrl = `https://example.com?device=${deviceId}`;
    console.log("生成二维码URL:", qrUrl);
    
    // 获取当前页面实例
    const page = this;
    
    // 使用qrcode工具生成二维码
    qrcode.createQRCode({
      canvasId: 'qrcode',
      text: qrUrl,
      width: 200,
      height: 200,
      colorDark: "#000000",
      colorLight: "#ffffff",
      callback: (res) => {
        console.log("二维码生成完成:", res);
      }
    });
  },
  
  // 保存二维码到相册
  saveQRCode() {
    // 检查授权状态
    wx.getSetting({
      success: (res) => {
        if (!res.authSetting['scope.writePhotosAlbum']) {
          wx.authorize({
            scope: 'scope.writePhotosAlbum',
            success: () => {
              this.doSaveQRCode();
            },
            fail: () => {
              wx.showModal({
                title: '提示',
                content: '需要您授权保存图片到相册',
                confirmText: '去授权',
                success: (res) => {
                  if (res.confirm) {
                    wx.openSetting();
                  }
                }
              });
            }
          });
        } else {
          this.doSaveQRCode();
        }
      }
    });
  },
  
  // 执行保存二维码
  doSaveQRCode() {
    qrcode.saveQRCodeToAlbum('qrcode', 
      (res) => {
        wx.showToast({
          title: '保存成功',
          icon: 'success',
          duration: 2000
        });
      }, 
      (err) => {
        console.error("保存二维码失败:", err);
        this.showModalTips("保存二维码失败");
      }
    );
  },
  
  // 查询设备信息
  queryDeviceInfo() {
    if (!this.data.connected) {
      this.showModalTips("请先连接BLE设备...");
      return;
    }
    
    // 检查特征值是否已准备好
    if (!this._deviceId || !this._serviceId || !this._characteristicId) {
      this.showModalTips("特征值未准备好，请稍后再试");
      return;
    }
    
    // 构建查询设备信息指令，使用Python脚本中的指令
    const command = "66F00077"; // 查询设备信息命令
    const buffer = hexStringToArrayBuffer(command);
    
    console.log('准备发送设备信息查询命令:', {
      deviceId: this._deviceId,
      serviceId: this._serviceId,
      characteristicId: this._characteristicId,
      command: command
    });
    
    wx.writeBLECharacteristicValue({
      deviceId: this._deviceId,
      serviceId: this._serviceId,
      characteristicId: this._characteristicId,
      value: buffer,
      success: (res) => {
        console.log('发送设备信息查询指令成功');
        this.showModalTips("正在查询设备信息...");
      },
      fail: (res) => {
        console.error('发送设备信息查询指令失败:', res);
        this.showModalTips("设备信息查询失败: " + res.errMsg);
      }
    });
  },
})