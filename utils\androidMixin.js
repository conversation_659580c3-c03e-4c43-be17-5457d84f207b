/**
 * 安卓设备适配混入
 * 用于检测安卓设备并应用相应的样式适配
 */
import { getSystemInfoSync } from './systemInfoCompat.js';

export default {
  data() {
    return {
      isAndroidDevice: false,
      systemInfo: null
    }
  },

  created() {
    this.detectAndroidDevice();
  },

  methods: {
    /**
     * 检测是否为安卓设备
     */
    detectAndroidDevice() {
      try {
        // 获取系统信息 - 使用兼容性工具
        this.systemInfo = getSystemInfoSync();
        
        // 检测是否为安卓设备
        this.isAndroidDevice = this.systemInfo.platform === 'android';
        
        console.log('设备平台检测:', {
          platform: this.systemInfo.platform,
          isAndroid: this.isAndroidDevice,
          model: this.systemInfo.model,
          system: this.systemInfo.system
        });
        
        // 为页面根元素添加安卓适配类名
        this.$nextTick(() => {
          this.applyAndroidClass();
        });
        
      } catch (e) {
        console.error('检测安卓设备失败:', e);
        this.isAndroidDevice = false;
      }
    },
    
    /**
     * 为页面根元素应用安卓适配类名
     */
    applyAndroidClass() {
      if (!this.isAndroidDevice) return;
      
      try {
        // 查找页面容器元素
        const containerSelectors = [
          '.container',
          '.page-container',
          '.page-index',
          '.page-profile',
          '.page-scan',
          '.page-orders',
          '.page-contact',
          '.page-report'
        ];
        
        containerSelectors.forEach(selector => {
          const query = uni.createSelectorQuery().in(this);
          query.select(selector).boundingClientRect((data) => {
            if (data) {
              // 通过DOM操作添加类名（在H5环境下）
              // #ifdef H5
              const element = document.querySelector(selector);
              if (element && !element.classList.contains('android-page')) {
                element.classList.add('android-page');
              }
              // #endif
              
              // 在小程序环境下，通过数据绑定的方式处理
              // #ifdef MP-WEIXIN
              if (this.containerClass) {
                this.containerClass += ' android-page';
              } else {
                this.containerClass = 'android-page';
              }
              // #endif
            }
          }).exec();
        });
        
      } catch (e) {
        console.error('应用安卓适配类名失败:', e);
      }
    },
    
    /**
     * 获取容器类名（包含安卓适配）
     */
    getContainerClass(baseClass = '') {
      let classes = baseClass;
      if (this.isAndroidDevice) {
        classes += ' android-page';
      }
      return classes.trim();
    }
  }
}
