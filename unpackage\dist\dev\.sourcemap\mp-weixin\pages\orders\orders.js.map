{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/orders/orders.vue?3477", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/orders/orders.vue?a6c1", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/orders/orders.vue?5511", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/orders/orders.vue?55bc", "uni-app:///pages/orders/orders.vue", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/orders/orders.vue?e264", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-用户端/pages/orders/orders.vue?99e2"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "activeTab", "orders", "loading", "page", "systemInfo", "navbarStyle", "isIOS", "pageSize", "hasMore", "orderStatusMap", "isDevMode", "computed", "filteredOrders", "onLoad", "onReady", "onPullDownRefresh", "onReachBottom", "methods", "calculateIOSAdaptation", "console", "finalTopPosition", "model", "marginTop", "position", "top", "goBack", "uni", "changeTab", "navigateTo", "url", "getStoreName", "refreshOrders", "loadOrders", "title", "status", "size", "params", "API", "then", "orderList", "orderStatus", "rawStatus", "id", "orderNo", "storeName", "startTime", "amount", "catch", "icon", "loadMoreOrders", "viewOrderDetail", "payOrder", "timeStamp", "nonceStr", "package", "signType", "paySign", "success", "fail", "content", "showCancel", "checkPaymentStatus", "debugOrderList", "method", "header"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACa;;;AAGlE;AACgM;AAChM,gBAAgB,uMAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC7BA;AAAA;AAAA;AAAA;AAAovB,CAAgB,kvBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACsGxwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACA;MACAC;QACA;QACA;QACA;QACA;MACA;MACA;MACAC;IACA;EACA;EACAC;IACAC;MACA;QACA;MACA;QACA;UAAA;QAAA;MACA;QACA;UAAA;QAAA;MACA;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;IACA;EACA;EACA;EACAC;IACA;EACA;EACA;EACAC;IACA;MACA;IACA;EACA;EACAC;IACA;IACAC;MACA;QACA;QACA;QAEAC;QACAA;QAEA;UACA;UACA;UACA;UAEAA;UACAA;UACAA;UAEA;;UAEA;UACA;YACA;YACAC;YACAD;;YAEA;YACA;cACAC;cACAD;YACA;;YAEA;YACA;cACAC;cACAD;YACA;UACA;YACAC;UACA,wEACAC;YACAD;UACA;YACAA;UACA;UAEA;YACAE;YACA;YACAC;YACAC;UACA;UAEAL;UACAA;QACA;UACA;QACA;MACA;QACAA;QACA;MACA;IACA;IAEAM;MACAC;IACA;IACAC;MACA;QACA;QACA;MACA;IACA;IACAC;MACAF;QACAG;MACA;IACA;IACA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MAEA;;MAEA;MACAN;QACAO;MACA;;MAEA;MACA;MACA;QACAC;MACA;QACAA;MACA;;MAEA;MACA;QACA/B;QACAgC;MACA;;MAEA;MACA;QACAC;MACA;;MAEA;MACAC,6DACAC;QACAZ;QACAA;QACA;;QAEA;QACA;;QAEA;QACA;UACA;UACAa;QACA;UACA;UACAA;QACA;UACA;UACAA;QACA;UACA;UACAA;QACA;;QAEA;QACA;UACA;UACA;UACA;;UAEA;UACA;YACA;YACA;cACA;cACAC;cACAC;YACA;cACA;cACAD;cACAC;YACA;UACA;YACA;YACA;cACAD;cACAC;YACA;cACAD;cACAC;YACA;UACA;UAEA;YACAC;YACAC;YACAC;YAAA;YACAC;YACAC;YACAZ;YACAO;UACA;QACA;;QAEA;QACA;UACA;QACA;UACA;QACA;;QAEA;QACA;;QAEA;QACA;UACA;QACA;;QAEA;QACA;UACAtB;QACA;MACA,GACA4B;QACArB;QACAA;QACA;;QAEA;QACAA;UACAO;UACAe;QACA;;QAEA;QACA;UACA,gBACA;YACAN;YACAC;YACAC;YACAC;YACAC;YACAZ;YACAO;UACA,GACA;YACAC;YACAC;YACAC;YACAC;YACAC;YACAZ;YACAO;UACA,EACA;QACA;MACA;IACA;IAEA;IACAQ;MACA;QACA;MACA;IACA;IAEA;IACAC;MACAxB;QACAG;MACA;IACA;IAEA;IACAsB;MAAA;MACAzB;QACAO;MACA;;MAEA;MACAI,oCACAC;QACAZ;QAEA;;QAEA;QACAA;UACA0B;UACAC;UACAC;UACAC;UACAC;UACAC;YACA;YACA/B;cACAO;cACAe;YACA;;YAEA;YACA;UACA;UACAU;YACAvC;;YAEA;YACAO;cACAO;cACA0B;cACAC;YACA;UACA;QACA;MACA,GACAb;QACArB;;QAEA;QACAA;UACAO;UACA0B;UACAC;QACA;MACA;IACA;IAEA;IACAC;MAAA;MACAnC;QACAO;MACA;;MAEA;MACAI,6CACAC;QACAZ;QAEA;UACA;UACA;QACA;UACA;UACAA;YACAO;YACA0B;YACAC;UACA;QACA;MACA,GACAb;QACArB;;QAEA;QACAA;UACAO;UACAe;QACA;;QAEA;QACA;MACA;IACA;IAEA;IACAc;MACA;MACA3C;;MAEA;MACAA;MACAA;MACAA;;MAEA;MACA;MACAA;;MAEA;MACAO;QACAO;MACA;;MAEA;MACA;MACA;QACAC;MACA;QACAA;MACA;;MAEA;MACA;QACA/B;QACAgC;MACA;;MAEA;MACA;QACAC;MACA;;MAEA;MACA;QACAP;QACAkC;QACAhE;MACA;MACAoB;;MAEA;MACAO;QACAG;QACAkC;QACAhE;QACAiE;UACA;UACA;QACA;QACAP;UACAtC;UACAO;;UAEA;UACAA;YACAO;YACA0B;YACAC;UACA;QACA;QACAF;UACAvC;UACAO;;UAEA;UACAA;YACAO;YACA0B;YACAC;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1kBA;AAAA;AAAA;AAAA;AAAikC,CAAgB,2hCAAG,EAAC,C;;;;;;;;;;;ACArlC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/orders/orders.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/orders/orders.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./orders.vue?vue&type=template&id=8a71bbb4&\"\nvar renderjs\nimport script from \"./orders.vue?vue&type=script&lang=js&\"\nexport * from \"./orders.vue?vue&type=script&lang=js&\"\nimport style0 from \"./orders.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/orders/orders.vue\"\nexport default component.exports", "export * from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orders.vue?vue&type=template&id=8a71bbb4&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.orders.length\n  var l0 =\n    g0 > 0\n      ? _vm.__map(_vm.filteredOrders, function (order, index) {\n          var $orig = _vm.__get_orig(order)\n          var m0 = _vm.getStoreName(order.storeName)\n          return {\n            $orig: $orig,\n            m0: m0,\n          }\n        })\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orders.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orders.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container page-orders\">\r\n\t\t<!-- 页面背景 -->\r\n\t\t<view class=\"page-background\">\r\n\t\t\t<!-- 背景图片 -->\r\n\t\t\t<image class=\"background-image\" src=\"https://wuji1112-1312674791.cos.ap-guangzhou.myqcloud.com/static/shouye3.png\" mode=\"aspectFill\"></image>\r\n\t\t\t\r\n\t\t\t<!-- 深磨砂效果叠加层 -->\r\n\t\t\t<view class=\"frosted-overlay\"></view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 导航栏 -->\r\n\t\t<view class=\"navbar\" :style=\"navbarStyle\">\r\n\t\t\t<view class=\"navbar-left\" @click=\"goBack\">\r\n\t\t\t\t<text class=\"material-icons md-24 text-primary\">arrow_back</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"navbar-title\">我的订单</view>\r\n\t\t\t<view class=\"navbar-right\"></view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 页面内容区域 -->\r\n\t\t<view class=\"page-content\">\r\n\t\t\t<!-- 订单类型选择 -->\r\n\t\t\t<view class=\"tabs-container\">\r\n\t\t\t\t<view class=\"tabs\">\r\n\t\t\t\t\t<view class=\"tab\" :class=\"{'active': activeTab === 0}\" @click=\"changeTab(0)\">\r\n\t\t\t\t\t\t<text class=\"material-icons md-18 icon-inline\">list_alt</text>\r\n\t\t\t\t\t\t<text>全部</text>\r\n\t\t\t\t\t\t<view class=\"tab-line\" v-if=\"activeTab === 0\"></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"tab\" :class=\"{'active': activeTab === 1}\" @click=\"changeTab(1)\">\r\n\t\t\t\t\t\t<text class=\"material-icons md-18 icon-inline\">pending_actions</text>\r\n\t\t\t\t\t\t<text>进行中</text>\r\n\t\t\t\t\t\t<view class=\"tab-line\" v-if=\"activeTab === 1\"></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"tab\" :class=\"{'active': activeTab === 2}\" @click=\"changeTab(2)\">\r\n\t\t\t\t\t\t<text class=\"material-icons md-18 icon-inline\">task_alt</text>\r\n\t\t\t\t\t\t<text>已完成</text>\r\n\t\t\t\t\t\t<view class=\"tab-line\" v-if=\"activeTab === 2\"></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<!-- 调试按钮，仅在开发环境显示 -->\r\n\t\t\t\t<view class=\"debug-btn\" @click=\"debugOrderList\" v-if=\"isDevMode\">调试</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 订单列表 -->\r\n\t\t\t<view class=\"content\">\r\n\t\t\t\t<view v-if=\"orders.length > 0\">\r\n\t\t\t\t\t<view class=\"order-item\" v-for=\"(order, index) in filteredOrders\" :key=\"index\" @click=\"viewOrderDetail(order.id)\">\r\n\t\t\t\t\t\t<view class=\"order-header flex justify-between items-center\">\r\n\t\t\t\t\t\t\t<view class=\"order-store\">\r\n\t\t\t\t\t\t\t\t<text class=\"title-sm\">{{order.orderNo}}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"status\" :class=\"{\r\n\t\t\t\t\t\t\t\t'active': order.rawStatus === 1,\r\n\t\t\t\t\t\t\t\t'unpaid': order.rawStatus === 0,\r\n\t\t\t\t\t\t\t\t'completed': order.rawStatus === 2,\r\n\t\t\t\t\t\t\t\t'cancelled': order.rawStatus === 3,\r\n\t\t\t\t\t\t\t\t'inactive': order.rawStatus !== 0 && order.rawStatus !== 1 && order.rawStatus !== 2 && order.rawStatus !== 3\r\n\t\t\t\t\t\t\t}\">\r\n\t\t\t\t\t\t\t\t{{order.status}}\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"divider mt-sm\"></view>\r\n\t\t\t\t\t\t<view class=\"order-details mt-md\">\r\n\t\t\t\t\t\t\t<view class=\"detail-item flex justify-between\">\r\n\t\t\t\t\t\t\t\t<text class=\"detail-label\"><text class=\"material-icons md-18 icon-inline\">store</text> 门店</text>\r\n\t\t\t\t\t\t\t\t<text class=\"detail-value\">{{getStoreName(order.storeName)}}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"detail-item flex justify-between mt-sm\">\r\n\t\t\t\t\t\t\t\t<text class=\"detail-label\"><text class=\"material-icons md-18 icon-inline\">schedule</text> 下单时间</text>\r\n\t\t\t\t\t\t\t\t<text class=\"detail-value\">{{order.startTime}}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"detail-item flex justify-between mt-sm\">\r\n\t\t\t\t\t\t\t\t<text class=\"detail-label\"><text class=\"material-icons md-18 icon-inline\">payments</text> 消费金额</text>\r\n\t\t\t\t\t\t\t\t<text class=\"primary-light\">{{order.amount}}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<!-- 修改订单操作按钮部分，移除\"结束使用\"按钮 -->\r\n\t\t\t\t\t\t<view class=\"order-actions flex mt-md\" v-if=\"order.rawStatus === 0\">\r\n\t\t\t\t\t\t\t<button class=\"btn btn-primary btn-sm flex-1\" @click.stop=\"payOrder(order.id)\">\r\n\t\t\t\t\t\t\t\t<text class=\"material-icons md-18 icon-inline\">payment</text> 去支付\r\n\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<!-- 空状态 -->\r\n\t\t\t\t<view v-else class=\"empty-state\">\r\n\t\t\t\t\t<text class=\"material-icons md-48 text-tertiary\">receipt_long</text>\r\n\t\t\t\t\t<text class=\"empty-text\">暂无订单记录</text>\r\n\t\t\t\t\t<button class=\"btn btn-outline mt-md\" @click=\"navigateTo('/pages/index/index')\">\r\n\t\t\t\t\t\t<text class=\"material-icons md-18 icon-inline\">home</text> 返回首页\r\n\t\t\t\t\t</button>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport API from '@/static/js/api.js';\r\n\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tactiveTab: 0,\r\n\t\t\torders: [],\r\n\t\t\tloading: false,\r\n\t\t\tpage: 1,\r\n\t\t\t// iOS适配相关\r\n\t\t\tsystemInfo: {},\r\n\t\t\tnavbarStyle: {},\r\n\t\t\tisIOS: false,\r\n\t\t\tpageSize: 10,\r\n\t\t\thasMore: true,\r\n\t\t\t// 添加订单状态映射\r\n\t\t\torderStatusMap: {\r\n\t\t\t\t0: '未支付',\r\n\t\t\t\t1: '进行中',\r\n\t\t\t\t2: '已完成',\r\n\t\t\t\t3: '已取消'\r\n\t\t\t},\r\n\t\t\t// 是否为开发模式\r\n\t\t\tisDevMode: process.env.NODE_ENV === 'development' || true\r\n\t\t}\r\n\t},\r\n\tcomputed: {\r\n\t\tfilteredOrders() {\r\n\t\t\tif (this.activeTab === 0) {\r\n\t\t\t\treturn this.orders;\r\n\t\t\t} else if (this.activeTab === 1) {\r\n\t\t\t\treturn this.orders.filter(order => order.status === '进行中');\r\n\t\t\t} else {\r\n\t\t\t\treturn this.orders.filter(order => order.status === '已完成');\r\n\t\t\t}\r\n\t\t}\r\n\t},\r\n\tonLoad() {\r\n\t\t// 加载订单列表\r\n\t\tthis.loadOrders();\r\n\t},\r\n\tonReady() {\r\n\t\t// 获取系统信息并计算iOS适配参数\r\n\t\tthis.calculateIOSAdaptation();\r\n\t},\r\n\t// 下拉刷新\r\n\tonPullDownRefresh() {\r\n\t\tthis.refreshOrders();\r\n\t},\r\n\t// 上拉加载更多\r\n\tonReachBottom() {\r\n\t\tif (this.hasMore && !this.loading) {\r\n\t\t\tthis.loadMoreOrders();\r\n\t\t}\r\n\t},\r\n\tmethods: {\r\n\t\t// 计算iOS适配参数\r\n\t\tcalculateIOSAdaptation() {\r\n\t\t\ttry {\r\n\t\t\t\tthis.systemInfo = uni.getSystemInfoSync();\r\n\t\t\t\tthis.isIOS = this.systemInfo.platform === 'ios';\r\n\r\n\t\t\t\tconsole.log('系统信息:', this.systemInfo);\r\n\t\t\t\tconsole.log('是否iOS:', this.isIOS);\r\n\r\n\t\t\t\tif (this.isIOS) {\r\n\t\t\t\t\tconst statusBarHeight = this.systemInfo.statusBarHeight || 44;\r\n\t\t\t\t\tconst model = this.systemInfo.model || '';\r\n\t\t\t\t\tconst safeAreaTop = this.systemInfo.safeArea ? this.systemInfo.safeArea.top : statusBarHeight;\r\n\r\n\t\t\t\t\tconsole.log('状态栏高度:', statusBarHeight);\r\n\t\t\t\t\tconsole.log('设备型号:', model);\r\n\t\t\t\t\tconsole.log('安全区域顶部:', safeAreaTop);\r\n\r\n\t\t\t\t\tlet finalTopPosition;\r\n\r\n\t\t\t\t\t// 超激进适配策略 - 尝试多种不同的计算方式\r\n\t\t\t\t\tif (model.includes('iPhone 16 Pro')) {\r\n\t\t\t\t\t\t// 方案1：直接使用状态栏高度，无额外间距\r\n\t\t\t\t\t\tfinalTopPosition = statusBarHeight;\r\n\t\t\t\t\t\tconsole.log('iPhone 16 Pro - 方案1（状态栏高度）:', finalTopPosition);\r\n\r\n\t\t\t\t\t\t// 方案2：如果还是太靠下，尝试更小的值\r\n\t\t\t\t\t\tif (finalTopPosition > 50) {\r\n\t\t\t\t\t\t\tfinalTopPosition = 44; // 使用标准状态栏高度\r\n\t\t\t\t\t\t\tconsole.log('iPhone 16 Pro - 方案2（标准高度）:', finalTopPosition);\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t// 方案3：如果还是太靠下，尝试负值\r\n\t\t\t\t\t\tif (finalTopPosition > 45) {\r\n\t\t\t\t\t\t\tfinalTopPosition = statusBarHeight - 10; // 负偏移\r\n\t\t\t\t\t\t\tconsole.log('iPhone 16 Pro - 方案3（负偏移）:', finalTopPosition);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else if (model.includes('iPhone 15 Pro') || model.includes('iPhone 14 Pro')) {\r\n\t\t\t\t\t\tfinalTopPosition = statusBarHeight;\r\n\t\t\t\t\t} else if (model.includes('iPhone X') || model.includes('iPhone 11') ||\r\n\t\t\t\t\t\t\t  model.includes('iPhone 12') || model.includes('iPhone 13')) {\r\n\t\t\t\t\t\tfinalTopPosition = statusBarHeight;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tfinalTopPosition = statusBarHeight;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tthis.navbarStyle = {\r\n\t\t\t\t\t\tmarginTop: finalTopPosition + 'px',\r\n\t\t\t\t\t\t// 添加更多样式确保生效\r\n\t\t\t\t\t\tposition: 'relative',\r\n\t\t\t\t\t\ttop: '0px'\r\n\t\t\t\t\t};\r\n\r\n\t\t\t\t\tconsole.log('超激进适配 - 最终顶部位置:', finalTopPosition + 'px');\r\n\t\t\t\t\tconsole.log('超激进适配 - 导航栏样式:', this.navbarStyle);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.navbarStyle = {};\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('计算iOS适配参数失败:', error);\r\n\t\t\t\tthis.navbarStyle = {};\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\tgoBack() {\r\n\t\t\tuni.navigateBack();\r\n\t\t},\r\n\t\tchangeTab(index) {\r\n\t\t\tif (this.activeTab !== index) {\r\n\t\t\t\tthis.activeTab = index;\r\n\t\t\t\tthis.refreshOrders();\r\n\t\t\t}\r\n\t\t},\r\n\t\tnavigateTo(url) {\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: url\r\n\t\t\t});\r\n\t\t},\r\n\t\t// 处理门店名称，移除\"今夜城堡 - \"前缀\r\n\t\tgetStoreName(fullName) {\r\n\t\t\treturn fullName.replace('今夜城堡 - ', '');\r\n\t\t},\r\n\t\t\r\n\t\t// 刷新订单列表\r\n\t\trefreshOrders() {\r\n\t\t\tthis.page = 1;\r\n\t\t\tthis.hasMore = true;\r\n\t\t\tthis.orders = [];\r\n\t\t\tthis.loadOrders();\r\n\t\t},\r\n\t\t\r\n\t\t// 加载订单列表\r\n\t\tloadOrders() {\r\n\t\t\tif (this.loading) return;\r\n\t\t\t\r\n\t\t\tthis.loading = true;\r\n\t\t\t\r\n\t\t\t// 显示加载中\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '加载中...'\r\n\t\t\t});\r\n\t\t\t\r\n\t\t\t// 获取对应状态的订单\r\n\t\t\tlet status = 0;\r\n\t\t\tif (this.activeTab === 1) {\r\n\t\t\t\tstatus = 1; // 进行中\r\n\t\t\t} else if (this.activeTab === 2) {\r\n\t\t\t\tstatus = 2; // 已完成\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 构建请求参数\r\n\t\t\tconst params = {\r\n\t\t\t\tpage: this.page,\r\n\t\t\t\tsize: this.pageSize\r\n\t\t\t};\r\n\t\t\t\r\n\t\t\t// 只有当status不为0时才添加到参数中\r\n\t\t\tif (status !== 0) {\r\n\t\t\t\tparams.status = status;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 调用API获取订单列表\r\n\t\t\tAPI.order.getList(status, this.page, this.pageSize)\r\n\t\t\t\t.then(res => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tuni.stopPullDownRefresh();\r\n\t\t\t\t\tthis.loading = false;\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 处理返回数据，增强兼容性\r\n\t\t\t\t\tlet orderList = [];\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 兼容不同的返回格式\r\n\t\t\t\t\tif (Array.isArray(res.data)) {\r\n\t\t\t\t\t\t// 直接返回数组的情况\r\n\t\t\t\t\t\torderList = res.data;\r\n\t\t\t\t\t} else if (res.data && Array.isArray(res.data.list)) {\r\n\t\t\t\t\t\t// 包含list数组的情况\r\n\t\t\t\t\t\torderList = res.data.list;\r\n\t\t\t\t\t} else if (res.data && Array.isArray(res.data.records)) {\r\n\t\t\t\t\t\t// 包含records数组的情况\r\n\t\t\t\t\t\torderList = res.data.records;\r\n\t\t\t\t\t} else if (res.data && typeof res.data === 'object') {\r\n\t\t\t\t\t\t// 其他情况，尝试将对象转为数组\r\n\t\t\t\t\t\torderList = [res.data];\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 格式化订单数据\r\n\t\t\t\t\tconst formattedOrders = orderList.map(item => {\r\n\t\t\t\t\t\t// 确定订单状态\r\n\t\t\t\t\t\tlet orderStatus = '未知';\r\n\t\t\t\t\t\tlet rawStatus = 0;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 根据payStatus和status确定状态\r\n\t\t\t\t\t\tif (item.payStatus === 1) {\r\n\t\t\t\t\t\t\t// 已支付\r\n\t\t\t\t\t\t\tif (item.endTime) {\r\n\t\t\t\t\t\t\t\t// 已结束\r\n\t\t\t\t\t\t\t\torderStatus = '已完成';\r\n\t\t\t\t\t\t\t\trawStatus = 2;\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t// 进行中\r\n\t\t\t\t\t\t\t\torderStatus = '进行中';\r\n\t\t\t\t\t\t\t\trawStatus = 1;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t// 未支付或已取消\r\n\t\t\t\t\t\t\tif (item.remark && item.remark.includes('取消')) {\r\n\t\t\t\t\t\t\t\torderStatus = '已取消';\r\n\t\t\t\t\t\t\t\trawStatus = 3;\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\torderStatus = '未支付';\r\n\t\t\t\t\t\t\t\trawStatus = 0;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\tid: item.id || item.orderId,\r\n\t\t\t\t\t\t\torderNo: item.orderNo,\r\n\t\t\t\t\t\t\tstoreName: item.storeName || item.shopName || '未知门店', // 兼容API返回的shopName\r\n\t\t\t\t\t\t\tstartTime: item.startTime || item.createTime || '未知时间',\r\n\t\t\t\t\t\t\tamount: item.amount ? `¥${parseFloat(item.amount).toFixed(2)}` : '¥0.00',\r\n\t\t\t\t\t\t\tstatus: orderStatus,\r\n\t\t\t\t\t\t\trawStatus: rawStatus\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t});\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 第一页直接替换，其他页追加\r\n\t\t\t\t\tif (this.page === 1) {\r\n\t\t\t\t\t\tthis.orders = formattedOrders;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.orders = [...this.orders, ...formattedOrders];\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 判断是否还有更多数据\r\n\t\t\t\t\tthis.hasMore = orderList.length === this.pageSize;\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 更新页码\r\n\t\t\t\t\tif (orderList.length > 0) {\r\n\t\t\t\t\t\tthis.page++;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 处理空数据情况\r\n\t\t\t\t\tif (this.orders.length === 0) {\r\n\t\t\t\t\t\tconsole.log('没有订单数据');\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t.catch(err => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tuni.stopPullDownRefresh();\r\n\t\t\t\t\tthis.loading = false;\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 显示错误提示\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: err.message || '获取订单失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 如果是第一页加载失败，显示一些默认数据\r\n\t\t\t\t\tif (this.page === 1 && this.orders.length === 0) {\r\n\t\t\t\t\t\tthis.orders = [\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tid: '1',\r\n\t\t\t\t\t\t\t\torderNo: 'JYC202407100001',\r\n\t\t\t\t\t\t\t\tstoreName: '今夜城堡 - 西湖店',\r\n\t\t\t\t\t\t\t\tstartTime: '2024-07-10 14:00',\r\n\t\t\t\t\t\t\t\tamount: '¥29.00',\r\n\t\t\t\t\t\t\t\tstatus: '进行中',\r\n\t\t\t\t\t\t\t\trawStatus: 1\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tid: '2',\r\n\t\t\t\t\t\t\t\torderNo: 'JYC202407050001',\r\n\t\t\t\t\t\t\t\tstoreName: '今夜城堡 - 滨江店',\r\n\t\t\t\t\t\t\t\tstartTime: '2024-07-05 20:00',\r\n\t\t\t\t\t\t\t\tamount: '¥35.00',\r\n\t\t\t\t\t\t\t\tstatus: '已完成',\r\n\t\t\t\t\t\t\t\trawStatus: 2\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t];\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 加载更多订单\r\n\t\tloadMoreOrders() {\r\n\t\t\tif (this.hasMore) {\r\n\t\t\t\tthis.loadOrders();\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 查看订单详情\r\n\t\tviewOrderDetail(orderId) {\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: `/pages/orders/detail?id=${orderId}`\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 支付订单\r\n\t\tpayOrder(orderId) {\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '处理中...'\r\n\t\t\t});\r\n\t\t\t\r\n\t\t\t// 调用支付接口\r\n\t\t\tAPI.order.payment(orderId)\r\n\t\t\t\t.then(res => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\r\n\t\t\t\t\tconst payInfo = res.data;\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 调用微信支付\r\n\t\t\t\t\tuni.requestPayment({\r\n\t\t\t\t\t\ttimeStamp: payInfo.timeStamp,\r\n\t\t\t\t\t\tnonceStr: payInfo.nonceStr,\r\n\t\t\t\t\t\tpackage: payInfo.packageValue,\r\n\t\t\t\t\t\tsignType: payInfo.signType,\r\n\t\t\t\t\t\tpaySign: payInfo.paySign,\r\n\t\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\t\t// 支付成功\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: '支付成功',\r\n\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t// 查询支付状态\r\n\t\t\t\t\t\t\tthis.checkPaymentStatus(orderId);\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\t\tconsole.error('支付失败:', err);\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t// 显示错误提示\r\n\t\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\t\ttitle: '支付失败',\r\n\t\t\t\t\t\t\t\tcontent: '支付未完成，请重试或选择其他支付方式',\r\n\t\t\t\t\t\t\t\tshowCancel: false\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t})\r\n\t\t\t\t.catch(err => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 显示错误提示\r\n\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\ttitle: '支付失败',\r\n\t\t\t\t\t\tcontent: err.message || '创建支付订单失败，请重试',\r\n\t\t\t\t\t\tshowCancel: false\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 查询支付状态\r\n\t\tcheckPaymentStatus(orderId) {\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '查询支付状态...'\r\n\t\t\t});\r\n\t\t\t\r\n\t\t\t// 调用查询支付状态接口\r\n\t\t\tAPI.order.getPaymentStatus(orderId)\r\n\t\t\t\t.then(res => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\r\n\t\t\t\t\tif (res.data === true) {\r\n\t\t\t\t\t\t// 支付成功，刷新订单列表\r\n\t\t\t\t\t\tthis.refreshOrders();\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// 支付失败或未完成\r\n\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\ttitle: '支付状态',\r\n\t\t\t\t\t\t\tcontent: '支付可能未完成，请查看订单状态',\r\n\t\t\t\t\t\t\tshowCancel: false\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t.catch(err => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 显示错误提示\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: err.message || '查询支付状态失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 刷新订单列表\r\n\t\t\t\t\tthis.refreshOrders();\r\n\t\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 调试订单列表\r\n\t\tdebugOrderList() {\r\n\t\t\t// 实现调试订单列表的逻辑\r\n\t\t\tconsole.log('调试订单列表');\r\n\t\t\t\r\n\t\t\t// 显示当前状态\r\n\t\t\tconsole.log('当前Tab:', this.activeTab);\r\n\t\t\tconsole.log('当前页码:', this.page);\r\n\t\t\tconsole.log('当前订单数:', this.orders.length);\r\n\t\t\t\r\n\t\t\t// 使用硬编码的API地址\r\n\t\t\tconst apiBaseUrl = 'https://api.jycb888.com/';\r\n\t\t\tconsole.log('API基础地址:', apiBaseUrl);\r\n\t\t\t\r\n\t\t\t// 创建一个测试请求\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '调试中...'\r\n\t\t\t});\r\n\t\t\t\r\n\t\t\t// 获取对应状态的订单\r\n\t\t\tlet status = 0;\r\n\t\t\tif (this.activeTab === 1) {\r\n\t\t\t\tstatus = 1; // 进行中\r\n\t\t\t} else if (this.activeTab === 2) {\r\n\t\t\t\tstatus = 2; // 已完成\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 构建请求参数\r\n\t\t\tconst params = { \r\n\t\t\t\tpage: 1, \r\n\t\t\t\tsize: this.pageSize \r\n\t\t\t};\r\n\t\t\t\r\n\t\t\t// 只有当status不为0时才添加到参数中\r\n\t\t\tif (status !== 0) {\r\n\t\t\t\tparams.status = status;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 显示请求信息\r\n\t\t\tconst requestInfo = {\r\n\t\t\t\turl: '/api/wx/miniapp/order/list',\r\n\t\t\t\tmethod: 'GET',\r\n\t\t\t\tdata: params\r\n\t\t\t};\r\n\t\t\tconsole.log('请求信息:', requestInfo);\r\n\t\t\t\r\n\t\t\t// 发送测试请求\r\n\t\t\tuni.request({\r\n\t\t\t\turl: apiBaseUrl + requestInfo.url,\r\n\t\t\t\tmethod: requestInfo.method,\r\n\t\t\t\tdata: requestInfo.data,\r\n\t\t\t\theader: {\r\n\t\t\t\t\t'Authorization': uni.getStorageSync('token') || '',\r\n\t\t\t\t\t'Content-Type': 'application/json'\r\n\t\t\t\t},\r\n\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\tconsole.log('原始响应:', res);\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 显示调试信息\r\n\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\ttitle: '调试信息',\r\n\t\t\t\t\t\tcontent: '请查看控制台输出',\r\n\t\t\t\t\t\tshowCancel: false\r\n\t\t\t\t\t});\r\n\t\t\t\t},\r\n\t\t\t\tfail: (err) => {\r\n\t\t\t\t\tconsole.error('请求失败:', err);\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 显示错误信息\r\n\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\ttitle: '请求失败',\r\n\t\t\t\t\t\tcontent: JSON.stringify(err),\r\n\t\t\t\t\t\tshowCancel: false\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style>\r\n\t/* iOS适配 */\r\n\t.page-orders {\r\n\t\t/* 适配iOS设备的安全区域和灵动岛 */\r\n\t\tpadding-top: env(safe-area-inset-top);\r\n\t}\r\n\r\n\t/* 页面基础样式 */\r\n\t.page-orders {\r\n\t\tcolor: #ffffff;\r\n\t\theight: 100vh;\r\n\t\tmin-height: 100vh;\r\n\t\tbox-sizing: border-box;\r\n\t\tposition: relative;\r\n\t\toverflow: hidden; /* 确保页面不会整体滚动 */\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t}\r\n\t\r\n\t/* 页面背景样式 */\r\n\t.page-background {\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tz-index: 0;\r\n\t}\r\n\t\r\n\t/* 背景图片样式 */\r\n\t.background-image {\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tz-index: 0;\r\n\t\tobject-fit: cover;\r\n\t}\r\n\t\r\n\t/* 深磨砂效果叠加层 */\r\n\t.frosted-overlay {\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tbackground-color: rgba(18, 18, 18, 0.7); /* 深色半透明背景 */\r\n\t\tbackdrop-filter: blur(8px); /* 较强模糊效果 */\r\n\t\t-webkit-backdrop-filter: blur(8px);\r\n\t\tz-index: 1;\r\n\t}\r\n\t\r\n\t/* 顶部导航样式 */\r\n\t.navbar {\r\n\t\theight: 90rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t\tpadding: 0 30rpx;\r\n\t\t/* 完全移除CSS的margin-top，只使用JavaScript动态设置 */\r\n\t\tmargin-top: 0;\r\n\t\tposition: relative;\r\n\t\tz-index: 100;\r\n\t}\r\n\t\r\n\t.navbar-title {\r\n\t\tfont-size: 40rpx;\r\n\t\tfont-weight: 600;\r\n\t}\r\n\t\r\n\t.navbar-left, .navbar-right {\r\n\t\twidth: 60rpx;\r\n\t\theight: 60rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t}\r\n\t\r\n\t/* 页面内容区域 */\r\n\t.page-content {\r\n\t\tflex: 1;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tposition: relative;\r\n\t\tz-index: 5;\r\n\t\toverflow: hidden; /* 确保内部content滚动 */\r\n\t\theight: calc(100vh - var(--status-bar-height) - 90rpx); /* 计算剩余高度 */\r\n\t}\r\n\t\r\n\t/* 标签页样式 - 往下移动 */\r\n\t.tabs-container {\r\n\t\tposition: relative;\r\n\t\tz-index: 10;\r\n\t\tborder-bottom: 1px solid rgba(255, 255, 255, 0.1);\r\n\t\tmargin-bottom: 20rpx;\r\n\t\tmargin-top: 20rpx;\r\n\t\tbackground-color: rgba(30, 30, 40, 0.5);\r\n\t\tborder-radius: 20rpx;\r\n\t\tbackdrop-filter: blur(10px);\r\n\t\t-webkit-backdrop-filter: blur(10px);\r\n\t\tbox-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);\r\n\t\tpadding: 0 10rpx;\r\n\t\tflex-shrink: 0; /* 防止标签栏被压缩 */\r\n\t}\r\n\t\r\n\t.tabs {\r\n\t\tdisplay: flex;\r\n\t\theight: 90rpx;\r\n\t\tborder-bottom: none;\r\n\t\tposition: relative;\r\n\t\toverflow: hidden;\r\n\t}\r\n\t\r\n\t.tab {\r\n\t\tflex: 1;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tposition: relative;\r\n\t\tcolor: var(--text-tertiary, rgba(255, 255, 255, 0.5));\r\n\t\tfont-size: 30rpx;\r\n\t\ttransition: all 0.3s ease;\r\n\t\toverflow: hidden;\r\n\t\tgap: 8rpx;\r\n\t}\r\n\t\r\n\t.tab.active {\r\n\t\tcolor: var(--primary-light, #A875FF);\r\n\t\tfont-weight: 500;\r\n\t\ttext-shadow: 0 0 8rpx rgba(168, 117, 255, 0.5);\r\n\t}\r\n\t\r\n\t.tab:before {\r\n\t\tcontent: '';\r\n\t\tposition: absolute;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tbackground: rgba(168, 117, 255, 0.05);\r\n\t\topacity: 0;\r\n\t\ttransition: opacity 0.3s ease;\r\n\t\tz-index: -1;\r\n\t}\r\n\t\r\n\t.tab.active:before {\r\n\t\topacity: 1;\r\n\t}\r\n\t\r\n\t.tab-line {\r\n\t\tposition: absolute;\r\n\t\tbottom: 0;\r\n\t\twidth: 40rpx;\r\n\t\theight: 4rpx;\r\n\t\tbackground: linear-gradient(to right, rgba(168, 117, 255, 0.8), rgba(139, 92, 246, 0.9));\r\n\t\tborder-radius: 2rpx;\r\n\t\tbox-shadow: 0 0 10rpx rgba(168, 117, 255, 0.5);\r\n\t\ttransition: width 0.3s ease;\r\n\t}\r\n\t\r\n\t.tab:active {\r\n\t\ttransform: scale(0.98);\r\n\t}\r\n\t\r\n\t/* 内容区域 */\r\n\t.content {\r\n\t\tflex: 1;\r\n\t\tpadding: 30rpx 30rpx;\r\n\t\tposition: relative;\r\n\t\twidth: 100%;\r\n\t\tbox-sizing: border-box;\r\n\t\toverflow-y: auto !important; /* 强制确保可以垂直滚动 */\r\n\t\t-webkit-overflow-scrolling: touch; /* 增加滚动惯性 */\r\n\t\theight: 100%; /* 确保高度占满 */\r\n\t\tpadding-bottom: 50rpx; /* 添加底部内边距，确保内容不会被遮挡 */\r\n\t\tmax-height: calc(100vh - var(--status-bar-height) - 90rpx - 130rpx); /* 减去导航栏和标签栏的高度 */\r\n\t}\r\n\t\r\n\t/* 订单卡片样式 */\r\n\t.order-item {\r\n\t\tmargin-bottom: 30rpx;\r\n\t\tpadding: 30rpx;\r\n\t\tbackground-color: rgba(30, 30, 40, 0.5);\r\n\t\tborder-radius: 20rpx;\r\n\t\tborder: 1px solid rgba(168, 117, 255, 0.3);\r\n\t\tbox-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);\r\n\t\tbackdrop-filter: blur(10px);\r\n\t\t-webkit-backdrop-filter: blur(10px);\r\n\t\ttransition: all 0.3s ease;\r\n\t\twidth: 100%;\r\n\t\tbox-sizing: border-box;\r\n\t\tmargin-left: auto;\r\n\t\tmargin-right: auto;\r\n\t}\r\n\t\r\n\t.order-item:active {\r\n\t\ttransform: scale(0.98);\r\n\t\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);\r\n\t}\r\n\t\r\n\t.order-header {\r\n\t\tmargin-bottom: 10rpx;\r\n\t}\r\n\t\r\n\t.order-store {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t}\r\n\t\r\n\t.order-store .title-sm {\r\n\t\tmargin-bottom: 0;\r\n\t}\r\n\t\r\n\t/* 分隔线 */\r\n\t.divider {\r\n\t\theight: 1px;\r\n\t\tbackground: linear-gradient(to right, rgba(168, 117, 255, 0.1), rgba(168, 117, 255, 0.3), rgba(168, 117, 255, 0.1));\r\n\t\tmargin: 10rpx 0;\r\n\t}\r\n\t\r\n\t/* 状态标签 */\r\n\t.status {\r\n\t\tpadding: 6rpx 18rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t\tfont-size: 26rpx;\r\n\t\tfont-weight: 500;\r\n\t\tdisplay: inline-flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\theight: 40rpx;\r\n\t\tmin-width: 80rpx;\r\n\t\ttext-align: center;\r\n\t\tline-height: 1;\r\n\t\twhite-space: nowrap;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\t\r\n\t.status.active {\r\n\t\tbackground-color: rgba(168, 117, 255, 0.15);\r\n\t\tcolor: var(--primary-light, #A875FF);\r\n\t\tborder: 1px solid rgba(168, 117, 255, 0.25);\r\n\t\tbox-shadow: 0 0 8rpx rgba(168, 117, 255, 0.2);\r\n\t}\r\n\t\r\n\t.status.unpaid {\r\n\t\tbackground-color: rgba(255, 152, 0, 0.15);\r\n\t\tcolor: #FF9800;\r\n\t\tborder: 1px solid rgba(255, 152, 0, 0.25);\r\n\t\tbox-shadow: 0 0 8rpx rgba(255, 152, 0, 0.2);\r\n\t}\r\n\t\r\n\t.status.completed {\r\n\t\tbackground-color: rgba(76, 175, 80, 0.15);\r\n\t\tcolor: #4CAF50;\r\n\t\tborder: 1px solid rgba(76, 175, 80, 0.25);\r\n\t\tbox-shadow: 0 0 8rpx rgba(76, 175, 80, 0.2);\r\n\t}\r\n\t\r\n\t.status.cancelled {\r\n\t\tbackground-color: rgba(244, 67, 54, 0.15);\r\n\t\tcolor: #F44336;\r\n\t\tborder: 1px solid rgba(244, 67, 54, 0.25);\r\n\t\tbox-shadow: 0 0 8rpx rgba(244, 67, 54, 0.2);\r\n\t}\r\n\t\r\n\t.status.inactive {\r\n\t\tbackground-color: rgba(255, 255, 255, 0.07);\r\n\t\tcolor: var(--text-tertiary, rgba(255, 255, 255, 0.5));\r\n\t\tborder: 1px solid rgba(255, 255, 255, 0.1);\r\n\t}\r\n\t\r\n\t/* 订单详情项 */\r\n\t.detail-item {\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\t\r\n\t/* 详情标签和值 - 增大字体 */\r\n\t.detail-label {\r\n\t\tcolor: rgba(255, 255, 255, 0.7);\r\n\t\tfont-size: 30rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t}\r\n\t\r\n\t.detail-value {\r\n\t\tcolor: #FFFFFF;\r\n\t\tfont-size: 30rpx;\r\n\t}\r\n\t\r\n\t/* 按钮样式 */\r\n\t.btn {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tborder-radius: 15rpx;\r\n\t\tpadding: 20rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: 600;\r\n\t\ttransition: all 0.3s ease;\r\n\t}\r\n\t\r\n\t.btn-primary {\r\n\t\tbackground: linear-gradient(135deg, rgba(168, 117, 255, 0.8), rgba(139, 92, 246, 0.9));\r\n\t\tcolor: #FFFFFF;\r\n\t\tbox-shadow: 0 4rpx 20rpx rgba(168, 117, 255, 0.4);\r\n\t\tborder: none;\r\n\t}\r\n\t\r\n\t.btn-primary:active {\r\n\t\ttransform: scale(0.98);\r\n\t\tbox-shadow: 0 2rpx 10rpx rgba(168, 117, 255, 0.3);\r\n\t}\r\n\t\r\n\t.btn-outline {\r\n\t\tbackground-color: transparent;\r\n\t\tborder: 1px solid rgba(168, 117, 255, 0.4);\r\n\t\tcolor: var(--primary-light, #A875FF);\r\n\t}\r\n\t\r\n\t.btn-outline:active {\r\n\t\tbackground-color: rgba(168, 117, 255, 0.1);\r\n\t\ttransform: scale(0.98);\r\n\t}\r\n\t\r\n\t.btn-sm {\r\n\t\tfont-size: 26rpx;\r\n\t\tpadding: 15rpx;\r\n\t}\r\n\t\r\n\t/* 空状态样式 */\r\n\t.empty-state {\r\n\t\tpadding: 100rpx 0;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tbackground-color: rgba(30, 30, 40, 0.5);\r\n\t\tborder-radius: 20rpx;\r\n\t\tbackdrop-filter: blur(10px);\r\n\t\t-webkit-backdrop-filter: blur(10px);\r\n\t\tborder: 1px solid rgba(255, 255, 255, 0.1);\r\n\t\tmargin-top: 30rpx; /* 添加顶部间距 */\r\n\t\tmin-height: 400rpx; /* 确保空状态有足够高度 */\r\n\t}\r\n\t\r\n\t.empty-text {\r\n\t\tmargin-top: 30rpx;\r\n\t\tmargin-bottom: 30rpx;\r\n\t\tcolor: var(--text-tertiary, rgba(255, 255, 255, 0.5));\r\n\t\tfont-size: 28rpx;\r\n\t}\r\n\t\r\n\t/* Material Icons 字体 */\r\n\t@font-face {\r\n\t\tfont-family: 'Material Icons';\r\n\t\tfont-style: normal;\r\n\t\tfont-weight: 400;\r\n\t\tsrc: url(https://fonts.gstatic.com/s/materialicons/v139/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format('woff2');\r\n\t}\r\n\r\n\t.material-icons {\r\n\t\tfont-family: 'Material Icons';\r\n\t\tfont-weight: normal;\r\n\t\tfont-style: normal;\r\n\t\tfont-size: 24rpx;\r\n\t\tline-height: 1;\r\n\t\tletter-spacing: normal;\r\n\t\ttext-transform: none;\r\n\t\tdisplay: inline-block;\r\n\t\twhite-space: nowrap;\r\n\t\tword-wrap: normal;\r\n\t\tdirection: ltr;\r\n\t\t-webkit-font-smoothing: antialiased;\r\n\t\t-moz-osx-font-smoothing: grayscale;\r\n\t}\r\n\t\r\n\t.material-icons.md-18 {\r\n\t\tfont-size: 36rpx;\r\n\t}\r\n\t\r\n\t.material-icons.md-24 {\r\n\t\tfont-size: 48rpx;\r\n\t}\r\n\t\r\n\t.material-icons.md-48 {\r\n\t\tfont-size: 96rpx;\r\n\t}\r\n\t\r\n\t.material-icons.text-primary {\r\n\t\tcolor: var(--text-primary, #FFFFFF);\r\n\t}\r\n\t\r\n\t.material-icons.text-tertiary {\r\n\t\tcolor: var(--text-tertiary, rgba(255, 255, 255, 0.5));\r\n\t}\r\n\t\r\n\t.icon-inline {\r\n\t\tmargin-right: 8rpx;\r\n\t\tvertical-align: middle;\r\n\t}\r\n\t\r\n\t/* 辅助类 */\r\n\t.flex {\r\n\t\tdisplay: flex;\r\n\t}\r\n\t\r\n\t.flex-col {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t}\r\n\t\r\n\t.justify-between {\r\n\t\tjustify-content: space-between;\r\n\t}\r\n\t\r\n\t.items-center {\r\n\t\talign-items: center;\r\n\t}\r\n\t\r\n\t.text-center {\r\n\t\ttext-align: center;\r\n\t}\r\n\t\r\n\t.mt-sm {\r\n\t\tmargin-top: 16rpx;\r\n\t}\r\n\t\r\n\t.mt-md {\r\n\t\tmargin-top: 30rpx;\r\n\t}\r\n\t\r\n\t.mt-lg {\r\n\t\tmargin-top: 60rpx;\r\n\t}\r\n\t\r\n\t.gap-md {\r\n\t\tgap: 20rpx;\r\n\t}\r\n\t\r\n\t.flex-1 {\r\n\t\tflex: 1;\r\n\t}\r\n\t\r\n\t/* 文本样式 */\r\n\t.title-sm {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: 500;\r\n\t\tcolor: #ffffff;\r\n\t}\r\n\t\r\n\t.text-secondary {\r\n\t\tcolor: rgba(255, 255, 255, 0.7);\r\n\t\tfont-size: 28rpx;\r\n\t}\r\n\t\r\n\t.text-tertiary {\r\n\t\tcolor: rgba(255, 255, 255, 0.5);\r\n\t\tfont-size: 26rpx;\r\n\t}\r\n\t\r\n\t.text-primary {\r\n\t\tcolor: #FFFFFF;\r\n\t\tfont-size: 28rpx;\r\n\t}\r\n\t\r\n\t.primary-light {\r\n\t\tcolor: var(--primary-light, #A875FF);\r\n\t\tfont-size: 34rpx;\r\n\t\tfont-weight: 600;\r\n\t}\r\n\t\r\n\t.agreement-tip {\r\n\t\tmargin-top: 20rpx;\r\n\t\ttext-align: center;\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: rgba(255, 255, 255, 0.6);\r\n\t}\r\n\t\r\n\t/* 调试按钮样式 */\r\n\t.debug-btn {\r\n\t\tposition: absolute;\r\n\t\tright: 20rpx;\r\n\t\ttop: 10rpx;\r\n\t\tbackground-color: rgba(255, 0, 0, 0.7);\r\n\t\tcolor: white;\r\n\t\tpadding: 4rpx 16rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t\tfont-size: 24rpx;\r\n\t\tz-index: 10;\r\n\t\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);\r\n\t\tborder: 1px solid rgba(255, 0, 0, 0.3);\r\n\t}\r\n\t\r\n\t.debug-btn:active {\r\n\t\ttransform: scale(0.95);\r\n\t\tbackground-color: rgba(255, 0, 0, 0.8);\r\n\t}\r\n\r\n\t/* 移除所有CSS适配，完全依赖JavaScript动态设置 */\r\n</style>", "import mod from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orders.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orders.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754165306711\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}