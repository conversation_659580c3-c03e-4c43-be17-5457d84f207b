















































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































/* CSS变量定义 */
page {
	--primary-light: #A875FF;
	--neon-pink: #ff36f9;
	--success-color: #4CAF50;
	--error-color: #F44336;
	overflow: hidden; /* 禁用页面级别的滚动 */
	height: 100vh; /* 固定页面高度 */
}
/* 页面基础样式 */
.page-device {
	padding-top: 120rpx; /* 减小顶部内边距，从160rpx改为120rpx */
	padding-bottom: calc(50rpx + env(safe-area-inset-bottom));
	color: #ffffff;
	height: 100vh;
	min-height: 100vh;
	box-sizing: border-box;
	position: relative;
	overflow: hidden;
}
/* 页面背景样式 */
.page-background {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 0;
	will-change: transform;
	backface-visibility: hidden;
	-webkit-backface-visibility: hidden;
}
.background-image {
	width: 100%;
	height: 100%;
	object-fit: cover;
	transition: opacity 0.3s ease;
	-webkit-transform: scale(1.05);
	        transform: scale(1.05); /* 稍微放大背景图片，确保覆盖边缘 */
}
.gradient-overlay {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: linear-gradient(to bottom, 
		rgba(18, 18, 18, 0.7) 0%, 
		rgba(18, 18, 18, 0.6) 50%,
		rgba(18, 18, 18, 0.7) 100%);
	z-index: 1;
}
/* 顶部状态栏和导航栏 */
.status-bar {
	width: 100%;
	background: transparent; /* 移除背景色，改为透明 */
	backdrop-filter: none; /* 移除模糊效果 */
	-webkit-backdrop-filter: none; /* 移除模糊效果 */
	position: fixed;
	top: 0;
	left: 0;
	z-index: 100;
}
/* 导航栏 */
.nav-bar {
	position: fixed;
	top: calc(env(safe-area-inset-top) + 60rpx);
	left: 15rpx;
	right: 15rpx;
	height: 110rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background: transparent; /* 移除渐变背景，改为透明 */
	backdrop-filter: none; /* 移除模糊效果 */
	-webkit-backdrop-filter: none; /* 移除模糊效果 */
	z-index: 100;
	padding: 0 30rpx;
	border-bottom: none; /* 移除底部边框 */
	box-shadow: none; /* 移除阴影效果 */
	border-radius: 0 0 30rpx 30rpx;
}
.nav-back {
	position: absolute;
	left: 30rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	width: 70rpx;
	height: 70rpx;
	border-radius: 50%;
	background-color: rgba(168, 117, 255, 0.15);
	border: 1px solid rgba(168, 117, 255, 0.3);
}
.nav-back .material-icons {
	font-size: 44rpx;
	color: rgba(255, 255, 255, 0.9);
	text-shadow: 0 0 8rpx rgba(168, 117, 255, 0.5);
}
.nav-title {
	font-size: 38rpx;
	font-weight: 600;
	color: #ffffff;
	text-shadow: 0 0 10rpx rgba(168, 117, 255, 0.5);
	letter-spacing: 2rpx;
}
/* Banner样式 - iOS真机优化 */
.banner-container {
	position: fixed;
	top: 140rpx; /* 将位置从170rpx向上移动到140rpx */
	left: 0;
	right: 0;
	width: 92%; /* 从100%缩小到92% */
	max-width: 700rpx; /* 从750rpx缩小到700rpx */
	margin: 0 auto; /* 使用margin auto实现水平居中 */
	display: flex;
	justify-content: center;
	align-items: center;
	z-index: 10;
	padding: 20rpx; /* 增加内边距，扩大点击区域 */
	/* iOS兼容性 - 关键优化 */
	-webkit-tap-highlight-color: rgba(0,0,0,0);
	-webkit-touch-callout: none;
	-webkit-user-select: none;
	user-select: none;
	cursor: pointer;
	/* 确保可点击区域 */
	min-height: 220rpx; /* 增加最小高度 */
	/* iOS触摸优化 */
	touch-action: manipulation;
	/* 添加点击反馈 */
	transition: all 0.1s ease;
	/* 强制硬件加速 */
	-webkit-transform: translateZ(0);
	transform: translateZ(0);
	will-change: transform, opacity;
}
.banner-container:active {
	opacity: 0.7;
	-webkit-transform: scale(0.98) translateZ(0);
	        transform: scale(0.98) translateZ(0);
}
.banner-image {
	width: 100%;
	height: 180rpx; /* 调整高度为180rpx */
	object-fit: contain;
	/* iOS兼容性 - 完全禁用图片交互 */
	pointer-events: none;
	-webkit-tap-highlight-color: transparent;
	-webkit-touch-callout: none;
	-webkit-user-select: none;
	user-select: none;
	/* 禁用图片拖拽 */
	-webkit-user-drag: none;
	-khtml-user-drag: none;
	-moz-user-drag: none;
	-o-user-drag: none;
}
.banner-glow {
	/* 确保glow元素完全不拦截点击事件 */
	pointer-events: none;
	-webkit-tap-highlight-color: transparent;
	-webkit-touch-callout: none;
	-webkit-user-select: none;
	user-select: none;
	touch-action: none;
}
/* 删除Logo区域样式 */
/* 内容区域 */
.content {
	padding: 0 30rpx 30rpx; /* 保持左右内边距 */
	position: relative;
	z-index: 2;
	height: calc(100vh - 120rpx - env(safe-area-inset-bottom) - 50rpx); /* 调整高度，从160rpx改为120rpx */
	max-height: calc(100vh - 120rpx - env(safe-area-inset-bottom) - 50rpx); /* 限制最大高度 */
	display: flex;
	flex-direction: column;
	overflow: hidden; /* 禁用滚动 */
	touch-action: none; /* 禁用触摸滚动 */
	margin-top: 660rpx; /* 调整顶部边距，使内容稍微往下移 */
}
/* 欢迎标题样式 */
.welcome-section {
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-bottom: 30rpx;
}
.welcome-title {
	font-size: 60rpx;
	font-weight: 700;
	color: #ffffff;
	text-align: center;
	text-shadow: 
		0 0 10rpx rgba(255, 255, 255, 0.5),
		0 0 20rpx rgba(168, 117, 255, 0.5);
	letter-spacing: 4rpx;
}
.welcome-subtitle {
	font-size: 80rpx;
	font-weight: 800;
	color: #ffffff;
	text-align: center;
	background: linear-gradient(to right, #A875FF, #ff36f9);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	text-shadow: 0 0 20rpx rgba(168, 117, 255, 0.7);
	letter-spacing: 6rpx;
	margin-top: 10rpx;
	-webkit-animation: glow 2s ease-in-out infinite alternate;
	        animation: glow 2s ease-in-out infinite alternate;
}
@-webkit-keyframes glow {
from {
		text-shadow: 0 0 10rpx rgba(168, 117, 255, 0.5), 0 0 20rpx rgba(168, 117, 255, 0.3);
}
to {
		text-shadow: 0 0 20rpx rgba(168, 117, 255, 0.7), 0 0 30rpx rgba(168, 117, 255, 0.5), 0 0 40rpx rgba(168, 117, 255, 0.3);
}
}
@keyframes glow {
from {
		text-shadow: 0 0 10rpx rgba(168, 117, 255, 0.5), 0 0 20rpx rgba(168, 117, 255, 0.3);
}
to {
		text-shadow: 0 0 20rpx rgba(168, 117, 255, 0.7), 0 0 30rpx rgba(168, 117, 255, 0.5), 0 0 40rpx rgba(168, 117, 255, 0.3);
}
}
.welcome-device {
	display: flex;
	align-items: center;
	justify-content: center;
	margin-top: 30rpx;
	padding: 15rpx 40rpx;
	background: rgba(168, 117, 255, 0.2);
	border: 1px solid rgba(168, 117, 255, 0.4);
	border-radius: 50rpx;
}
.welcome-device .material-icons {
	font-size: 36rpx;
	margin-right: 10rpx;
	color: rgba(255, 255, 255, 0.9);
}
.welcome-device text {
	font-size: 30rpx;
	color: rgba(255, 255, 255, 0.9);
}
/* 登录提示样式 */
.login-prompt {
	background: rgba(255, 193, 7, 0.1);
	border: 1px solid rgba(255, 193, 7, 0.3);
	border-radius: 20rpx;
	padding: 30rpx;
	margin: 30rpx 0;
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 20rpx;
}
.login-prompt-text {
	display: flex;
	align-items: center;
	gap: 15rpx;
	color: rgba(255, 255, 255, 0.9);
	font-size: 28rpx;
}
.login-prompt-text .material-icons {
	font-size: 32rpx;
	color: #FFC107;
}
.login-prompt-button {
	background: linear-gradient(135deg, #FFC107, #FF9800);
	border-radius: 50rpx;
	padding: 20rpx 40rpx;
	color: #000;
	font-size: 28rpx;
	font-weight: 600;
	box-shadow: 0 8rpx 20rpx rgba(255, 193, 7, 0.3);
	transition: all 0.3s ease;
}
.login-prompt-button:active {
	-webkit-transform: scale(0.95);
	        transform: scale(0.95);
	box-shadow: 0 4rpx 10rpx rgba(255, 193, 7, 0.2);
}
/* 设备价格与支付区域 */
.price-section {
	margin-top: 30rpx; /* 减小与上方元素的间距，从60rpx改为30rpx */
}
/* 设备卡片样式 */
.price-card {
	padding: 30rpx; /* 减小内边距，从40rpx改为30rpx */
	background-color: rgba(30, 30, 30, 0.5); /* 添加背景色 */
	border-radius: 20rpx;
	backdrop-filter: blur(10px); /* 添加模糊效果 */
	-webkit-backdrop-filter: blur(10px); /* 添加模糊效果 */
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2); /* 添加阴影 */
	border: 1px solid rgba(168, 117, 255, 0.3); /* 保留紫色边框 */
}
/* 设备连接区域 */
.connect-section {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 30rpx 0; /* 减小内边距，从40rpx改为30rpx */
	margin-top: 20rpx; /* 减小与上方元素的间距，因为已经有欢迎标题了 */
}
/* 霓虹灯标题效果 */
.neon-title {
	font-size: 42rpx;
	font-weight: 700;
	text-align: center;
	color: #fff;
	text-shadow: 
		0 0 5rpx rgba(255, 255, 255, 0.5),
		0 0 10rpx rgba(255, 255, 255, 0.3), 
		0 0 15rpx rgba(168, 117, 255, 0.4),
		0 0 25rpx rgba(168, 117, 255, 0.3);
	letter-spacing: 4rpx;
	-webkit-animation: soft-flicker 4s infinite alternate;
	        animation: soft-flicker 4s infinite alternate;
}
/* 智能门锁连接状态标题使用圆体 */
.status-title {
	font-family: "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "WenQuanYi Micro Hei", sans-serif;
	font-weight: 400;
	border-radius: 999rpx;
}
.neon-subtitle {
	font-size: 36rpx;
	font-weight: 600;
	text-align: center;
	margin-top: 10rpx;
	color: rgba(255, 255, 255, 0.9);
	text-shadow: 
		0 0 5rpx rgba(255, 255, 255, 0.3),
		0 0 10rpx rgba(255, 255, 255, 0.2), 
		0 0 15rpx rgba(255, 102, 221, 0.3);
	letter-spacing: 2rpx;
	-webkit-animation: soft-flicker 5s infinite alternate;
	        animation: soft-flicker 5s infinite alternate;
	-webkit-animation-delay: 0.5s;
	        animation-delay: 0.5s;
}
@-webkit-keyframes soft-flicker {
0%, 100% {
		opacity: 1;
}
30% {
		opacity: 0.9;
}
60% {
		opacity: 0.95;
}
}
@keyframes soft-flicker {
0%, 100% {
		opacity: 1;
}
30% {
		opacity: 0.9;
}
60% {
		opacity: 0.95;
}
}
/* 操作按钮 */
.action-buttons {
	margin-top: 40rpx;
	display: flex;
	justify-content: center;
}
.primary-button {
	position: relative;
	width: 240rpx; /* 增加按钮尺寸 */
	height: 240rpx; /* 增加按钮尺寸 */
	border-radius: 50%;
	background: rgba(139, 92, 246, 0.15);
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	box-shadow: 0 10rpx 40rpx rgba(0, 0, 0, 0.3); /* 增强阴影效果 */
	overflow: visible;
	border: 2px solid rgba(168, 117, 255, 0.4); /* 加粗边框 */
	transition: box-shadow 0.3s, -webkit-transform 0.3s;
	transition: transform 0.3s, box-shadow 0.3s;
	transition: transform 0.3s, box-shadow 0.3s, -webkit-transform 0.3s;
	-webkit-animation: pulse 2s infinite ease-in-out;
	        animation: pulse 2s infinite ease-in-out;
	backdrop-filter: blur(20px);
	-webkit-backdrop-filter: blur(20px);
	z-index: 1;
}
.primary-button:active {
	-webkit-transform: scale(0.96);
	        transform: scale(0.96);
	box-shadow: 0 4rpx 10rpx rgba(168, 117, 255, 0.2);
}
/* 添加内部渐变效果 */
.primary-button::after {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	border-radius: 50%;
	background: linear-gradient(145deg, rgba(139, 92, 246, 0.3), rgba(168, 117, 255, 0.1));
	z-index: -1;
}
/* 添加涟漪动画效果 - 第一层 */
.primary-button::before {
	content: '';
	position: absolute;
	top: 50%;
	left: 50%;
	width: 100%;
	height: 100%;
	border-radius: 50%;
	-webkit-transform: translate(-50%, -50%) scale(0.9);
	        transform: translate(-50%, -50%) scale(0.9);
	opacity: 0;
	z-index: -2;
	border: 2px solid rgba(168, 117, 255, 0.5);
	-webkit-animation: ripple 2s infinite linear;
	        animation: ripple 2s infinite linear;
}
/* 添加第二层涟漪 */
.primary-button .ripple-layer {
	content: '';
	position: absolute;
	top: 50%;
	left: 50%;
	width: 100%;
	height: 100%;
	border-radius: 50%;
	-webkit-transform: translate(-50%, -50%) scale(0.9);
	        transform: translate(-50%, -50%) scale(0.9);
	opacity: 0;
	z-index: -2;
	border: 2px solid rgba(168, 117, 255, 0.5);
	-webkit-animation: ripple 2s infinite linear 1s;
	        animation: ripple 2s infinite linear 1s;
}
@-webkit-keyframes ripple {
0% {
		-webkit-transform: translate(-50%, -50%) scale(0.9);
		        transform: translate(-50%, -50%) scale(0.9);
		opacity: 0.7;
}
100% {
		-webkit-transform: translate(-50%, -50%) scale(1.5);
		        transform: translate(-50%, -50%) scale(1.5);
		opacity: 0;
}
}
@keyframes ripple {
0% {
		-webkit-transform: translate(-50%, -50%) scale(0.9);
		        transform: translate(-50%, -50%) scale(0.9);
		opacity: 0.7;
}
100% {
		-webkit-transform: translate(-50%, -50%) scale(1.5);
		        transform: translate(-50%, -50%) scale(1.5);
		opacity: 0;
}
}
.primary-button .material-icons {
	font-size: 100rpx; /* 增大图标尺寸 */
	color: rgba(255, 255, 255, 0.85);
	margin-bottom: 10rpx;
}
.primary-button text:not(.material-icons) {
	font-size: 34rpx; /* 从36rpx改为34rpx */
	font-weight: 400; /* 从600改为400，取消加粗 */
	color: rgba(255, 255, 255, 0.9);
}
/* 按钮内部脉冲效果 */
@-webkit-keyframes pulse {
0%, 100% {
		box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);
}
50% {
		box-shadow: 0 8rpx 32rpx rgba(168, 117, 255, 0.4);
}
}
@keyframes pulse {
0%, 100% {
		box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);
}
50% {
		box-shadow: 0 8rpx 32rpx rgba(168, 117, 255, 0.4);
}
}
.open-button {
	background: rgba(76, 175, 80, 0.2); /* 简化背景，为毛玻璃效果做准备 */
	box-shadow: 0 8rpx 20rpx rgba(76, 175, 80, 0.2);
	border: 1px solid rgba(76, 175, 80, 0.5);
	-webkit-animation: pulse-green 2s infinite ease-in-out;
	        animation: pulse-green 2s infinite ease-in-out;
}
/* 添加开门按钮内部渐变效果 */
.open-button::after {
	background: linear-gradient(145deg, rgba(76, 175, 80, 0.4), rgba(76, 175, 80, 0.1));
}
/* 修改开门按钮涟漪效果 */
.open-button::before,
.open-button .ripple-layer {
	border-color: rgba(76, 175, 80, 0.5);
}
.open-button:active {
	box-shadow: 0 4rpx 10rpx rgba(76, 175, 80, 0.2);
}
@-webkit-keyframes pulse-green {
0%, 100% {
		box-shadow: 0 8rpx 20rpx rgba(76, 175, 80, 0.2);
}
50% {
		box-shadow: 0 8rpx 32rpx rgba(76, 175, 80, 0.5);
}
}
@keyframes pulse-green {
0%, 100% {
		box-shadow: 0 8rpx 20rpx rgba(76, 175, 80, 0.2);
}
50% {
		box-shadow: 0 8rpx 32rpx rgba(76, 175, 80, 0.5);
}
}
/* 连接状态展示 */
.connection-status {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 16rpx 40rpx; /* 从20rpx 50rpx缩小到16rpx 40rpx */
	margin-top: 40rpx;
	background: linear-gradient(145deg, rgba(139, 92, 246, 0.3), rgba(168, 117, 255, 0.5));
	border: 1px solid rgba(168, 117, 255, 0.5);
	border-radius: 40rpx; /* 从50rpx缩小到40rpx */
	box-shadow: 0 8rpx 20rpx rgba(168, 117, 255, 0.2);
	transition: all 0.3s ease;
	position: relative;
	overflow: hidden;
	-webkit-transform: scale(0.95);
	        transform: scale(0.95); /* 整体缩小到95% */
}
/* 添加连接状态的波纹背景效果 */
.connection-status::before {
	content: '';
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
	-webkit-animation: wave 2s infinite linear;
	        animation: wave 2s infinite linear;
}
@-webkit-keyframes wave {
0% {
		left: -100%;
}
50%, 100% {
		left: 100%;
}
}
@keyframes wave {
0% {
		left: -100%;
}
50%, 100% {
		left: 100%;
}
}
.connection-status.connected {
	background: linear-gradient(145deg, rgba(76, 175, 80, 0.3), rgba(76, 175, 80, 0.5));
	box-shadow: 0 8rpx 20rpx rgba(76, 175, 80, 0.2);
	border: 1px solid rgba(76, 175, 80, 0.5);
	-webkit-animation: glow-green 2s infinite alternate;
	        animation: glow-green 2s infinite alternate;
}
@-webkit-keyframes glow-green {
0% {
		box-shadow: 0 8rpx 20rpx rgba(76, 175, 80, 0.2);
}
100% {
		box-shadow: 0 8rpx 30rpx rgba(76, 175, 80, 0.6);
}
}
@keyframes glow-green {
0% {
		box-shadow: 0 8rpx 20rpx rgba(76, 175, 80, 0.2);
}
100% {
		box-shadow: 0 8rpx 30rpx rgba(76, 175, 80, 0.6);
}
}
.connection-status.disconnected {
	background: linear-gradient(145deg, rgba(158, 158, 158, 0.2), rgba(158, 158, 158, 0.3));
	box-shadow: 0 0 20rpx rgba(158, 158, 158, 0.2);
}
.connection-text {
	font-size: 28rpx; /* 从32rpx缩小到28rpx */
	font-weight: 500;
	color: #ffffff;
	font-family: "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "WenQuanYi Micro Hei", sans-serif;
}
.connection-status .material-icons {
	margin-right: 12rpx; /* 从16rpx缩小到12rpx */
	font-size: 36rpx; /* 从40rpx缩小到36rpx */
}
/* 连接提示 */
.connection-tips {
	margin-top: 30rpx; /* 从40rpx减小到30rpx，使文字上移 */
	display: flex;
	flex-direction: column;
	align-items: center;
	text-align: center;
}
.connection-tips .text-tertiary {
	line-height: 48rpx;
}
.connection-tips .text-agreement {
	font-size: 26rpx; /* 从28rpx缩小到26rpx */
	color: rgba(255, 255, 255, 0.4);
	line-height: 40rpx;
	margin-top: 10rpx;
}
/* 操作结果提示 */
.result-message {
	margin-top: 40rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	-webkit-animation: fadeIn 0.5s;
	        animation: fadeIn 0.5s;
}
@-webkit-keyframes fadeIn {
from {
		opacity: 0;
		-webkit-transform: translateY(20rpx);
		        transform: translateY(20rpx);
}
to {
		opacity: 1;
		-webkit-transform: translateY(0);
		        transform: translateY(0);
}
}
@keyframes fadeIn {
from {
		opacity: 0;
		-webkit-transform: translateY(20rpx);
		        transform: translateY(20rpx);
}
to {
		opacity: 1;
		-webkit-transform: translateY(0);
		        transform: translateY(0);
}
}
.result-icon {
	font-size: 80rpx;
	margin-bottom: 16rpx;
}
.result-icon.success {
	color: var(--success-color, #4CAF50);
}
.result-icon:not(.success) {
	color: var(--error-color, #F44336);
}
.result-text {
	font-size: 32rpx;
	font-weight: 500;
	color: #ffffff;
}
.text-connected {
	color: var(--success-color, #4CAF50);
	text-shadow: 0 0 10rpx rgba(76, 175, 80, 0.5); /* 增强绿色发光效果 */
}
.text-connecting {
	color: var(--primary-light, #A875FF);
	text-shadow: 0 0 10rpx rgba(168, 117, 255, 0.5); /* 增强紫色发光效果 */
}
.text-disconnected {
	color: rgba(158, 158, 158, 0.9);
	text-shadow: 0 0 10rpx rgba(158, 158, 158, 0.4);
	-webkit-animation: pulse-text 2s infinite;
	        animation: pulse-text 2s infinite;
}
@-webkit-keyframes pulse-text {
0% {
		opacity: 0.7;
}
50% {
		opacity: 1;
}
100% {
		opacity: 0.7;
}
}
@keyframes pulse-text {
0% {
		opacity: 0.7;
}
50% {
		opacity: 1;
}
100% {
		opacity: 0.7;
}
}
/* Material Icons 字体 */
@font-face {
	font-family: 'Material Icons';
	font-style: normal;
	font-weight: 400;
	src: url(https://fonts.gstatic.com/s/materialicons/v139/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format('woff2');
}
.material-icons {
	font-family: 'Material Icons';
	font-weight: normal;
	font-style: normal;
	font-size: 48rpx;
	line-height: 1;
	letter-spacing: normal;
	text-transform: none;
	display: inline-block;
	white-space: nowrap;
	word-wrap: normal;
	direction: ltr;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}
/* 辅助类 */
.flex {
	display: flex;
}
.justify-between {
	justify-content: space-between;
}
.items-center {
	align-items: center;
}
.mt-sm {
	margin-top: 16rpx;
}
.mt-md {
	margin-top: 30rpx;
}
.mt-lg {
	margin-top: 60rpx;
}
/* 标题和文本样式 */
.title-md {
	font-size: 38rpx;
	font-weight: 600;
	color: #ffffff;
}
.text-secondary {
	color: rgba(255, 255, 255, 0.7);
}
.text-tertiary {
	color: rgba(255, 255, 255, 0.5);
}
/* 卡片样式 */
.card {
	border-radius: 20rpx;
	background-color: transparent !important; /* 使用!important确保覆盖其他样式 */
	box-shadow: none !important; /* 移除阴影 */
	border: 1px solid rgba(168, 117, 255, 0.3); /* 添加紫色边框 */
}
.text-paid {
	color: var(--success-color, #4CAF50);
	text-shadow: 0 0 10rpx rgba(76, 175, 80, 0.5);
}
.text-unpaid {
	color: #FF9800;
	text-shadow: 0 0 10rpx rgba(255, 152, 0, 0.5);
}
/* 付款模态框样式 */
.modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.7);
	backdrop-filter: blur(5px);
	-webkit-backdrop-filter: blur(5px);
	z-index: 1000;
	display: flex;
	justify-content: center;
	align-items: center;
	-webkit-animation: fadeIn 0.3s ease;
	        animation: fadeIn 0.3s ease;
}
@keyframes fadeIn {
from {
		opacity: 0;
}
to {
		opacity: 1;
}
}
.payment-modal, .open-door-modal {
	width: 80%;
	max-width: 600rpx;
	background: linear-gradient(145deg, rgba(30, 30, 30, 0.9), rgba(40, 40, 40, 0.9));
	border-radius: 20rpx;
	box-shadow: 0 10rpx 40rpx rgba(0, 0, 0, 0.5);
	border: 1px solid rgba(168, 117, 255, 0.3);
	overflow: hidden;
	-webkit-animation: slideUp 0.3s ease;
	        animation: slideUp 0.3s ease;
	z-index: 1001;
	position: fixed;
	top: 50%;
	left: 50%;
	-webkit-transform: translate(-50%, -55%);
	        transform: translate(-50%, -55%);
}
@-webkit-keyframes slideUp {
from {
		-webkit-transform: translate(-50%, -45%);
		        transform: translate(-50%, -45%);
		opacity: 0;
}
to {
		-webkit-transform: translate(-50%, -55%);
		        transform: translate(-50%, -55%);
		opacity: 1;
}
}
@keyframes slideUp {
from {
		-webkit-transform: translate(-50%, -45%);
		        transform: translate(-50%, -45%);
		opacity: 0;
}
to {
		-webkit-transform: translate(-50%, -55%);
		        transform: translate(-50%, -55%);
		opacity: 1;
}
}
.modal-header {
	padding: 20rpx 30rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	border-bottom: 1px solid rgba(168, 117, 255, 0.2);
}
.modal-title {
	font-size: 34rpx;
	font-weight: 600;
	color: #ffffff;
	text-shadow: 0 0 10rpx rgba(168, 117, 255, 0.5);
}
.close-icon {
	font-size: 40rpx;
	color: rgba(255, 255, 255, 0.7);
}
.modal-body {
	padding: 30rpx;
}
/* 支付弹窗样式 */
.payment-info {
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-bottom: 30rpx;
}
.payment-device-name {
	font-size: 32rpx;
	font-weight: 600;
	color: #ffffff;
	margin-bottom: 10rpx;
}
.payment-price {
	font-size: 48rpx;
	font-weight: 700;
	color: #ffffff;
	background: linear-gradient(to right, #A875FF, #ff36f9);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	text-shadow: 0 0 10rpx rgba(168, 117, 255, 0.3);
}
.payment-button {
	width: 100%;
	height: 80rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background: linear-gradient(145deg, #A875FF, #ff36f9);
	border-radius: 40rpx;
	margin: 20rpx 0;
	position: relative;
	overflow: hidden;
}
.payment-button::before {
	content: '';
	position: absolute;
	top: -50%;
	left: -50%;
	width: 200%;
	height: 200%;
	background: linear-gradient(transparent, rgba(255, 255, 255, 0.3), transparent);
	-webkit-transform: rotate(45deg);
	        transform: rotate(45deg);
	-webkit-animation: shine 2s infinite;
	        animation: shine 2s infinite;
}
@-webkit-keyframes shine {
0% {
		left: -50%;
}
100% {
		left: 150%;
}
}
@keyframes shine {
0% {
		left: -50%;
}
100% {
		left: 150%;
}
}
.payment-button text {
	font-size: 32rpx;
	font-weight: 600;
	color: #ffffff;
	z-index: 1;
}
.payment-button.loading {
	background: linear-gradient(145deg, rgba(168, 117, 255, 0.7), rgba(255, 54, 249, 0.7));
}
.loading-spinner {
	width: 40rpx;
	height: 40rpx;
	border: 4rpx solid rgba(255, 255, 255, 0.3);
	border-top: 4rpx solid #ffffff;
	border-radius: 50%;
	-webkit-animation: spin 1s linear infinite;
	        animation: spin 1s linear infinite;
}
@-webkit-keyframes spin {
0% {
		-webkit-transform: rotate(0deg);
		        transform: rotate(0deg);
}
100% {
		-webkit-transform: rotate(360deg);
		        transform: rotate(360deg);
}
}
@keyframes spin {
0% {
		-webkit-transform: rotate(0deg);
		        transform: rotate(0deg);
}
100% {
		-webkit-transform: rotate(360deg);
		        transform: rotate(360deg);
}
}
.payment-tips {
	font-size: 24rpx;
	color: rgba(255, 255, 255, 0.5);
	text-align: center;
	margin-top: 10rpx;
}
/* 开门弹窗样式 */
.door-info {
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-bottom: 30rpx;
}
.door-icon {
	width: 120rpx;
	height: 120rpx;
	border-radius: 50%;
	background: rgba(76, 175, 80, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 20rpx;
	border: 1px solid rgba(76, 175, 80, 0.5);
	box-shadow: 0 0 20rpx rgba(76, 175, 80, 0.3);
}
.door-icon .material-icons {
	font-size: 60rpx;
	color: rgba(76, 175, 80, 0.9);
}
.door-message {
	font-size: 32rpx;
	font-weight: 600;
	color: #ffffff;
	margin-bottom: 20rpx;
}
.door-device-name {
	font-size: 28rpx;
	color: rgba(255, 255, 255, 0.8);
	margin-bottom: 10rpx;
}
.door-price {
	font-size: 36rpx;
	font-weight: 700;
	color: #ffffff;
	background: linear-gradient(to right, #A875FF, #ff36f9);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	text-shadow: 0 0 10rpx rgba(168, 117, 255, 0.3);
}
.door-buttons {
	display: flex;
	justify-content: space-between;
	margin-top: 30rpx;
}
.door-cancel-button, .door-pay-button {
	width: 45%;
	height: 80rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 40rpx;
}
.door-cancel-button {
	background: rgba(255, 255, 255, 0.1);
	border: 1px solid rgba(255, 255, 255, 0.3);
}
.door-pay-button {
	background: linear-gradient(145deg, #A875FF, #ff36f9);
	position: relative;
	overflow: hidden;
}
.door-pay-button::before {
	content: '';
	position: absolute;
	top: -50%;
	left: -50%;
	width: 200%;
	height: 200%;
	background: linear-gradient(transparent, rgba(255, 255, 255, 0.3), transparent);
	-webkit-transform: rotate(45deg);
	        transform: rotate(45deg);
	-webkit-animation: shine 2s infinite;
	        animation: shine 2s infinite;
}
.door-cancel-button text {
	font-size: 28rpx;
	color: rgba(255, 255, 255, 0.8);
}
.door-pay-button text {
	font-size: 28rpx;
	font-weight: 600;
	color: #ffffff;
	z-index: 1;
}
/* 价格信息样式 */
.price-info {
	margin-top: 40rpx;
	padding: 20rpx;
	background: rgba(30, 30, 30, 0.5);
	border-radius: 20rpx;
	border: 1px solid rgba(168, 117, 255, 0.3);
	display: flex;
	flex-direction: column;
	align-items: center;
}
.price-title {
	font-size: 28rpx;
	color: rgba(255, 255, 255, 0.7);
	margin-bottom: 10rpx;
}
.price-value {
	font-size: 40rpx;
	font-weight: 700;
	color: #ffffff;
	background: linear-gradient(to right, #A875FF, #ff36f9);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	text-shadow: 0 0 10rpx rgba(168, 117, 255, 0.3);
}
/* 支付按钮样式 */
.pay-button {
	background: rgba(255, 152, 0, 0.2);
	box-shadow: 0 8rpx 20rpx rgba(255, 152, 0, 0.2);
	border: 1px solid rgba(255, 152, 0, 0.5);
	-webkit-animation: pulse-orange 2s infinite ease-in-out;
	        animation: pulse-orange 2s infinite ease-in-out;
}
.pay-button::after {
	background: linear-gradient(145deg, rgba(255, 152, 0, 0.4), rgba(255, 152, 0, 0.1));
}
.pay-button::before,
.pay-button .ripple-layer {
	border-color: rgba(255, 152, 0, 0.5);
}
.pay-button:active {
	box-shadow: 0 4rpx 10rpx rgba(255, 152, 0, 0.2);
}
@-webkit-keyframes pulse-orange {
0%, 100% {
		box-shadow: 0 8rpx 20rpx rgba(255, 152, 0, 0.2);
}
50% {
		box-shadow: 0 8rpx 32rpx rgba(255, 152, 0, 0.5);
}
}
@keyframes pulse-orange {
0%, 100% {
		box-shadow: 0 8rpx 20rpx rgba(255, 152, 0, 0.2);
}
50% {
		box-shadow: 0 8rpx 32rpx rgba(255, 152, 0, 0.5);
}
}
/* 添加锁状态显示区域样式 */
.lock-status-section {
	margin-top: 40rpx;
	padding: 30rpx;
	background: rgba(30, 30, 30, 0.5);
	border-radius: 20rpx;
	border: 1px solid rgba(168, 117, 255, 0.3);
	display: flex;
	flex-direction: column;
	align-items: center;
}
.lock-status-title {
	font-size: 32rpx;
	color: rgba(255, 255, 255, 0.8);
	margin-bottom: 20rpx;
}
.lock-status-display {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 15rpx 40rpx;
	border-radius: 40rpx;
	margin-bottom: 20rpx;
}
.lock-status-display .material-icons {
	font-size: 44rpx;
	margin-right: 10rpx;
}
.lock-status-display text:not(.material-icons) {
	font-size: 32rpx;
	font-weight: 500;
}
.lock-open {
	background: rgba(76, 175, 80, 0.2);
	border: 1px solid rgba(76, 175, 80, 0.5);
	color: rgba(76, 175, 80, 0.9);
}
.lock-closed {
	background: rgba(158, 158, 158, 0.2);
	border: 1px solid rgba(158, 158, 158, 0.4);
	color: rgba(255, 255, 255, 0.8);
}
.lock-battery-info {
	display: flex;
	align-items: center;
	color: rgba(255, 255, 255, 0.7);
	font-size: 28rpx;
}
.lock-battery-info .material-icons {
	font-size: 36rpx;
	margin-right: 8rpx;
}
/* 底部导航栏样式 */
.bottom-nav-bar {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	height: 100rpx;
	background: rgba(30, 30, 30, 0.8);
	backdrop-filter: blur(10px);
	-webkit-backdrop-filter: blur(10px);
	display: flex;
	justify-content: space-around;
	align-items: center;
	border-top: 1px solid rgba(168, 117, 255, 0.3);
	padding-bottom: env(safe-area-inset-bottom);
	z-index: 100;
}
.nav-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	flex: 1;
	height: 100%;
	color: rgba(255, 255, 255, 0.7);
}
.nav-item .material-icons {
	font-size: 48rpx;
	margin-bottom: 4rpx;
}
.nav-item text:not(.material-icons) {
	font-size: 24rpx;
}
.nav-item.active {
	color: var(--primary-light, #A875FF);
}
/* 调整内容区域，确保不被底部导航栏遮挡 */
.content {
	padding-bottom: calc(150rpx + env(safe-area-inset-bottom));
}
/* 订单状态显示区域样式 */
.order-status-section {
	margin-top: 40rpx;
	padding: 30rpx;
	background: rgba(30, 30, 30, 0.5);
	border-radius: 20rpx;
	border: 1px solid rgba(168, 117, 255, 0.3);
	display: flex;
	flex-direction: column;
	align-items: center;
	-webkit-animation: fadeIn 0.5s ease;
	        animation: fadeIn 0.5s ease;
}
.order-status-title {
	font-size: 32rpx;
	color: rgba(255, 255, 255, 0.8);
	margin-bottom: 20rpx;
}
.order-status-display {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 15rpx 40rpx;
	border-radius: 40rpx;
	margin-bottom: 20rpx;
	min-width: 200rpx;
	text-align: center;
}
.order-status-display .material-icons {
	font-size: 44rpx;
	margin-right: 10rpx;
}
.order-status-display text:not(.material-icons) {
	font-size: 32rpx;
	font-weight: 500;
}
.order-paid {
	background: rgba(76, 175, 80, 0.2);
	border: 1px solid rgba(76, 175, 80, 0.5);
	color: rgba(76, 175, 80, 0.9);
	-webkit-animation: pulse-green-soft 2s infinite alternate;
	        animation: pulse-green-soft 2s infinite alternate;
}
@-webkit-keyframes pulse-green-soft {
0% {
		box-shadow: 0 0 10rpx rgba(76, 175, 80, 0.2);
}
100% {
		box-shadow: 0 0 20rpx rgba(76, 175, 80, 0.5);
}
}
@keyframes pulse-green-soft {
0% {
		box-shadow: 0 0 10rpx rgba(76, 175, 80, 0.2);
}
100% {
		box-shadow: 0 0 20rpx rgba(76, 175, 80, 0.5);
}
}
.order-unpaid {
	background: rgba(255, 152, 0, 0.2);
	border: 1px solid rgba(255, 152, 0, 0.5);
	color: rgba(255, 152, 0, 0.9);
}
.order-finished {
	background: rgba(33, 150, 243, 0.2);
	border: 1px solid rgba(33, 150, 243, 0.5);
	color: rgba(33, 150, 243, 0.9);
}
.order-cancelled {
	background: rgba(158, 158, 158, 0.2);
	border: 1px solid rgba(158, 158, 158, 0.4);
	color: rgba(255, 255, 255, 0.8);
}
.order-info {
	width: 100%;
	margin-top: 20rpx;
}
.order-info-item {
	display: flex;
	align-items: center;
	margin-bottom: 10rpx;
	color: rgba(255, 255, 255, 0.8);
}
.order-info-item .material-icons {
	font-size: 36rpx;
	margin-right: 10rpx;
}
.order-info-item text:not(.material-icons) {
	font-size: 28rpx;
}
.order-unpaid-tip {
	margin-top: 15rpx;
	color: rgba(255, 152, 0, 0.9);
	font-size: 28rpx;
	-webkit-animation: blink 1.5s infinite;
	        animation: blink 1.5s infinite;
}
@-webkit-keyframes blink {
0%, 100% {
		opacity: 1;
}
50% {
		opacity: 0.6;
}
}
@keyframes blink {
0%, 100% {
		opacity: 1;
}
50% {
		opacity: 0.6;
}
}
/* 状态标题 */
.status-title {
	font-size: 46rpx; /* 从44rpx增大到46rpx */
	font-weight: 600; /* 从500增加到600，加粗文字 */
	margin-bottom: 40rpx;
	text-align: center;
	color: #ffffff;
	text-shadow: 0 0 10rpx rgba(168, 117, 255, 0.8);
	letter-spacing: 2rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	-webkit-animation: breathing 4s ease-in-out infinite;
	        animation: breathing 4s ease-in-out infinite; /* 从3秒改为4秒，使动画更慢 */
}
.status-title view {
	line-height: 70rpx; /* 从68rpx增大到70rpx，适应更大的字体 */
}
/* 增强的呼吸灯动画 */
@-webkit-keyframes breathing {
0% {
		text-shadow: 0 0 8rpx rgba(168, 117, 255, 0.6);
		opacity: 0.85;
		-webkit-transform: scale(0.96);
		        transform: scale(0.96);
		font-size: 44rpx;
}
25% {
		text-shadow: 0 0 12rpx rgba(168, 117, 255, 0.75);
		opacity: 0.92;
		-webkit-transform: scale(0.98);
		        transform: scale(0.98);
		font-size: 45rpx;
}
50% {
		text-shadow: 
			0 0 15rpx rgba(168, 117, 255, 0.9),
			0 0 25rpx rgba(168, 117, 255, 0.6),
			0 0 35rpx rgba(168, 117, 255, 0.4),
			0 0 45rpx rgba(168, 117, 255, 0.2);
		opacity: 1;
		-webkit-transform: scale(1.04);
		        transform: scale(1.04);
		font-size: 48rpx;
}
75% {
		text-shadow: 0 0 12rpx rgba(168, 117, 255, 0.75);
		opacity: 0.92;
		-webkit-transform: scale(0.98);
		        transform: scale(0.98);
		font-size: 45rpx;
}
100% {
		text-shadow: 0 0 8rpx rgba(168, 117, 255, 0.6);
		opacity: 0.85;
		-webkit-transform: scale(0.96);
		        transform: scale(0.96);
		font-size: 44rpx;
}
}
@keyframes breathing {
0% {
		text-shadow: 0 0 8rpx rgba(168, 117, 255, 0.6);
		opacity: 0.85;
		-webkit-transform: scale(0.96);
		        transform: scale(0.96);
		font-size: 44rpx;
}
25% {
		text-shadow: 0 0 12rpx rgba(168, 117, 255, 0.75);
		opacity: 0.92;
		-webkit-transform: scale(0.98);
		        transform: scale(0.98);
		font-size: 45rpx;
}
50% {
		text-shadow: 
			0 0 15rpx rgba(168, 117, 255, 0.9),
			0 0 25rpx rgba(168, 117, 255, 0.6),
			0 0 35rpx rgba(168, 117, 255, 0.4),
			0 0 45rpx rgba(168, 117, 255, 0.2);
		opacity: 1;
		-webkit-transform: scale(1.04);
		        transform: scale(1.04);
		font-size: 48rpx;
}
75% {
		text-shadow: 0 0 12rpx rgba(168, 117, 255, 0.75);
		opacity: 0.92;
		-webkit-transform: scale(0.98);
		        transform: scale(0.98);
		font-size: 45rpx;
}
100% {
		text-shadow: 0 0 8rpx rgba(168, 117, 255, 0.6);
		opacity: 0.85;
		-webkit-transform: scale(0.96);
		        transform: scale(0.96);
		font-size: 44rpx;
}
}
.payment-shop-name {
	font-size: 28rpx;
	color: rgba(255, 255, 255, 0.8);
	margin: 8rpx 0 12rpx;
	padding: 6rpx 16rpx;
	background-color: rgba(168, 117, 255, 0.2);
	border-radius: 20rpx;
	border: 1px solid rgba(168, 117, 255, 0.3);
	display: inline-block;
}
.door-shop-name {
	font-size: 28rpx;
	color: rgba(255, 255, 255, 0.8);
	margin: 8rpx 0 12rpx;
	padding: 6rpx 16rpx;
	background-color: rgba(168, 117, 255, 0.2);
	border-radius: 20rpx;
	border: 1px solid rgba(168, 117, 255, 0.3);
	display: inline-block;
}

