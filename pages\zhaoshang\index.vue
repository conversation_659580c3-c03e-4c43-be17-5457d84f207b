<template>
	<view class="container page-zhaoshang" :class="{'ios-device': platform === 'ios'}" :data-version="styleVersion">
		<!-- 页面背景 -->
		<view class="page-background">
			<!-- 背景图片 -->
			<image class="background-image" src="/static/images/image.png" mode="aspectFill" :style="{opacity: isPageLoaded ? 1 : 0}" @load="onBackgroundImageLoaded"></image>
			
			<!-- 页面渐变背景 - 磨砂效果 -->
			<view class="gradient-overlay"></view>
		</view>
		
		<!-- 顶部状态栏占位 -->
		<view class="status-bar" :style="{height: statusBarHeight + 'px'}"></view>
		
		<!-- 返回按钮 -->
		<view class="floating-back" :style="Object.assign(platform === 'ios' ? dynamicBackStyle : {top: (statusBarHeight + 2) + 'px'})" @click="goBack">
			<text class="material-icons back-icon">arrow_back</text>
		</view>
		
		<!-- 内容区域 -->
		<scroll-view class="content-scroll" :style="Object.assign(platform === 'ios' ? dynamicContentStyle : {paddingTop: (statusBarHeight + 10) + 'px'})" scroll-y="true" enable-back-to-top show-scrollbar="false" enhanced="true" bounces="false" fast-deceleration="true" scroll-with-animation="true">
			<view class="content-container">
				<!-- 顶部图片 -->
				<image class="top-image" src="https://wuji1112-1312674791.cos.ap-guangzhou.myqcloud.com/zhaoshang/<EMAIL>" mode="widthFix" lazy-load="true"></image>
				
				<!-- 表单区域 -->
				<view class="form-container">
					<view class="form-title">填写信息，领取限时优惠政策!</view>
					
					<view class="form-group">
						<view class="form-item">
							<view class="label-container">
								<text class="form-label">联系人</text>
								<text class="required">*</text>
							</view>
							<input class="form-input" type="text" v-model="formData.name" placeholder="请输入您的姓名" placeholder-class="placeholder-style" />
						</view>
						
						<view class="form-item">
							<view class="label-container">
								<text class="form-label">联系方式</text>
								<text class="required">*</text>
							</view>
							<input class="form-input" type="number" v-model="formData.phone" maxlength="11" placeholder="请输入您的手机号" placeholder-class="placeholder-style" />
						</view>
						
						<view class="form-item">
							<view class="label-container">
								<text class="form-label">合作区域</text>
								<text class="required">*</text>
							</view>
							<input class="form-input" type="text" v-model="formData.region" placeholder="请输入您的合作区域" placeholder-class="placeholder-style" />
						</view>
						
						<button class="submit-button" @click="submitForm">
							<text class="submit-text">立即提交</text>
						</button>
					</view>
				</view>
				
				<!-- 底部图片 -->
				<image class="bottom-image" src="https://wuji1112-1312674791.cos.ap-guangzhou.myqcloud.com/zhaoshang/<EMAIL>" mode="widthFix" lazy-load="true"></image>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				formData: {
					name: '',
					phone: '',
					region: ''
				},
				isPageLoaded: false,
				statusBarHeight: 20, // 默认值
				backgroundLoaded: false,
				styleVersion: Date.now(), // 添加样式版本号，用于强制刷新样式
				platform: 'android', // 默认为android，将在onLoad中更新
				// iOS适配相关
				systemInfo: {},
				dynamicBackStyle: {},
				dynamicContentStyle: {}
			}
		},
		onLoad() {
			// 设置状态栏样式
			uni.setNavigationBarColor({
				frontColor: '#ffffff',
				backgroundColor: '#5433e0'
			});
			
			// 获取系统信息
			uni.getSystemInfo({
				success: (res) => {
					// 更新平台信息
					this.platform = res.platform.toLowerCase();
					this.systemInfo = res;
					console.log('招商页面 - 当前平台:', this.platform);
					console.log('招商页面 - 系统信息:', res);

					// 获取状态栏高度
					this.getStatusBarHeight();

					// 计算iOS适配参数
					this.calculateIOSAdaptation();
				}
			});
			
			// 预加载图片以提高性能
			this.preloadImages();
			
			// 强制刷新页面样式
			this.forceRefreshStyle();
		},
		onShow() {
			// 页面每次显示时强制刷新样式
			this.forceRefreshStyle();
			
			// 确保平台信息正确
			if (!this.platform || this.platform === '') {
				uni.getSystemInfo({
					success: (res) => {
						this.platform = res.platform.toLowerCase();
						console.log('onShow更新平台:', this.platform);
					}
				});
			}
		},
		onUnload() {
			// 页面卸载时释放资源
			this.isPageLoaded = false;
			this.backgroundLoaded = false;
		},
		methods: {
			// 计算iOS适配参数
			calculateIOSAdaptation() {
				if (this.platform === 'ios' && this.systemInfo) {
					const statusBarHeight = this.systemInfo.statusBarHeight || 44;
					const model = this.systemInfo.model || '';
					const safeAreaTop = this.systemInfo.safeArea ? this.systemInfo.safeArea.top : statusBarHeight;

					console.log('招商页面 - 状态栏高度:', statusBarHeight);
					console.log('招商页面 - 设备型号:', model);
					console.log('招商页面 - 安全区域顶部:', safeAreaTop);

					let backButtonTop, contentPaddingTop;

					// 全新的激进适配策略 - 直接使用状态栏高度
					if (model.includes('iPhone 16 Pro')) {
						// iPhone 16 Pro系列：使用状态栏高度，最小间距
						backButtonTop = statusBarHeight + 8;
						contentPaddingTop = statusBarHeight + 12;
					} else if (model.includes('iPhone 15 Pro') || model.includes('iPhone 14 Pro')) {
						// 其他灵动岛设备：使用状态栏高度加适度间距
						backButtonTop = statusBarHeight + 10;
						contentPaddingTop = statusBarHeight + 15;
					} else if (model.includes('iPhone X') || model.includes('iPhone 11') ||
							  model.includes('iPhone 12') || model.includes('iPhone 13')) {
						// 刘海屏设备：使用状态栏高度
						backButtonTop = statusBarHeight + 8;
						contentPaddingTop = statusBarHeight + 12;
					} else {
						// 其他iOS设备：使用安全区域
						backButtonTop = safeAreaTop + 5;
						contentPaddingTop = safeAreaTop + 10;
					}

					this.dynamicBackStyle = {
						top: backButtonTop + 'px'
					};

					this.dynamicContentStyle = {
						paddingTop: contentPaddingTop + 'px'
					};

					console.log('招商页面 - 全新适配策略 - 返回按钮位置:', backButtonTop + 'px');
					console.log('招商页面 - 全新适配策略 - 内容区域位置:', contentPaddingTop + 'px');
					console.log('招商页面 - 返回按钮样式:', this.dynamicBackStyle);
					console.log('招商页面 - 内容区域样式:', this.dynamicContentStyle);
				} else {
					this.dynamicBackStyle = {};
					this.dynamicContentStyle = {};
				}
			},
			// 强制刷新页面样式
			forceRefreshStyle() {
				// 使用setTimeout延迟执行，确保DOM已渲染
				setTimeout(() => {
					// 更新样式版本号，触发视图更新
					this.styleVersion = Date.now();
					
					// 触发页面重新计算样式
					uni.createSelectorQuery()
						.selectAll('.page-zhaoshang')
						.boundingClientRect()
						.exec(() => {
							console.log('页面样式已刷新');
							
							// 针对iOS设备进行额外处理
							if (this.platform === 'ios') {
								console.log('iOS设备样式优化');
								// 这里可以添加iOS特定的样式处理逻辑
								
								// 延迟一点时间再次刷新，确保iOS样式生效
								setTimeout(() => {
									this.styleVersion = Date.now() + 1;
								}, 100);
							}
						});
				}, 50);
			},
			
			// 背景图片加载完成
			onBackgroundImageLoaded() {
				this.backgroundLoaded = true;
				this.checkPageLoaded();
			},
			
			// 检查页面是否加载完成
			checkPageLoaded() {
				if (this.backgroundLoaded) {
					this.isPageLoaded = true;
				}
			},
			
			// 获取状态栏高度
			getStatusBarHeight() {
				// 获取系统信息
				uni.getSystemInfo({
					success: (res) => {
						this.statusBarHeight = res.statusBarHeight;
						console.log('状态栏高度:', this.statusBarHeight, '平台:', this.platform);
					}
				});
			},
			
			// 预加载图片
			preloadImages() {
				const imageUrls = [
					'https://wuji1112-1312674791.cos.ap-guangzhou.myqcloud.com/zhaoshang/<EMAIL>',
					'https://wuji1112-1312674791.cos.ap-guangzhou.myqcloud.com/zhaoshang/<EMAIL>'
				];
				
				// 使用Promise.all并行预加载所有图片
				Promise.all(imageUrls.map(url => {
					return new Promise((resolve) => {
						const image = new Image();
						image.onload = () => resolve();
						image.onerror = () => resolve(); // 即使加载失败也继续
						image.src = url;
					});
				})).then(() => {
					this.checkPageLoaded();
				});
			},
			
			// 返回上一页
			goBack() {
				uni.navigateBack({
					delta: 1
				});
			},
			
			// 提交表单
			submitForm() {
				// 表单验证
				if (!this.formData.name.trim()) {
					uni.showToast({
						title: '请输入联系人姓名',
						icon: 'none'
					});
					return;
				}
				
				if (!this.formData.phone || !/^1[3-9]\d{9}$/.test(this.formData.phone)) {
					uni.showToast({
						title: '请输入正确的手机号',
						icon: 'none'
					});
					return;
				}
				
				if (!this.formData.region.trim()) {
					uni.showToast({
						title: '请输入合作区域',
						icon: 'none'
					});
					return;
				}
				
				// 显示加载中
				uni.showLoading({
					title: '提交中...'
				});
				
				// 模拟API提交
				setTimeout(() => {
					uni.hideLoading();
					
					// 显示提交成功
					uni.showToast({
						title: '提交成功',
						icon: 'success',
						duration: 2000,
						success: () => {
							// 清空表单
							this.formData.name = '';
							this.formData.phone = '';
							this.formData.region = '';
						}
					});
				}, 1500);
			}
		}
	}
</script>

<style>
	/* CSS变量定义 */
	page {
		--primary-color: #5433e0;
		--primary-light: #A875FF;
		--text-color: #333333;
		--border-color: rgba(165, 166, 185, 0.5);
		--placeholder-color: rgba(153, 153, 153, 0.6);
		--error-color: #ba4d67;
		--safe-area-top: env(safe-area-inset-top);
		--safe-area-bottom: env(safe-area-inset-bottom);
		background-color: var(--primary-color);
		height: 100%;
		overflow: hidden;
	}
	
	/* 页面基础样式 */
	.page-zhaoshang {
		position: relative;
		width: 100%;
		height: 100%;
		display: flex;
		flex-direction: column;
	}
	
	/* iOS特定样式 */
	.ios-specific {
		/* iOS设备顶部区域更加紧凑 */
		--ios-top-padding: 0;
		--ios-back-top: 0;
	}
	
	/* 页面背景 */
	.page-background {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		z-index: 0;
		will-change: transform;
		backface-visibility: hidden;
		-webkit-backface-visibility: hidden;
	}
	
	.background-image {
		width: 100%;
		height: 100%;
		object-fit: cover;
		transition: opacity 0.3s ease;
	}
	
	.gradient-overlay {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background: linear-gradient(to bottom, 
			rgba(55, 0, 179, 0.8) 0%, 
			rgba(92, 15, 230, 0.7) 30%,
			rgba(108, 25, 240, 0.7) 100%);
		backdrop-filter: blur(5px);
		-webkit-backdrop-filter: blur(5px);
		z-index: 1;
	}
	
	/* iOS设备特定样式 - 渐变背景 */
	.ios-device .gradient-overlay {
		background: linear-gradient(to bottom, 
			rgba(55, 0, 179, 0.85) 0%, 
			rgba(92, 15, 230, 0.75) 20%,
			rgba(108, 25, 240, 0.7) 100%);
		backdrop-filter: blur(7px);
		-webkit-backdrop-filter: blur(7px);
	}
	
	/* 顶部状态栏占位 */
	.status-bar {
		width: 100%;
		position: fixed;
		top: 0;
		z-index: 100;
		background-color: transparent;
		height: 0; /* 减少高度 */
	}
	
	/* 返回按钮 */
	.floating-back {
		position: fixed;
		left: 30rpx;
		width: 60rpx; /* 从70rpx减少到60rpx */
		height: 60rpx; /* 从70rpx减少到60rpx */
		border-radius: 50%;
		background-color: rgba(168, 117, 255, 0.7);
		border: 2rpx solid rgba(255, 255, 255, 0.9);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 100;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.5);
	}
	
	/* iOS设备特定样式 - 返回按钮 */
	.ios-device .floating-back {
		width: 55rpx; /* iOS设备上更小的宽度 */
		height: 55rpx; /* iOS设备上更小的高度 */
		left: 25rpx; /* iOS设备上更靠左 */
	}

	.floating-back .back-icon {
		font-size: 36rpx !important; /* 从42rpx减少到36rpx，添加!important覆盖全局样式 */
		color: #ffffff;
		text-shadow: 0 0 10rpx rgba(0, 0, 0, 0.5);
	}
	
	/* iOS设备特定样式 - 返回按钮图标 */
	.ios-device .floating-back .back-icon {
		font-size: 32rpx !important; /* iOS设备上更小的图标 */
	}
	
	/* 内容滚动区域 */
	.content-scroll {
		position: relative;
		flex: 1;
		z-index: 10;
		padding-bottom: var(--safe-area-bottom);
		box-sizing: border-box;
		-webkit-overflow-scrolling: touch; /* iOS流畅滚动 */
	}
	
	/* 内容容器 */
	.content-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 0 30rpx 60rpx; /* 底部内边距从80rpx减少到60rpx */
		box-sizing: border-box;
		width: 100%;
	}
	
	/* iOS设备特定样式 - 内容容器 */
	.ios-device .content-container {
		padding: 0 30rpx 40rpx; /* iOS设备上更小的底部内边距 */
		margin-top: -15rpx; /* 为iOS设备添加负顶部边距，减少顶部空间 */
	}
	
	/* 顶部图片 */
	.top-image {
		width: 100%;
		max-height: 220rpx; /* 从240rpx减少到220rpx */
		object-fit: contain;
		will-change: transform;
		transform: translateZ(0);
		margin-top: -20rpx; /* 从-10rpx减少到-20rpx */
		margin-bottom: -10rpx; /* 添加负的底部边距 */
		filter: hue-rotate(150deg) saturate(1.2); /* 直接将蓝色转为紫色 */
	}
	
	/* iOS设备特定样式 - 顶部图片 */
	.ios-device .top-image {
		max-height: 200rpx; /* iOS设备上更小的高度 */
		margin-top: -40rpx; /* 从-30rpx改为-40rpx，进一步减少顶部空间 */
		margin-bottom: -15rpx; /* iOS设备上更大的负底部边距 */
		filter: hue-rotate(150deg) saturate(1.3); /* 直接将蓝色转为紫色，稍微增加饱和度 */
	}
	
	/* 表单容器 */
	.form-container {
		width: 100%;
		margin: 5rpx auto; /* 从10rpx减少到5rpx */
		background: #ffffff;
		border-radius: 20rpx;
		box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
		overflow: hidden;
		will-change: transform;
		transform: translateZ(0);
	}
	
	/* iOS设备特定样式 - 表单容器 */
	.ios-device .form-container {
		margin: 0 auto; /* iOS设备上移除上边距 */
	}
	
	.form-title {
		font-size: 32rpx; /* 从34rpx减少到32rpx */
		color: var(--primary-color);
		font-weight: bold;
		text-align: center;
		margin: 15rpx 0; /* 从20rpx减少到15rpx */
	}
	
	/* iOS设备特定样式 - 表单标题 */
	.ios-device .form-title {
		font-size: 30rpx; /* iOS设备上更小的字体 */
		margin: 12rpx 0; /* iOS设备上更小的边距 */
	}
	
	.form-group {
		padding: 0 40rpx 20rpx; /* 底部内边距从25rpx减少到20rpx */
	}
	
	/* iOS设备特定样式 - 表单组 */
	.ios-device .form-group {
		padding: 0 40rpx 15rpx; /* iOS设备上更小的内边距 */
	}
	
	/* 表单项 */
	.form-item {
		margin-bottom: 15rpx; /* 从20rpx减少到15rpx */
	}
	
	/* iOS设备特定样式 - 表单项 */
	.ios-device .form-item {
		margin-bottom: 12rpx; /* iOS设备上更小的下边距 */
	}
	
	.label-container {
		display: flex;
		align-items: center;
		margin-bottom: 6rpx; /* 从8rpx减少到6rpx */
	}
	
	/* iOS设备特定样式 - 标签容器 */
	.ios-device .label-container {
		margin-bottom: 4rpx; /* iOS设备上更小的下边距 */
	}
	
	.form-label {
		font-size: 24rpx;
		color: var(--text-color);
	}
	
	.required {
		color: var(--error-color);
		font-size: 22rpx;
		margin-left: 5rpx;
	}
	
	.form-input {
		width: 100%;
		height: 60rpx; /* 从65rpx减少到60rpx */
		background: #ffffff;
		border: 1rpx solid var(--border-color);
		border-radius: 10rpx;
		padding: 0 30rpx;
		font-size: 28rpx;
		color: var(--text-color);
		box-sizing: border-box;
	}
	
	/* iOS设备特定样式 - 输入框 */
	.ios-device .form-input {
		height: 55rpx; /* iOS设备上更小的高度 */
	}
	
	.placeholder-style {
		color: var(--placeholder-color);
	}
	
	/* 提交按钮 */
	.submit-button {
		width: 100%;
		height: 70rpx; /* 从75rpx减少到70rpx */
		background: var(--primary-color);
		border-radius: 40rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		margin-top: 20rpx; /* 从25rpx减少到20rpx */
		box-shadow: 0 8rpx 16rpx rgba(84, 51, 224, 0.3);
		border: none;
		will-change: transform;
	}
	
	/* iOS设备特定样式 - 提交按钮 */
	.ios-device .submit-button {
		height: 65rpx; /* iOS设备上更小的高度 */
		margin-top: 15rpx; /* iOS设备上更小的上边距 */
	}
	
	.submit-text {
		font-size: 30rpx; /* 从32rpx减少到30rpx */
		font-weight: 500;
		color: #ffffff;
		transform: translateZ(0);
	}
	
	.submit-button:active {
		transform: scale(0.98);
		box-shadow: 0 4rpx 8rpx rgba(84, 51, 224, 0.3);
	}
	
	/* 底部图片 */
	.bottom-image {
		width: 100%;
		max-height: 300rpx; /* 从320rpx减少到300rpx */
		object-fit: contain;
		margin-top: 5rpx; /* 从10rpx减少到5rpx */
		will-change: transform;
		transform: translateZ(0);
	}
	
	/* iOS设备特定样式 - 底部图片 */
	.ios-device .bottom-image {
		max-height: 280rpx; /* iOS设备上更小的高度 */
		margin-top: 0; /* iOS设备上移除上边距 */
	}
	
	/* Material Icons 字体 */
	@font-face {
		font-family: 'Material Icons';
		font-style: normal;
		font-weight: 400;
		src: url(https://fonts.gstatic.com/s/materialicons/v139/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format('woff2');
	}

	.material-icons {
		font-family: 'Material Icons';
		font-weight: normal;
		font-style: normal;
		font-size: 48rpx;
		line-height: 1;
		letter-spacing: normal;
		text-transform: none;
		display: inline-block;
		white-space: nowrap;
		word-wrap: normal;
		direction: ltr;
		-webkit-font-smoothing: antialiased;
		-moz-osx-font-smoothing: grayscale;
	}

	/* iOS灵动岛设备专用适配 */
	@supports (padding-top: max(0px, env(safe-area-inset-top))) {
		.ios-device .floating-back {
			top: calc(env(safe-area-inset-top) + 20px) !important;
		}

		.ios-device .content-scroll {
			padding-top: calc(env(safe-area-inset-top) + 25px) !important;
		}
	}

	/* iPhone 14 Pro及以上设备的特殊适配 */
	@media screen and (min-device-width: 393px) and (max-device-width: 430px) and (-webkit-device-pixel-ratio: 3) {
		.ios-device .floating-back {
			top: calc(env(safe-area-inset-top) + 25px) !important;
		}

		.ios-device .content-scroll {
			padding-top: calc(env(safe-area-inset-top) + 30px) !important;
		}
	}
</style>